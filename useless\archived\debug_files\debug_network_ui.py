"""
调试网络状态UI更新问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import QTimer
from showforai.sync.offline_manager import get_offline_manager
from loguru import logger

class DebugWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Network Status Debug")
        self.setFixedSize(400, 300)
        
        # Central widget
        central = QWidget()
        self.setCentralWidget(central)
        layout = QVBoxLayout(central)
        
        # Status labels
        self.network_label = QLabel("Network: Checking...")
        layout.addWidget(self.network_label)
        
        self.recording_label = QLabel("Recording: Checking...")
        layout.addWidget(self.recording_label)
        
        self.offline_warning = QLabel("⚠ Offline - Recording disabled")
        self.offline_warning.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #ff6b6b;
                padding: 5px;
                background-color: #fff5f5;
                border: 1px solid #ffdddd;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.offline_warning)
        
        self.log_label = QLabel("Logs:\n")
        self.log_label.setWordWrap(True)
        layout.addWidget(self.log_label)
        
        # Check button
        check_btn = QPushButton("Manual Check")
        check_btn.clicked.connect(self.check_network_status)
        layout.addWidget(check_btn)
        
        # Get offline manager
        self.offline_manager = get_offline_manager()
        self.offline_manager.add_status_callback(self.on_network_status_changed)
        
        # Timer for auto check
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_network_status)
        self.timer.start(5000)
        
        # Initial check
        self.check_network_status()
        
    def check_network_status(self):
        """Check and update network status display."""
        self.add_log("Checking network status...")
        
        # Check actual status
        is_online = self.offline_manager.is_online()
        can_record = self.offline_manager.is_recording_allowed()
        
        self.add_log(f"is_online(): {is_online}")
        self.add_log(f"is_recording_allowed(): {can_record}")
        
        # Update labels
        self.network_label.setText(f"Network: {'✅ ONLINE' if is_online else '❌ OFFLINE'}")
        self.recording_label.setText(f"Recording: {'✅ Allowed' if can_record else '❌ Not Allowed'}")
        
        # Update warning visibility
        if is_online:
            self.add_log("Hiding offline warning")
            self.offline_warning.hide()
        else:
            self.add_log("Showing offline warning")
            self.offline_warning.show()
            
    def on_network_status_changed(self, is_online: bool):
        """Handle network status change callback."""
        self.add_log(f"Callback triggered: is_online={is_online}")
        self.update_network_ui(is_online)
        
    def update_network_ui(self, is_online: bool):
        """Update UI based on network status."""
        self.add_log(f"update_network_ui called: is_online={is_online}")
        
        if is_online:
            self.offline_warning.hide()
            self.network_label.setText("Network: ✅ ONLINE")
            self.recording_label.setText("Recording: ✅ Allowed")
        else:
            self.offline_warning.show()
            self.network_label.setText("Network: ❌ OFFLINE")
            self.recording_label.setText("Recording: ❌ Not Allowed")
            
    def add_log(self, msg):
        """Add log message to display."""
        current = self.log_label.text()
        lines = current.split('\n')
        if len(lines) > 10:
            lines = lines[-10:]
        lines.append(msg)
        self.log_label.setText('\n'.join(lines))
        logger.info(msg)

def main():
    app = QApplication([])
    window = DebugWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()