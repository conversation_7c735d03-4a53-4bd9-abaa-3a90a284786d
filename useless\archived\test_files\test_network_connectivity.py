"""
网络连接测试程序
测试从中国青岛到新加坡服务器的连接情况
"""

import socket
import time
import json
import urllib.request
import urllib.error
import ssl
from datetime import datetime

def test_dns_resolution():
    """测试DNS解析"""
    print("\n" + "="*50)
    print("1. DNS解析测试")
    print("="*50)
    
    domains = [
        ("www.google.com", "国际（可能被墙）"),
        ("www.baidu.com", "国内"),
        ("*************", "AI服务器IP（无需DNS）"),
        ("api.anthropic.com", "Anthropic API"),
        ("www.bing.com", "微软（通常可访问）")
    ]
    
    results = {}
    for domain, desc in domains:
        try:
            start = time.time()
            ip = socket.gethostbyname(domain)
            elapsed = (time.time() - start) * 1000
            print(f"✅ {domain:20} ({desc:15}) -> {ip:15} [{elapsed:.0f}ms]")
            results[domain] = True
        except Exception as e:
            print(f"❌ {domain:20} ({desc:15}) -> 失败: {str(e)[:30]}")
            results[domain] = False
    
    return results

def test_socket_connection():
    """测试Socket连接"""
    print("\n" + "="*50)
    print("2. Socket直连测试")
    print("="*50)
    
    targets = [
        ("*******", 53, "Google DNS"),
        ("*******", 53, "Cloudflare DNS"),
        ("*********", 53, "阿里DNS"),
        ("***************", 53, "114 DNS"),
        ("*************", 8080, "AI服务器"),
        ("*************", 80, "AI服务器HTTP"),
        ("www.baidu.com", 80, "百度"),
    ]
    
    results = {}
    for host, port, desc in targets:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            start = time.time()
            result = sock.connect_ex((host, port))
            elapsed = (time.time() - start) * 1000
            sock.close()
            
            if result == 0:
                print(f"✅ {host:20}:{port:<5} ({desc:15}) 连接成功 [{elapsed:.0f}ms]")
                results[f"{host}:{port}"] = True
            else:
                print(f"❌ {host:20}:{port:<5} ({desc:15}) 连接失败 (错误码: {result})")
                results[f"{host}:{port}"] = False
        except Exception as e:
            print(f"❌ {host:20}:{port:<5} ({desc:15}) 异常: {str(e)[:30]}")
            results[f"{host}:{port}"] = False
    
    return results

def test_http_requests():
    """测试HTTP请求"""
    print("\n" + "="*50)
    print("3. HTTP请求测试")
    print("="*50)
    
    urls = [
        ("http://*************:8080", "AI服务器根路径"),
        ("http://*************:8080/api/v2/detect", "AI检测接口"),
        ("https://www.baidu.com", "百度HTTPS"),
        ("http://www.baidu.com", "百度HTTP"),
        ("https://api.anthropic.com", "Anthropic API"),
        ("https://www.bing.com", "Bing"),
    ]
    
    # 创建不验证SSL的context（用于测试）
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    results = {}
    for url, desc in urls:
        try:
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'ShowForAI-Test/1.0')
            
            start = time.time()
            if url.startswith('https'):
                response = urllib.request.urlopen(req, timeout=5, context=ssl_context)
            else:
                response = urllib.request.urlopen(req, timeout=5)
            
            elapsed = (time.time() - start) * 1000
            status = response.getcode()
            response.close()
            
            print(f"✅ {url:40} ({desc:15}) HTTP {status} [{elapsed:.0f}ms]")
            results[url] = True
        except urllib.error.URLError as e:
            print(f"❌ {url:40} ({desc:15}) 失败: {str(e.reason)[:30]}")
            results[url] = False
        except Exception as e:
            print(f"❌ {url:40} ({desc:15}) 异常: {str(e)[:30]}")
            results[url] = False
    
    return results

def test_ai_service_detailed():
    """详细测试AI服务"""
    print("\n" + "="*50)
    print("4. AI服务详细测试")
    print("="*50)
    
    base_url = "http://*************:8080"
    
    # 测试根路径
    try:
        req = urllib.request.Request(base_url)
        response = urllib.request.urlopen(req, timeout=10)
        data = response.read()
        response.close()
        
        try:
            json_data = json.loads(data)
            print(f"✅ 服务器响应正常")
            print(f"   响应内容: {str(json_data)[:100]}...")
        except:
            print(f"✅ 服务器响应正常（非JSON）")
            print(f"   响应长度: {len(data)} bytes")
    except Exception as e:
        print(f"❌ 无法连接到AI服务器: {e}")
        return False
    
    # 测试不同的端口
    ports = [8080, 80, 443, 8000, 3000]
    print(f"\n测试不同端口:")
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("*************", port))
            sock.close()
            if result == 0:
                print(f"  ✅ 端口 {port}: 开放")
            else:
                print(f"  ❌ 端口 {port}: 关闭")
        except:
            print(f"  ❌ 端口 {port}: 测试失败")
    
    return True

def test_alternative_check_methods():
    """测试替代的网络检查方法"""
    print("\n" + "="*50)
    print("5. 替代网络检查方法测试")
    print("="*50)
    
    methods = []
    
    # 方法1：检查国内DNS
    try:
        socket.gethostbyname("www.baidu.com")
        print("✅ 方法1: 使用百度DNS检查 - 可用")
        methods.append("baidu_dns")
    except:
        print("❌ 方法1: 使用百度DNS检查 - 不可用")
    
    # 方法2：直接检查AI服务器
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(("*************", 8080))
        sock.close()
        if result == 0:
            print("✅ 方法2: 直接连接AI服务器 - 可用")
            methods.append("ai_server_direct")
    except:
        print("❌ 方法2: 直接连接AI服务器 - 不可用")
    
    # 方法3：使用国内DNS服务器
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(("*********", 53))
        sock.close()
        if result == 0:
            print("✅ 方法3: 连接阿里DNS - 可用")
            methods.append("ali_dns")
    except:
        print("❌ 方法3: 连接阿里DNS - 不可用")
    
    # 方法4：HTTP请求百度
    try:
        req = urllib.request.Request("http://www.baidu.com")
        response = urllib.request.urlopen(req, timeout=5)
        response.close()
        print("✅ 方法4: HTTP请求百度 - 可用")
        methods.append("baidu_http")
    except:
        print("❌ 方法4: HTTP请求百度 - 不可用")
    
    return methods

def main():
    print("="*60)
    print("ShowForAI V3 网络连接诊断工具")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试位置: 中国青岛")
    print(f"目标服务器: 新加坡 (*************:8080)")
    
    # 运行所有测试
    dns_results = test_dns_resolution()
    socket_results = test_socket_connection()
    http_results = test_http_requests()
    ai_service_ok = test_ai_service_detailed()
    alt_methods = test_alternative_check_methods()
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    # 关键连接状态
    ai_server_socket = socket_results.get("*************:8080", False)
    ai_server_http = http_results.get("http://*************:8080", False)
    google_dns = dns_results.get("www.google.com", False)
    baidu_dns = dns_results.get("www.baidu.com", False)
    
    print(f"\n关键状态:")
    print(f"  AI服务器Socket连接: {'✅ 正常' if ai_server_socket else '❌ 失败'}")
    print(f"  AI服务器HTTP请求: {'✅ 正常' if ai_server_http else '❌ 失败'}")
    print(f"  Google DNS解析: {'✅ 正常' if google_dns else '❌ 失败（预期，因为GFW）'}")
    print(f"  百度DNS解析: {'✅ 正常' if baidu_dns else '❌ 失败'}")
    
    print(f"\n推荐的网络检查方法:")
    if ai_server_socket or ai_server_http:
        print("  ✅ 直接检查AI服务器连接（最可靠）")
    if baidu_dns:
        print("  ✅ 使用百度DNS作为网络检查")
    if "ali_dns" in alt_methods:
        print("  ✅ 使用阿里DNS作为备选")
    
    print("\n" + "="*60)
    print("建议的修改方案:")
    print("="*60)
    print("""
    将 offline_manager.py 中的 _check_connectivity 方法修改为:
    
    1. 首选：直接检查AI服务器连接
       socket.connect_ex(("*************", 8080))
    
    2. 备选：检查国内可访问的服务
       socket.gethostbyname("www.baidu.com")
    
    3. 不使用：www.google.com（在中国被墙）
    
    这样可以确保在中国境内正确检测网络状态。
    """)

if __name__ == "__main__":
    main()