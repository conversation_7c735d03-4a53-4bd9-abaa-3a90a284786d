# ShowForAI V3 GUI优化专项计划

## 1. GUI现状分析

### 1.1 当前问题
- 界面风格陈旧，缺乏现代感
- 布局不够直观，功能分区不明确
- 缺少视觉反馈和动画效果
- 响应式设计不足
- 色彩方案单调

### 1.2 改进目标
- 打造现代化、专业的界面
- 提供直观的用户操作流程
- 增强视觉吸引力和交互体验
- 支持多种主题和自定义
- 提高操作效率

## 2. 设计理念

### 2.1 设计原则
- **简洁性**：去除冗余元素，突出核心功能
- **一致性**：统一的视觉语言和交互模式
- **可访问性**：考虑不同用户的使用需求
- **响应性**：适应不同屏幕尺寸
- **现代感**：采用流行的设计趋势

### 2.2 设计风格
- Material Design 3.0 + Fluent Design混合
- 扁平化设计with微妙的深度感
- 圆角卡片式布局
- 柔和的阴影效果
- 平滑的动画过渡

## 3. 视觉设计规范

### 3.1 色彩方案

#### 暗色主题（默认）
```css
/* 主色调 */
--primary: #4A9EFF;        /* 主色 - 科技蓝 */
--primary-hover: #6BB0FF;  /* 主色悬停 */
--primary-active: #2E8AE6; /* 主色激活 */

/* 背景色 */
--bg-primary: #1A1A1A;     /* 主背景 */
--bg-secondary: #242424;   /* 次级背景 */
--bg-tertiary: #2E2E2E;    /* 第三级背景 */
--bg-elevated: #333333;    /* 提升背景 */

/* 文字色 */
--text-primary: #E5E5E5;   /* 主文字 */
--text-secondary: #A0A0A0; /* 次级文字 */
--text-disabled: #666666;  /* 禁用文字 */

/* 状态色 */
--success: #4CAF50;        /* 成功 */
--warning: #FF9800;        /* 警告 */
--error: #F44336;          /* 错误 */
--info: #2196F3;           /* 信息 */

/* 边框色 */
--border: #3A3A3A;         /* 边框 */
--border-hover: #4A4A4A;   /* 悬停边框 */
```

#### 亮色主题
```css
/* 主色调 */
--primary: #2196F3;        /* 主色 */
--primary-hover: #42A5F5;  /* 主色悬停 */
--primary-active: #1976D2; /* 主色激活 */

/* 背景色 */
--bg-primary: #FFFFFF;     /* 主背景 */
--bg-secondary: #F5F5F5;   /* 次级背景 */
--bg-tertiary: #EEEEEE;    /* 第三级背景 */
--bg-elevated: #FFFFFF;    /* 提升背景 */

/* 文字色 */
--text-primary: #212121;   /* 主文字 */
--text-secondary: #757575; /* 次级文字 */
--text-disabled: #BDBDBD;  /* 禁用文字 */
```

### 3.2 字体规范

```css
/* 字体家族 */
--font-primary: 'Segoe UI', 'Microsoft YaHei', sans-serif;
--font-mono: 'Cascadia Code', 'Consolas', monospace;

/* 字体大小 */
--text-xs: 11px;
--text-sm: 13px;
--text-base: 14px;
--text-lg: 16px;
--text-xl: 18px;
--text-2xl: 24px;
--text-3xl: 32px;

/* 字重 */
--font-light: 300;
--font-regular: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### 3.3 间距系统

```css
/* 间距规范 */
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 12px;
--spacing-lg: 16px;
--spacing-xl: 24px;
--spacing-2xl: 32px;
--spacing-3xl: 48px;

/* 圆角 */
--radius-sm: 4px;
--radius-md: 8px;
--radius-lg: 12px;
--radius-xl: 16px;
--radius-full: 9999px;
```

## 4. 组件设计

### 4.1 按钮组件

```python
# button_styles.py
BUTTON_STYLES = """
/* 基础按钮 */
QPushButton {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: 8px 16px;
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    transition: all 0.2s ease;
}

QPushButton:hover {
    background: var(--bg-elevated);
    border-color: var(--border-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 主按钮 */
QPushButton.primary {
    background: var(--primary);
    color: white;
    border: none;
}

QPushButton.primary:hover {
    background: var(--primary-hover);
}

/* 危险按钮 */
QPushButton.danger {
    background: var(--error);
    color: white;
    border: none;
}

/* 图标按钮 */
QPushButton.icon-button {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: var(--radius-full);
}
"""
```

### 4.2 卡片组件

```python
# card_design.py
class ModernCard(QWidget):
    """现代化卡片组件"""
    
    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            ModernCard {
                background: var(--bg-secondary);
                border-radius: var(--radius-lg);
                padding: var(--spacing-lg);
            }
            
            ModernCard:hover {
                background: var(--bg-tertiary);
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }
        """)
        
        # 添加进入/离开动画
        self.hover_animation = QPropertyAnimation(self, b"shadow")
        self.hover_animation.setDuration(200)
```

### 4.3 输入框组件

```python
# input_styles.py
INPUT_STYLES = """
QLineEdit, QTextEdit {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--border);
    border-radius: var(--radius-md);
    padding: 8px 12px;
    font-size: var(--text-base);
}

QLineEdit:focus, QTextEdit:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.2);
}

QLineEdit::placeholder {
    color: var(--text-disabled);
}
"""
```

## 5. 布局设计

### 5.1 主窗口布局

```python
# main_window_layout.py
class ModernMainWindow(QMainWindow):
    """现代化主窗口"""
    
    def setup_layout(self):
        # 创建中央widget
        central = QWidget()
        self.setCentralWidget(central)
        
        # 主布局
        main_layout = QVBoxLayout(central)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 顶部区域
        self.create_header()
        
        # 内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 侧边栏（240px固定宽度）
        self.sidebar = self.create_sidebar()
        self.sidebar.setFixedWidth(240)
        
        # 主内容区
        self.content_area = self.create_content_area()
        
        # 右侧面板（可折叠）
        self.right_panel = self.create_right_panel()
        self.right_panel.setFixedWidth(300)
        
        content_splitter.addWidget(self.sidebar)
        content_splitter.addWidget(self.content_area)
        content_splitter.addWidget(self.right_panel)
        
        main_layout.addWidget(content_splitter)
        
        # 底部状态栏
        self.create_status_bar()
```

### 5.2 侧边栏设计

```python
# sidebar_design.py
class ModernSidebar(QWidget):
    """现代化侧边栏"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setSpacing(8)
        
        # Logo区域
        logo_widget = self.create_logo()
        layout.addWidget(logo_widget)
        
        # 导航菜单
        nav_items = [
            ("📊", "仪表板", self.show_dashboard),
            ("📝", "我的脚本", self.show_scripts),
            ("▶️", "执行中心", self.show_executor),
            ("⚙️", "设置", self.show_settings),
        ]
        
        for icon, text, callback in nav_items:
            btn = self.create_nav_button(icon, text, callback)
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # 用户信息区
        user_widget = self.create_user_widget()
        layout.addWidget(user_widget)
```

## 6. 动画效果

### 6.1 过渡动画

```python
# animations.py
class AnimationHelper:
    """动画辅助类"""
    
    @staticmethod
    def fade_in(widget, duration=300):
        """淡入效果"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0)
        animation.setEndValue(1)
        animation.setEasingCurve(QEasingCurve.InOutQuad)
        animation.start()
        
        return animation
    
    @staticmethod
    def slide_in(widget, direction="left", duration=300):
        """滑入效果"""
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        
        if direction == "left":
            animation.setStartValue(QPoint(-widget.width(), widget.y()))
        elif direction == "right":
            animation.setStartValue(QPoint(widget.parent().width(), widget.y()))
        
        animation.setEndValue(widget.pos())
        animation.setEasingCurve(QEasingCurve.OutCubic)
        animation.start()
        
        return animation
    
    @staticmethod
    def bounce(widget, duration=500):
        """弹跳效果"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setEasingCurve(QEasingCurve.OutBounce)
        
        # 创建弹跳路径
        original = widget.geometry()
        animation.setKeyValueAt(0, original)
        animation.setKeyValueAt(0.5, original.adjusted(0, -20, 0, -20))
        animation.setKeyValueAt(1, original)
        
        animation.start()
        return animation
```

### 6.2 加载动画

```python
# loading_animation.py
class ModernLoadingWidget(QWidget):
    """现代化加载动画"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 创建旋转动画
        self.spinner = QLabel()
        self.spinner.setFixedSize(48, 48)
        
        # 使用SVG或动画GIF
        movie = QMovie("assets/spinner.gif")
        self.spinner.setMovie(movie)
        movie.start()
        
        # 加载文字
        self.text = QLabel("加载中...")
        self.text.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(self.spinner, alignment=Qt.AlignCenter)
        layout.addWidget(self.text)
```

## 7. 交互优化

### 7.1 拖拽支持

```python
# drag_drop.py
class DragDropArea(QWidget):
    """拖拽区域"""
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        
    def dragEnterEvent(self, event):
        if event.mimeData().hasFormat("application/x-script"):
            event.acceptProposedAction()
            self.setStyleSheet("""
                DragDropArea {
                    border: 2px dashed var(--primary);
                    background: rgba(74, 158, 255, 0.1);
                }
            """)
    
    def dropEvent(self, event):
        # 处理拖拽
        data = event.mimeData().data("application/x-script")
        self.handle_drop(data)
```

### 7.2 右键菜单

```python
# context_menu.py
class ModernContextMenu(QMenu):
    """现代化右键菜单"""
    
    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            QMenu {
                background: var(--bg-elevated);
                border: 1px solid var(--border);
                border-radius: var(--radius-md);
                padding: 4px;
            }
            
            QMenu::item {
                padding: 8px 16px;
                border-radius: var(--radius-sm);
            }
            
            QMenu::item:selected {
                background: var(--primary);
                color: white;
            }
        """)
```

## 8. 响应式设计

### 8.1 自适应布局

```python
# responsive_layout.py
class ResponsiveLayout(QLayout):
    """响应式布局"""
    
    def __init__(self):
        super().__init__()
        self.breakpoints = {
            'xs': 576,
            'sm': 768,
            'md': 992,
            'lg': 1200,
            'xl': 1920
        }
    
    def adjust_layout(self, width):
        """根据宽度调整布局"""
        if width < self.breakpoints['sm']:
            # 移动端布局
            self.set_mobile_layout()
        elif width < self.breakpoints['md']:
            # 平板布局
            self.set_tablet_layout()
        else:
            # 桌面布局
            self.set_desktop_layout()
```

## 9. 无障碍设计

### 9.1 键盘导航

```python
# keyboard_navigation.py
class KeyboardNavigator:
    """键盘导航支持"""
    
    def setup_shortcuts(self, window):
        # 全局快捷键
        shortcuts = {
            'Ctrl+N': window.new_script,
            'Ctrl+O': window.open_script,
            'Ctrl+S': window.save_script,
            'F5': window.start_execution,
            'Esc': window.stop_execution,
            'Tab': window.next_focus,
            'Shift+Tab': window.prev_focus,
        }
        
        for key, action in shortcuts.items():
            shortcut = QShortcut(QKeySequence(key), window)
            shortcut.activated.connect(action)
```

### 9.2 高对比度模式

```python
# high_contrast.py
HIGH_CONTRAST_THEME = """
/* 高对比度主题 */
* {
    background: #000000;
    color: #FFFFFF;
    border-color: #FFFFFF;
}

QPushButton:focus {
    outline: 3px solid #FFFF00;
}

QLineEdit:focus {
    border: 3px solid #FFFF00;
}
"""
```

## 10. 性能优化

### 10.1 虚拟滚动

```python
# virtual_scroll.py
class VirtualListWidget(QListWidget):
    """虚拟滚动列表"""
    
    def __init__(self):
        super().__init__()
        self.setUniformItemSizes(True)  # 优化性能
        self.setLayoutMode(QListView.Batched)  # 批量布局
        self.setBatchSize(20)  # 每批20个
```

### 10.2 延迟加载

```python
# lazy_loading.py
class LazyLoadWidget(QWidget):
    """延迟加载组件"""
    
    def __init__(self):
        super().__init__()
        self.is_loaded = False
        
    def showEvent(self, event):
        if not self.is_loaded:
            self.load_content()
            self.is_loaded = True
        super().showEvent(event)
```

## 11. 实施计划

### 第1周：基础框架
- [ ] 实现主题系统
- [ ] 创建基础组件库
- [ ] 设置样式变量

### 第2周：核心界面
- [ ] 重构主窗口
- [ ] 实现侧边栏
- [ ] 创建内容区域

### 第3周：组件开发
- [ ] 开发所有UI组件
- [ ] 实现动画系统
- [ ] 添加交互效果

### 第4周：优化完善
- [ ] 响应式适配
- [ ] 性能优化
- [ ] 无障碍支持

## 12. 测试验收

### 视觉测试
- [ ] 所有组件符合设计规范
- [ ] 主题切换正常
- [ ] 动画流畅

### 交互测试
- [ ] 所有交互响应及时
- [ ] 键盘导航完整
- [ ] 拖拽功能正常

### 性能测试
- [ ] 界面渲染 < 16ms
- [ ] 动画帧率 >= 60fps
- [ ] 内存占用合理

---

**文档状态**：完成
**创建时间**：2025-01-13
**版本**：1.0