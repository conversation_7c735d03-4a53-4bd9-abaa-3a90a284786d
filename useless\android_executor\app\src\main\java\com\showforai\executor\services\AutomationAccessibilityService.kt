package com.showforai.executor.services

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.view.accessibility.AccessibilityEvent
import kotlinx.coroutines.*
import timber.log.Timber
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 自动化无障碍服务
 * 
 * 使用AccessibilityService实现自动化操作：
 * - 点击、长按、双击
 * - 滑动手势
 * - 文本输入
 * - 滚动操作
 */
class AutomationAccessibilityService : AccessibilityService() {
    
    companion object {
        @Volatile
        private var instance: AutomationAccessibilityService? = null
        
        fun getInstance(): AutomationAccessibilityService? = instance
        
        fun isServiceEnabled(): Boolean = instance != null
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        Timber.i("AutomationAccessibilityService created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        serviceScope.cancel()
        Timber.i("AutomationAccessibilityService destroyed")
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 这里可以监听系统事件，用于调试或状态监控
        // 对于DSL执行器，我们主要使用手势功能，不需要处理事件
    }
    
    override fun onInterrupt() {
        Timber.w("AutomationAccessibilityService interrupted")
    }
    
    /**
     * 执行点击操作
     */
    suspend fun performClick(x: Float, y: Float): Boolean {
        return performGesture(createClickGesture(x, y))
    }
    
    /**
     * 执行长按操作
     */
    suspend fun performLongPress(x: Float, y: Float, duration: Long = 1000): Boolean {
        return performGesture(createLongPressGesture(x, y, duration))
    }
    
    /**
     * 执行双击操作
     */
    suspend fun performDoubleClick(x: Float, y: Float): Boolean {
        val firstClick = performClick(x, y)
        if (!firstClick) return false
        
        delay(100) // 双击间隔
        return performClick(x, y)
    }
    
    /**
     * 执行滑动操作
     */
    suspend fun performSwipe(
        startX: Float, 
        startY: Float, 
        endX: Float, 
        endY: Float, 
        duration: Long = 500
    ): Boolean {
        return performGesture(createSwipeGesture(startX, startY, endX, endY, duration))
    }
    
    /**
     * 执行滚动操作
     */
    suspend fun performScroll(
        centerX: Float,
        centerY: Float,
        direction: ScrollDirection,
        distance: Float = 500f,
        duration: Long = 300
    ): Boolean {
        val (startX, startY, endX, endY) = when (direction) {
            ScrollDirection.UP -> {
                val startY = centerY + distance / 2
                val endY = centerY - distance / 2
                arrayOf(centerX, startY, centerX, endY)
            }
            ScrollDirection.DOWN -> {
                val startY = centerY - distance / 2
                val endY = centerY + distance / 2
                arrayOf(centerX, startY, centerX, endY)
            }
            ScrollDirection.LEFT -> {
                val startX = centerX + distance / 2
                val endX = centerX - distance / 2
                arrayOf(startX, centerY, endX, centerY)
            }
            ScrollDirection.RIGHT -> {
                val startX = centerX - distance / 2
                val endX = centerX + distance / 2
                arrayOf(startX, centerY, endX, centerY)
            }
        }
        
        return performSwipe(startX, startY, endX, endY, duration)
    }
    
    /**
     * 执行缩放操作
     */
    suspend fun performPinch(
        centerX: Float,
        centerY: Float,
        startDistance: Float,
        endDistance: Float,
        duration: Long = 500
    ): Boolean {
        return performGesture(createPinchGesture(centerX, centerY, startDistance, endDistance, duration))
    }
    
    /**
     * 输入文本
     */
    suspend fun inputText(text: String): Boolean {
        return try {
            // 使用系统输入法输入文本
            // 注意：这需要先聚焦到输入框
            val arguments = android.os.Bundle().apply {
                putCharSequence(AccessibilityService.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            }
            
            rootInActiveWindow?.performAction(AccessibilityService.ACTION_SET_TEXT, arguments) ?: false
        } catch (e: Exception) {
            Timber.e(e, "Failed to input text: $text")
            false
        }
    }
    
    /**
     * 执行手势
     */
    private suspend fun performGesture(gesture: GestureDescription): Boolean {
        return suspendCoroutine { continuation ->
            val callback = object : AccessibilityService.GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    continuation.resume(true)
                }
                
                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Timber.w("Gesture cancelled")
                    continuation.resume(false)
                }
            }
            
            val success = dispatchGesture(gesture, callback, null)
            if (!success) {
                Timber.e("Failed to dispatch gesture")
                continuation.resume(false)
            }
        }
    }
    
    /**
     * 创建点击手势
     */
    private fun createClickGesture(x: Float, y: Float): GestureDescription {
        val path = Path().apply {
            moveTo(x, y)
        }
        
        val stroke = GestureDescription.StrokeDescription(path, 0, 50)
        return GestureDescription.Builder().addStroke(stroke).build()
    }
    
    /**
     * 创建长按手势
     */
    private fun createLongPressGesture(x: Float, y: Float, duration: Long): GestureDescription {
        val path = Path().apply {
            moveTo(x, y)
        }
        
        val stroke = GestureDescription.StrokeDescription(path, 0, duration)
        return GestureDescription.Builder().addStroke(stroke).build()
    }
    
    /**
     * 创建滑动手势
     */
    private fun createSwipeGesture(
        startX: Float, 
        startY: Float, 
        endX: Float, 
        endY: Float, 
        duration: Long
    ): GestureDescription {
        val path = Path().apply {
            moveTo(startX, startY)
            lineTo(endX, endY)
        }
        
        val stroke = GestureDescription.StrokeDescription(path, 0, duration)
        return GestureDescription.Builder().addStroke(stroke).build()
    }
    
    /**
     * 创建缩放手势
     */
    private fun createPinchGesture(
        centerX: Float,
        centerY: Float,
        startDistance: Float,
        endDistance: Float,
        duration: Long
    ): GestureDescription {
        val builder = GestureDescription.Builder()
        
        // 第一个手指路径
        val path1 = Path().apply {
            moveTo(centerX - startDistance / 2, centerY)
            lineTo(centerX - endDistance / 2, centerY)
        }
        
        // 第二个手指路径
        val path2 = Path().apply {
            moveTo(centerX + startDistance / 2, centerY)
            lineTo(centerX + endDistance / 2, centerY)
        }
        
        val stroke1 = GestureDescription.StrokeDescription(path1, 0, duration)
        val stroke2 = GestureDescription.StrokeDescription(path2, 0, duration)
        
        return builder.addStroke(stroke1).addStroke(stroke2).build()
    }
}

/**
 * 滚动方向枚举
 */
enum class ScrollDirection {
    UP, DOWN, LEFT, RIGHT
}
