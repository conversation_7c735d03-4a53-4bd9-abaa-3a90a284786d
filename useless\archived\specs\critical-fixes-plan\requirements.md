# Critical Fixes Requirements

## 项目现状分析

### 1. 主要阻塞问题（P0级）
1. **循环导入错误** - 导致程序无法启动
   - ai模块和gui模块之间存在循环依赖
   - element_detector.py 依赖 gui.status_manager
   - gui.main_window 依赖 api.adapter
   - api.adapter 依赖 ai.ai_service

2. **类名不一致错误**
   - 测试期待 `Standardizer768`，实际类名是 `Standardizer`
   - 测试期待 `CircularBuffer`，实际类名是 `CircularFrameBuffer`

3. **核心功能缺失**
   - 网络状态检测与UI集成不完整
   - 离线模式录制按钮禁用逻辑未实现
   - 进度反馈系统未完成

### 2. 违反产品原则的问题（P1级）
1. **录制流程不完整**
   - 缺少10FPS持续截图机制的正确实现
   - 点击前第3帧获取逻辑未集成
   - 操作间隔记录不精确

2. **智能等待机制未完全实现**
   - 主动模式的步进执行逻辑不完整
   - 辅助模式的无限等待未正确实现
   - 首次识别失败后的1秒重试机制未集成

3. **BBOX裁切流程不统一**
   - 裁切逻辑分散在多个模块
   - 768×768坐标系统使用不一致

### 3. 功能完整性问题（P2级）
1. 分享模块API未完全实现
2. 脚本管理对话框存在TODO占位
3. 帮助文档链接未实现

## 修复要求

### P0级要求（立即修复，阻塞产品运行）
1. **解决循环导入**
   - 必须让程序能成功启动
   - 保持模块职责清晰

2. **修复类名不一致**
   - 统一所有引用
   - 确保测试能运行

3. **实现离线模式核心逻辑**
   - 网络状态检测
   - 录制按钮禁用
   - 状态提示

### P1级要求（核心功能，影响产品原则）
1. **完善录制流程**
   - 实现10FPS循环缓冲
   - 精确的时间间隔记录
   - 点击前第3帧获取

2. **实现智能等待机制**
   - 主动模式步进执行
   - 辅助模式无限等待
   - 持续识别机制

3. **统一BBOX处理**
   - 集中裁切逻辑
   - 确保768×768一致性

### P2级要求（功能完善）
1. 完成分享功能集成
2. 实现所有对话框
3. 添加帮助文档

## 验收标准

### P0级验收
- [ ] `python -m showforai --help` 能正常显示帮助信息
- [ ] `python -m showforai gui` 能启动主界面
- [ ] 离线状态下录制按钮显示为禁用
- [ ] 所有测试能成功导入（不一定通过）

### P1级验收
- [ ] 录制功能能正常工作并记录准确的时间间隔
- [ ] 执行时能按照录制间隔进行智能等待
- [ ] BBOX裁切在768×768坐标系下准确执行

### P2级验收
- [ ] 分享功能端到端可用
- [ ] 所有UI交互流畅无TODO
- [ ] 帮助文档可访问