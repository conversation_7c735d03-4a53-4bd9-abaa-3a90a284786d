# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MessageQueueForCreateEventRuleInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_encrypt': 'list[int]',
        'endpoints': 'str',
        'instance_id': 'str',
        'password': 'str',
        'region': 'str',
        'topic': 'str',
        'type': 'str',
        'username': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'auth_encrypt': 'AuthEncrypt',
        'endpoints': 'Endpoints',
        'instance_id': 'InstanceId',
        'password': 'Password',
        'region': 'Region',
        'topic': 'Topic',
        'type': 'Type',
        'username': 'Username',
        'vpc_id': 'VpcId'
    }

    def __init__(self, auth_encrypt=None, endpoints=None, instance_id=None, password=None, region=None, topic=None, type=None, username=None, vpc_id=None, _configuration=None):  # noqa: E501
        """MessageQueueForCreateEventRuleInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_encrypt = None
        self._endpoints = None
        self._instance_id = None
        self._password = None
        self._region = None
        self._topic = None
        self._type = None
        self._username = None
        self._vpc_id = None
        self.discriminator = None

        if auth_encrypt is not None:
            self.auth_encrypt = auth_encrypt
        if endpoints is not None:
            self.endpoints = endpoints
        if instance_id is not None:
            self.instance_id = instance_id
        if password is not None:
            self.password = password
        if region is not None:
            self.region = region
        if topic is not None:
            self.topic = topic
        if type is not None:
            self.type = type
        if username is not None:
            self.username = username
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def auth_encrypt(self):
        """Gets the auth_encrypt of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The auth_encrypt of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: list[int]
        """
        return self._auth_encrypt

    @auth_encrypt.setter
    def auth_encrypt(self, auth_encrypt):
        """Sets the auth_encrypt of this MessageQueueForCreateEventRuleInput.


        :param auth_encrypt: The auth_encrypt of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: list[int]
        """

        self._auth_encrypt = auth_encrypt

    @property
    def endpoints(self):
        """Gets the endpoints of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The endpoints of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._endpoints

    @endpoints.setter
    def endpoints(self, endpoints):
        """Sets the endpoints of this MessageQueueForCreateEventRuleInput.


        :param endpoints: The endpoints of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._endpoints = endpoints

    @property
    def instance_id(self):
        """Gets the instance_id of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The instance_id of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this MessageQueueForCreateEventRuleInput.


        :param instance_id: The instance_id of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def password(self):
        """Gets the password of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The password of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this MessageQueueForCreateEventRuleInput.


        :param password: The password of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def region(self):
        """Gets the region of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The region of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this MessageQueueForCreateEventRuleInput.


        :param region: The region of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def topic(self):
        """Gets the topic of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The topic of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._topic

    @topic.setter
    def topic(self, topic):
        """Sets the topic of this MessageQueueForCreateEventRuleInput.


        :param topic: The topic of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._topic = topic

    @property
    def type(self):
        """Gets the type of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The type of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this MessageQueueForCreateEventRuleInput.


        :param type: The type of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def username(self):
        """Gets the username of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The username of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this MessageQueueForCreateEventRuleInput.


        :param username: The username of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._username = username

    @property
    def vpc_id(self):
        """Gets the vpc_id of this MessageQueueForCreateEventRuleInput.  # noqa: E501


        :return: The vpc_id of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this MessageQueueForCreateEventRuleInput.


        :param vpc_id: The vpc_id of this MessageQueueForCreateEventRuleInput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MessageQueueForCreateEventRuleInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MessageQueueForCreateEventRuleInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MessageQueueForCreateEventRuleInput):
            return True

        return self.to_dict() != other.to_dict()
