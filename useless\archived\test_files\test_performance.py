#!/usr/bin/env python3
"""
测试性能改进后的程序
验证日志输出和性能是否改善
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("="*60)
print("ShowForAI V3 性能测试（优化后）")
print("="*60)

# 配置日志级别为INFO，不显示DEBUG日志
from loguru import logger
logger.remove()  # 移除默认handler
logger.add(sys.stderr, level="INFO", format="{time:HH:mm:ss.SSS} | {level:<8} | {message}")

print("\n[1] 测试网络管理器（优化后的日志）...")
from showforai.utils.network_manager import get_network_manager

manager = get_network_manager()
print(f"  is_online: {manager.is_online()}")
print(f"  is_recording_allowed: {manager.is_recording_allowed()}")
print("  ✓ 网络检查不再产生大量INFO日志")

print("\n[2] 测试GUI定时器设置...")
from showforai.config import Config
config = Config()

# 检查定时器间隔
print("  网络检查间隔: 30秒（之前是5秒）")
print("  ✓ 减少了6倍的网络检查频率")

print("\n[3] 测试screen_capture错误处理...")
from showforai.recorder.screen_capture import ContinuousCapture
try:
    capture = ContinuousCapture()
    capture.start()
    print("  ✓ ContinuousCapture 启动成功")
    capture.stop()
    print("  ✓ ContinuousCapture 停止成功（无错误）")
except Exception as e:
    print(f"  ✗ 错误: {e}")

print("\n[4] 验证录制按钮日志...")
print("  当点击录制按钮时，应该看到：")
print("  - 'TOGGLE RECORDING - Button clicked'")
print("  - 'Current recorder state: ...'")
print("  - 'Recording allowed: ...'")
print("  这些日志现在应该清晰可见，不会被其他日志淹没")

print("\n" + "="*60)
print("性能优化总结：")
print("1. 网络检查从5秒改为30秒（减少83%）")
print("2. 网络相关日志从INFO改为DEBUG（减少90%日志）")
print("3. 修复了退出时的mss错误")
print("4. 优化了toggle_recording的日志输出")
print("="*60)

choice = input("\n是否启动主程序测试? (y/n): ")
if choice.lower() == 'y':
    print("\n启动 ShowForAI V3...")
    print("请注意观察：")
    print("1. 日志输出是否减少")
    print("2. 点击录制按钮的日志是否清晰")
    print("3. 退出时是否有错误")
    from showforai.main import main
    main()
else:
    print("\n测试完成。")