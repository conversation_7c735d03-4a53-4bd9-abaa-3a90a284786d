"""Bug Fixes and Patches for ShowForAI V3

This module contains critical bug fixes and patches for known issues
in the ShowForAI system, including memory management, crash prevention,
and resource cleanup.

Key Fixes:
- Recording crash prevention
- Memory leak fixes
- Offline mode detection
- BBOX validation
- Resource cleanup management

Product Principles:
- Stability first
- Graceful error handling
- Resource efficiency
- Data integrity
"""

import gc
import sys
import time
import threading
import weakref
import traceback
from typing import Optional, Dict, Any, List, Callable, Set
from dataclasses import dataclass, field
from contextlib import contextmanager
import psutil
import numpy as np
from PIL import Image
from loguru import logger


@dataclass
class ResourceMonitor:
    """Monitors system resources to prevent crashes."""
    
    memory_threshold_mb: int = 500  # Warn if available memory below this
    cpu_threshold_percent: int = 90  # Warn if CPU usage above this
    check_interval: float = 5.0  # Seconds between checks
    
    def __post_init__(self):
        """Initialize monitoring."""
        self.is_monitoring = False
        self._monitor_thread = None
        self._callbacks: List[Callable] = []
    
    def start_monitoring(self):
        """Start resource monitoring."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        self.is_monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1)
        logger.info("Resource monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                # Check memory
                memory = psutil.virtual_memory()
                available_mb = memory.available / (1024 * 1024)
                
                if available_mb < self.memory_threshold_mb:
                    self._trigger_warning("memory", available_mb)
                
                # Check CPU
                cpu_percent = psutil.cpu_percent(interval=1)
                
                if cpu_percent > self.cpu_threshold_percent:
                    self._trigger_warning("cpu", cpu_percent)
                
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
            
            time.sleep(self.check_interval)
    
    def _trigger_warning(self, resource_type: str, value: float):
        """Trigger resource warning."""
        logger.warning(f"Resource warning: {resource_type} = {value}")
        
        for callback in self._callbacks:
            try:
                callback(resource_type, value)
            except Exception as e:
                logger.error(f"Resource callback error: {e}")
    
    def add_callback(self, callback: Callable):
        """Add warning callback."""
        self._callbacks.append(callback)
    
    def get_current_usage(self) -> Dict[str, Any]:
        """Get current resource usage."""
        try:
            memory = psutil.virtual_memory()
            return {
                "memory_percent": memory.percent,
                "memory_available_mb": memory.available / (1024 * 1024),
                "cpu_percent": psutil.cpu_percent(interval=0.1),
                "process_memory_mb": psutil.Process().memory_info().rss / (1024 * 1024)
            }
        except Exception as e:
            logger.error(f"Failed to get resource usage: {e}")
            return {}


class MemoryManager:
    """Manages memory usage and prevents leaks."""
    
    def __init__(self, max_cache_size_mb: int = 100):
        """Initialize memory manager.
        
        Args:
            max_cache_size_mb: Maximum cache size in MB
        """
        self.max_cache_size = max_cache_size_mb * 1024 * 1024  # Convert to bytes
        self._cache: weakref.WeakValueDictionary = weakref.WeakValueDictionary()
        self._cache_sizes: Dict[str, int] = {}
        self._total_cache_size = 0
        self._lock = threading.RLock()
    
    def cache_image(self, key: str, image: Image.Image) -> bool:
        """Cache an image with memory management.
        
        Args:
            key: Cache key
            image: PIL Image to cache
            
        Returns:
            True if cached successfully
        """
        with self._lock:
            # Calculate image size
            size = self._estimate_image_size(image)
            
            # Check if we need to free memory
            if self._total_cache_size + size > self.max_cache_size:
                self._free_memory(size)
            
            # Cache the image
            self._cache[key] = image
            self._cache_sizes[key] = size
            self._total_cache_size += size
            
            return True
    
    def get_cached_image(self, key: str) -> Optional[Image.Image]:
        """Get cached image.
        
        Args:
            key: Cache key
            
        Returns:
            Cached image or None
        """
        return self._cache.get(key)
    
    def clear_cache(self):
        """Clear all cached data."""
        with self._lock:
            self._cache.clear()
            self._cache_sizes.clear()
            self._total_cache_size = 0
            gc.collect()
            logger.info("Cache cleared")
    
    def _estimate_image_size(self, image: Image.Image) -> int:
        """Estimate image memory size.
        
        Args:
            image: PIL Image
            
        Returns:
            Estimated size in bytes
        """
        # Rough estimation: width * height * channels * bytes_per_pixel
        channels = len(image.getbands())
        return image.width * image.height * channels * 1
    
    def _free_memory(self, required_size: int):
        """Free memory to make room for new data.
        
        Args:
            required_size: Size needed in bytes
        """
        # Remove items until we have enough space
        freed_size = 0
        keys_to_remove = []
        
        for key, size in list(self._cache_sizes.items()):
            if key not in self._cache:  # Already garbage collected
                keys_to_remove.append(key)
                freed_size += size
            elif freed_size < required_size:
                keys_to_remove.append(key)
                freed_size += size
            else:
                break
        
        # Remove items
        for key in keys_to_remove:
            self._cache.pop(key, None)
            size = self._cache_sizes.pop(key, 0)
            self._total_cache_size -= size
        
        # Force garbage collection
        gc.collect()
    
    @contextmanager
    def temporary_cache(self):
        """Context manager for temporary caching."""
        temp_cache = {}
        
        try:
            yield temp_cache
        finally:
            # Clear temporary cache
            temp_cache.clear()
            gc.collect()


class RecordingCrashFixer:
    """Fixes and prevents recording crashes."""
    
    @staticmethod
    def safe_screenshot(capture_func: Callable) -> Optional[np.ndarray]:
        """Safely capture screenshot with error handling.
        
        Args:
            capture_func: Screenshot capture function
            
        Returns:
            Screenshot array or None on error
        """
        max_retries = 3
        retry_delay = 0.5
        
        for attempt in range(max_retries):
            try:
                screenshot = capture_func()
                
                # Validate screenshot
                if screenshot is None or screenshot.size == 0:
                    raise ValueError("Invalid screenshot captured")
                
                return screenshot
                
            except Exception as e:
                logger.warning(f"Screenshot attempt {attempt + 1} failed: {e}")
                
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"Failed to capture screenshot after {max_retries} attempts")
                    return None
    
    @staticmethod
    def safe_recording_loop(
        record_func: Callable,
        error_callback: Optional[Callable] = None,
        max_errors: int = 5
    ):
        """Safe recording loop with error recovery.
        
        Args:
            record_func: Recording function
            error_callback: Callback on error
            max_errors: Maximum consecutive errors before stopping
        """
        consecutive_errors = 0
        
        while True:
            try:
                # Execute recording function
                should_continue = record_func()
                
                if not should_continue:
                    break
                
                # Reset error count on success
                consecutive_errors = 0
                
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"Recording error ({consecutive_errors}/{max_errors}): {e}")
                logger.debug(traceback.format_exc())
                
                if error_callback:
                    try:
                        error_callback(e)
                    except Exception as cb_error:
                        logger.error(f"Error callback failed: {cb_error}")
                
                if consecutive_errors >= max_errors:
                    logger.error("Too many consecutive errors, stopping recording")
                    break
                
                # Wait before retry
                time.sleep(1.0)
    
    @staticmethod
    def prevent_memory_leak(objects_to_cleanup: List[Any]):
        """Prevent memory leaks by ensuring cleanup.
        
        Args:
            objects_to_cleanup: List of objects to clean up
        """
        for obj in objects_to_cleanup:
            try:
                # Try common cleanup methods
                if hasattr(obj, 'close'):
                    obj.close()
                elif hasattr(obj, 'cleanup'):
                    obj.cleanup()
                elif hasattr(obj, 'destroy'):
                    obj.destroy()
                elif hasattr(obj, 'release'):
                    obj.release()
                
                # Clear references
                if hasattr(obj, '__dict__'):
                    obj.__dict__.clear()
                    
            except Exception as e:
                logger.debug(f"Cleanup error for {type(obj)}: {e}")
        
        # Clear list and force garbage collection
        objects_to_cleanup.clear()
        gc.collect()


class OfflineModeDetector:
    """Detects and handles offline mode."""
    
    def __init__(self, check_urls: Optional[List[str]] = None):
        """Initialize offline detector.
        
        Args:
            check_urls: URLs to check for connectivity
        """
        self.check_urls = check_urls or [
            "https://www.google.com",
            "https://www.cloudflare.com",
            "https://www.microsoft.com"
        ]
        self._is_offline = False
        self._last_check = 0
        self._check_interval = 30  # seconds
    
    def is_offline(self, force_check: bool = False) -> bool:
        """Check if system is offline.
        
        Args:
            force_check: Force connectivity check
            
        Returns:
            True if offline
        """
        current_time = time.time()
        
        # Use cached result if recent
        if not force_check and current_time - self._last_check < self._check_interval:
            return self._is_offline
        
        # Perform connectivity check
        self._is_offline = not self._check_connectivity()
        self._last_check = current_time
        
        return self._is_offline
    
    def _check_connectivity(self) -> bool:
        """Check internet connectivity.
        
        Returns:
            True if connected
        """
        import socket
        import urllib.request
        
        # First try DNS resolution
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return True
        except OSError:
            pass
        
        # Try HTTP requests
        for url in self.check_urls:
            try:
                urllib.request.urlopen(url, timeout=5)
                return True
            except Exception:
                continue
        
        return False
    
    @contextmanager
    def offline_fallback(self, online_func: Callable, offline_func: Callable):
        """Context manager for offline fallback.
        
        Args:
            online_func: Function to use when online
            offline_func: Function to use when offline
        """
        if self.is_offline():
            logger.info("Operating in offline mode")
            yield offline_func
        else:
            yield online_func


class BBoxValidator:
    """Validates and fixes bounding boxes."""
    
    @staticmethod
    def validate_bbox(
        bbox: List[int],
        image_width: int,
        image_height: int,
        min_size: int = 5
    ) -> bool:
        """Validate bounding box.
        
        Args:
            bbox: [x1, y1, x2, y2]
            image_width: Image width
            image_height: Image height
            min_size: Minimum bbox dimension
            
        Returns:
            True if valid
        """
        if not bbox or len(bbox) != 4:
            return False
        
        x1, y1, x2, y2 = bbox
        
        # Check bounds
        if x1 < 0 or y1 < 0 or x2 > image_width or y2 > image_height:
            return False
        
        # Check size
        if x2 - x1 < min_size or y2 - y1 < min_size:
            return False
        
        # Check order
        if x2 <= x1 or y2 <= y1:
            return False
        
        return True
    
    @staticmethod
    def fix_bbox(
        bbox: List[int],
        image_width: int,
        image_height: int,
        min_size: int = 5
    ) -> Optional[List[int]]:
        """Fix invalid bounding box.
        
        Args:
            bbox: [x1, y1, x2, y2]
            image_width: Image width
            image_height: Image height
            min_size: Minimum bbox dimension
            
        Returns:
            Fixed bbox or None if unfixable
        """
        if not bbox or len(bbox) != 4:
            return None
        
        x1, y1, x2, y2 = bbox
        
        # Fix order
        if x2 < x1:
            x1, x2 = x2, x1
        if y2 < y1:
            y1, y2 = y2, y1
        
        # Clip to image bounds
        x1 = max(0, min(x1, image_width - min_size))
        y1 = max(0, min(y1, image_height - min_size))
        x2 = max(x1 + min_size, min(x2, image_width))
        y2 = max(y1 + min_size, min(y2, image_height))
        
        # Ensure minimum size
        if x2 - x1 < min_size:
            if x1 + min_size <= image_width:
                x2 = x1 + min_size
            else:
                x1 = x2 - min_size
        
        if y2 - y1 < min_size:
            if y1 + min_size <= image_height:
                y2 = y1 + min_size
            else:
                y1 = y2 - min_size
        
        # Final validation
        fixed_bbox = [int(x1), int(y1), int(x2), int(y2)]
        
        if BBoxValidator.validate_bbox(fixed_bbox, image_width, image_height, min_size):
            return fixed_bbox
        
        return None
    
    @staticmethod
    def expand_bbox(
        bbox: List[int],
        expansion: int,
        image_width: int,
        image_height: int
    ) -> List[int]:
        """Expand bounding box by pixels.
        
        Args:
            bbox: [x1, y1, x2, y2]
            expansion: Pixels to expand
            image_width: Image width
            image_height: Image height
            
        Returns:
            Expanded bbox
        """
        x1, y1, x2, y2 = bbox
        
        # Expand
        x1 = max(0, x1 - expansion)
        y1 = max(0, y1 - expansion)
        x2 = min(image_width, x2 + expansion)
        y2 = min(image_height, y2 + expansion)
        
        return [x1, y1, x2, y2]


class ResourceCleanupManager:
    """Manages resource cleanup to prevent leaks."""
    
    def __init__(self):
        """Initialize cleanup manager."""
        self._resources: Set[Any] = set()
        self._cleanup_funcs: Dict[Any, Callable] = {}
        self._lock = threading.RLock()
    
    def register(self, resource: Any, cleanup_func: Optional[Callable] = None):
        """Register a resource for cleanup.
        
        Args:
            resource: Resource to track
            cleanup_func: Optional cleanup function
        """
        with self._lock:
            self._resources.add(resource)
            
            if cleanup_func:
                self._cleanup_funcs[resource] = cleanup_func
    
    def unregister(self, resource: Any):
        """Unregister a resource.
        
        Args:
            resource: Resource to unregister
        """
        with self._lock:
            self._resources.discard(resource)
            self._cleanup_funcs.pop(resource, None)
    
    def cleanup(self, resource: Any = None):
        """Clean up resources.
        
        Args:
            resource: Specific resource to clean (all if None)
        """
        with self._lock:
            if resource:
                self._cleanup_single(resource)
            else:
                self._cleanup_all()
    
    def _cleanup_single(self, resource: Any):
        """Clean up a single resource."""
        try:
            # Use custom cleanup function if available
            if resource in self._cleanup_funcs:
                self._cleanup_funcs[resource]()
            else:
                # Try common cleanup methods
                if hasattr(resource, 'close'):
                    resource.close()
                elif hasattr(resource, 'cleanup'):
                    resource.cleanup()
                elif hasattr(resource, 'release'):
                    resource.release()
            
            # Remove from tracking
            self._resources.discard(resource)
            self._cleanup_funcs.pop(resource, None)
            
        except Exception as e:
            logger.error(f"Resource cleanup error: {e}")
    
    def _cleanup_all(self):
        """Clean up all resources."""
        resources_copy = list(self._resources)
        
        for resource in resources_copy:
            self._cleanup_single(resource)
        
        # Force garbage collection
        gc.collect()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
    
    @contextmanager
    def managed_resource(self, resource: Any, cleanup_func: Optional[Callable] = None):
        """Context manager for a managed resource.
        
        Args:
            resource: Resource to manage
            cleanup_func: Optional cleanup function
        """
        self.register(resource, cleanup_func)
        
        try:
            yield resource
        finally:
            self.cleanup(resource)


# Global instances
_resource_monitor = ResourceMonitor()
_memory_manager = MemoryManager()
_offline_detector = OfflineModeDetector()
_cleanup_manager = ResourceCleanupManager()


def get_resource_monitor() -> ResourceMonitor:
    """Get global resource monitor."""
    return _resource_monitor


def get_memory_manager() -> MemoryManager:
    """Get global memory manager."""
    return _memory_manager


def get_offline_detector() -> OfflineModeDetector:
    """Get global offline detector."""
    return _offline_detector


def get_cleanup_manager() -> ResourceCleanupManager:
    """Get global cleanup manager."""
    return _cleanup_manager


# Convenience functions
def safe_execute(func: Callable, *args, **kwargs) -> Optional[Any]:
    """Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or None on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Safe execution failed: {e}")
        logger.debug(traceback.format_exc())
        return None


def check_memory_usage() -> Dict[str, Any]:
    """Check current memory usage.
    
    Returns:
        Memory usage statistics
    """
    return get_resource_monitor().get_current_usage()


def is_offline() -> bool:
    """Check if system is offline.
    
    Returns:
        True if offline
    """
    return get_offline_detector().is_offline()


if __name__ == "__main__":
    # Test bug fixes
    print("Testing Bug Fixes Module...")
    
    # Test resource monitor
    monitor = get_resource_monitor()
    monitor.start_monitoring()
    
    print("\nResource Usage:")
    usage = check_memory_usage()
    for key, value in usage.items():
        print(f"  {key}: {value:.2f}")
    
    # Test memory manager
    memory_mgr = get_memory_manager()
    
    # Create test image
    test_image = Image.new('RGB', (100, 100), color='red')
    memory_mgr.cache_image("test_image", test_image)
    
    cached = memory_mgr.get_cached_image("test_image")
    print(f"\nCached image retrieved: {cached is not None}")
    
    # Test offline detection
    offline = is_offline()
    print(f"\nOffline mode: {offline}")
    
    # Test BBOX validation
    bbox = [10, 10, 100, 100]
    is_valid = BBoxValidator.validate_bbox(bbox, 200, 200)
    print(f"\nBBOX {bbox} valid: {is_valid}")
    
    # Fix invalid bbox
    invalid_bbox = [100, 100, 10, 10]  # Wrong order
    fixed = BBoxValidator.fix_bbox(invalid_bbox, 200, 200)
    print(f"Fixed BBOX: {fixed}")
    
    # Test resource cleanup
    with get_cleanup_manager() as cleanup:
        # Create mock resource
        class MockResource:
            def __init__(self):
                self.closed = False
            
            def close(self):
                self.closed = True
                print("  Resource closed")
        
        resource = MockResource()
        cleanup.register(resource)
        
        print("\nResource registered for cleanup")
    
    print("\nAfter context manager exit (resource should be closed)")
    
    # Stop monitoring
    monitor.stop_monitoring()
    
    print("\nBug fixes test completed!")