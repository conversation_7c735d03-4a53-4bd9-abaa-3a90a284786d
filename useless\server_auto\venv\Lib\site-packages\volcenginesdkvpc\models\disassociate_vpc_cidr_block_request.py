# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DisassociateVpcCidrBlockRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'secondary_cidr_block': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'secondary_cidr_block': 'SecondaryCidrBlock',
        'vpc_id': 'VpcId'
    }

    def __init__(self, secondary_cidr_block=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DisassociateVpcCidrBlockRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._secondary_cidr_block = None
        self._vpc_id = None
        self.discriminator = None

        if secondary_cidr_block is not None:
            self.secondary_cidr_block = secondary_cidr_block
        self.vpc_id = vpc_id

    @property
    def secondary_cidr_block(self):
        """Gets the secondary_cidr_block of this DisassociateVpcCidrBlockRequest.  # noqa: E501


        :return: The secondary_cidr_block of this DisassociateVpcCidrBlockRequest.  # noqa: E501
        :rtype: str
        """
        return self._secondary_cidr_block

    @secondary_cidr_block.setter
    def secondary_cidr_block(self, secondary_cidr_block):
        """Sets the secondary_cidr_block of this DisassociateVpcCidrBlockRequest.


        :param secondary_cidr_block: The secondary_cidr_block of this DisassociateVpcCidrBlockRequest.  # noqa: E501
        :type: str
        """

        self._secondary_cidr_block = secondary_cidr_block

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DisassociateVpcCidrBlockRequest.  # noqa: E501


        :return: The vpc_id of this DisassociateVpcCidrBlockRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DisassociateVpcCidrBlockRequest.


        :param vpc_id: The vpc_id of this DisassociateVpcCidrBlockRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DisassociateVpcCidrBlockRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DisassociateVpcCidrBlockRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DisassociateVpcCidrBlockRequest):
            return True

        return self.to_dict() != other.to_dict()
