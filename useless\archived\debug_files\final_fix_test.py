"""
最终修复验证脚本
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("="*60)
print("ShowForAI V3 最终修复验证")
print("="*60)

# 1. 测试NetworkChecker（适配中国网络）
print("\n1. 测试 NetworkChecker:")
from showforai.utils.network_checker import NetworkChecker, is_online
checker = NetworkChecker()
result = checker._perform_check()
print(f"   NetworkChecker._perform_check(): {result}")
print(f"   is_online(): {is_online()}")

# 2. 测试OfflineModeManager（直接检查AI服务器）
print("\n2. 测试 OfflineModeManager:")
from showforai.sync.offline_manager import get_offline_manager
manager = get_offline_manager()
print(f"   is_online(): {manager.is_online()}")
print(f"   is_recording_allowed(): {manager.is_recording_allowed()}")

# 3. 测试所有GUI的录制按钮逻辑
print("\n3. 测试GUI文件的网络检查:")

# main_window.py现在使用is_online()
print("   main_window.py: 使用 is_online() 函数")

# enhanced_main_window.py使用is_online()
print("   enhanced_main_window.py: 使用 is_online() 函数")

# recorder/gui.py使用get_offline_manager()
print("   recorder/gui.py: 使用 get_offline_manager()")

# 4. 运行实际的录制器GUI
print("\n4. 选择要运行的GUI:")
print("   a. recorder/gui.py (推荐)")
print("   b. main_window.py")
print("   c. enhanced_main_window.py")

choice = input("\n请选择 (a/b/c) [默认a]: ").strip().lower() or 'a'

if choice == 'a':
    print("\n运行 recorder/gui.py...")
    from showforai.recorder.gui import main
    main()
elif choice == 'b':
    print("\n运行 main_window.py...")
    from PyQt6.QtWidgets import QApplication
    from showforai.gui.main_window import MainWindow
    app = QApplication([])
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
elif choice == 'c':
    print("\n运行 enhanced_main_window.py...")
    from PyQt6.QtWidgets import QApplication
    from showforai.gui.enhanced_main_window import EnhancedMainWindow
    app = QApplication([])
    window = EnhancedMainWindow()
    window.show()
    sys.exit(app.exec())
else:
    print("无效选择")