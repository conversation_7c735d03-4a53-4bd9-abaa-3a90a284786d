{"server": {"production": {"url": "http://*************:8080", "api_key": "YOUR_API_KEY_HERE", "timeout": 30, "retry_count": 3}, "development": {"url": "http://localhost:8080", "api_key": "test-key-123", "timeout": 10, "retry_count": 1}}, "detection": {"default_mode": "basic", "save_results": true, "results_dir": "./detection_results", "max_file_size_mb": 10, "supported_formats": ["png", "jpg", "jpeg", "webp"]}, "security": {"use_https": false, "verify_ssl": true, "use_signature": false, "encryption_enabled": false}, "performance": {"max_concurrent_requests": 5, "connection_pool_size": 10, "keep_alive": true}, "logging": {"level": "INFO", "file": "./logs/client.log", "max_size_mb": 10, "backup_count": 5}}