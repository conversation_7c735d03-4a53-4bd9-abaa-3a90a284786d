"""
ShowForAI 服务监控脚本
定期检查服务器健康状态并发送告警
"""

import time
import json
import smtplib
from datetime import datetime
from email.mime.text import MIMEText
from showforai_client import ShowForAIClient

class ServiceMonitor:
    def __init__(self, config_file="monitor_config.json"):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        self.client = ShowForAIClient(
            api_key=self.config["api_key"],
            server_url=self.config["server_url"]
        )
        
        self.failures = 0
        self.last_alert = None
    
    def check_health(self):
        """检查服务健康状态"""
        try:
            health = self.client.health_check()
            if health['status'] == 'healthy':
                return True, "Service is healthy"
            else:
                return False, f"Service unhealthy: {health}"
        except Exception as e:
            return False, f"Health check failed: {str(e)}"
    
    def check_api(self):
        """测试API功能"""
        try:
            stats = self.client.get_stats()
            return True, "API is responsive"
        except Exception as e:
            return False, f"API check failed: {str(e)}"
    
    def send_alert(self, message):
        """发送告警（这里是示例，实际可以集成邮件、短信、钉钉等）"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        alert_msg = f"[{timestamp}] ShowForAI Alert: {message}"
        
        print(f"🚨 {alert_msg}")
        
        # 记录到文件
        with open("monitor_alerts.log", "a") as f:
            f.write(alert_msg + "\n")
        
        # TODO: 实现邮件/短信/钉钉告警
        # self.send_email(alert_msg)
        # self.send_dingtalk(alert_msg)
    
    def run(self):
        """运行监控"""
        print("=" * 60)
        print("ShowForAI Service Monitor")
        print(f"Server: {self.config['server_url']}")
        print(f"Check Interval: {self.config['check_interval']}s")
        print("=" * 60)
        
        while True:
            try:
                # 健康检查
                health_ok, health_msg = self.check_health()
                
                # API检查
                api_ok, api_msg = self.check_api()
                
                # 记录状态
                timestamp = datetime.now().strftime("%H:%M:%S")
                
                if health_ok and api_ok:
                    print(f"[{timestamp}] ✓ All systems operational")
                    self.failures = 0
                else:
                    self.failures += 1
                    print(f"[{timestamp}] ✗ Service issue detected")
                    print(f"  Health: {health_msg}")
                    print(f"  API: {api_msg}")
                    
                    # 连续失败达到阈值时发送告警
                    if self.failures >= self.config["alert_threshold"]:
                        # 避免频繁告警
                        if not self.last_alert or \
                           (datetime.now() - self.last_alert).seconds > self.config["alert_cooldown"]:
                            self.send_alert(f"Service down! Failures: {self.failures}")
                            self.last_alert = datetime.now()
                
                # 等待下次检查
                time.sleep(self.config["check_interval"])
                
            except KeyboardInterrupt:
                print("\n监控已停止")
                break
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(10)

def create_default_config():
    """创建默认配置文件"""
    config = {
        "server_url": "http://188.166.247.5:8080",
        "api_key": "YOUR_API_KEY_HERE",
        "check_interval": 60,  # 检查间隔（秒）
        "alert_threshold": 3,  # 连续失败多少次后告警
        "alert_cooldown": 300,  # 告警冷却时间（秒）
        "email": {
            "enabled": False,
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "sender": "<EMAIL>",
            "password": "your_password",
            "receivers": ["<EMAIL>"]
        },
        "dingtalk": {
            "enabled": False,
            "webhook": "https://oapi.dingtalk.com/robot/send?access_token=xxx"
        }
    }
    
    with open("monitor_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("已创建配置文件: monitor_config.json")
    print("请编辑配置文件后重新运行")

if __name__ == "__main__":
    import os
    
    if not os.path.exists("monitor_config.json"):
        create_default_config()
    else:
        monitor = ServiceMonitor()
        monitor.run()