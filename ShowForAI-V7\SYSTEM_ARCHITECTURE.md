# 系统架构设计

> "Simplicity is the ultimate sophistication." - <PERSON> da <PERSON>

## 架构理念

1. **简单直接** - 不过度抽象
2. **清晰分层** - 职责明确
3. **数据驱动** - 数据流决定架构

## 系统总体架构

```
┌─────────────────────────────────────────────────────────┐
│                    用户界面层                              │
│                 Electron + React                          │
│         录制界面 | 脚本管理界面 | 设置界面                      │
└────────────────────┬────────────────────────────────────┘
                     │ IPC通信
┌────────────────────▼────────────────────────────────────┐
│                  Tauri 后端层                             │
│                   (Rust Core)                            │
├─────────────┬──────────────┬────────────────────────────┤
│   录制模块   │    执行模块    │      管理模块                │
│             │              │                            │
│ • 屏幕截图   │ • 图像匹配    │ • 脚本存储                  │
│ • 动作捕获   │ • 动作执行    │ • 配置管理                  │
│ • 数据标准化  │ • 状态管理    │ • 缓存管理                  │
└─────────────┴──────────────┴────────────────────────────┘
                     │
┌────────────────────▼────────────────────────────────────┐
│                  平台服务层                                │
│          Windows API | macOS API | Linux API             │
└─────────────────────────────────────────────────────────┘
                     │
┌────────────────────▼────────────────────────────────────┐
│                   云服务层                                 │
│                 (AI处理服务)                               │
│            • AI元素识别                                    │
│            • BBOX生成                                     │
│            • 脚本分享                                     │
└─────────────────────────────────────────────────────────┘
```

## 模块详细设计

### 1. 录制模块

#### 职责
- 捕获屏幕和用户操作
- 标准化图像到768×768
- 缓存帧数据
- 上传到服务器

#### 核心组件
```rust
pub struct RecordingModule {
    /// 屏幕捕获器
    capture: ScreenCapture,
    /// 帧缓冲区（循环缓冲）
    frame_buffer: RingBuffer<RawFrame>,
    /// 动作记录器
    action_recorder: ActionRecorder,
    /// 标准化处理器
    standardizer: ImageStandardizer,
}

impl RecordingModule {
    /// 开始录制
    pub fn start_recording(&mut self) -> Result<()> {
        // 1. 启动屏幕捕获（10 FPS）
        self.capture.start(10)?;
        // 2. 注册全局钩子
        self.action_recorder.register_hooks()?;
        // 3. 开始缓存帧
        self.start_frame_caching()?;
        Ok(())
    }
    
    /// 停止录制并处理数据
    pub fn stop_recording(&mut self) -> Result<RecordingData> {
        // 1. 停止捕获
        self.capture.stop()?;
        // 2. 卸载钩子
        self.action_recorder.unregister_hooks()?;
        // 3. 处理缓存的帧
        let frames = self.process_cached_frames()?;
        // 4. 返回录制数据
        Ok(RecordingData { frames, actions })
    }
    
    /// 标准化并上传
    pub async fn upload_recording(&self, data: RecordingData) -> Result<ScriptId> {
        // 1. 标准化所有帧到768×768
        let standard_frames = self.standardize_frames(data.frames)?;
        // 2. 准备上传数据
        let upload_data = self.prepare_upload(standard_frames, data.actions)?;
        // 3. 上传到服务器
        let response = api::upload_recording(upload_data).await?;
        // 4. 返回脚本ID
        Ok(response.script_id)
    }
}
```

### 2. 执行模块（双线程独立架构）

#### 设计理念
- **Active和Auxiliary独立运行**：两个独立的执行线程，不互相干扰
- **共享状态协调**：通过原子变量实现轻量级协调
- **避让而不终止**：Auxiliary检测到Active运行时跳过动作，但继续识别

#### 核心架构
```
┌─────────────────────────────────────────────────────┐
│                 执行管理器                            │
│          (ExecutionManager)                         │
├──────────────────┬──────────────────────────────────┤
│                  │                                  │
│    Active执行器   │    Auxiliary执行器                │
│   (独立线程)      │     (独立线程)                     │
│                  │                                  │
│  • 界面最小化     │   • 界面保持可见                    │
│  • 优先级最高     │   • 持续监控                       │
│  • 完成后停止     │   • 检测Active时避让                │
│                  │                                  │
└──────────────────┴──────────────────────────────────┘
           ↓                    ↓
    ┌────────────────────────────────────┐
    │      共享执行状态                     │
    │   (SharedExecutionState)            │
    │  • active_running: AtomicBool       │
    │  • input_owner: AtomicU8            │
    └────────────────────────────────────┘
```

#### Active执行器
```rust
pub struct ActiveExecutor {
    /// 接收队列
    rx: mpsc::Receiver<Script>,
    /// 共享状态
    shared_state: Arc<SharedExecutionState>,
    /// 图像匹配器
    matcher: ImageMatcher,
    /// 动作执行器
    executor: ActionExecutor,
}

impl ActiveExecutor {
    pub async fn run(mut self) {
        while let Some(script) = self.rx.recv().await {
            // 1. 设置Active运行标志
            self.shared_state.active_running.store(true, Ordering::SeqCst);
            
            // 2. 最小化界面
            minimize_window();
            
            // 3. 执行脚本（拥有最高优先级）
            self.execute_script(script).await;
            
            // 4. 清除运行标志
            self.shared_state.active_running.store(false, Ordering::SeqCst);
            
            // 5. 恢复界面
            restore_window();
        }
    }
}
```

#### Auxiliary执行器
```rust
pub struct AuxiliaryExecutor {
    /// 接收队列
    rx: mpsc::Receiver<Script>,
    /// 共享状态
    shared_state: Arc<SharedExecutionState>,
    /// 图像匹配器
    matcher: ImageMatcher,
    /// 动作执行器
    executor: ActionExecutor,
}

impl AuxiliaryExecutor {
    pub async fn run(mut self) {
        while let Some(script) = self.rx.recv().await {
            // 界面始终保持可见
            loop {
                for action in &script.actions {
                    // 1. 等待元素出现（持续识别）
                    let element = self.wait_for_element(&action.element_image).await?;
                    
                    // 2. 检查是否需要避让
                    if self.shared_state.active_running.load(Ordering::Relaxed) {
                        // Active运行中，跳过动作但继续循环
                        println!("Auxiliary: 避让Active模式");
                        tokio::time::sleep(Duration::from_secs(1)).await;
                        continue;
                    }
                    
                    // 3. 执行动作
                    self.executor.execute(&action.action, element.position)?;
                    
                    // 4. 智能等待
                    tokio::time::sleep(Duration::from_millis(action.interval_ms)).await;
                }
                
                // 检查是否继续循环
                if script.loop_count != 999 {  // 999表示无限循环
                    break;
                }
            }
        }
    }
}
```

### 3. 管理模块

#### 职责
- 脚本存储和管理
- 配置管理
- 缓存管理
- 数据同步

#### 核心组件
```rust
pub struct ManagementModule {
    /// 数据库连接
    db: Database,
    /// 缓存管理器
    cache: CacheManager,
    /// 配置管理器
    config: ConfigManager,
    /// 同步服务
    sync: SyncService,
}

impl ManagementModule {
    /// 保存脚本到本地
    pub fn save_script(&self, script: Script) -> Result<()> {
        // 1. 序列化脚本
        let data = serialize(&script)?;
        // 2. 存储到数据库
        self.db.insert_script(&script.id, &data)?;
        // 3. 缓存元素图像
        for action in &script.actions {
            self.cache.store_element(&action.element_image)?;
        }
        Ok(())
    }
    
    /// 从服务器同步脚本
    pub async fn sync_script(&self, script_id: &str) -> Result<Script> {
        // 1. 从服务器获取数据
        let server_data = api::get_script(script_id).await?;
        
        // 2. 处理BBOX裁切
        let mut script = self.process_server_data(server_data)?;
        
        // 3. 保存到本地
        self.save_script(script.clone())?;
        
        Ok(script)
    }
    
    /// BBOX裁切处理（关键流程）
    fn process_server_data(&self, data: ServerScriptData) -> Result<Script> {
        let mut actions = Vec::new();
        
        for (i, server_action) in data.actions.iter().enumerate() {
            // 1. 获取768×768图像
            let image_768 = &data.frames[i];
            
            // 2. 根据BBOX裁切元素
            let element_image = self.crop_element(image_768, &server_action.bbox)?;
            
            // 3. Base64编码
            let element_base64 = base64::encode(&element_image);
            
            // 4. 构建本地动作
            actions.push(ScriptAction {
                index: i as u32,
                element_image: element_base64,
                action: server_action.action.clone(),
                bbox: Some(server_action.bbox.clone()),
            });
        }
        
        Ok(Script {
            id: data.id,
            name: data.name,
            description: data.description,
            actions,
            ..Default::default()
        })
    }
}
```

## 数据流设计

### 录制数据流
```
用户操作 → 捕获模块 → 帧缓冲(15帧) → 点击时取第3帧 → 
标准化768×768 → 上传服务器 → AI识别 → 返回BBOX → 
客户端裁切 → 存储脚本
```

### 执行数据流
```
加载脚本 → 读取元素图像 → 截屏 → 图像匹配 → 
找到位置 → 执行动作 → 等待 → 下一步
```

## 关键设计决策

### 1. 为什么使用Tauri而不是纯Electron？
- **性能**: Rust后端处理图像更快
- **内存**: 避免JavaScript的GC开销
- **安全**: Rust的内存安全保证
- **体积**: 最终程序更小

### 2. 为什么是768×768？
- **AI模型输入**: 大多数视觉模型使用这个尺寸
- **平衡点**: 足够的细节 vs 合理的处理时间
- **统一标准**: 消除分辨率差异
- **固定大小**: 2,359,296字节，便于内存管理

### 3. 为什么客户端裁切而不是服务端？
- **灵活性**: 客户端可以调整裁切策略
- **性能**: 减少服务器负载
- **存储**: 服务器只存一份768图像

## 性能设计

### 关键路径优化

1. **录制优化**
   - 10 FPS 循环缓冲，不是持续存储
   - 异步上传，不阻塞UI
   - 批量处理帧数据

2. **执行优化**
   - 元素图像缓存（LRU）
   - 多级匹配降级策略
   - 预编译匹配模板

3. **存储优化**
   - MessagePack序列化
   - 图像PNG压缩
   - SQLite索引优化

### 性能指标

| 组件 | 指标 | 目标 |
|-----|------|-----|
| 屏幕捕获 | 延迟 | < 16ms |
| 图像标准化 | 处理时间 | < 20ms |
| 模板匹配 | 匹配时间 | < 50ms |
| 动作执行 | 响应时间 | < 10ms |
| 脚本加载 | 加载时间 | < 100ms |

## 错误处理

### 错误分层
```rust
// 系统级错误
pub enum SystemError {
    ScreenCaptureFailed,
    HookRegistrationFailed,
    MemoryAllocationFailed,
}

// 业务级错误
pub enum BusinessError {
    ElementNotFound,
    ScriptCorrupted,
    NetworkTimeout,
}

// 用户级错误
pub enum UserError {
    InvalidScript,
    NoPermission,
    StorageFull,
}
```

### 错误恢复策略

1. **录制失败**: 保存已录制部分
2. **执行失败**: 记录失败步骤，可续执行
3. **网络失败**: 本地缓存，延迟上传

## 安全设计

### 安全原则
1. **最小权限**: 只请求必要的系统权限
2. **数据加密**: 敏感数据加密存储
3. **签名验证**: API请求签名防重放

### 具体措施
```rust
// API签名
pub fn sign_request(request: &ApiRequest, secret: &str) -> String {
    let data = format!("{}{}{}", 
        request.request_id, 
        request.timestamp, 
        request.body
    );
    hmac_sha256(data, secret)
}

// 本地加密
pub fn encrypt_sensitive_data(data: &[u8]) -> Result<Vec<u8>> {
    // 使用AES-256-GCM
    aes_encrypt(data, get_local_key()?)
}
```

## 扩展性设计

### 插件系统（未来）
```rust
pub trait Plugin {
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    fn on_recording_start(&mut self) -> Result<()>;
    fn on_recording_stop(&mut self) -> Result<()>;
    fn on_execution_start(&mut self) -> Result<()>;
    fn on_execution_stop(&mut self) -> Result<()>;
}
```

### 跨平台支持
- Windows: 使用 Windows API
- macOS: 使用 Core Graphics
- Linux: 使用 X11/Wayland

## 部署架构

### 客户端部署
```
ShowForAI.exe (单文件)
├── 主程序 (Electron + Tauri)
├── 嵌入资源
└── 自动更新模块
```

### 服务端部署
```
API服务器
├── 负载均衡器
├── API网关
├── AI处理集群
└── 对象存储(图片)
```

---

*"Make it work, make it right, make it fast."* - Kent Beck