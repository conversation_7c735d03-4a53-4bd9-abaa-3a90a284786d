"""
测试GUI中的网络状态问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication
from showforai.recorder.gui import RecorderWindow
from showforai.config import Config
from showforai.sync.offline_manager import get_offline_manager
import time

def test_singleton():
    """测试单例是否工作"""
    print("="*60)
    print("测试单例模式")
    print("="*60)
    
    # 先获取offline manager
    manager1 = get_offline_manager()
    print(f"manager1 id: {id(manager1)}")
    print(f"manager1.is_online(): {manager1.is_online()}")
    print(f"manager1._is_online: {manager1._is_online}")
    
    # 创建GUI
    app = QApplication([])
    config = Config()
    window = RecorderWindow(config)
    
    # 检查GUI的manager
    print(f"\nwindow.offline_manager id: {id(window.offline_manager)}")
    print(f"是否同一实例: {manager1 is window.offline_manager}")
    print(f"window.offline_manager.is_online(): {window.offline_manager.is_online()}")
    print(f"window.offline_manager._is_online: {window.offline_manager._is_online}")
    
    # 检查UI状态
    print(f"\nUI状态:")
    print(f"offline_warning.isHidden(): {window.offline_warning.isHidden()}")
    print(f"record_button.isEnabled(): {window.record_button.isEnabled()}")
    
    # 手动调用check_network_status
    print("\n手动调用check_network_status...")
    window.check_network_status()
    
    print(f"\n调用后UI状态:")
    print(f"offline_warning.isHidden(): {window.offline_warning.isHidden()}")
    print(f"record_button.isEnabled(): {window.record_button.isEnabled()}")
    
    # 模拟点击录制按钮
    print("\n模拟点击录制按钮...")
    window.toggle_recording()
    
    return window

def test_different_instance():
    """测试是否创建了不同的实例"""
    print("\n" + "="*60)
    print("测试不同实例问题")
    print("="*60)
    
    # 清理全局单例
    import showforai.sync.offline_manager as om_module
    om_module._offline_manager = None
    print("清理了全局单例")
    
    # 创建新的manager
    manager = get_offline_manager()
    print(f"新manager id: {id(manager)}")
    print(f"新manager.is_online(): {manager.is_online()}")
    
    # 创建GUI
    app = QApplication([])
    config = Config()
    window = RecorderWindow(config)
    
    print(f"\nwindow.offline_manager id: {id(window.offline_manager)}")
    print(f"是否同一实例: {manager is window.offline_manager}")
    
    return window

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    window = test_singleton()
    # window = test_different_instance()
    
    # 显示窗口
    window.show()
    print("\n窗口已显示，请测试录制功能")