"""
Demo script to showcase API Security implementation
Shows how the security mechanism protects API endpoints
"""

import asyncio
import json
import time
from src.showforai.security.api_security import APISecurityManager, SecurityContext
from src.showforai.security.nonce_manager import Nonce<PERSON>anager
from src.showforai.security.secure_config import SecureAP<PERSON><PERSON>eyManager


def demo_signature_creation():
    """Demonstrate signature creation for API requests"""
    print("\n" + "="*60)
    print("DEMO: API Request Signature Creation")
    print("="*60)
    
    # Initialize security manager with a secret key
    secret_key = "demo-secret-key-12345"
    security_manager = APISecurityManager(secret_key=secret_key)
    
    # Create a sample API request
    method = "POST"
    url = "https://api.showforai.com/v3/execute"
    body = json.dumps({
        "action": "click",
        "x": 100,
        "y": 200
    })
    
    # Generate secure headers
    headers = security_manager.create_secure_headers(
        method=method,
        url=url,
        body=body
    )
    
    print(f"\nRequest Details:")
    print(f"  Method: {method}")
    print(f"  URL: {url}")
    print(f"  Body: {body[:50]}...")
    
    print(f"\nGenerated Security Headers:")
    print(f"  X-Timestamp: {headers['X-Timestamp']}")
    print(f"  X-Nonce: {headers['X-Nonce']}")
    print(f"  X-Signature: {headers['X-Signature']}")
    
    return security_manager, method, url, headers, body


def demo_request_verification():
    """Demonstrate request verification"""
    print("\n" + "="*60)
    print("DEMO: Request Verification")
    print("="*60)
    
    # Get request components from signature creation
    security_manager, method, url, headers, body = demo_signature_creation()
    
    # Initialize nonce manager
    nonce_manager = NonceManager()
    
    print("\n--- Verifying Valid Request ---")
    # Verify the request
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=headers,
        body=body,
        verify_nonce_callback=nonce_manager.verify_nonce
    )
    
    print(f"  Verification Result: {'✓ VALID' if valid else '✗ INVALID'}")
    if error:
        print(f"  Error: {error}")
    
    print("\n--- Attempting Replay Attack (Same Nonce) ---")
    # Try to replay the same request (should fail due to nonce)
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=headers,
        body=body,
        verify_nonce_callback=nonce_manager.verify_nonce
    )
    
    print(f"  Verification Result: {'✓ VALID' if valid else '✗ INVALID'}")
    print(f"  Error: {error}")
    print("  → Replay attack prevented!")
    
    print("\n--- Attempting Request with Expired Timestamp ---")
    # Create headers with old timestamp
    old_headers = headers.copy()
    old_headers['X-Timestamp'] = str(int(time.time()) - 400)  # 6+ minutes old
    old_headers['X-Nonce'] = security_manager.generate_nonce()  # New nonce
    
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=old_headers,
        body=body,
        verify_nonce_callback=nonce_manager.verify_nonce
    )
    
    print(f"  Verification Result: {'✓ VALID' if valid else '✗ INVALID'}")
    print(f"  Error: {error}")
    print("  → Expired request rejected!")
    
    print("\n--- Attempting Request with Invalid Signature ---")
    # Create headers with wrong signature
    bad_headers = headers.copy()
    bad_headers['X-Signature'] = "invalid-signature-12345"
    bad_headers['X-Nonce'] = security_manager.generate_nonce()  # New nonce
    
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=bad_headers,
        body=body,
        verify_nonce_callback=nonce_manager.verify_nonce
    )
    
    print(f"  Verification Result: {'✓ VALID' if valid else '✗ INVALID'}")
    print(f"  Error: {error}")
    print("  → Tampered request rejected!")
    
    # Cleanup
    nonce_manager.shutdown()


def demo_nonce_management():
    """Demonstrate nonce management for preventing replay attacks"""
    print("\n" + "="*60)
    print("DEMO: Nonce Management (Replay Attack Prevention)")
    print("="*60)
    
    nonce_manager = NonceManager(ttl=10)  # 10 second TTL for demo
    
    # Generate some nonces
    nonces = [f"demo-nonce-{i}" for i in range(5)]
    
    print("\n--- Adding Nonces ---")
    for nonce in nonces[:3]:
        result = nonce_manager.add_nonce(nonce)
        print(f"  Add '{nonce}': {'Success' if result else 'Failed (duplicate)'}")
    
    print("\n--- Checking Nonce Status ---")
    stats = nonce_manager.get_stats()
    print(f"  Total nonces stored: {stats['total']}")
    print(f"  Active nonces: {stats['active']}")
    print(f"  TTL: {stats['ttl']} seconds")
    
    print("\n--- Attempting to Reuse Nonces ---")
    for nonce in nonces[:3]:
        result = nonce_manager.verify_nonce(nonce)
        print(f"  Verify '{nonce}': {'New (accepted)' if result else 'Used (rejected)'}")
    
    print("\n--- Adding New Nonces ---")
    for nonce in nonces[3:]:
        result = nonce_manager.verify_nonce(nonce)
        print(f"  Verify '{nonce}': {'New (accepted)' if result else 'Used (rejected)'}")
    
    # Final stats
    print("\n--- Final Statistics ---")
    stats = nonce_manager.get_stats()
    print(f"  Total nonces: {stats['total']}")
    print(f"  Active nonces: {stats['active']}")
    
    nonce_manager.shutdown()


def demo_api_key_management():
    """Demonstrate secure API key management"""
    print("\n" + "="*60)
    print("DEMO: Secure API Key Management")
    print("="*60)
    
    key_manager = SecureAPIKeyManager()
    
    print("\n--- Generating API Secrets ---")
    secret1 = key_manager.generate_api_secret(16)  # 16 bytes
    secret2 = key_manager.generate_api_secret(32)  # 32 bytes
    
    print(f"  16-byte secret: {secret1[:20]}...")
    print(f"  32-byte secret: {secret2[:20]}...")
    
    print("\n--- API Key Generation with Expiration ---")
    security_manager = APISecurityManager(secret_key="demo-key")
    
    # Generate API keys for different users
    users = ["user-123", "user-456", "user-789"]
    
    for user_id in users:
        key_data = security_manager.generate_api_key(user_id, expiry_days=30)
        print(f"\n  User: {user_id}")
        print(f"    API Key: {key_data['api_key'][:20]}...")
        print(f"    Created: {key_data['created_at'][:19]}")
        print(f"    Expires: {key_data['expires_at'][:19]}")
        print(f"    Signature: {key_data['signature'][:20]}...")


def demo_security_context():
    """Demonstrate security context manager usage"""
    print("\n" + "="*60)
    print("DEMO: Security Context Manager")
    print("="*60)
    
    security_manager = APISecurityManager(secret_key="context-demo-key")
    
    print("\n--- Using Security Context ---")
    with SecurityContext(security_manager) as context:
        print(f"  Request ID: {context.request_id}")
        print("  Performing secure operations...")
        time.sleep(0.5)  # Simulate some work
        print("  Operations completed successfully")
    
    print("  Context closed, request logged")


async def demo_complete_flow():
    """Demonstrate complete security flow"""
    print("\n" + "="*60)
    print("DEMO: Complete Security Flow")
    print("="*60)
    
    # Initialize components
    secret_key = "production-secret-key"
    security_manager = APISecurityManager(secret_key=secret_key)
    nonce_manager = NonceManager()
    
    # Simulate multiple API requests
    endpoints = [
        ("GET", "https://api.showforai.com/v3/scripts", None),
        ("POST", "https://api.showforai.com/v3/execute", json.dumps({"action": "click"})),
        ("PUT", "https://api.showforai.com/v3/config", json.dumps({"theme": "dark"})),
    ]
    
    print("\n--- Processing Secure API Requests ---")
    for method, url, body in endpoints:
        print(f"\n  {method} {url}")
        
        # Create secure headers
        headers = security_manager.create_secure_headers(
            method=method,
            url=url,
            body=body
        )
        
        # Verify request
        valid, error = security_manager.verify_request(
            method=method,
            url=url,
            headers=headers,
            body=body,
            verify_nonce_callback=nonce_manager.verify_nonce
        )
        
        if valid:
            print(f"    ✓ Request verified and processed")
            print(f"    Timestamp: {headers['X-Timestamp']}")
            print(f"    Nonce: {headers['X-Nonce'][:20]}...")
        else:
            print(f"    ✗ Request rejected: {error}")
        
        await asyncio.sleep(0.1)  # Small delay between requests
    
    # Show final statistics
    print("\n--- Security Statistics ---")
    stats = nonce_manager.get_stats()
    print(f"  Nonces processed: {stats['total']}")
    print(f"  Active nonces: {stats['active']}")
    print(f"  Security: All requests verified with unique nonces")
    
    nonce_manager.shutdown()


def main():
    """Run all demonstrations"""
    print("\n" + "#"*60)
    print("#" + " "*20 + "API SECURITY DEMO" + " "*21 + "#")
    print("#"*60)
    
    # Run demos
    demo_nonce_management()
    demo_request_verification()
    demo_api_key_management()
    demo_security_context()
    
    # Run async demo
    asyncio.run(demo_complete_flow())
    
    print("\n" + "#"*60)
    print("#" + " "*18 + "DEMO COMPLETED" + " "*24 + "#")
    print("#"*60)
    print("\nAPI Security Features Demonstrated:")
    print("  ✓ HMAC-SHA256 request signatures")
    print("  ✓ Timestamp validation (5-minute window)")
    print("  ✓ Nonce-based replay attack prevention")
    print("  ✓ Secure API key generation and management")
    print("  ✓ Request verification with multiple security checks")
    print("\nAll security requirements from product principles implemented!")


if __name__ == "__main__":
    main()