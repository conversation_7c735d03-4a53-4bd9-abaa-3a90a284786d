"""
Test script for visual feedback system in ShowForAI V3
Tests all status manager operations and UI feedback components
"""

import sys
import time
import asyncio
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit
from PyQt6.QtCore import QThread, pyqtSignal, QTimer

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from showforai.gui.status_manager import StatusManager, OperationType, StatusType
from showforai.gui.status_bar import EnhancedStatusBar


class TestThread(QThread):
    """Thread to simulate various operations"""
    
    def __init__(self, status_manager: StatusManager, operation: OperationType):
        super().__init__()
        self.status_manager = status_manager
        self.operation = operation
    
    def run(self):
        """Run the test operation"""
        if self.operation == OperationType.RECORDING_UPLOAD:
            self.test_upload()
        elif self.operation == OperationType.AI_PROCESSING:
            self.test_ai_processing()
        elif self.operation == OperationType.SCRIPT_GENERATION:
            self.test_script_generation()
        elif self.operation == OperationType.SCRIPT_EXECUTION:
            self.test_script_execution()
        elif self.operation == OperationType.ELEMENT_SEARCH:
            self.test_element_search()
        elif self.operation == OperationType.CLOUD_SYNC:
            self.test_cloud_sync()
    
    def test_upload(self):
        """Test upload operation"""
        self.status_manager.start_operation(
            OperationType.RECORDING_UPLOAD,
            "正在准备上传录制数据...",
            100
        )
        
        for i in range(10):
            time.sleep(0.5)
            self.status_manager.update_progress(
                OperationType.RECORDING_UPLOAD,
                percent=(i + 1) * 10,
                message=f"正在上传... {(i + 1) * 10}%"
            )
        
        self.status_manager.complete_operation(
            OperationType.RECORDING_UPLOAD,
            success=True,
            message="录制数据上传完成"
        )
    
    def test_ai_processing(self):
        """Test AI processing operation"""
        self.status_manager.start_operation(
            OperationType.AI_PROCESSING,
            "正在识别界面元素...",
            3
        )
        
        for i in range(3):
            time.sleep(1)
            self.status_manager.update_progress(
                OperationType.AI_PROCESSING,
                processed=i + 1,
                message=f"正在识别界面元素... (尝试 {i + 1}/3)"
            )
        
        self.status_manager.complete_operation(
            OperationType.AI_PROCESSING,
            success=True,
            message="界面元素识别完成"
        )
    
    def test_script_generation(self):
        """Test script generation operation"""
        total_actions = 15
        self.status_manager.start_operation(
            OperationType.SCRIPT_GENERATION,
            f"正在生成脚本 (共{total_actions}个操作)...",
            total_actions
        )
        
        for i in range(total_actions):
            time.sleep(0.3)
            self.status_manager.update_progress(
                OperationType.SCRIPT_GENERATION,
                processed=i + 1,
                message=f"正在处理第 {i + 1}/{total_actions} 个操作..."
            )
        
        self.status_manager.complete_operation(
            OperationType.SCRIPT_GENERATION,
            success=True,
            message=f"脚本生成完成 (共{total_actions}个操作)"
        )
    
    def test_script_execution(self):
        """Test script execution operation"""
        total_steps = 10
        self.status_manager.start_operation(
            OperationType.SCRIPT_EXECUTION,
            f"正在执行脚本 (共{total_steps}个步骤)...",
            total_steps
        )
        
        for i in range(total_steps):
            time.sleep(0.8)
            action_type = ["click", "type", "scroll", "wait"][i % 4]
            self.status_manager.update_progress(
                OperationType.SCRIPT_EXECUTION,
                current_step=i + 1,
                total_steps=total_steps,
                message=f"正在执行第 {i + 1}/{total_steps} 步: {action_type}"
            )
        
        self.status_manager.complete_operation(
            OperationType.SCRIPT_EXECUTION,
            success=True,
            message=f"脚本执行完成 (成功: {total_steps}/{total_steps})"
        )
    
    def test_element_search(self):
        """Test element search with 3-second delay"""
        # Wait 4 seconds to trigger the delayed message
        time.sleep(4)
        
        # Element search is started internally after 3 seconds
        # Simulate finding element after 2 more seconds
        time.sleep(2)
    
    def test_cloud_sync(self):
        """Test cloud sync operation"""
        total_items = 5
        self.status_manager.start_operation(
            OperationType.CLOUD_SYNC,
            f"正在同步 {total_items} 个脚本到云端...",
            total_items
        )
        
        for i in range(total_items):
            time.sleep(0.6)
            self.status_manager.update_progress(
                OperationType.CLOUD_SYNC,
                processed=i + 1,
                message=f"正在上传: Script_{i + 1}.json ({i + 1}/{total_items})"
            )
        
        self.status_manager.complete_operation(
            OperationType.CLOUD_SYNC,
            success=True,
            message=f"云端同步完成 (上传: {total_items}, 跳过: 0)"
        )


class TestWindow(QMainWindow):
    """Test window for visual feedback system"""
    
    def __init__(self):
        super().__init__()
        self.status_manager = StatusManager()
        self.init_ui()
        self.test_threads = []
    
    def init_ui(self):
        """Initialize UI"""
        self.setWindowTitle("Visual Feedback Test - ShowForAI V3")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Test buttons
        self.upload_btn = QPushButton("Test Upload Feedback")
        self.upload_btn.clicked.connect(lambda: self.run_test(OperationType.RECORDING_UPLOAD))
        layout.addWidget(self.upload_btn)
        
        self.ai_btn = QPushButton("Test AI Processing Feedback")
        self.ai_btn.clicked.connect(lambda: self.run_test(OperationType.AI_PROCESSING))
        layout.addWidget(self.ai_btn)
        
        self.gen_btn = QPushButton("Test Script Generation Feedback")
        self.gen_btn.clicked.connect(lambda: self.run_test(OperationType.SCRIPT_GENERATION))
        layout.addWidget(self.gen_btn)
        
        self.exec_btn = QPushButton("Test Script Execution Feedback")
        self.exec_btn.clicked.connect(lambda: self.run_test(OperationType.SCRIPT_EXECUTION))
        layout.addWidget(self.exec_btn)
        
        self.search_btn = QPushButton("Test Element Search (3s delay)")
        self.search_btn.clicked.connect(lambda: self.run_test(OperationType.ELEMENT_SEARCH))
        layout.addWidget(self.search_btn)
        
        self.sync_btn = QPushButton("Test Cloud Sync Feedback")
        self.sync_btn.clicked.connect(lambda: self.run_test(OperationType.CLOUD_SYNC))
        layout.addWidget(self.sync_btn)
        
        # Log output
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        layout.addWidget(self.log_output)
        
        # Status bar
        self.status_bar = EnhancedStatusBar(self.status_manager)
        self.setStatusBar(self.status_bar)
        
        # Connect status manager signals
        self.status_manager.status_updated.connect(self.on_status_updated)
        self.status_manager.operation_started.connect(self.on_operation_started)
        self.status_manager.operation_completed.connect(self.on_operation_completed)
        
        # Set initial status
        self.status_bar.set_connection_status("online")
        self.status_bar.set_user("<EMAIL>")
    
    def run_test(self, operation: OperationType):
        """Run a test operation"""
        self.log_output.append(f"\n--- Testing {operation.value} ---")
        
        # Special handling for element search
        if operation == OperationType.ELEMENT_SEARCH:
            self.log_output.append("Starting element search (will show message after 3 seconds)...")
            # Simulate element search with delay
            QTimer.singleShot(0, lambda: self.simulate_element_search())
        else:
            # Create and start test thread
            thread = TestThread(self.status_manager, operation)
            thread.finished.connect(lambda: self.on_test_completed(operation))
            self.test_threads.append(thread)
            thread.start()
    
    def simulate_element_search(self):
        """Simulate element search with 3-second delay"""
        # Start a timer to show when 3 seconds have passed
        start_time = time.time()
        
        def check_time():
            elapsed = time.time() - start_time
            if elapsed < 3:
                self.log_output.append(f"Searching... {elapsed:.1f}s (no message yet)")
                QTimer.singleShot(500, check_time)
            elif elapsed < 4:
                self.log_output.append("3 seconds passed - message should appear now!")
                self.status_manager.start_operation(
                    OperationType.ELEMENT_SEARCH,
                    "正在查找目标元素...",
                    100
                )
                QTimer.singleShot(1000, lambda: self.complete_element_search())
        
        check_time()
    
    def complete_element_search(self):
        """Complete element search"""
        self.status_manager.complete_operation(
            OperationType.ELEMENT_SEARCH,
            success=True,
            message="找到目标元素"
        )
        self.log_output.append("Element search completed!")
    
    def on_status_updated(self, status):
        """Handle status update"""
        msg = f"[{status.type.value}] {status.message}"
        if status.progress is not None:
            msg += f" ({status.progress:.0f}%)"
        self.log_output.append(msg)
    
    def on_operation_started(self, operation, message):
        """Handle operation start"""
        self.log_output.append(f"Started: {operation.value} - {message}")
    
    def on_operation_completed(self, operation, success):
        """Handle operation completion"""
        status = "Success" if success else "Failed"
        self.log_output.append(f"Completed: {operation.value} - {status}")
    
    def on_test_completed(self, operation):
        """Handle test completion"""
        self.log_output.append(f"Test for {operation.value} completed\n")
    
    def closeEvent(self, event):
        """Clean up threads on close"""
        for thread in self.test_threads:
            if thread.isRunning():
                thread.quit()
                thread.wait()
        event.accept()


def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Set dark theme
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
        }
        QPushButton {
            background-color: #3c3c3c;
            color: white;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #484848;
        }
        QTextEdit {
            background-color: #1e1e1e;
            color: #cccccc;
            border: 1px solid #555;
            font-family: 'Consolas', 'Monaco', monospace;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()