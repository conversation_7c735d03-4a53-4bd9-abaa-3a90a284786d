package com.showforai.executor.data.models

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * DSL模型单元测试
 * 
 * 测试DSL模型的JSON解析和序列化
 */
class DSLModelsTest {
    
    private lateinit var moshi: Moshi
    private lateinit var dslScriptAdapter: JsonAdapter<DSLScript>
    
    @Before
    fun setup() {
        moshi = Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
        
        dslScriptAdapter = moshi.adapter(DSLScript::class.java)
    }
    
    @Test
    fun testParseValidDSLScript() {
        // 有效的DSL脚本JSON
        val validJson = """
            {
              "version": "3.1",
              "metadata": {
                "session_id": "test_session_001",
                "description": "Test Script",
                "created_at": "2024-01-01T12:00:00Z",
                "device_info": "Test Device"
              },
              "steps": [
                {
                  "type": "action",
                  "command": "CLICK",
                  "timeout_seconds": 10,
                  "target": {
                    "description": "Test Button",
                    "visual_hash": "test_hash.png",
                    "text_content": "Click Me",
                    "bounding_box": [0.1, 0.2, 0.3, 0.4]
                  }
                }
              ]
            }
        """.trimIndent()
        
        // 解析JSON
        val script = dslScriptAdapter.fromJson(validJson)
        
        // 验证解析结果
        assertNotNull("Script should not be null", script)
        assertEquals("3.1", script?.version)
        assertEquals("test_session_001", script?.metadata?.sessionId)
        assertEquals("Test Script", script?.metadata?.description)
        assertEquals(1, script?.steps?.size)
        
        // 验证步骤
        val step = script?.steps?.get(0)
        assertEquals("action", step?.type)
        assertEquals(CommandType.CLICK, step?.command)
        assertEquals(10, step?.timeoutSeconds)
        
        // 验证目标
        val target = step?.target
        assertEquals("Test Button", target?.description)
        assertEquals("test_hash.png", target?.visualHash)
        assertEquals("Click Me", target?.textContent)
        assertEquals(4, target?.boundingBox?.size)
        assertEquals(0.1f, target?.boundingBox?.get(0))
        assertEquals(0.2f, target?.boundingBox?.get(1))
        assertEquals(0.3f, target?.boundingBox?.get(2))
        assertEquals(0.4f, target?.boundingBox?.get(3))
    }
    
    @Test
    fun testParseInvalidDSLScript() {
        // 无效的DSL脚本JSON（缺少必需字段）
        val invalidJson = """
            {
              "version": "3.1",
              "metadata": {
                "session_id": "test_session_001"
              }
              // 缺少steps字段
            }
        """.trimIndent()
        
        // 解析JSON应该失败
        val script = dslScriptAdapter.fromJson(invalidJson)
        assertNull("Script should be null for invalid JSON", script)
    }
    
    @Test
    fun testSerializeDSLScript() {
        // 创建DSL脚本对象
        val script = DSLScript(
            version = "3.1",
            metadata = DSLMetadata(
                sessionId = "test_session_002",
                description = "Serialization Test",
                createdAt = "2024-01-02T12:00:00Z",
                deviceInfo = "Test Device"
            ),
            steps = listOf(
                DSLStep(
                    type = "action",
                    command = CommandType.SWIPE,
                    timeoutSeconds = 15,
                    target = DSLTarget(
                        description = "Swipe Area",
                        visualHash = null,
                        textContent = "Swipe Here",
                        boundingBox = listOf(0.2f, 0.3f, 0.8f, 0.7f)
                    ),
                    parameters = DSLParameters(
                        direction = SwipeDirection.UP,
                        distance = 200f,
                        duration = 500L,
                        text = null,
                        startPoint = null,
                        endPoint = null,
                        scrollDirection = null,
                        scrollAmount = null
                    )
                )
            )
        )
        
        // 序列化为JSON
        val json = dslScriptAdapter.toJson(script)
        
        // 验证JSON包含预期的字段
        assertTrue("JSON should contain version", json.contains("\"version\":\"3.1\""))
        assertTrue("JSON should contain session_id", json.contains("\"session_id\":\"test_session_002\""))
        assertTrue("JSON should contain description", json.contains("\"description\":\"Serialization Test\""))
        assertTrue("JSON should contain command", json.contains("\"command\":\"SWIPE\""))
        assertTrue("JSON should contain direction", json.contains("\"direction\":\"UP\""))
        
        // 反序列化并验证
        val deserializedScript = dslScriptAdapter.fromJson(json)
        assertEquals("3.1", deserializedScript?.version)
        assertEquals("test_session_002", deserializedScript?.metadata?.sessionId)
        assertEquals(CommandType.SWIPE, deserializedScript?.steps?.get(0)?.command)
        assertEquals(SwipeDirection.UP, deserializedScript?.steps?.get(0)?.parameters?.direction)
    }
    
    @Test
    fun testAllCommandTypes() {
        // 测试所有命令类型的解析
        val commandsJson = """
            {
              "version": "3.1",
              "metadata": { "session_id": "test_commands" },
              "steps": [
                { "type": "action", "command": "CLICK", "timeout_seconds": 10 },
                { "type": "action", "command": "SWIPE", "timeout_seconds": 10 },
                { "type": "action", "command": "INPUT_TEXT", "timeout_seconds": 10 },
                { "type": "action", "command": "WAIT", "timeout_seconds": 10 },
                { "type": "action", "command": "SCROLL", "timeout_seconds": 10 },
                { "type": "action", "command": "LONG_PRESS", "timeout_seconds": 10 },
                { "type": "action", "command": "DOUBLE_CLICK", "timeout_seconds": 10 }
              ]
            }
        """.trimIndent()
        
        val script = dslScriptAdapter.fromJson(commandsJson)
        
        // 验证所有命令类型
        assertNotNull("Script should not be null", script)
        assertEquals(7, script?.steps?.size)
        assertEquals(CommandType.CLICK, script?.steps?.get(0)?.command)
        assertEquals(CommandType.SWIPE, script?.steps?.get(1)?.command)
        assertEquals(CommandType.INPUT_TEXT, script?.steps?.get(2)?.command)
        assertEquals(CommandType.WAIT, script?.steps?.get(3)?.command)
        assertEquals(CommandType.SCROLL, script?.steps?.get(4)?.command)
        assertEquals(CommandType.LONG_PRESS, script?.steps?.get(5)?.command)
        assertEquals(CommandType.DOUBLE_CLICK, script?.steps?.get(6)?.command)
    }
    
    @Test
    fun testParametersAndTargets() {
        // 测试参数和目标的解析
        val paramsJson = """
            {
              "version": "3.1",
              "metadata": { "session_id": "test_params" },
              "steps": [
                {
                  "type": "action",
                  "command": "INPUT_TEXT",
                  "timeout_seconds": 10,
                  "target": {
                    "description": "Input Field",
                    "text_content": "Username"
                  },
                  "parameters": {
                    "text": "<EMAIL>"
                  }
                },
                {
                  "type": "action",
                  "command": "SCROLL",
                  "timeout_seconds": 10,
                  "parameters": {
                    "scroll_direction": "DOWN",
                    "scroll_amount": 3
                  }
                }
              ]
            }
        """.trimIndent()
        
        val script = dslScriptAdapter.fromJson(paramsJson)
        
        // 验证参数和目标
        assertNotNull("Script should not be null", script)
        assertEquals(2, script?.steps?.size)
        
        // 验证INPUT_TEXT步骤
        val inputStep = script?.steps?.get(0)
        assertEquals(CommandType.INPUT_TEXT, inputStep?.command)
        assertEquals("Input Field", inputStep?.target?.description)
        assertEquals("Username", inputStep?.target?.textContent)
        assertEquals("<EMAIL>", inputStep?.parameters?.text)
        
        // 验证SCROLL步骤
        val scrollStep = script?.steps?.get(1)
        assertEquals(CommandType.SCROLL, scrollStep?.command)
        assertEquals(ScrollDirection.DOWN, scrollStep?.parameters?.scrollDirection)
        assertEquals(3, scrollStep?.parameters?.scrollAmount)
    }
}
