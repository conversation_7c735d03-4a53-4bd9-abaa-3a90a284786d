package com.showforai.executor.performance

import android.os.Debug
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.showforai.executor.data.models.*
import com.showforai.executor.utils.PerformanceMonitor
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject

/**
 * 性能测试
 * 
 * 测试应用的性能指标和资源使用情况
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class PerformanceTest {
    
    @get:Rule
    val hiltRule = HiltAndroidRule(this)
    
    @Inject
    lateinit var performanceMonitor: PerformanceMonitor
    
    private val context = InstrumentationRegistry.getInstrumentation().targetContext
    
    @Before
    fun setup() {
        hiltRule.inject()
    }
    
    @Test
    fun testMemoryUsage() = runBlocking {
        // 测试内存使用情况
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        // 启动性能监控
        performanceMonitor.startMonitoring()
        
        // 等待一段时间收集数据
        delay(5000)
        
        val metrics = performanceMonitor.performanceMetrics.value
        
        // 验证内存使用在合理范围内
        assertTrue("Memory usage should be less than 200MB", 
            metrics.memoryUsageMB < 200)
        
        // 验证内存使用率不超过80%
        assertTrue("Memory usage percentage should be less than 80%", 
            metrics.memoryUsagePercent < 0.8f)
        
        performanceMonitor.stopMonitoring()
    }
    
    @Test
    fun testMemoryLeakDetection() = runBlocking {
        // 测试内存泄漏检测
        val runtime = Runtime.getRuntime()
        
        // 记录初始内存
        System.gc()
        delay(1000)
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        // 模拟多次操作
        repeat(10) {
            // 创建一些对象
            val testScript = createTestScript()
            
            // 模拟处理
            delay(100)
            
            // 强制垃圾回收
            System.gc()
        }
        
        // 等待垃圾回收完成
        delay(2000)
        System.gc()
        delay(1000)
        
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()
        val memoryIncrease = finalMemory - initialMemory
        
        // 验证内存增长在合理范围内（小于10MB）
        assertTrue("Memory increase should be less than 10MB: ${memoryIncrease / (1024 * 1024)}MB", 
            memoryIncrease < 10 * 1024 * 1024)
    }
    
    @Test
    fun testStartupTime() {
        // 测试启动时间
        val startTime = System.currentTimeMillis()
        
        // 模拟应用启动过程
        // 这里可以测试各个组件的初始化时间
        
        val endTime = System.currentTimeMillis()
        val startupTime = endTime - startTime
        
        // 验证启动时间小于3秒
        assertTrue("Startup time should be less than 3 seconds: ${startupTime}ms", 
            startupTime < 3000)
    }
    
    @Test
    fun testImageProcessingPerformance() = runBlocking {
        // 测试图像处理性能
        val testImageSize = 1080 * 1920 * 4 // ARGB_8888
        val testImage = android.graphics.Bitmap.createBitmap(1080, 1920, 
            android.graphics.Bitmap.Config.ARGB_8888)
        
        val startTime = System.currentTimeMillis()
        
        // 模拟图像处理操作
        repeat(5) {
            // 模拟图像缩放
            val scaledImage = android.graphics.Bitmap.createScaledBitmap(
                testImage, 540, 960, true)
            scaledImage.recycle()
            
            delay(10)
        }
        
        val endTime = System.currentTimeMillis()
        val processingTime = endTime - startTime
        
        // 验证图像处理时间合理
        assertTrue("Image processing should be efficient: ${processingTime}ms", 
            processingTime < 2000)
        
        testImage.recycle()
    }
    
    @Test
    fun testConcurrentOperations() = runBlocking {
        // 测试并发操作性能
        val startTime = System.currentTimeMillis()
        
        // 启动多个并发任务
        val jobs = (1..5).map { index ->
            kotlinx.coroutines.async {
                repeat(10) {
                    // 模拟一些计算密集型操作
                    val result = fibonacci(20)
                    delay(10)
                }
            }
        }
        
        // 等待所有任务完成
        jobs.forEach { it.await() }
        
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        
        // 验证并发操作效率
        assertTrue("Concurrent operations should complete in reasonable time: ${totalTime}ms", 
            totalTime < 5000)
    }
    
    @Test
    fun testLongRunningStability() = runBlocking {
        // 测试长时间运行稳定性
        performanceMonitor.startMonitoring()
        
        val startTime = System.currentTimeMillis()
        val testDuration = 30000L // 30秒测试
        
        var operationCount = 0
        var errorCount = 0
        
        while (System.currentTimeMillis() - startTime < testDuration) {
            try {
                // 模拟各种操作
                when (operationCount % 4) {
                    0 -> simulateScriptParsing()
                    1 -> simulateImageProcessing()
                    2 -> simulateFileOperation()
                    3 -> simulateNetworkOperation()
                }
                
                operationCount++
                delay(100)
                
            } catch (e: Exception) {
                errorCount++
            }
        }
        
        performanceMonitor.stopMonitoring()
        
        // 验证稳定性指标
        val errorRate = errorCount.toFloat() / operationCount
        assertTrue("Error rate should be less than 5%: ${errorRate * 100}%", 
            errorRate < 0.05f)
        
        assertTrue("Should complete at least 100 operations", operationCount >= 100)
    }
    
    @Test
    fun testGarbageCollectionImpact() = runBlocking {
        // 测试垃圾回收影响
        val gcCountBefore = Debug.getRuntimeStat("art.gc.gc-count")?.toLongOrNull() ?: 0L
        val gcTimeBefore = Debug.getRuntimeStat("art.gc.gc-time")?.toLongOrNull() ?: 0L
        
        // 执行一些内存密集型操作
        repeat(100) {
            val largeArray = ByteArray(1024 * 1024) // 1MB
            // 使用数组
            largeArray[0] = 1
            delay(10)
        }
        
        // 等待GC
        delay(2000)
        
        val gcCountAfter = Debug.getRuntimeStat("art.gc.gc-count")?.toLongOrNull() ?: 0L
        val gcTimeAfter = Debug.getRuntimeStat("art.gc.gc-time")?.toLongOrNull() ?: 0L
        
        val gcCount = gcCountAfter - gcCountBefore
        val gcTime = gcTimeAfter - gcTimeBefore
        
        // 验证GC影响在合理范围内
        assertTrue("GC count should be reasonable: $gcCount", gcCount < 50)
        assertTrue("GC time should be reasonable: ${gcTime}ms", gcTime < 1000)
    }
    
    @Test
    fun testResourceCleanup() = runBlocking {
        // 测试资源清理
        val initialFileDescriptors = getOpenFileDescriptorCount()
        
        // 执行一些资源操作
        repeat(20) {
            try {
                // 模拟文件操作
                val tempFile = java.io.File.createTempFile("test", ".tmp", context.cacheDir)
                tempFile.writeText("test data")
                val content = tempFile.readText()
                tempFile.delete()
                
                delay(50)
            } catch (e: Exception) {
                // 忽略错误，专注于资源清理
            }
        }
        
        // 等待资源清理
        delay(1000)
        System.gc()
        delay(1000)
        
        val finalFileDescriptors = getOpenFileDescriptorCount()
        val fdIncrease = finalFileDescriptors - initialFileDescriptors
        
        // 验证文件描述符没有泄漏
        assertTrue("File descriptor increase should be minimal: $fdIncrease", 
            fdIncrease < 10)
    }
    
    // 辅助方法
    private fun createTestScript(): DSLScript {
        return DSLScript(
            version = "3.1",
            metadata = DSLMetadata(
                sessionId = "test_${System.currentTimeMillis()}",
                description = "Performance test script",
                createdAt = "2024-01-01T12:00:00Z",
                deviceInfo = "Test device"
            ),
            steps = listOf(
                DSLStep(
                    type = "action",
                    command = CommandType.CLICK,
                    timeoutSeconds = 10,
                    target = DSLTarget(
                        description = "Test target",
                        visualHash = null,
                        textContent = "Test",
                        boundingBox = listOf(0.1f, 0.2f, 0.3f, 0.4f)
                    ),
                    parameters = null
                )
            )
        )
    }
    
    private fun fibonacci(n: Int): Long {
        return if (n <= 1) n.toLong() else fibonacci(n - 1) + fibonacci(n - 2)
    }
    
    private suspend fun simulateScriptParsing() {
        val script = createTestScript()
        // 模拟脚本解析
        delay(10)
    }
    
    private suspend fun simulateImageProcessing() {
        val bitmap = android.graphics.Bitmap.createBitmap(100, 100, 
            android.graphics.Bitmap.Config.ARGB_8888)
        // 模拟图像处理
        delay(20)
        bitmap.recycle()
    }
    
    private suspend fun simulateFileOperation() {
        val tempFile = java.io.File.createTempFile("perf_test", ".tmp", context.cacheDir)
        tempFile.writeText("test")
        tempFile.readText()
        tempFile.delete()
        delay(5)
    }
    
    private suspend fun simulateNetworkOperation() {
        // 模拟网络操作延迟
        delay(50)
    }
    
    private fun getOpenFileDescriptorCount(): Int {
        return try {
            val fdDir = java.io.File("/proc/self/fd")
            fdDir.listFiles()?.size ?: 0
        } catch (e: Exception) {
            0
        }
    }
}
