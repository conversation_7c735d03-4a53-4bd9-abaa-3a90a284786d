package com.showforai.executor.ui.permissions

import android.app.Activity
import android.content.Intent
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.showforai.executor.data.models.PermissionStatus
import com.showforai.executor.data.repositories.PermissionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 权限引导ViewModel
 * 
 * 管理权限引导界面的状态和逻辑：
 * - 权限状态检查
 * - 权限请求处理
 * - UI状态管理
 */
@HiltViewModel
class PermissionGuideViewModel @Inject constructor(
    private val permissionRepository: PermissionRepository
) : ViewModel() {
    
    // 权限状态
    private val _permissionStatus = MutableStateFlow(PermissionStatus())
    val permissionStatus: StateFlow<PermissionStatus> = _permissionStatus.asStateFlow()
    
    // UI状态
    val uiState: StateFlow<PermissionGuideUiState> = permissionStatus.map { status ->
        PermissionGuideUiState(
            permissionItems = createPermissionItems(status),
            allPermissionsGranted = status.allGranted
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = PermissionGuideUiState()
    )
    
    /**
     * 检查权限状态
     */
    fun checkPermissions() {
        viewModelScope.launch {
            try {
                val status = permissionRepository.checkAllPermissions()
                _permissionStatus.value = status
                Timber.d("Permission status updated: $status")
            } catch (e: Exception) {
                Timber.e(e, "Failed to check permissions")
            }
        }
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermission(activity: Activity) {
        viewModelScope.launch {
            try {
                Timber.d("Requesting storage permission")
                permissionRepository.requestStoragePermission(activity)
                
                // 延迟后重新检查权限状态
                kotlinx.coroutines.delay(1000)
                checkPermissions()
            } catch (e: Exception) {
                Timber.e(e, "Error requesting storage permission")
            }
        }
    }
    
    /**
     * 请求悬浮窗权限
     */
    fun requestOverlayPermission(activity: Activity) {
        viewModelScope.launch {
            try {
                Timber.d("Requesting overlay permission")
                permissionRepository.requestOverlayPermission(activity)
                
                // 延迟后重新检查权限状态
                kotlinx.coroutines.delay(1000)
                checkPermissions()
            } catch (e: Exception) {
                Timber.e(e, "Error requesting overlay permission")
            }
        }
    }
    
    /**
     * 请求屏幕捕捉权限
     */
    fun requestMediaProjectionPermission(activity: Activity) {
        viewModelScope.launch {
            try {
                Timber.d("Requesting media projection permission")
                permissionRepository.requestMediaProjectionPermission(activity)
                
                // MediaProjection权限需要在Activity结果中处理
            } catch (e: Exception) {
                Timber.e(e, "Error requesting media projection permission")
            }
        }
    }
    
    /**
     * 请求无障碍服务权限
     */
    fun requestAccessibilityPermission(activity: Activity) {
        viewModelScope.launch {
            try {
                Timber.d("Requesting accessibility permission")
                permissionRepository.requestAccessibilityPermission(activity)
                
                // 延迟后重新检查权限状态
                kotlinx.coroutines.delay(2000)
                checkPermissions()
            } catch (e: Exception) {
                Timber.e(e, "Error requesting accessibility permission")
            }
        }
    }
    
    /**
     * 处理权限请求结果
     */
    fun handlePermissionResults(permissions: Map<String, Boolean>) {
        Timber.d("Permission results: $permissions")
        
        // 重新检查权限状态
        checkPermissions()
    }
    
    /**
     * 处理Activity结果
     */
    fun handleActivityResult(resultCode: Int, data: Intent?) {
        Timber.d("Activity result: resultCode=$resultCode")
        
        // 处理MediaProjection权限结果
        if (resultCode == Activity.RESULT_OK) {
            // MediaProjection权限已授予
            val currentStatus = _permissionStatus.value
            _permissionStatus.value = currentStatus.copy(screenCapture = true)
        }
        
        // 重新检查其他权限状态
        checkPermissions()
    }
    
    /**
     * 创建权限项列表
     */
    private fun createPermissionItems(status: PermissionStatus): List<PermissionItem> {
        return listOf(
            PermissionItem(
                type = "storage",
                title = "存储权限",
                description = "允许应用读取DSL脚本文件和保存执行日志",
                icon = Icons.Default.Storage,
                granted = status.storage,
                steps = listOf(
                    "点击"授予"按钮",
                    "在设置页面中找到"文件和媒体"权限",
                    "开启"允许访问所有文件"选项"
                )
            ),
            PermissionItem(
                type = "overlay",
                title = "悬浮窗权限",
                description = "允许应用在其他应用上方显示执行状态和控制界面",
                icon = Icons.Default.OpenInNew,
                granted = status.overlay,
                steps = listOf(
                    "点击"授予"按钮",
                    "在设置页面中找到"显示在其他应用的上层"选项",
                    "开启该权限"
                )
            ),
            PermissionItem(
                type = "screen_capture",
                title = "屏幕捕捉权限",
                description = "允许应用捕捉屏幕内容，用于图像识别和UI元素定位",
                icon = Icons.Default.Screenshot,
                granted = status.screenCapture,
                steps = listOf(
                    "点击"授予"按钮",
                    "在弹出的对话框中选择"立即开始"",
                    "确认允许应用录制屏幕"
                )
            ),
            PermissionItem(
                type = "accessibility",
                title = "无障碍服务权限",
                description = "允许应用执行自动化操作，如点击、滑动、文本输入等",
                icon = Icons.Default.Accessibility,
                granted = status.accessibility,
                steps = listOf(
                    "点击"授予"按钮",
                    "在无障碍设置页面中找到"DSL Executor"",
                    "开启该服务",
                    "在弹出的对话框中确认"确定""
                )
            )
        )
    }
}

/**
 * 权限引导UI状态
 */
data class PermissionGuideUiState(
    val permissionItems: List<PermissionItem> = emptyList(),
    val allPermissionsGranted: Boolean = false
)
