import { test, expect } from '@playwright/test';

test.describe('Recording Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should navigate to recording page', async ({ page }) => {
    await page.click('[data-testid="recording-nav"]');
    await expect(page).toHaveURL(/.*recording/);
    await expect(page.locator('h1')).toContainText('Recording');
  });

  test('should show record button', async ({ page }) => {
    await page.goto('/recording');
    
    const recordButton = page.locator('[data-testid="record-button"]');
    await expect(recordButton).toBeVisible();
    await expect(recordButton).toContainText('Start Recording');
  });

  test('should handle recording flow', async ({ page }) => {
    await page.goto('/recording');
    
    const recordButton = page.locator('[data-testid="record-button"]');
    
    // Start recording
    await recordButton.click();
    await expect(recordButton).toContainText('Stop Recording');
    
    // Wait a moment to simulate recording
    await page.waitForTimeout(1000);
    
    // Stop recording
    await recordButton.click();
    await expect(recordButton).toContainText('Start Recording');
    
    // Should show naming dialog
    const namingDialog = page.locator('[data-testid="naming-dialog"]');
    await expect(namingDialog).toBeVisible();
  });

  test('should validate recording name input', async ({ page }) => {
    await page.goto('/recording');
    
    // Start and stop recording to trigger naming dialog
    const recordButton = page.locator('[data-testid="record-button"]');
    await recordButton.click();
    await page.waitForTimeout(500);
    await recordButton.click();
    
    const namingDialog = page.locator('[data-testid="naming-dialog"]');
    const nameInput = namingDialog.locator('input[name="recordingName"]');
    const saveButton = namingDialog.locator('[data-testid="save-button"]');
    
    // Test empty name validation
    await saveButton.click();
    await expect(page.locator('.ant-form-item-explain-error')).toBeVisible();
    
    // Test valid name
    await nameInput.fill('Test Recording');
    await saveButton.click();
    await expect(namingDialog).not.toBeVisible();
  });

  test('should show recording settings', async ({ page }) => {
    await page.goto('/recording');
    
    const settingsButton = page.locator('[data-testid="recording-settings"]');
    await settingsButton.click();
    
    const settingsModal = page.locator('[data-testid="settings-modal"]');
    await expect(settingsModal).toBeVisible();
    
    // Check for setting options
    await expect(settingsModal.locator('text=Capture Interval')).toBeVisible();
    await expect(settingsModal.locator('text=Quality')).toBeVisible();
    await expect(settingsModal.locator('text=Include Clicks')).toBeVisible();
  });

  test('should update recording settings', async ({ page }) => {
    await page.goto('/recording');
    
    const settingsButton = page.locator('[data-testid="recording-settings"]');
    await settingsButton.click();
    
    const settingsModal = page.locator('[data-testid="settings-modal"]');
    
    // Change quality setting
    const qualitySelect = settingsModal.locator('[data-testid="quality-select"]');
    await qualitySelect.click();
    await page.locator('.ant-select-item').filter({ hasText: 'Medium' }).click();
    
    // Toggle include clicks
    const includeClicksSwitch = settingsModal.locator('[data-testid="include-clicks"]');
    await includeClicksSwitch.click();
    
    // Save settings
    const saveButton = settingsModal.locator('[data-testid="save-settings"]');
    await saveButton.click();
    
    await expect(settingsModal).not.toBeVisible();
  });

  test('should handle recording errors gracefully', async ({ page }) => {
    // Mock API to return error
    await page.route('**/api/recording/start', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Recording failed' })
      });
    });
    
    await page.goto('/recording');
    
    const recordButton = page.locator('[data-testid="record-button"]');
    await recordButton.click();
    
    // Should show error message
    await expect(page.locator('.ant-message-error')).toBeVisible();
    await expect(page.locator('.ant-message-error')).toContainText('Recording failed');
  });

  test('should show progress indicator during recording', async ({ page }) => {
    await page.goto('/recording');
    
    const recordButton = page.locator('[data-testid="record-button"]');
    await recordButton.click();
    
    // Should show progress indicator
    const progressIndicator = page.locator('[data-testid="progress-indicator"]');
    await expect(progressIndicator).toBeVisible();
    
    // Stop recording
    await recordButton.click();
    await expect(progressIndicator).not.toBeVisible();
  });
});