[package]
name = "showforai"
version = "0.1.0"
description = "ShowForAI - AI-powered screen recording and automation tool"
authors = ["ShowForAI Team"]
license = "MIT"
repository = "https://github.com/showforai/showforai"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
tauri = { version = "1.5", features = ["api-all", "system-tray", "global-shortcut"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
image = "0.24"
rusqlite = { version = "0.30", features = ["bundled"] }
base64 = "0.21"
hmac = "0.12"
sha2 = "0.10"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"
reqwest = { version = "0.11", features = ["json", "multipart", "rustls-tls"] }
rdev = "0.4"
aes-gcm = "0.10"
rand = "0.8"
hex = "0.4"
argon2 = "0.5"
jsonwebtoken = "9.2"
screenshots = "0.3"
directories = "5.0"
parking_lot = "0.12"
gethostname = "0.4"
lru = "0.12"
once_cell = "1.19"
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
tempfile = "3.8"
criterion = { version = "0.5", features = ["html_reports"] }
mockall = "0.12"
serial_test = "3.0"

[features]
# This feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
default = ["custom-protocol"]

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi", "handleapi"] }

[target.'cfg(target_os = "macos")'.dependencies]
core-graphics = "0.23"
core-foundation = "0.9"

[target.'cfg(target_os = "linux")'.dependencies]
x11 = { version = "2.21", features = ["xlib", "xtest"] }

[[bench]]
name = "image_matching"
harness = false

[[bench]]
name = "recording_performance"
harness = false