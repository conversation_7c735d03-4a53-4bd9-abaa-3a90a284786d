package com.showforai.executor.services

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.showforai.executor.R
import com.showforai.executor.data.models.ScreenInfo
import com.showforai.executor.ui.main.MainActivity
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import timber.log.Timber
import java.nio.ByteBuffer

/**
 * 屏幕捕捉服务
 * 
 * 使用MediaProjection API实现屏幕捕捉功能
 * 为DSL执行引擎提供实时屏幕截图
 */
class ScreenCaptureService : Service() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "screen_capture_channel"
        private const val VIRTUAL_DISPLAY_NAME = "DSLExecutorCapture"
        
        const val ACTION_START_CAPTURE = "com.showforai.executor.START_CAPTURE"
        const val ACTION_STOP_CAPTURE = "com.showforai.executor.STOP_CAPTURE"
        
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_RESULT_DATA = "result_data"
    }
    
    private val binder = ScreenCaptureBinder()
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // MediaProjection相关
    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var imageReader: ImageReader? = null
    
    // 屏幕信息
    private lateinit var screenInfo: ScreenInfo
    private lateinit var windowManager: WindowManager
    
    // 截图流
    private val _screenshotFlow = MutableSharedFlow<Bitmap>()
    val screenshotFlow: SharedFlow<Bitmap> = _screenshotFlow.asSharedFlow()
    
    // 服务状态
    private var isCapturing = false
    
    inner class ScreenCaptureBinder : Binder() {
        fun getService(): ScreenCaptureService = this@ScreenCaptureService
    }
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("ScreenCaptureService created")
        
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        initializeScreenInfo()
        createNotificationChannel()
    }
    
    override fun onBind(intent: Intent?): IBinder = binder
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CAPTURE -> {
                val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, Activity.RESULT_CANCELED)
                val resultData = intent.getParcelableExtra<Intent>(EXTRA_RESULT_DATA)
                
                if (resultCode == Activity.RESULT_OK && resultData != null) {
                    startCapture(resultCode, resultData)
                } else {
                    Timber.e("Invalid MediaProjection permission")
                    stopSelf()
                }
            }
            ACTION_STOP_CAPTURE -> {
                stopCapture()
            }
        }
        
        return START_NOT_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopCapture()
        serviceScope.cancel()
        Timber.d("ScreenCaptureService destroyed")
    }
    
    /**
     * 初始化屏幕信息
     */
    private fun initializeScreenInfo() {
        val displayMetrics = DisplayMetrics()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = display
            display?.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        screenInfo = ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density,
            orientation = resources.configuration.orientation
        )
        
        Timber.d("Screen info: ${screenInfo.width}x${screenInfo.height}, density=${screenInfo.density}")
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Screen Capture",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "DSL Executor screen capture service"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 开始屏幕捕捉
     */
    private fun startCapture(resultCode: Int, resultData: Intent) {
        if (isCapturing) {
            Timber.w("Screen capture already running")
            return
        }
        
        try {
            // 创建MediaProjection
            val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, resultData)
            
            // 创建ImageReader
            imageReader = ImageReader.newInstance(
                screenInfo.width,
                screenInfo.height,
                PixelFormat.RGBA_8888,
                2
            ).apply {
                setOnImageAvailableListener(imageAvailableListener, null)
            }
            
            // 创建VirtualDisplay
            virtualDisplay = mediaProjection?.createVirtualDisplay(
                VIRTUAL_DISPLAY_NAME,
                screenInfo.width,
                screenInfo.height,
                (screenInfo.density * 160).toInt(),
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader?.surface,
                null,
                null
            )
            
            isCapturing = true
            startForeground(NOTIFICATION_ID, createNotification())
            
            Timber.i("Screen capture started")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start screen capture")
            stopCapture()
        }
    }
    
    /**
     * 停止屏幕捕捉
     */
    private fun stopCapture() {
        if (!isCapturing) return
        
        try {
            virtualDisplay?.release()
            virtualDisplay = null
            
            imageReader?.close()
            imageReader = null
            
            mediaProjection?.stop()
            mediaProjection = null
            
            isCapturing = false
            stopForeground(true)
            
            Timber.i("Screen capture stopped")
            
        } catch (e: Exception) {
            Timber.e(e, "Error stopping screen capture")
        }
    }
    
    /**
     * 图像可用监听器
     */
    private val imageAvailableListener = ImageReader.OnImageAvailableListener { reader ->
        serviceScope.launch(Dispatchers.IO) {
            try {
                val image = reader.acquireLatestImage()
                image?.use { img ->
                    val bitmap = imageToBitmap(img)
                    if (bitmap != null) {
                        _screenshotFlow.emit(bitmap)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error processing captured image")
            }
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * screenInfo.width
            
            val bitmap = Bitmap.createBitmap(
                screenInfo.width + rowPadding / pixelStride,
                screenInfo.height,
                Bitmap.Config.ARGB_8888
            )
            
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有行填充，需要裁剪
            if (rowPadding != 0) {
                Bitmap.createBitmap(bitmap, 0, 0, screenInfo.width, screenInfo.height)
            } else {
                bitmap
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to convert image to bitmap")
            null
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("DSL Executor")
            .setContentText("Screen capture is running")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    /**
     * 获取当前屏幕截图
     */
    suspend fun captureScreen(): Bitmap? {
        return if (isCapturing) {
            // 等待下一个截图
            withTimeoutOrNull(5000) {
                screenshotFlow.first()
            }
        } else {
            null
        }
    }
    
    /**
     * 获取屏幕信息
     */
    fun getScreenInfo(): ScreenInfo = screenInfo
    
    /**
     * 检查是否正在捕捉
     */
    fun isCapturing(): Boolean = isCapturing
}
