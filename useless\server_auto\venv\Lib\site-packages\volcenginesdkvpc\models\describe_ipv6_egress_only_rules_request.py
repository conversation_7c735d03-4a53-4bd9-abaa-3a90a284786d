# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeIpv6EgressOnlyRulesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'ipv6_address': 'str',
        'ipv6_egress_only_rule_ids': 'str',
        'ipv6_gateway_id': 'str',
        'max_results': 'int',
        'name': 'str',
        'next_token': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'ipv6_address': 'Ipv6Address',
        'ipv6_egress_only_rule_ids': 'Ipv6EgressOnlyRuleIds',
        'ipv6_gateway_id': 'Ipv6GatewayId',
        'max_results': 'MaxResults',
        'name': 'Name',
        'next_token': 'NextToken'
    }

    def __init__(self, instance_id=None, ipv6_address=None, ipv6_egress_only_rule_ids=None, ipv6_gateway_id=None, max_results=None, name=None, next_token=None, _configuration=None):  # noqa: E501
        """DescribeIpv6EgressOnlyRulesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._ipv6_address = None
        self._ipv6_egress_only_rule_ids = None
        self._ipv6_gateway_id = None
        self._max_results = None
        self._name = None
        self._next_token = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if ipv6_address is not None:
            self.ipv6_address = ipv6_address
        if ipv6_egress_only_rule_ids is not None:
            self.ipv6_egress_only_rule_ids = ipv6_egress_only_rule_ids
        self.ipv6_gateway_id = ipv6_gateway_id
        if max_results is not None:
            self.max_results = max_results
        if name is not None:
            self.name = name
        if next_token is not None:
            self.next_token = next_token

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The instance_id of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeIpv6EgressOnlyRulesRequest.


        :param instance_id: The instance_id of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def ipv6_address(self):
        """Gets the ipv6_address of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The ipv6_address of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_address

    @ipv6_address.setter
    def ipv6_address(self, ipv6_address):
        """Sets the ipv6_address of this DescribeIpv6EgressOnlyRulesRequest.


        :param ipv6_address: The ipv6_address of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: str
        """

        self._ipv6_address = ipv6_address

    @property
    def ipv6_egress_only_rule_ids(self):
        """Gets the ipv6_egress_only_rule_ids of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The ipv6_egress_only_rule_ids of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_egress_only_rule_ids

    @ipv6_egress_only_rule_ids.setter
    def ipv6_egress_only_rule_ids(self, ipv6_egress_only_rule_ids):
        """Sets the ipv6_egress_only_rule_ids of this DescribeIpv6EgressOnlyRulesRequest.


        :param ipv6_egress_only_rule_ids: The ipv6_egress_only_rule_ids of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: str
        """

        self._ipv6_egress_only_rule_ids = ipv6_egress_only_rule_ids

    @property
    def ipv6_gateway_id(self):
        """Gets the ipv6_gateway_id of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The ipv6_gateway_id of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_gateway_id

    @ipv6_gateway_id.setter
    def ipv6_gateway_id(self, ipv6_gateway_id):
        """Sets the ipv6_gateway_id of this DescribeIpv6EgressOnlyRulesRequest.


        :param ipv6_gateway_id: The ipv6_gateway_id of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ipv6_gateway_id is None:
            raise ValueError("Invalid value for `ipv6_gateway_id`, must not be `None`")  # noqa: E501

        self._ipv6_gateway_id = ipv6_gateway_id

    @property
    def max_results(self):
        """Gets the max_results of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The max_results of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeIpv6EgressOnlyRulesRequest.


        :param max_results: The max_results of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def name(self):
        """Gets the name of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The name of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeIpv6EgressOnlyRulesRequest.


        :param name: The name of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def next_token(self):
        """Gets the next_token of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501


        :return: The next_token of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeIpv6EgressOnlyRulesRequest.


        :param next_token: The next_token of this DescribeIpv6EgressOnlyRulesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeIpv6EgressOnlyRulesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeIpv6EgressOnlyRulesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeIpv6EgressOnlyRulesRequest):
            return True

        return self.to_dict() != other.to_dict()
