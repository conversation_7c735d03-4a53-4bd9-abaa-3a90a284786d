package com.showforai.executor.utils

import android.content.Context
import android.content.res.Configuration
import android.graphics.Point
import android.os.Build
import android.util.DisplayMetrics
import android.view.WindowManager
import com.showforai.executor.data.models.ScreenInfo
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt

/**
 * 屏幕工具类
 * 
 * 提供屏幕相关的工具方法：
 * - 屏幕尺寸和密度信息
 * - 坐标转换（归一化坐标 ↔ 像素坐标）
 * - DPI缩放处理
 * - 屏幕方向检测
 */
@Singleton
class ScreenUtils @Inject constructor(
    private val context: Context
) {
    
    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var cachedScreenInfo: ScreenInfo? = null
    
    /**
     * 获取当前屏幕信息
     */
    fun getScreenInfo(): ScreenInfo {
        // 检查缓存是否有效
        cachedScreenInfo?.let { cached ->
            val currentOrientation = context.resources.configuration.orientation
            if (cached.orientation == currentOrientation) {
                return cached
            }
        }
        
        // 重新获取屏幕信息
        val displayMetrics = DisplayMetrics()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = context.display
            display?.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        val screenInfo = ScreenInfo(
            width = displayMetrics.widthPixels,
            height = displayMetrics.heightPixels,
            density = displayMetrics.density,
            orientation = context.resources.configuration.orientation
        )
        
        cachedScreenInfo = screenInfo
        Timber.d("Screen info updated: ${screenInfo.width}x${screenInfo.height}, density=${screenInfo.density}")
        
        return screenInfo
    }
    
    /**
     * 将归一化坐标转换为像素坐标
     * @param normalizedX 归一化X坐标 (0.0 - 1.0)
     * @param normalizedY 归一化Y坐标 (0.0 - 1.0)
     * @return 像素坐标点
     */
    fun normalizedToPixel(normalizedX: Float, normalizedY: Float): Point {
        val screenInfo = getScreenInfo()
        val pixelX = (normalizedX * screenInfo.width).roundToInt()
        val pixelY = (normalizedY * screenInfo.height).roundToInt()
        
        return Point(pixelX, pixelY)
    }
    
    /**
     * 将像素坐标转换为归一化坐标
     * @param pixelX 像素X坐标
     * @param pixelY 像素Y坐标
     * @return 归一化坐标点
     */
    fun pixelToNormalized(pixelX: Int, pixelY: Int): com.showforai.executor.data.models.Point {
        val screenInfo = getScreenInfo()
        val normalizedX = pixelX.toFloat() / screenInfo.width
        val normalizedY = pixelY.toFloat() / screenInfo.height
        
        return com.showforai.executor.data.models.Point(normalizedX, normalizedY)
    }
    
    /**
     * 将归一化边界框转换为像素边界框
     * @param normalizedBox 归一化边界框 [x1, y1, x2, y2]
     * @return 像素边界框
     */
    fun normalizedBoxToPixel(normalizedBox: List<Float>): android.graphics.RectF {
        if (normalizedBox.size != 4) {
            throw IllegalArgumentException("Bounding box must have 4 elements: [x1, y1, x2, y2]")
        }
        
        val screenInfo = getScreenInfo()
        val left = normalizedBox[0] * screenInfo.width
        val top = normalizedBox[1] * screenInfo.height
        val right = normalizedBox[2] * screenInfo.width
        val bottom = normalizedBox[3] * screenInfo.height
        
        return android.graphics.RectF(left, top, right, bottom)
    }
    
    /**
     * 将像素边界框转换为归一化边界框
     * @param pixelBox 像素边界框
     * @return 归一化边界框 [x1, y1, x2, y2]
     */
    fun pixelBoxToNormalized(pixelBox: android.graphics.RectF): List<Float> {
        val screenInfo = getScreenInfo()
        return listOf(
            pixelBox.left / screenInfo.width,
            pixelBox.top / screenInfo.height,
            pixelBox.right / screenInfo.width,
            pixelBox.bottom / screenInfo.height
        )
    }
    
    /**
     * 获取屏幕中心点（像素坐标）
     */
    fun getScreenCenter(): Point {
        val screenInfo = getScreenInfo()
        return Point(screenInfo.width / 2, screenInfo.height / 2)
    }
    
    /**
     * 获取屏幕中心点（归一化坐标）
     */
    fun getScreenCenterNormalized(): com.showforai.executor.data.models.Point {
        return com.showforai.executor.data.models.Point(0.5f, 0.5f)
    }
    
    /**
     * 检查坐标是否在屏幕范围内
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 是否在屏幕范围内
     */
    fun isPointInScreen(x: Int, y: Int): Boolean {
        val screenInfo = getScreenInfo()
        return x >= 0 && x < screenInfo.width && y >= 0 && y < screenInfo.height
    }
    
    /**
     * 检查归一化坐标是否有效
     * @param normalizedX 归一化X坐标
     * @param normalizedY 归一化Y坐标
     * @return 是否有效
     */
    fun isNormalizedPointValid(normalizedX: Float, normalizedY: Float): Boolean {
        return normalizedX >= 0f && normalizedX <= 1f && normalizedY >= 0f && normalizedY <= 1f
    }
    
    /**
     * 限制坐标在屏幕范围内
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 限制后的坐标
     */
    fun clampToScreen(x: Int, y: Int): Point {
        val screenInfo = getScreenInfo()
        val clampedX = x.coerceIn(0, screenInfo.width - 1)
        val clampedY = y.coerceIn(0, screenInfo.height - 1)
        return Point(clampedX, clampedY)
    }
    
    /**
     * 限制归一化坐标在有效范围内
     * @param normalizedX 归一化X坐标
     * @param normalizedY 归一化Y坐标
     * @return 限制后的归一化坐标
     */
    fun clampNormalizedPoint(normalizedX: Float, normalizedY: Float): com.showforai.executor.data.models.Point {
        val clampedX = normalizedX.coerceIn(0f, 1f)
        val clampedY = normalizedY.coerceIn(0f, 1f)
        return com.showforai.executor.data.models.Point(clampedX, clampedY)
    }
    
    /**
     * 获取DPI缩放比例
     */
    fun getDpiScale(): Float {
        return getScreenInfo().density
    }
    
    /**
     * 将DP转换为像素
     * @param dp DP值
     * @return 像素值
     */
    fun dpToPx(dp: Float): Int {
        val density = getDpiScale()
        return (dp * density + 0.5f).toInt()
    }
    
    /**
     * 将像素转换为DP
     * @param px 像素值
     * @return DP值
     */
    fun pxToDp(px: Int): Float {
        val density = getDpiScale()
        return px / density
    }
    
    /**
     * 检查屏幕方向是否为横屏
     */
    fun isLandscape(): Boolean {
        return getScreenInfo().orientation == Configuration.ORIENTATION_LANDSCAPE
    }
    
    /**
     * 检查屏幕方向是否为竖屏
     */
    fun isPortrait(): Boolean {
        return getScreenInfo().orientation == Configuration.ORIENTATION_PORTRAIT
    }
    
    /**
     * 获取状态栏高度
     */
    fun getStatusBarHeight(): Int {
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            context.resources.getDimensionPixelSize(resourceId)
        } else {
            dpToPx(24f) // 默认状态栏高度
        }
    }
    
    /**
     * 获取导航栏高度
     */
    fun getNavigationBarHeight(): Int {
        val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            context.resources.getDimensionPixelSize(resourceId)
        } else {
            dpToPx(48f) // 默认导航栏高度
        }
    }
    
    /**
     * 清除缓存的屏幕信息（在屏幕旋转时调用）
     */
    fun clearCache() {
        cachedScreenInfo = null
        Timber.d("Screen info cache cleared")
    }
}
