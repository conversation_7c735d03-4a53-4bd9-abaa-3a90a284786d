"""
测试按钮点击问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 设置日志级别
import logging
logging.basicConfig(level=logging.DEBUG)
from loguru import logger

from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt
from showforai.sync.offline_manager import get_offline_manager
from showforai.recorder.recorder import Recorder, RecorderState
from showforai.config import Config

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Button Click")
        self.setFixedSize(300, 200)
        
        # Create layout
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Status label
        self.status_label = QLabel("Status: Ready")
        layout.addWidget(self.status_label)
        
        # Network info label
        self.network_label = QLabel("Network: Checking...")
        layout.addWidget(self.network_label)
        
        # Test button
        self.test_button = QPushButton("Test Recording")
        self.test_button.clicked.connect(self.on_button_click)
        layout.addWidget(self.test_button)
        
        # Initialize components like GUI does
        self.config = Config()
        self.recorder = Recorder(config=self.config)
        self.offline_manager = get_offline_manager()
        
        # Update network status
        self.update_network_status()
        
    def update_network_status(self):
        """Update network status display"""
        is_online = self.offline_manager.is_online()
        can_record = self.offline_manager.is_recording_allowed()
        
        self.network_label.setText(f"Network: {'Online' if is_online else 'Offline'}, Recording: {'Allowed' if can_record else 'Not Allowed'}")
        
        print(f"Network status updated:")
        print(f"  is_online: {is_online}")
        print(f"  can_record: {can_record}")
        
    def on_button_click(self):
        """Handle button click"""
        print("\n" + "="*60)
        print("BUTTON CLICKED!")
        print(f"Recorder state: {self.recorder.get_state()}")
        print(f"Is IDLE? {self.recorder.get_state() == RecorderState.IDLE}")
        
        if self.recorder.get_state() == RecorderState.IDLE:
            print("State is IDLE, checking if recording allowed...")
            
            # Check network
            is_allowed = self.offline_manager.is_recording_allowed()
            print(f"is_recording_allowed() returned: {is_allowed}")
            print(f"Type: {type(is_allowed)}")
            
            # Try different checks
            print(f"\nDebug checks:")
            print(f"  is_allowed == True: {is_allowed == True}")
            print(f"  is_allowed is True: {is_allowed is True}")
            print(f"  bool(is_allowed): {bool(is_allowed)}")
            print(f"  not is_allowed: {not is_allowed}")
            
            if not is_allowed:
                print("=> Will show offline message")
                self.status_label.setText("Status: Offline - Recording not allowed")
            else:
                print("=> Will start recording")
                self.status_label.setText("Status: Recording started!")
        
        print("="*60)

def main():
    app = QApplication([])
    window = TestWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()