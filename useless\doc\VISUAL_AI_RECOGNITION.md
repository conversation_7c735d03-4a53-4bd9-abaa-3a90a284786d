# ShowForAI APP - 视觉AI识别流程文档

## 1. 概述

本文档详细描述ShowForAI APP中使用火山引擎视觉大模型进行UI元素识别的核心流程。该系统通过AI精准识别用户点击位置的UI元素边界，生成高质量的元素图像用于后续的快速匹配。

## 2. 技术架构

### 2.1 核心技术栈

- **视觉大模型**: 火山引擎视觉理解大模型
- **截图库**: Python MSS (Multi-Screen Shot) - 跨平台高性能截图
- **图像处理**: OpenCV + NumPy
- **图像匹配**: OpenCV特征匹配算法

### 2.2 性能指标

- **截图性能**: MSS可达到20-60 FPS (16-47ms/截图)
- **AI识别**: 200-500ms/次 (取决于网络和模型响应)
- **本地匹配**: 5-20ms/次 (使用优化后的算法)

## 3. UI元素识别流程

### 3.1 流程图

```mermaid
graph TD
    A[用户点击] --> B[捕获点击坐标]
    B --> C[MSS快速截图]
    C --> D[截取点击区域]
    D --> E[调用火山引擎API]
    E --> F{AI识别元素边界}
    F --> G[返回边界坐标]
    G --> H[精确裁切元素]
    H --> I[保存元素图像]
    I --> J[生成特征描述]
    J --> K[存储到脚本]
```

### 3.2 详细步骤

#### 步骤1: 捕获用户操作

```python
def capture_click_context(x: int, y: int, context_size: int = 300) -> Dict:
    """捕获点击位置的上下文"""
    # 使用MSS快速截图
    with mss.mss() as sct:
        # 计算截图区域（以点击点为中心）
        monitor = sct.monitors[1]  # 主显示器
        region = {
            "left": max(0, x - context_size // 2),
            "top": max(0, y - context_size // 2),
            "width": min(context_size, monitor["width"] - x + context_size // 2),
            "height": min(context_size, monitor["height"] - y + context_size // 2)
        }
        
        # 截图（高性能，16-47ms）
        screenshot = sct.grab(region)
        
        # 转换为numpy数组
        img_array = np.array(screenshot)
        
        # 计算点击点在截图中的相对位置
        relative_x = x - region["left"]
        relative_y = y - region["top"]
        
        return {
            "screenshot": img_array,
            "click_point": (relative_x, relative_y),
            "region": region,
            "timestamp": time.time()
        }
```

#### 步骤2: AI模型提示词设计

```python
def generate_element_recognition_prompt(click_x: int, click_y: int) -> str:
    """生成UI元素识别提示词"""
    
    prompt = f"""你是一个专业的UI元素识别专家。请分析这张截图并完成以下任务：

## 任务描述
用户在坐标({click_x}, {click_y})处进行了点击操作。请识别该点击位置所在的UI元素。

## 识别要求
1. **精确定位**: 找到包含点击坐标的最小完整UI元素
2. **边界识别**: 确定该元素的精确边界框
3. **元素类型**: 判断元素类型（按钮、输入框、链接、图标等）
4. **视觉特征**: 描述元素的主要视觉特征

## 输出格式（JSON）
{{
    "element_type": "button|input|link|icon|text|image|other",
    "bounding_box": {{
        "left": <左边界x坐标>,
        "top": <上边界y坐标>,
        "right": <右边界x坐标>,
        "bottom": <下边界y坐标>
    }},
    "confidence": <0-1的置信度>,
    "visual_features": {{
        "has_text": <bool>,
        "text_content": "<识别到的文字>",
        "background_color": "<主要背景色>",
        "has_border": <bool>,
        "shape": "rectangle|circle|irregular"
    }},
    "reasoning": "<识别推理过程>"
}}

## 深度思考要求
- 仔细观察点击位置周围的视觉线索
- 考虑常见UI设计模式和元素边界特征
- 如果存在嵌套元素，选择最合适的层级
- 确保边界框完整包含元素的所有视觉部分

请基于以上要求，识别点击位置的UI元素。
"""
    
    return prompt
```

#### 步骤3: 调用视觉大模型

```python
class VolcanoVisionAPI:
    """火山引擎视觉API封装"""
    
    def __init__(self, api_key: str, endpoint: str):
        self.api_key = api_key
        self.endpoint = endpoint
        self.session = aiohttp.ClientSession()
        
    async def recognize_ui_element(self, 
                                 screenshot: np.ndarray, 
                                 click_point: Tuple[int, int]) -> Dict:
        """识别UI元素"""
        
        # 图像编码
        _, buffer = cv2.imencode('.png', screenshot)
        image_base64 = base64.b64encode(buffer).decode()
        
        # 生成提示词
        prompt = generate_element_recognition_prompt(click_point[0], click_point[1])
        
        # 构建请求
        request_data = {
            "model": "volcano-vision-ui-v2",  # 假设的模型名
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1,  # 低温度保证输出稳定性
            "response_format": {"type": "json_object"},  # 强制JSON输出
            "max_tokens": 500
        }
        
        # 发送请求
        async with self.session.post(
            self.endpoint,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            json=request_data
        ) as response:
            result = await response.json()
            
        # 解析响应
        return self._parse_response(result)
        
    def _parse_response(self, response: Dict) -> Dict:
        """解析模型响应"""
        try:
            content = response['choices'][0]['message']['content']
            element_info = json.loads(content)
            
            # 验证必要字段
            required_fields = ['element_type', 'bounding_box', 'confidence']
            for field in required_fields:
                if field not in element_info:
                    raise ValueError(f"Missing required field: {field}")
                    
            return element_info
            
        except Exception as e:
            logging.error(f"Failed to parse AI response: {e}")
            raise
```

#### 步骤4: 元素裁切和优化

```python
def crop_element_with_padding(screenshot: np.ndarray, 
                            bounding_box: Dict,
                            padding: int = 5) -> np.ndarray:
    """根据AI识别的边界裁切元素"""
    
    # 提取边界坐标
    left = max(0, bounding_box['left'] - padding)
    top = max(0, bounding_box['top'] - padding)
    right = min(screenshot.shape[1], bounding_box['right'] + padding)
    bottom = min(screenshot.shape[0], bounding_box['bottom'] + padding)
    
    # 裁切图像
    element_image = screenshot[top:bottom, left:right]
    
    # 图像质量优化
    # 1. 降噪
    element_image = cv2.fastNlMeansDenoising(element_image)
    
    # 2. 锐化
    kernel = np.array([[-1,-1,-1], 
                      [-1, 9,-1], 
                      [-1,-1,-1]])
    element_image = cv2.filter2D(element_image, -1, kernel)
    
    return element_image
```

## 5. 执行引擎匹配设计

### 5.1 算法选择

#### 5.1.1 主算法: ORB
- **性能**: 2-5ms/图像
- **特性**: 旋转不变性，适合UI元素
- **内存**: 低占用
- **准确率**: 90%+

#### 5.1.2 备用算法: SIFT
- **性能**: 20-50ms/图像
- **特性**: 尺度不变性，高精度
- **场景**: 特殊高精度需求

### 5.2 匹配优化策略

#### 5.2.1 特征配置
```yaml
ORB参数:
  nfeatures: 500       # 特征点数
  scaleFactor: 1.2     # 金字塔缩放因子
  nlevels: 8           # 金字塔层数
  edgeThreshold: 31    # 边缘阈值
  
FLANN配置:
  algorithm: LSH       # 针对二进制描述符
  table_number: 12
  key_size: 20
  multi_probe_level: 2
```

#### 5.2.2 缓存策略
- **特征缓存**: LRU缓存最近500个特征
- **屏幕缓存**: 哈希对比避免重复处理
- **结果缓存**: 相似元素匹配结果

### 5.3 持续扫描优化

#### 5.3.1 扫描策略
- **扫描间隔**: 50ms
- **屏幕变化检测**: 哈希比较
- **智能等待**: 动态调整间隔

#### 5.3.2 性能指标
- **单次匹配**: 5-20ms
- **持续扫描**: 20FPS
- **CPU占用**: < 10%

## 6. 错误处理设计

### 6.1 降级策略

#### 6.1.1 AI识别降级链
1. **主策略**: 火山引擎AI识别 (置信度 > 0.8)
2. **降级1**: 传统边缘检测 (置信度 0.6-0.8)
3. **降级2**: 固定大小裁切 (置信度 < 0.6)

#### 6.1.2 传统边缘检测
- **算法**: Canny边缘检测
- **阈值**: 50-150
- **轮廓选择**: 最小包围矩形
- **适用场景**: 明显边界的UI元素

#### 6.1.3 固定大小裁切
- **默认大小**: 100x50像素
- **中心点**: 点击位置
- **使用场景**: 完全失败时的保底方案

### 6.2 异常处理

#### 6.2.1 网络异常
- **超时设置**: 5秒
- **重试机制**: 3次，指数退避
- **本地缓存**: 历史识别结果

#### 6.2.2 模型异常
- **输出验证**: JSON格式检查
- **必要字段**: element_type, bounding_box, confidence
- **异常记录**: 详细日志和指标

## 7. 性能监控

### 7.1 指标收集

#### 7.1.1 核心指标
- **截图时间**: mean, p95, p99
- **AI识别时间**: mean, p95, p99
- **匹配时间**: mean, p95, p99
- **缓存命中率**: hits/(hits+misses)

#### 7.1.2 统计策略
- **滚动窗口**: 最近1000条记录
- **实时统计**: 动态计算
- **持久化**: 可选存储

### 7.2 性能优化建议

#### 7.2.1 截图优化
- **区域限制**: 只截取必要区域
- **分辨率适配**: 动态调整
- **多显示器**: 指定监视器

#### 7.2.2 AI识别优化  
- **批量请求**: 合并多个识别
- **结果缓存**: 相似元素复用
- **预加载**: 预测性加载

#### 7.2.3 匹配优化
- **特征缓存**: LRU策略
- **并行化**: 多线程处理
- **GPU加速**: 可选OpenCV GPU

## 8. 最佳实践

### 8.1 提示词优化

1. **明确坐标信息**: 始终包含精确点击坐标
2. **输出格式约束**: JSON Schema强制
3. **深度思考引导**: 要求推理过程
4. **视觉特征提取**: 关键特征描述

### 8.2 性能优化

1. **区域截图**: 避免全屏
2. **特征缓存**: 减少重复计算
3. **并行处理**: 提高吞吐量
4. **智能降级**: 保证可用性

### 8.3 精度保证

1. **多级验证**: 交叉验证
2. **置信度阈值**: 动态调整
3. **边界扩展**: 确保完整性

## 9. 安全设计

### 9.1 API安全

- **认证**: Bearer Token
- **加密**: HTTPS传输
- **限流**: 防止滥用

### 9.2 数据安全

- **截图处理**: 不存储敏感信息
- **结果缓存**: 加密存储
- **日志脱敏**: 移除敏感数据

## 10. 未来优化方向

1. **本地模型**: 部署轻量级视觉模型
2. **GPU加速**: 特征提取和匹配
3. **增量更新**: 只处理变化部分
4. **预测优化**: 基于用户行为预测
5. **多模态融合**: 结合文本和图像特征