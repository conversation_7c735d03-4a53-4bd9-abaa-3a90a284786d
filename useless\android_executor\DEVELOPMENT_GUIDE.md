# Android DSL Executor 开发指南

## 🚀 项目概述

Android DSL Executor是ShowForAI平台的移动端执行组件，实现了与桌面版相同的DSL规范和三层级联定位策略。

## 📋 已完成的开发内容

### ✅ 基础架构
- [x] 项目结构创建
- [x] Gradle配置和依赖管理
- [x] Hilt依赖注入配置
- [x] 核心数据模型定义
- [x] 应用程序类和全局配置

### ✅ 核心组件
- [x] DSL执行引擎框架
- [x] 屏幕捕捉服务 (MediaProjection)
- [x] 无障碍服务 (AccessibilityService)
- [x] 主界面Activity和UI组件
- [x] 权限管理框架

### ✅ 工具类和管理器
- [x] PermissionManager - 权限管理
- [x] ScreenUtils - 屏幕工具类
- [x] FileManager - 文件管理
- [x] OpenCVManager - OpenCV图像处理
- [x] OCRProcessor - ML Kit文本识别

### ✅ 三层定位策略
- [x] VisualMatchingStrategy - OpenCV模板匹配
- [x] OCRStrategy - OCR文本识别
- [x] CoordinateFallbackStrategy - 坐标降级
- [x] LocationStrategyManager - 策略管理器

### ✅ 动作执行系统
- [x] ActionExecutorManager - 动作执行管理器
- [x] 完整的DSL步骤执行方法
- [x] 手势操作支持（点击、滑动、长按等）

### ✅ 用户界面
- [x] MainActivity - 主界面
- [x] MainViewModel - 主界面状态管理
- [x] PermissionGuideActivity - 权限引导界面
- [x] PermissionGuideViewModel - 权限引导状态管理
- [x] Jetpack Compose UI组件

### ✅ 数据层
- [x] ScriptRepository - 脚本数据仓库
- [x] PermissionRepository - 权限数据仓库
- [x] 完整的数据模型定义

### ✅ 错误处理和监控
- [x] ErrorHandler - 智能错误处理和恢复
- [x] PerformanceMonitor - 性能监控系统
- [x] ImageCache - 图像缓存管理

### ✅ 测试覆盖
- [x] DSL模型单元测试
- [x] 工具类单元测试
- [x] 集成测试套件

### ✅ 配置文件
- [x] Android清单文件
- [x] 无障碍服务配置
- [x] 文件提供者配置
- [x] Material 3主题和样式
- [x] 字符串资源和颜色资源

## 🔧 已完成的开发任务

### Phase 1: 集成和完善 (已完成)
1. **服务集成**
   - [x] 完善ScreenCaptureService与LocationStrategies的集成
   - [x] 实现FileManager的资源文件加载功能
   - [x] 完善OpenCV初始化和资源管理

2. **执行监控界面**
   - [x] ExecutionActivity - 实时执行状态显示
   - [x] ExecutionViewModel - 执行状态管理
   - [x] 执行进度和日志显示

3. **设置界面**
   - [x] SettingsActivity - 配置参数管理
   - [x] SettingsViewModel - 设置状态管理
   - [x] 执行参数配置（超时、阈值等）

### Phase 2: 测试和优化 (已完成)
1. **单元测试**
   - [x] DSL模型单元测试
   - [x] 工具类单元测试
   - [x] 定位策略测试

2. **集成测试**
   - [x] 端到端执行流程测试
   - [x] 权限管理测试
   - [x] 文件操作测试

3. **性能优化**
   - [x] 内存使用优化 (ImageCache)
   - [x] 图像处理性能优化 (OpenCVManager)
   - [x] 性能监控系统 (PerformanceMonitor)

### Phase 3: 高级功能 (已完成)
1. **错误处理增强**
   - [x] 智能重试机制
   - [x] 错误恢复策略
   - [x] 详细错误报告

2. **用户体验优化**
   - [x] 执行预览功能
   - [x] 示例脚本
   - [x] 执行历史记录

### Phase 4: 未来扩展 (待开发)
1. **云端功能**
   - [ ] 云端脚本同步
   - [ ] 脚本分享功能
   - [ ] 用户账户系统

2. **高级功能**
   - [ ] 脚本编辑器
   - [ ] 高级手势支持
   - [ ] 自定义插件系统

## 🛠️ 开发环境设置

### 必需工具
- Android Studio Arctic Fox+
- Android SDK API 21+
- Kotlin 1.9+
- Gradle 8.0+

### 依赖库
- **UI**: Jetpack Compose + Material3
- **架构**: MVVM + Hilt
- **图像处理**: OpenCV for Android
- **OCR**: Google ML Kit
- **JSON**: Moshi
- **协程**: Kotlinx Coroutines

## 📱 权限要求

### 运行时权限
- `READ_EXTERNAL_STORAGE`: 读取DSL脚本文件
- `WRITE_EXTERNAL_STORAGE`: 保存执行日志
- `SYSTEM_ALERT_WINDOW`: 悬浮窗显示

### 特殊权限
- **MediaProjection**: 屏幕录制权限
- **AccessibilityService**: 无障碍服务权限

## 🔍 调试指南

### 日志查看
```bash
# 查看应用日志
adb logcat | grep "DSLExecutor"

# 查看特定组件日志
adb logcat | grep "ScreenCapture"
adb logcat | grep "Automation"
```

### 安装和运行
```bash
# 安装调试版本
./gradlew installDebug

# 启动应用
adb shell am start -n com.showforai.executor/.ui.main.MainActivity
```

### 权限调试
```bash
# 检查权限状态
adb shell dumpsys package com.showforai.executor | grep permission

# 手动授予权限
adb shell pm grant com.showforai.executor android.permission.READ_EXTERNAL_STORAGE
```

## 📐 架构设计

### 核心组件关系
```
DSLExecutionEngine
├── LocationStrategyManager
│   ├── VisualMatchingStrategy
│   ├── OCRStrategy
│   └── CoordinateFallbackStrategy
├── ActionExecutorManager
└── FileManager

Services
├── ScreenCaptureService
├── AutomationAccessibilityService
└── DSLExecutionService

UI Layer
├── MainActivity
├── ExecutionActivity
├── PermissionGuideActivity
└── SettingsActivity
```

### 数据流
```
DSL Script File → FileManager → DSLExecutionEngine
                                      ↓
ScreenCapture → LocationStrategies → ActionExecutors
                                      ↓
                              UI Updates ← ExecutionResult
```

## 🎯 开发重点

### 1. 三层级联定位策略
这是整个系统的核心，需要确保：
- 视觉匹配的准确性和性能
- OCR识别的可靠性
- 坐标降级的兼容性

### 2. 权限管理
Android的权限系统比较复杂，需要：
- 友好的权限申请流程
- 清晰的权限状态提示
- 权限缺失时的降级处理

### 3. 性能优化
移动设备资源有限，需要：
- 内存使用优化
- CPU占用控制
- 电池消耗管理

## 🚨 注意事项

1. **OpenCV初始化**: OpenCV for Android需要异步初始化，确保在使用前完成初始化
2. **权限申请**: MediaProjection和AccessibilityService需要用户手动授权
3. **生命周期管理**: 服务的启动和停止需要正确处理
4. **异常处理**: 图像识别和手势操作可能失败，需要完善的错误处理
5. **兼容性**: 不同Android版本和设备的兼容性测试

## 📚 参考资料

- [Android AccessibilityService官方文档](https://developer.android.com/reference/android/accessibilityservice/AccessibilityService)
- [MediaProjection API指南](https://developer.android.com/reference/android/media/projection/MediaProjection)
- [OpenCV for Android教程](https://docs.opencv.org/4.x/d9/df8/tutorial_root.html)
- [ML Kit文本识别](https://developers.google.com/ml-kit/vision/text-recognition)
- [Jetpack Compose指南](https://developer.android.com/jetpack/compose)

## 🤝 贡献指南

1. 遵循Kotlin编码规范
2. 添加适当的注释和文档
3. 编写单元测试
4. 提交前运行代码检查
5. 更新相关文档
