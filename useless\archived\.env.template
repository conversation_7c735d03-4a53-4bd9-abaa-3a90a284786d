# ShowForAI V3 Environment Configuration Template
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# ====================
# AI Service Configuration
# ====================
# API endpoint for AI element detection service
AI_SERVICE_URL=

# API key for authentication with AI service
AI_API_KEY=

# Timeout for AI service requests (in seconds)
AI_REQUEST_TIMEOUT=30

# Maximum retries for failed AI requests
AI_MAX_RETRIES=3

# ====================
# Supabase Configuration
# ====================
# Supabase project URL
SUPABASE_URL=

# Supabase anonymous key for client access
SUPABASE_ANON_KEY=

# Supabase service role key (for admin operations)
SUPABASE_SERVICE_KEY=

# ====================
# Storage Configuration
# ====================
# Storage bucket name for screenshots
STORAGE_BUCKET=screenshots

# Storage bucket for shared scripts
SHARED_SCRIPTS_BUCKET=shared-scripts

# Maximum file size for uploads (in MB)
MAX_UPLOAD_SIZE_MB=10

# ====================
# Database Configuration
# ====================
# Local database path (SQLite)
LOCAL_DB_PATH=./data/showforai.db

# Enable cloud sync (true/false)
ENABLE_CLOUD_SYNC=true

# Sync interval in seconds
SYNC_INTERVAL=300

# ====================
# Security Configuration
# ====================
# Enable API security features (true/false)
ENABLE_API_SECURITY=true

# Nonce expiration time in seconds
NONCE_EXPIRATION=300

# Request signature algorithm
SIGNATURE_ALGORITHM=SHA256

# ====================
# Performance Configuration
# ====================
# Enable caching (true/false)
ENABLE_CACHE=true

# Cache directory path
CACHE_DIR=./cache

# Cache TTL in seconds
CACHE_TTL=3600

# Maximum cache size in MB
MAX_CACHE_SIZE_MB=500

# ====================
# Recording Configuration
# ====================
# Default recording frame rate
RECORDING_FPS=30

# Screenshot quality (1-100)
SCREENSHOT_QUALITY=95

# Enable frame buffer (true/false)
ENABLE_FRAME_BUFFER=true

# Frame buffer size (number of frames)
FRAME_BUFFER_SIZE=15

# ====================
# Execution Configuration
# ====================
# Default execution mode (auxiliary/active)
DEFAULT_EXECUTION_MODE=auxiliary

# Smart wait timeout in seconds
SMART_WAIT_TIMEOUT=30

# Element matching threshold (0.0-1.0)
MATCHING_THRESHOLD=0.85

# Enable multi-level matching (true/false)
ENABLE_MULTI_LEVEL_MATCHING=true

# ====================
# UI Configuration
# ====================
# Default theme (light/dark)
DEFAULT_THEME=light

# Enable animations (true/false)
ENABLE_ANIMATIONS=true

# Window opacity (0.0-1.0)
WINDOW_OPACITY=0.95

# ====================
# Logging Configuration
# ====================
# Log level (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=./logs/showforai.log

# Enable console logging (true/false)
ENABLE_CONSOLE_LOG=true

# Log rotation size in MB
LOG_ROTATION_SIZE_MB=10

# Log retention days
LOG_RETENTION_DAYS=30

# ====================
# Network Configuration
# ====================
# Proxy settings (optional)
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# Network timeout in seconds
NETWORK_TIMEOUT=30

# ====================
# Development Configuration
# ====================
# Debug mode (true/false)
DEBUG_MODE=false

# Development server URL (for testing)
DEV_SERVER_URL=

# Enable hot reload (true/false)
ENABLE_HOT_RELOAD=false

# ====================
# Feature Flags
# ====================
# Enable experimental features (true/false)
ENABLE_EXPERIMENTAL=false

# Enable beta features (true/false)
ENABLE_BETA_FEATURES=false

# Enable telemetry (true/false)
ENABLE_TELEMETRY=false

# ====================
# Thresholds Configuration
# ====================
# Template matching threshold
TEMPLATE_MATCHING_THRESHOLD=0.90

# ORB minimum points
ORB_MIN_POINTS=15

# SIFT minimum points
SIFT_MIN_POINTS=20

# Minimum confidence threshold
MIN_CONFIDENCE_THRESHOLD=0.80