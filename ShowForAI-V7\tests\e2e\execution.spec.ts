import { test, expect } from '@playwright/test';

test.describe('Execution Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should navigate to execution page', async ({ page }) => {
    await page.click('[data-testid="execution-nav"]');
    await expect(page).toHaveURL(/.*execution/);
    await expect(page.locator('h1')).toContainText('Execution');
  });

  test('should show workflow selection', async ({ page }) => {
    await page.goto('/execution');
    
    const workflowSelect = page.locator('[data-testid="workflow-select"]');
    await expect(workflowSelect).toBeVisible();
    await expect(workflowSelect).toContainText('Select Workflow');
  });

  test('should load and display workflows', async ({ page }) => {
    // Mock workflows API
    await page.route('**/api/workflows', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          { id: '1', name: 'Test Workflow 1', description: 'First test workflow' },
          { id: '2', name: 'Test Workflow 2', description: 'Second test workflow' }
        ])
      });
    });

    await page.goto('/execution');
    
    const workflowSelect = page.locator('[data-testid="workflow-select"]');
    await workflowSelect.click();
    
    await expect(page.locator('.ant-select-item').filter({ hasText: 'Test Workflow 1' })).toBeVisible();
    await expect(page.locator('.ant-select-item').filter({ hasText: 'Test Workflow 2' })).toBeVisible();
  });

  test('should execute workflow', async ({ page }) => {
    // Mock workflows and execution APIs
    await page.route('**/api/workflows', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          { id: '1', name: 'Test Workflow', description: 'Test workflow' }
        ])
      });
    });

    await page.route('**/api/execution/start', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, executionId: 'exec_123' })
      });
    });

    await page.goto('/execution');
    
    // Select workflow
    const workflowSelect = page.locator('[data-testid="workflow-select"]');
    await workflowSelect.click();
    await page.locator('.ant-select-item').filter({ hasText: 'Test Workflow' }).click();
    
    // Execute workflow
    const executeButton = page.locator('[data-testid="execute-button"]');
    await expect(executeButton).toBeEnabled();
    await executeButton.click();
    
    // Should show execution progress
    const progressIndicator = page.locator('[data-testid="execution-progress"]');
    await expect(progressIndicator).toBeVisible();
  });

  test('should handle execution modes', async ({ page }) => {
    await page.goto('/execution');
    
    // Test active mode selection
    const activeModeRadio = page.locator('[data-testid="active-mode"]');
    await activeModeRadio.click();
    await expect(activeModeRadio).toBeChecked();
    
    // Test auxiliary mode selection
    const auxiliaryModeRadio = page.locator('[data-testid="auxiliary-mode"]');
    await auxiliaryModeRadio.click();
    await expect(auxiliaryModeRadio).toBeChecked();
    
    // Should show mode-specific options
    const auxiliaryOptions = page.locator('[data-testid="auxiliary-options"]');
    await expect(auxiliaryOptions).toBeVisible();
  });

  test('should cancel execution', async ({ page }) => {
    // Mock execution APIs
    await page.route('**/api/execution/start', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, executionId: 'exec_123' })
      });
    });

    await page.route('**/api/execution/cancel', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    await page.goto('/execution');
    
    // Start execution (mock)
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('start-execution', {
        detail: { executionId: 'exec_123' }
      }));
    });
    
    // Cancel execution
    const cancelButton = page.locator('[data-testid="cancel-button"]');
    await expect(cancelButton).toBeVisible();
    await cancelButton.click();
    
    // Should show confirmation dialog
    const confirmDialog = page.locator('.ant-modal');
    await expect(confirmDialog).toBeVisible();
    await expect(confirmDialog).toContainText('Are you sure');
    
    // Confirm cancellation
    const confirmButton = confirmDialog.locator('.ant-btn-primary');
    await confirmButton.click();
    
    await expect(confirmDialog).not.toBeVisible();
  });

  test('should show execution results', async ({ page }) => {
    // Mock execution status API
    await page.route('**/api/execution/status/*', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'completed',
          progress: 100,
          results: [
            { step: 1, action: 'click', status: 'success', timestamp: Date.now() },
            { step: 2, action: 'type', status: 'success', timestamp: Date.now() + 1000 }
          ]
        })
      });
    });

    await page.goto('/execution');
    
    // Simulate completed execution
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('execution-completed', {
        detail: { executionId: 'exec_123' }
      }));
    });
    
    // Should show results
    const resultsSection = page.locator('[data-testid="execution-results"]');
    await expect(resultsSection).toBeVisible();
    
    const resultItems = resultsSection.locator('[data-testid="result-item"]');
    await expect(resultItems).toHaveCount(2);
  });

  test('should handle execution errors', async ({ page }) => {
    // Mock error response
    await page.route('**/api/execution/start', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Execution failed' })
      });
    });

    await page.goto('/execution');
    
    // Try to execute (will fail)
    const executeButton = page.locator('[data-testid="execute-button"]');
    await executeButton.click();
    
    // Should show error message
    await expect(page.locator('.ant-message-error')).toBeVisible();
    await expect(page.locator('.ant-message-error')).toContainText('Execution failed');
  });

  test('should show emergency stop button during execution', async ({ page }) => {
    await page.goto('/execution');
    
    // Simulate running execution
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('execution-started', {
        detail: { executionId: 'exec_123' }
      }));
    });
    
    const emergencyStopButton = page.locator('[data-testid="emergency-stop"]');
    await expect(emergencyStopButton).toBeVisible();
    await expect(emergencyStopButton).toHaveCSS('background-color', 'rgb(255, 77, 79)'); // Red color
  });
});