# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyInstanceGroupMembersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'append_members': 'list[AppendMemberForModifyInstanceGroupMembersInput]',
        'instance_group_id': 'str',
        'remove_members': 'list[RemoveMemberForModifyInstanceGroupMembersInput]'
    }

    attribute_map = {
        'append_members': 'AppendMembers',
        'instance_group_id': 'InstanceGroupId',
        'remove_members': 'RemoveMembers'
    }

    def __init__(self, append_members=None, instance_group_id=None, remove_members=None, _configuration=None):  # noqa: E501
        """ModifyInstanceGroupMembersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._append_members = None
        self._instance_group_id = None
        self._remove_members = None
        self.discriminator = None

        if append_members is not None:
            self.append_members = append_members
        self.instance_group_id = instance_group_id
        if remove_members is not None:
            self.remove_members = remove_members

    @property
    def append_members(self):
        """Gets the append_members of this ModifyInstanceGroupMembersRequest.  # noqa: E501


        :return: The append_members of this ModifyInstanceGroupMembersRequest.  # noqa: E501
        :rtype: list[AppendMemberForModifyInstanceGroupMembersInput]
        """
        return self._append_members

    @append_members.setter
    def append_members(self, append_members):
        """Sets the append_members of this ModifyInstanceGroupMembersRequest.


        :param append_members: The append_members of this ModifyInstanceGroupMembersRequest.  # noqa: E501
        :type: list[AppendMemberForModifyInstanceGroupMembersInput]
        """

        self._append_members = append_members

    @property
    def instance_group_id(self):
        """Gets the instance_group_id of this ModifyInstanceGroupMembersRequest.  # noqa: E501


        :return: The instance_group_id of this ModifyInstanceGroupMembersRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_group_id

    @instance_group_id.setter
    def instance_group_id(self, instance_group_id):
        """Sets the instance_group_id of this ModifyInstanceGroupMembersRequest.


        :param instance_group_id: The instance_group_id of this ModifyInstanceGroupMembersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_group_id is None:
            raise ValueError("Invalid value for `instance_group_id`, must not be `None`")  # noqa: E501

        self._instance_group_id = instance_group_id

    @property
    def remove_members(self):
        """Gets the remove_members of this ModifyInstanceGroupMembersRequest.  # noqa: E501


        :return: The remove_members of this ModifyInstanceGroupMembersRequest.  # noqa: E501
        :rtype: list[RemoveMemberForModifyInstanceGroupMembersInput]
        """
        return self._remove_members

    @remove_members.setter
    def remove_members(self, remove_members):
        """Sets the remove_members of this ModifyInstanceGroupMembersRequest.


        :param remove_members: The remove_members of this ModifyInstanceGroupMembersRequest.  # noqa: E501
        :type: list[RemoveMemberForModifyInstanceGroupMembersInput]
        """

        self._remove_members = remove_members

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyInstanceGroupMembersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyInstanceGroupMembersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyInstanceGroupMembersRequest):
            return True

        return self.to_dict() != other.to_dict()
