# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSecurityGroupsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'security_group_ids': 'list[str]',
        'security_group_names': 'list[str]',
        'tag_filters': 'list[TagFilterForDescribeSecurityGroupsInput]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'security_group_ids': 'SecurityGroupIds',
        'security_group_names': 'SecurityGroupNames',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcId'
    }

    def __init__(self, max_results=None, next_token=None, page_number=None, page_size=None, project_name=None, security_group_ids=None, security_group_names=None, tag_filters=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeSecurityGroupsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._security_group_ids = None
        self._security_group_names = None
        self._tag_filters = None
        self._vpc_id = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if security_group_names is not None:
            self.security_group_names = security_group_names
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def max_results(self):
        """Gets the max_results of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The max_results of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeSecurityGroupsRequest.


        :param max_results: The max_results of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                max_results is not None and max_results > 100):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                max_results is not None and max_results < 1):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value greater than or equal to `1`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The next_token of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeSecurityGroupsRequest.


        :param next_token: The next_token of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The page_number of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSecurityGroupsRequest.


        :param page_number: The page_number of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The page_size of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSecurityGroupsRequest.


        :param page_size: The page_size of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The project_name of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeSecurityGroupsRequest.


        :param project_name: The project_name of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The security_group_ids of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this DescribeSecurityGroupsRequest.


        :param security_group_ids: The security_group_ids of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def security_group_names(self):
        """Gets the security_group_names of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The security_group_names of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_names

    @security_group_names.setter
    def security_group_names(self, security_group_names):
        """Sets the security_group_names of this DescribeSecurityGroupsRequest.


        :param security_group_names: The security_group_names of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_names = security_group_names

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeSecurityGroupsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeSecurityGroupsRequest.


        :param tag_filters: The tag_filters of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeSecurityGroupsInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeSecurityGroupsRequest.  # noqa: E501


        :return: The vpc_id of this DescribeSecurityGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeSecurityGroupsRequest.


        :param vpc_id: The vpc_id of this DescribeSecurityGroupsRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSecurityGroupsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSecurityGroupsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSecurityGroupsRequest):
            return True

        return self.to_dict() != other.to_dict()
