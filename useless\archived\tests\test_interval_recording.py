"""
Test script to verify that operation intervals are properly recorded and preserved.

This test ensures:
1. All operation types record intervals
2. Intervals are saved to script data
3. Cloud sync preserves intervals
"""

import sys
import time
import json
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.recorder.action_buffer import ActionBuffer, ActionType
from showforai.script.script_model import <PERSON>rip<PERSON>
from showforai.processing.script_generator import ScriptGenerator


def test_interval_recording():
    """Test that intervals are properly recorded for all action types."""
    print("Testing interval recording...")
    
    # Create action buffer
    buffer = ActionBuffer()
    
    # Simulate recording with different intervals
    base_time = time.time()
    
    # Add first click (no delay)
    buffer.add_click(100, 100, base_time, "screenshot1.png")
    print(f"Added click at t=0")
    
    # Add second click after 500ms
    buffer.add_click(200, 200, base_time + 0.5, "screenshot2.png")
    print(f"Added click at t=500ms")
    
    # Add double click after 1000ms total
    buffer.add_double_click(300, 300, base_time + 1.0, "screenshot3.png")
    print(f"Added double_click at t=1000ms")
    
    # Add right click after 1500ms total
    buffer.add_right_click(400, 400, base_time + 1.5, "screenshot4.png")
    print(f"Added right_click at t=1500ms")
    
    # Add key press after 2000ms total
    buffer.add_key_press("Enter", base_time + 2.0)
    print(f"Added key_press at t=2000ms")
    
    # Add scroll after 2500ms total
    buffer.add_scroll(500, 500, 0, -100, base_time + 2.5)
    print(f"Added scroll at t=2500ms")
    
    # Add drag after 3000ms total
    buffer.add_drag(600, 600, 700, 700, base_time + 3.0, "screenshot5.png")
    print(f"Added drag at t=3000ms")
    
    # Add text input (will be buffered and flushed)
    buffer.add_key_press("H", base_time + 3.5)
    buffer.add_key_press("e", base_time + 3.6)
    buffer.add_key_press("l", base_time + 3.7)
    buffer.add_key_press("l", base_time + 3.8)
    buffer.add_key_press("o", base_time + 3.9)
    print(f"Added text 'Hello' at t=3500-3900ms")
    
    # Calculate delays
    buffer.calculate_delays()
    
    # Convert to dict
    actions = buffer.to_dict(use_milliseconds=True)
    
    # Verify intervals
    print("\n=== Interval Verification ===")
    for i, action in enumerate(actions):
        action_type = action.get('type', 'unknown')
        delay_ms = action.get('delay_ms', -1)
        print(f"Action {i}: {action_type:15} - delay_ms: {delay_ms:5}ms")
        
        # Verify expected delays
        if i == 0:
            assert delay_ms == 0, f"First action should have 0 delay, got {delay_ms}"
        elif i == 1:
            assert 490 <= delay_ms <= 510, f"Second action should have ~500ms delay, got {delay_ms}"
        elif i == 2:
            assert 490 <= delay_ms <= 510, f"Third action should have ~500ms delay, got {delay_ms}"
        elif i == 3:
            assert 490 <= delay_ms <= 510, f"Fourth action should have ~500ms delay, got {delay_ms}"
        elif i == 4:
            assert 490 <= delay_ms <= 510, f"Fifth action should have ~500ms delay, got {delay_ms}"
        elif i == 5:
            assert 490 <= delay_ms <= 510, f"Sixth action should have ~500ms delay, got {delay_ms}"
        elif i == 6:
            assert 490 <= delay_ms <= 510, f"Seventh action should have ~500ms delay, got {delay_ms}"
    
    print("\n✅ All interval recordings verified!")
    return actions


def test_script_generation_preserves_intervals():
    """Test that script generation preserves delay_ms."""
    print("\n=== Testing Script Generation ===")
    
    # Get recorded actions
    actions = test_interval_recording()
    
    # Create recording data
    recording_data = {
        'metadata': {
            'version': '1.0',
            'session_id': 'test-session',
            'recorded_at': time.time()
        },
        'actions': actions
    }
    
    # Generate script
    generator = ScriptGenerator()
    script = generator.generate_script(recording_data)
    
    # Verify intervals are preserved
    print("\n=== Script Generation Verification ===")
    for i, action in enumerate(script['actions']):
        action_type = action.get('type', 'unknown')
        delay_ms = action.get('delay_ms', -1)
        print(f"Script Action {i}: {action_type:15} - delay_ms: {delay_ms:5}ms")
        
        # Verify delay_ms exists
        assert 'delay_ms' in action, f"Action {i} missing delay_ms field"
        assert isinstance(action['delay_ms'], (int, float)), f"Action {i} delay_ms is not numeric"
    
    print("\n✅ Script generation preserves intervals!")
    return script


def test_script_model_preserves_intervals():
    """Test that Script model preserves intervals during serialization."""
    print("\n=== Testing Script Model Serialization ===")
    
    # Create a script with actions containing delays
    script = Script(
        name="Test Script",
        description="Test interval preservation",
        actions=[
            {'type': 'click', 'delay_ms': 0, 'position': {'x': 100, 'y': 100}},
            {'type': 'click', 'delay_ms': 500, 'position': {'x': 200, 'y': 200}},
            {'type': 'double_click', 'delay_ms': 1000, 'position': {'x': 300, 'y': 300}},
        ]
    )
    
    # Convert to dict
    script_dict = script.to_dict()
    
    # Verify actions have delay_ms
    for i, action in enumerate(script_dict['actions']):
        assert 'delay_ms' in action, f"Serialized action {i} missing delay_ms"
        print(f"Serialized Action {i}: delay_ms = {action['delay_ms']}ms")
    
    # Convert to JSON and back
    script_json = script.to_json()
    restored_script = Script.from_json(script_json)
    
    # Verify restored script has delays
    for i, action in enumerate(restored_script.actions):
        assert 'delay_ms' in action, f"Restored action {i} missing delay_ms"
        print(f"Restored Action {i}: delay_ms = {action['delay_ms']}ms")
    
    print("\n✅ Script model preserves intervals through serialization!")


def main():
    """Run all interval recording tests."""
    print("=" * 60)
    print("INTERVAL RECORDING TEST SUITE")
    print("=" * 60)
    
    try:
        # Test 1: Basic interval recording
        test_interval_recording()
        
        # Test 2: Script generation
        test_script_generation_preserves_intervals()
        
        # Test 3: Script model serialization
        test_script_model_preserves_intervals()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("=" * 60)
        print("\nSummary:")
        print("- All operation types record intervals correctly")
        print("- Intervals are preserved during script generation")
        print("- Intervals are preserved during serialization")
        print("- Cloud sync will preserve intervals (uses same serialization)")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()