# ShowForAI-V3 代码清理计划

## 一、当前代码结构分析

### 1.1 GUI文件重复问题

当前存在3个GUI相关文件，功能重复且混乱：

1. **src/showforai/recorder/gui.py** (约600行)
   - 专门为录制器设计的GUI
   - 包含RecorderWindow类
   - 有网络检查逻辑
   - 有上传功能

2. **src/showforai/gui/main_window.py** (约400行)
   - 通用的主窗口
   - 包含MainWindow类
   - 有标签页设计（Dashboard、Script Editor、Execution）
   - 有网络检查逻辑

3. **src/showforai/gui/enhanced_main_window.py** (约500行)
   - 增强版主窗口
   - 包含EnhancedMainWindow类
   - 有自动登录功能
   - 有网络状态管理
   - 有增强的状态栏

### 1.2 启动入口混乱

存在多个启动入口，逻辑不清晰：

1. **src/showforai/cli.py** - 主CLI入口，有gui命令
2. **src/showforai/launcher.py** - 独立启动器，分别启动recorder或executor
3. **src/showforai/gui_launcher.py** - GUI启动器
4. **src/showforai/recorder/cli.py** - 录制器CLI，有gui命令
5. **src/showforai/executor/cli.py** - 执行器CLI，有gui命令

### 1.3 网络检查逻辑分散

网络检查和离线模式处理分散在多个文件中：
- utils/network_checker.py
- sync/offline_manager.py
- 各个GUI文件中都有重复的网络检查代码

### 1.4 录制器重复

存在两个录制器实现：
- recorder/recorder.py - 基础录制器
- recorder/optimized_recorder.py - 优化版录制器

## 二、清理方案

### 2.1 核心架构设计

根据产品原则，应该采用**最简洁的架构**：

```
ShowForAI-V3/
├── src/showforai/
│   ├── core/           # 核心功能
│   │   ├── recorder.py      # 统一的录制器
│   │   ├── executor.py      # 统一的执行器
│   │   ├── config.py        # 配置管理
│   │   └── network.py       # 网络状态管理
│   │
│   ├── processing/      # 处理逻辑
│   │   ├── standardizer.py  # 768x768标准化
│   │   ├── bbox_processor.py # BBOX处理
│   │   └── script_generator.py # 脚本生成
│   │
│   ├── ai/             # AI服务
│   │   ├── ai_service.py    # AI识别服务
│   │   └── upload_manager.py # 上传管理
│   │
│   ├── gui/            # 统一的GUI
│   │   ├── main_window.py   # 主窗口（整合所有功能）
│   │   ├── recorder_panel.py # 录制面板
│   │   ├── executor_panel.py # 执行面板
│   │   └── components/      # 通用组件
│   │
│   ├── cli.py          # 统一的CLI入口
│   └── __main__.py     # 程序入口
```

### 2.2 详细清理步骤

#### 第一步：备份现有代码
创建 `archived/` 文件夹，将要删除的文件移动到这里，不直接删除。

#### 第二步：整合GUI文件

**保留文件：src/showforai/gui/enhanced_main_window.py**
- 作为主干，因为它有最完整的功能（自动登录、网络状态管理）
- 重命名为 main_window.py

**合并内容：**
- 从 recorder/gui.py 提取录制相关的组件
- 从原 main_window.py 提取标签页设计
- 统一网络检查逻辑

**删除文件：**
- src/showforai/gui/main_window.py (原版本)
- src/showforai/recorder/gui.py
- src/showforai/executor/gui.py

#### 第三步：统一录制器实现

**保留文件：src/showforai/recorder/optimized_recorder.py**
- 重命名为 recorder.py
- 移动到 core/ 目录

**删除文件：**
- src/showforai/recorder/recorder.py (旧版本)

#### 第四步：简化启动流程

**保留方式：**
```python
# 统一入口
python -m showforai          # 启动GUI
python -m showforai recorder # 启动录制器模式
python -m showforai executor # 启动执行器模式
```

**整合文件：**
- 将 launcher.py 的逻辑整合到 cli.py
- 删除 gui_launcher.py
- 简化 recorder/cli.py 和 executor/cli.py

#### 第五步：统一网络管理

**创建文件：src/showforai/core/network.py**
```python
# 统一的网络状态管理
class NetworkManager:
    def __init__(self):
        self.is_online = False
        self.callbacks = []
    
    def check_network(self):
        # 统一的网络检查逻辑
        pass
    
    def require_online(self, func):
        # 装饰器：需要在线才能执行的功能
        pass
```

**删除重复：**
- 各个文件中的网络检查代码
- 统一使用 NetworkManager

### 2.3 功能简化

根据产品原则，删除不必要的功能：

#### 删除功能：
1. 设备心跳监控
2. 多策略元素定位（只保留图像识别）
3. 2FA认证
4. 撤销系统
5. 过多的键盘快捷键（只保留F9）
6. AI调用缓存（执行时不调用AI）

#### 保留核心功能：
1. **录制功能**（必须在线）
   - 10FPS截屏缓存
   - 点击前第3帧获取
   - 记录操作间隔

2. **上传处理**（必须在线）
   - 768x768标准化
   - AI识别返回BBOX
   - 本地裁切生成模板

3. **执行功能**（可离线）
   - 智能等待（基于录制间隔）
   - 多级图像匹配降级
   - 持续识别直到成功

4. **离线模式**
   - 录制按钮禁用（灰色）
   - 执行功能正常
   - 清晰的状态提示

### 2.4 代码组织优化

#### 模块职责清晰化：

1. **core/** - 核心业务逻辑
   - recorder.py: 录制逻辑
   - executor.py: 执行逻辑
   - config.py: 配置管理
   - network.py: 网络管理

2. **processing/** - 数据处理
   - standardizer.py: 768标准化
   - bbox_processor.py: BBOX裁切
   - script_generator.py: 脚本生成

3. **ai/** - AI服务接口
   - ai_service.py: AI识别服务
   - upload_manager.py: 上传管理

4. **gui/** - 用户界面
   - main_window.py: 主窗口
   - recorder_panel.py: 录制面板
   - executor_panel.py: 执行面板

5. **storage/** - 数据存储
   - local_storage.py: 本地存储
   - cloud_sync.py: 云同步

## 三、执行计划

### 阶段1：准备工作（Day 1）
- [ ] 创建 archived/ 文件夹
- [ ] 备份当前代码
- [ ] 创建新的目录结构

### 阶段2：核心模块重构（Day 2-3）
- [ ] 整合录制器代码
- [ ] 整合执行器代码
- [ ] 创建统一的网络管理器
- [ ] 简化配置管理

### 阶段3：GUI整合（Day 4-5）
- [ ] 合并三个GUI文件
- [ ] 创建统一的主窗口
- [ ] 分离录制和执行面板
- [ ] 统一样式和主题

### 阶段4：入口简化（Day 6）
- [ ] 整合CLI入口
- [ ] 简化启动流程
- [ ] 更新批处理文件

### 阶段5：测试验证（Day 7）
- [ ] 功能测试
- [ ] 离线模式测试
- [ ] 性能测试

## 四、注意事项

1. **保持向后兼容**
   - 已有的脚本文件格式不变
   - API接口保持稳定

2. **渐进式重构**
   - 每次只改一个模块
   - 确保测试通过再继续

3. **文档同步**
   - 更新使用说明
   - 更新API文档

4. **版本控制**
   - 每个阶段创建标签
   - 可以回滚到任何阶段

## 五、预期效果

### 5.1 代码量减少
- 删除重复代码约 40%
- GUI代码从3个文件减少到1个主文件
- 启动入口从5个减少到1个

### 5.2 结构清晰
- 模块职责单一
- 依赖关系明确
- 易于维护和扩展

### 5.3 用户体验提升
- 启动更快
- 界面统一
- 操作流程简化

### 5.4 符合产品原则
- 图像识别为核心
- 智能等待机制
- 录制在线/执行离线
- 简洁的架构

## 六、风险评估

1. **功能遗漏风险**
   - 缓解：详细的功能清单对比
   - 备份原代码可随时参考

2. **兼容性风险**
   - 缓解：保持数据格式不变
   - 充分的测试覆盖

3. **性能风险**
   - 缓解：性能测试对比
   - 优化关键路径

## 七、时间估算

总计：7个工作日
- 准备工作：1天
- 核心重构：2天
- GUI整合：2天
- 入口简化：1天
- 测试验证：1天

## 八、成功标准

1. 代码量减少30%以上
2. 启动时间缩短50%
3. 所有核心功能正常工作
4. 离线模式符合产品原则
5. 用户反馈积极