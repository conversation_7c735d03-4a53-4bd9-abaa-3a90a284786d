# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribePrefixListAssociationsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'prefix_list_id': 'str',
        'resource_type': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'prefix_list_id': 'PrefixListId',
        'resource_type': 'ResourceType'
    }

    def __init__(self, max_results=None, next_token=None, page_number=None, page_size=None, prefix_list_id=None, resource_type=None, _configuration=None):  # noqa: E501
        """DescribePrefixListAssociationsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._page_number = None
        self._page_size = None
        self._prefix_list_id = None
        self._resource_type = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        self.prefix_list_id = prefix_list_id
        if resource_type is not None:
            self.resource_type = resource_type

    @property
    def max_results(self):
        """Gets the max_results of this DescribePrefixListAssociationsRequest.  # noqa: E501


        :return: The max_results of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribePrefixListAssociationsRequest.


        :param max_results: The max_results of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                max_results is not None and max_results > 100):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                max_results is not None and max_results < 1):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value greater than or equal to `1`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribePrefixListAssociationsRequest.  # noqa: E501


        :return: The next_token of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribePrefixListAssociationsRequest.


        :param next_token: The next_token of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def page_number(self):
        """Gets the page_number of this DescribePrefixListAssociationsRequest.  # noqa: E501


        :return: The page_number of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribePrefixListAssociationsRequest.


        :param page_number: The page_number of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribePrefixListAssociationsRequest.  # noqa: E501


        :return: The page_size of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribePrefixListAssociationsRequest.


        :param page_size: The page_size of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501

        self._page_size = page_size

    @property
    def prefix_list_id(self):
        """Gets the prefix_list_id of this DescribePrefixListAssociationsRequest.  # noqa: E501


        :return: The prefix_list_id of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._prefix_list_id

    @prefix_list_id.setter
    def prefix_list_id(self, prefix_list_id):
        """Sets the prefix_list_id of this DescribePrefixListAssociationsRequest.


        :param prefix_list_id: The prefix_list_id of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and prefix_list_id is None:
            raise ValueError("Invalid value for `prefix_list_id`, must not be `None`")  # noqa: E501

        self._prefix_list_id = prefix_list_id

    @property
    def resource_type(self):
        """Gets the resource_type of this DescribePrefixListAssociationsRequest.  # noqa: E501


        :return: The resource_type of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this DescribePrefixListAssociationsRequest.


        :param resource_type: The resource_type of this DescribePrefixListAssociationsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["VpcRouteTable", "VpcSecurityGroup"]  # noqa: E501
        if (self._configuration.client_side_validation and
                resource_type not in allowed_values):
            raise ValueError(
                "Invalid value for `resource_type` ({0}), must be one of {1}"  # noqa: E501
                .format(resource_type, allowed_values)
            )

        self._resource_type = resource_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribePrefixListAssociationsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribePrefixListAssociationsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribePrefixListAssociationsRequest):
            return True

        return self.to_dict() != other.to_dict()
