"""
修复网络状态显示的临时脚本
"""

import sys
import os
import time
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    # 导入必要的模块
    from PyQt6.QtWidgets import QApplication
    from showforai.recorder.gui import RecorderWindow
    from showforai.config import Config
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建窗口
    config = Config()
    window = RecorderWindow(config)
    
    # 手动触发网络状态检查
    print("检查网络状态...")
    window.check_network_status()
    
    # 显示窗口
    window.show()
    
    # 定时检查网络状态（每5秒）
    from PyQt6.QtCore import QTimer
    timer = QTimer()
    timer.timeout.connect(window.check_network_status)
    timer.start(5000)  # 每5秒检查一次
    
    print("录制器窗口已启动，每5秒自动检查网络状态")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()