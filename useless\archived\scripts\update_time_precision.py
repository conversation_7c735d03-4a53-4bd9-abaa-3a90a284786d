"""
<PERSON><PERSON><PERSON> to update all time.time() calls to use high-precision time utilities
"""

import os
import re
from pathlib import Path

def update_file(file_path: Path) -> bool:
    """Update a single file to use time_utils"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    modified = False
    
    # Skip time_utils.py itself
    if file_path.name == 'time_utils.py':
        return False
    
    # Check if file uses time.time() for actual timing (not timestamps)
    if 'time.time()' in content:
        # Add import if not present
        if 'from showforai.utils.time_utils import' not in content:
            # Find the right place to add import
            import_lines = []
            lines = content.split('\n')
            
            # Find where imports are
            import_section_end = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    import_section_end = i + 1
                elif import_section_end > 0 and line and not line.startswith('#'):
                    break
            
            # Add our import after other imports
            if import_section_end > 0:
                lines.insert(import_section_end, 'from showforai.utils.time_utils import get_timestamp, get_unix_timestamp, calculate_interval')
                content = '\n'.join(lines)
                modified = True
        
        # Identify different use cases and replace appropriately
        
        # Pattern 1: Timing measurements (start_time = time.time())
        timing_pattern = r'(\w+)\s*=\s*time\.time\(\)'
        timing_matches = re.findall(timing_pattern, content)
        
        # Pattern 2: Interval calculations (time.time() - start_time)
        interval_pattern = r'time\.time\(\)\s*-\s*(\w+)'
        
        # Pattern 3: Timestamp for files/logging (needs Unix timestamp)
        timestamp_pattern = r'timestamp.*=\s*time\.time\(\)|datetime.*time\.time\(\)|str\(.*time\.time\(\)'
        
        # For timing measurements, use get_timestamp()
        for var_name in timing_matches:
            # Check if it's used for intervals
            if f'time.time() - {var_name}' in content or f'- {var_name}' in content:
                # This is for timing, use get_timestamp
                content = re.sub(f'{var_name}\\s*=\\s*time\\.time\\(\\)', f'{var_name} = get_timestamp()', content)
                content = re.sub(f'time\\.time\\(\\)\\s*-\\s*{var_name}', f'get_timestamp() - {var_name}', content)
                modified = True
            elif 'timestamp' in var_name.lower() or 'ts' in var_name.lower():
                # This looks like a Unix timestamp
                content = re.sub(f'{var_name}\\s*=\\s*time\\.time\\(\\)', f'{var_name} = get_unix_timestamp()', content)
                modified = True
        
        # Replace standalone time.time() used in calculations
        if 'time.time() -' in content:
            content = re.sub(r'time\.time\(\)\s*-\s*', 'get_timestamp() - ', content)
            modified = True
        
        # Replace time.time() used for timestamps in strings/logs
        if 'int(time.time())' in content:
            content = content.replace('int(time.time())', 'int(get_unix_timestamp())')
            modified = True
        
        if 'str(time.time())' in content:
            content = content.replace('str(time.time())', 'str(get_unix_timestamp())')
            modified = True
        
        # Special cases for specific patterns found in codebase
        
        # Case: API security timestamp validation
        if 'current_time = int(time.time())' in content:
            content = content.replace('current_time = int(time.time())', 'current_time = int(get_unix_timestamp())')
            modified = True
        
        # Case: General timestamp generation
        if 'timestamp = int(time.time())' in content:
            content = content.replace('timestamp = int(time.time())', 'timestamp = int(get_unix_timestamp())')
            modified = True
        
        # Case: Now for elapsed time
        if 'now = time.time()' in content:
            # Check context - if it's for elapsed time calculation
            if 'elapsed' in content or 'duration' in content or '- now' in content:
                content = content.replace('now = time.time()', 'now = get_timestamp()')
                modified = True
            else:
                content = content.replace('now = time.time()', 'now = get_unix_timestamp()')
                modified = True
    
    # Write back if modified
    if modified and content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    
    return False

def main():
    """Update all Python files in the src directory"""
    
    src_dir = Path(r'C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V3\src')
    
    updated_files = []
    skipped_files = []
    
    # Files to update
    target_files = [
        'showforai/recorder/recorder.py',
        'showforai/executor/executor.py',
        'showforai/executor/action_executor.py',
        'showforai/executor/element_matcher.py',
        'showforai/smart_wait.py',
        'showforai/buffer_manager.py',
        'showforai/execution_engine.py',
        'showforai/recorder/mouse_listener.py',
        'showforai/recorder/screen_capture.py',
        'showforai/ai/ai_service.py',
        'showforai/optimizer/resource_monitor.py',
        'showforai/optimizer/cache_manager.py',
        'showforai/robustness/retry_handler.py',
        'showforai/robustness/timeout_manager.py'
    ]
    
    for rel_path in target_files:
        file_path = src_dir / rel_path
        if file_path.exists():
            if update_file(file_path):
                updated_files.append(rel_path)
                print(f"✓ Updated: {rel_path}")
            else:
                skipped_files.append(rel_path)
                print(f"- Skipped: {rel_path} (no changes needed)")
        else:
            print(f"✗ Not found: {rel_path}")
    
    print(f"\n=== Summary ===")
    print(f"Updated: {len(updated_files)} files")
    print(f"Skipped: {len(skipped_files)} files")
    
    if updated_files:
        print("\nUpdated files:")
        for f in updated_files:
            print(f"  - {f}")

if __name__ == '__main__':
    main()