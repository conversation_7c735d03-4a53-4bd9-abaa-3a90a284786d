#!/usr/bin/env python3
"""
Launcher for ShowForAI V3 - Starts recorder or executor independently.

This launcher provides the ability to start either the recorder or executor
as separate, independent applications with their own authentication and configuration.
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

# Import logger first
from showforai.logger import setup_logging as setup_logger
from loguru import logger


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description="ShowForAI V3 Launcher - Start recorder or executor independently"
    )
    
    # Create mutually exclusive group for mode selection
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--recorder',
        action='store_true',
        help='Launch the recorder application'
    )
    mode_group.add_argument(
        '--executor', 
        action='store_true',
        help='Launch the executor application'
    )
    
    # Additional arguments
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with verbose logging'
    )
    parser.add_argument(
        '--config',
        type=str,
        help='Path to custom configuration file'
    )
    parser.add_argument(
        '--no-auth',
        action='store_true',
        help='Skip authentication check (development only)'
    )
    
    args = parser.parse_args()
    
    # Default to recorder if no mode specified
    if not args.recorder and not args.executor:
        args.recorder = True
        args.executor = False
    
    # Validate that both modes aren't selected (shouldn't happen with mutex group)
    if args.recorder and args.executor:
        parser.error("Cannot specify both --recorder and --executor")
        sys.exit(1)
    
    return args


def check_authentication() -> bool:
    """
    Check if user is authenticated.
    
    Returns:
        True if authenticated, False otherwise
    """
    try:
        from showforai.auth.supabase_auth import SupabaseAuth
        from showforai.config import Config
        
        config = Config()
        auth = SupabaseAuth(config)
        
        # Check if there's a valid session
        session = auth.get_session()
        if session and session.get('access_token'):
            logger.info("User authenticated successfully")
            return True
        
        logger.warning("No valid authentication session found")
        return False
        
    except Exception as e:
        logger.error(f"Authentication check failed: {e}")
        return False


def show_login_dialog() -> bool:
    """
    Show login dialog for user authentication.
    
    Returns:
        True if login successful, False otherwise
    """
    try:
        from PyQt6.QtWidgets import QApplication, QDialog, QMessageBox
        from showforai.gui.auth_dialog import AuthDialog  # We'll create this
        
        # Create temporary QApplication if not exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Show auth dialog
        dialog = AuthDialog()
        result = dialog.exec()
        
        if result == QDialog.DialogCode.Accepted:
            logger.info("User logged in successfully")
            return True
        else:
            logger.info("User cancelled login")
            return False
            
    except ImportError:
        logger.error("PyQt6 not installed, cannot show login dialog")
        return False
    except Exception as e:
        logger.error(f"Failed to show login dialog: {e}")
        return False


def launch_recorder():
    """Launch the recorder application."""
    try:
        # Import PyQt6 components
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # Import recorder window
        from showforai.recorder.gui import RecorderWindow
        from showforai.config import Config
        
        logger.info("Starting ShowForAI Recorder...")
        
        # Create Qt application
        app = QApplication(sys.argv)
        app.setApplicationName("ShowForAI Recorder")
        app.setOrganizationName("ShowForAI")
        app.setStyle('Fusion')
        
        # Enable high DPI scaling
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
        
        # Load configuration
        config = Config()
        
        # Validate configuration
        if not config.validate_all():
            logger.error("Configuration validation failed")
            sys.exit(1)
        
        # Create and show recorder window
        window = RecorderWindow(config=config)
        window.show()
        
        logger.info("Recorder window created and shown")
        
        # Run application event loop
        sys.exit(app.exec())
        
    except ImportError as e:
        logger.error(f"Missing required dependency: {e}")
        print(f"Error: Missing required dependency: {e}")
        print("\nPlease install PyQt6:")
        print("  pip install PyQt6")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to launch recorder: {e}", exc_info=True)
        print(f"Error starting recorder: {e}")
        sys.exit(1)


def launch_executor():
    """Launch the executor application."""
    try:
        # Import PyQt6 components
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # Import executor window
        from showforai.executor import ExecutorWindow
        from showforai.config import Config
        
        logger.info("Starting ShowForAI Executor...")
        
        # Create Qt application
        app = QApplication(sys.argv)
        app.setApplicationName("ShowForAI Executor")
        app.setOrganizationName("ShowForAI")
        app.setStyle('Fusion')
        
        # Enable high DPI scaling
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
        
        # Load configuration
        config = Config()
        
        # Validate configuration
        if not config.validate_all():
            logger.error("Configuration validation failed")
            sys.exit(1)
        
        # Create and show executor window
        window = ExecutorWindow(config=config)
        window.show()
        
        logger.info("Executor window created and shown")
        
        # Run application event loop
        sys.exit(app.exec())
        
    except ImportError as e:
        logger.error(f"Missing required dependency: {e}")
        print(f"Error: Missing required dependency: {e}")
        print("\nPlease install PyQt6:")
        print("  pip install PyQt6")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to launch executor: {e}", exc_info=True)
        print(f"Error starting executor: {e}")
        sys.exit(1)


def main():
    """Main entry point for the launcher."""
    # Parse command line arguments
    args = parse_arguments()
    
    # Setup logging
    log_level = "DEBUG" if args.debug else "INFO"
    setup_logger(level=log_level)
    
    logger.info(f"ShowForAI V3 Launcher starting...")
    logger.info(f"Mode: {'Recorder' if args.recorder else 'Executor'}")
    
    # Load launcher configuration
    from showforai.launcher_config import get_launcher_config, StartupValidator
    launcher_config = get_launcher_config()
    
    # Validate startup conditions
    if not args.no_auth:  # Skip validation in no-auth mode
        logger.info("Validating startup conditions...")
        if not StartupValidator.validate_all():
            logger.error("Startup validation failed")
            print("Error: Startup validation failed. Check the logs for details.")
            sys.exit(1)
    
    # Check authentication unless disabled
    if not args.no_auth and launcher_config.require_auth:
        logger.info("Checking authentication...")
        if not check_authentication():
            logger.warning("User not authenticated, showing login dialog...")
            if not show_login_dialog():
                logger.error("Authentication required to proceed")
                print("Error: Authentication required. Please login to continue.")
                sys.exit(1)
    else:
        if args.no_auth:
            logger.warning("Authentication check skipped (--no-auth flag)")
        else:
            logger.info("Authentication not required by configuration")
    
    # Launch appropriate application
    if args.recorder:
        launch_recorder()
    else:
        launch_executor()


if __name__ == "__main__":
    main()