package com.showforai.executor.utils

import android.content.Context
import android.net.Uri
import com.showforai.executor.data.models.DSLScript
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 文件管理器
 * 
 * 负责文件操作：
 * - DSL脚本文件的读取和解析
 * - 资源文件（图片）的管理
 * - 执行日志的保存
 * - 缓存管理
 */
@Singleton
class FileManager @Inject constructor(
    private val context: Context,
    private val moshi: Moshi
) {
    
    companion object {
        private const val SCRIPTS_DIR = "scripts"
        private const val ASSETS_DIR = "assets"
        private const val LOGS_DIR = "logs"
        private const val CACHE_DIR = "cache"
    }
    
    private val dslScriptAdapter = moshi.adapter(DSLScript::class.java)
    
    /**
     * 从URI加载DSL脚本
     */
    suspend fun loadScriptFromUri(uri: Uri): DSLScript? {
        return withContext(Dispatchers.IO) {
            try {
                context.contentResolver.openInputStream(uri)?.use { inputStream ->
                    val jsonString = inputStream.bufferedReader().use { it.readText() }
                    parseScript(jsonString)
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to load script from URI: $uri")
                null
            }
        }
    }
    
    /**
     * 从文件路径加载DSL脚本
     */
    suspend fun loadScriptFromFile(filePath: String): DSLScript? {
        return withContext(Dispatchers.IO) {
            try {
                val file = File(filePath)
                if (!file.exists()) {
                    Timber.w("Script file does not exist: $filePath")
                    return@withContext null
                }
                
                val jsonString = file.readText()
                parseScript(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "Failed to load script from file: $filePath")
                null
            }
        }
    }
    
    /**
     * 解析DSL脚本JSON
     */
    private fun parseScript(jsonString: String): DSLScript? {
        return try {
            dslScriptAdapter.fromJson(jsonString)
        } catch (e: Exception) {
            Timber.e(e, "Failed to parse DSL script JSON")
            null
        }
    }
    
    /**
     * 保存DSL脚本到本地
     */
    suspend fun saveScript(script: DSLScript, fileName: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val scriptsDir = getScriptsDirectory()
                val file = File(scriptsDir, fileName)
                
                val jsonString = dslScriptAdapter.toJson(script)
                file.writeText(jsonString)
                
                Timber.i("Script saved to: ${file.absolutePath}")
                true
            } catch (e: Exception) {
                Timber.e(e, "Failed to save script: $fileName")
                false
            }
        }
    }
    
    /**
     * 获取本地脚本列表
     */
    suspend fun getLocalScripts(): List<File> {
        return withContext(Dispatchers.IO) {
            try {
                val scriptsDir = getScriptsDirectory()
                scriptsDir.listFiles { file ->
                    file.isFile && file.extension.lowercase() == "json"
                }?.toList() ?: emptyList()
            } catch (e: Exception) {
                Timber.e(e, "Failed to get local scripts")
                emptyList()
            }
        }
    }
    
    /**
     * 删除本地脚本
     */
    suspend fun deleteScript(fileName: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val scriptsDir = getScriptsDirectory()
                val file = File(scriptsDir, fileName)
                
                if (file.exists()) {
                    val deleted = file.delete()
                    if (deleted) {
                        Timber.i("Script deleted: $fileName")
                    }
                    deleted
                } else {
                    Timber.w("Script file does not exist: $fileName")
                    false
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete script: $fileName")
                false
            }
        }
    }
    
    /**
     * 加载资源文件（图片）
     */
    suspend fun loadAsset(assetFileName: String): ByteArray? {
        return withContext(Dispatchers.IO) {
            try {
                val assetsDir = getAssetsDirectory()
                val file = File(assetsDir, assetFileName)
                
                if (file.exists()) {
                    file.readBytes()
                } else {
                    Timber.w("Asset file does not exist: $assetFileName")
                    null
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to load asset: $assetFileName")
                null
            }
        }
    }
    
    /**
     * 保存资源文件
     */
    suspend fun saveAsset(assetFileName: String, data: ByteArray): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val assetsDir = getAssetsDirectory()
                val file = File(assetsDir, assetFileName)
                
                file.writeBytes(data)
                Timber.d("Asset saved: $assetFileName")
                true
            } catch (e: Exception) {
                Timber.e(e, "Failed to save asset: $assetFileName")
                false
            }
        }
    }
    
    /**
     * 检查资源文件是否存在
     */
    fun assetExists(assetFileName: String): Boolean {
        val assetsDir = getAssetsDirectory()
        val file = File(assetsDir, assetFileName)
        return file.exists()
    }
    
    /**
     * 保存执行日志
     */
    suspend fun saveExecutionLog(logFileName: String, logContent: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val logsDir = getLogsDirectory()
                val file = File(logsDir, logFileName)
                
                file.writeText(logContent)
                Timber.d("Execution log saved: $logFileName")
                true
            } catch (e: Exception) {
                Timber.e(e, "Failed to save execution log: $logFileName")
                false
            }
        }
    }
    
    /**
     * 获取执行日志列表
     */
    suspend fun getExecutionLogs(): List<File> {
        return withContext(Dispatchers.IO) {
            try {
                val logsDir = getLogsDirectory()
                logsDir.listFiles { file ->
                    file.isFile && file.extension.lowercase() == "log"
                }?.toList() ?: emptyList()
            } catch (e: Exception) {
                Timber.e(e, "Failed to get execution logs")
                emptyList()
            }
        }
    }
    
    /**
     * 清理缓存文件
     */
    suspend fun clearCache(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val cacheDir = getCacheDirectory()
                val deleted = cacheDir.deleteRecursively()
                if (deleted) {
                    cacheDir.mkdirs() // 重新创建目录
                    Timber.i("Cache cleared successfully")
                }
                deleted
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear cache")
                false
            }
        }
    }
    
    /**
     * 获取缓存大小
     */
    suspend fun getCacheSize(): Long {
        return withContext(Dispatchers.IO) {
            try {
                val cacheDir = getCacheDirectory()
                calculateDirectorySize(cacheDir)
            } catch (e: Exception) {
                Timber.e(e, "Failed to calculate cache size")
                0L
            }
        }
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        if (directory.exists()) {
            directory.walkTopDown().forEach { file ->
                if (file.isFile) {
                    size += file.length()
                }
            }
        }
        return size
    }
    
    /**
     * 获取脚本目录
     */
    private fun getScriptsDirectory(): File {
        val dir = File(context.getExternalFilesDir(null), SCRIPTS_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
    
    /**
     * 获取资源目录
     */
    private fun getAssetsDirectory(): File {
        val dir = File(context.getExternalFilesDir(null), ASSETS_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
    
    /**
     * 获取日志目录
     */
    private fun getLogsDirectory(): File {
        val dir = File(context.getExternalFilesDir(null), LOGS_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
    
    /**
     * 获取缓存目录
     */
    private fun getCacheDirectory(): File {
        val dir = File(context.cacheDir, CACHE_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.2f %s", size, units[unitIndex])
    }
}
