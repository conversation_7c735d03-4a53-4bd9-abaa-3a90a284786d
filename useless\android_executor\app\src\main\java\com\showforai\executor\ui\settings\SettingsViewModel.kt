package com.showforai.executor.ui.settings

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.showforai.executor.BuildConfig
import com.showforai.executor.utils.FileManager
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 设置ViewModel
 * 
 * 管理应用配置参数：
 * - 执行参数配置
 * - 视觉匹配参数
 * - OCR参数
 * - 缓存管理
 * - 设置持久化
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val fileManager: FileManager
) : ViewModel() {
    
    companion object {
        private const val PREFS_NAME = "dsl_executor_settings"
        
        // 执行设置
        private const val KEY_EXECUTION_TIMEOUT = "execution_timeout"
        private const val KEY_RETRY_COUNT = "retry_count"
        private const val KEY_OPERATION_DELAY = "operation_delay"
        
        // 视觉匹配设置
        private const val KEY_VISUAL_MATCH_THRESHOLD = "visual_match_threshold"
        private const val KEY_USE_MULTI_SCALE_MATCHING = "use_multi_scale_matching"
        
        // OCR设置
        private const val KEY_OCR_CONFIDENCE_THRESHOLD = "ocr_confidence_threshold"
        private const val KEY_USE_EXACT_TEXT_MATCHING = "use_exact_text_matching"
        
        // 默认值
        private const val DEFAULT_EXECUTION_TIMEOUT = 30f
        private const val DEFAULT_RETRY_COUNT = 2
        private const val DEFAULT_OPERATION_DELAY = 500L
        private const val DEFAULT_VISUAL_MATCH_THRESHOLD = 0.8f
        private const val DEFAULT_USE_MULTI_SCALE_MATCHING = true
        private const val DEFAULT_OCR_CONFIDENCE_THRESHOLD = 0.7f
        private const val DEFAULT_USE_EXACT_TEXT_MATCHING = false
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // 执行设置
    private val _executionTimeout = MutableStateFlow(
        sharedPreferences.getFloat(KEY_EXECUTION_TIMEOUT, DEFAULT_EXECUTION_TIMEOUT)
    )
    val executionTimeout: StateFlow<Float> = _executionTimeout.asStateFlow()
    
    private val _retryCount = MutableStateFlow(
        sharedPreferences.getInt(KEY_RETRY_COUNT, DEFAULT_RETRY_COUNT)
    )
    val retryCount: StateFlow<Int> = _retryCount.asStateFlow()
    
    private val _operationDelay = MutableStateFlow(
        sharedPreferences.getLong(KEY_OPERATION_DELAY, DEFAULT_OPERATION_DELAY)
    )
    val operationDelay: StateFlow<Long> = _operationDelay.asStateFlow()
    
    // 视觉匹配设置
    private val _visualMatchThreshold = MutableStateFlow(
        sharedPreferences.getFloat(KEY_VISUAL_MATCH_THRESHOLD, DEFAULT_VISUAL_MATCH_THRESHOLD)
    )
    val visualMatchThreshold: StateFlow<Float> = _visualMatchThreshold.asStateFlow()
    
    private val _useMultiScaleMatching = MutableStateFlow(
        sharedPreferences.getBoolean(KEY_USE_MULTI_SCALE_MATCHING, DEFAULT_USE_MULTI_SCALE_MATCHING)
    )
    val useMultiScaleMatching: StateFlow<Boolean> = _useMultiScaleMatching.asStateFlow()
    
    // OCR设置
    private val _ocrConfidenceThreshold = MutableStateFlow(
        sharedPreferences.getFloat(KEY_OCR_CONFIDENCE_THRESHOLD, DEFAULT_OCR_CONFIDENCE_THRESHOLD)
    )
    val ocrConfidenceThreshold: StateFlow<Float> = _ocrConfidenceThreshold.asStateFlow()
    
    private val _useExactTextMatching = MutableStateFlow(
        sharedPreferences.getBoolean(KEY_USE_EXACT_TEXT_MATCHING, DEFAULT_USE_EXACT_TEXT_MATCHING)
    )
    val useExactTextMatching: StateFlow<Boolean> = _useExactTextMatching.asStateFlow()
    
    // 缓存大小
    private val _cacheSize = MutableStateFlow("计算中...")
    val cacheSize: StateFlow<String> = _cacheSize.asStateFlow()
    
    // UI状态
    val uiState: StateFlow<SettingsUiState> = combine(
        executionTimeout,
        retryCount,
        operationDelay,
        visualMatchThreshold,
        useMultiScaleMatching,
        ocrConfidenceThreshold,
        useExactTextMatching,
        cacheSize
    ) { timeout, retry, delay, visualThreshold, multiScale, ocrThreshold, exactText, cache ->
        SettingsUiState(
            executionTimeout = timeout,
            retryCount = retry,
            operationDelay = delay,
            visualMatchThreshold = visualThreshold,
            useMultiScaleMatching = multiScale,
            ocrConfidenceThreshold = ocrThreshold,
            useExactTextMatching = exactText,
            cacheSize = cache,
            appVersion = getAppVersion()
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = SettingsUiState()
    )
    
    init {
        // 计算缓存大小
        calculateCacheSize()
    }
    
    /**
     * 更新执行超时时间
     */
    fun updateExecutionTimeout(timeout: Float) {
        _executionTimeout.value = timeout
        saveFloatSetting(KEY_EXECUTION_TIMEOUT, timeout)
        Timber.d("Execution timeout updated: $timeout")
    }
    
    /**
     * 更新重试次数
     */
    fun updateRetryCount(count: Int) {
        _retryCount.value = count
        saveIntSetting(KEY_RETRY_COUNT, count)
        Timber.d("Retry count updated: $count")
    }
    
    /**
     * 更新操作延迟
     */
    fun updateOperationDelay(delay: Long) {
        _operationDelay.value = delay
        saveLongSetting(KEY_OPERATION_DELAY, delay)
        Timber.d("Operation delay updated: $delay")
    }
    
    /**
     * 更新视觉匹配阈值
     */
    fun updateVisualMatchThreshold(threshold: Float) {
        _visualMatchThreshold.value = threshold
        saveFloatSetting(KEY_VISUAL_MATCH_THRESHOLD, threshold)
        Timber.d("Visual match threshold updated: $threshold")
    }
    
    /**
     * 更新多尺度匹配设置
     */
    fun updateUseMultiScaleMatching(use: Boolean) {
        _useMultiScaleMatching.value = use
        saveBooleanSetting(KEY_USE_MULTI_SCALE_MATCHING, use)
        Timber.d("Multi-scale matching updated: $use")
    }
    
    /**
     * 更新OCR置信度阈值
     */
    fun updateOcrConfidenceThreshold(threshold: Float) {
        _ocrConfidenceThreshold.value = threshold
        saveFloatSetting(KEY_OCR_CONFIDENCE_THRESHOLD, threshold)
        Timber.d("OCR confidence threshold updated: $threshold")
    }
    
    /**
     * 更新精确文本匹配设置
     */
    fun updateUseExactTextMatching(use: Boolean) {
        _useExactTextMatching.value = use
        saveBooleanSetting(KEY_USE_EXACT_TEXT_MATCHING, use)
        Timber.d("Exact text matching updated: $use")
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        viewModelScope.launch {
            try {
                val success = fileManager.clearCache()
                if (success) {
                    Timber.i("Cache cleared successfully")
                    calculateCacheSize()
                } else {
                    Timber.w("Failed to clear cache")
                }
            } catch (e: Exception) {
                Timber.e(e, "Error clearing cache")
            }
        }
    }
    
    /**
     * 重置所有设置为默认值
     */
    fun resetToDefaults() {
        updateExecutionTimeout(DEFAULT_EXECUTION_TIMEOUT)
        updateRetryCount(DEFAULT_RETRY_COUNT)
        updateOperationDelay(DEFAULT_OPERATION_DELAY)
        updateVisualMatchThreshold(DEFAULT_VISUAL_MATCH_THRESHOLD)
        updateUseMultiScaleMatching(DEFAULT_USE_MULTI_SCALE_MATCHING)
        updateOcrConfidenceThreshold(DEFAULT_OCR_CONFIDENCE_THRESHOLD)
        updateUseExactTextMatching(DEFAULT_USE_EXACT_TEXT_MATCHING)
        
        Timber.i("Settings reset to defaults")
    }
    
    /**
     * 计算缓存大小
     */
    private fun calculateCacheSize() {
        viewModelScope.launch {
            try {
                val sizeBytes = fileManager.getCacheSize()
                val sizeText = fileManager.formatFileSize(sizeBytes)
                _cacheSize.value = sizeText
            } catch (e: Exception) {
                Timber.e(e, "Error calculating cache size")
                _cacheSize.value = "未知"
            }
        }
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName} (${packageInfo.longVersionCode})"
        } catch (e: Exception) {
            "未知版本"
        }
    }
    
    // 设置保存辅助方法
    private fun saveFloatSetting(key: String, value: Float) {
        sharedPreferences.edit().putFloat(key, value).apply()
    }
    
    private fun saveIntSetting(key: String, value: Int) {
        sharedPreferences.edit().putInt(key, value).apply()
    }
    
    private fun saveLongSetting(key: String, value: Long) {
        sharedPreferences.edit().putLong(key, value).apply()
    }
    
    private fun saveBooleanSetting(key: String, value: Boolean) {
        sharedPreferences.edit().putBoolean(key, value).apply()
    }
    
    // 公共访问方法，供其他组件使用
    fun getExecutionTimeoutSeconds(): Int = _executionTimeout.value.toInt()
    fun getRetryCount(): Int = _retryCount.value
    fun getOperationDelayMs(): Long = _operationDelay.value
    fun getVisualMatchThreshold(): Float = _visualMatchThreshold.value
    fun shouldUseMultiScaleMatching(): Boolean = _useMultiScaleMatching.value
    fun getOcrConfidenceThreshold(): Float = _ocrConfidenceThreshold.value
    fun shouldUseExactTextMatching(): Boolean = _useExactTextMatching.value
}

/**
 * 设置UI状态
 */
data class SettingsUiState(
    val executionTimeout: Float = 30f,
    val retryCount: Int = 2,
    val operationDelay: Long = 500L,
    val visualMatchThreshold: Float = 0.8f,
    val useMultiScaleMatching: Boolean = true,
    val ocrConfidenceThreshold: Float = 0.7f,
    val useExactTextMatching: Boolean = false,
    val cacheSize: String = "计算中...",
    val appVersion: String = "未知版本"
)
