# 🚀 Android Studio模拟器快速开始指南

## 📋 概述

这个指南将帮你快速在Android Studio模拟器上测试DSL Executor应用。整个过程大约需要15-30分钟。

## ⚡ 快速步骤

### 1. 准备工作 (5分钟)

#### 1.1 确认Android Studio已安装
- 确保Android Studio已安装并可以正常启动
- 确认Android SDK已配置

#### 1.2 创建模拟器
1. 打开Android Studio
2. 点击 `Tools` → `AVD Manager`
3. 点击 `Create Virtual Device`
4. 选择 `Phone` → `Pixel 6`
5. 选择 `API 29 (Android 10.0)` 系统镜像（如果没有，点击Download下载）
6. 点击 `Next` → `Finish`

#### 1.3 启动模拟器
1. 在AVD Manager中点击模拟器旁的 ▶️ 按钮
2. 等待模拟器完全启动（显示Android桌面）
3. 启用开发者选项：
   - 打开 `Settings` → `About phone`
   - 连续点击 `Build number` 7次
   - 返回Settings → `Developer options`
   - 启用 `USB debugging`

### 2. 导入和构建项目 (5-10分钟)

#### 2.1 导入项目
1. 打开Android Studio
2. 选择 `Open an existing project`
3. 导航到 `android_executor` 目录并选择
4. 等待Gradle同步完成（可能需要几分钟）

#### 2.2 解决依赖问题（如果有）
如果出现依赖错误：
1. 点击 `File` → `Sync Project with Gradle Files`
2. 如果提示安装SDK组件，点击安装
3. 等待同步完成

### 3. 运行应用 (2分钟)

#### 方法1: 直接从Android Studio运行
1. 确保模拟器正在运行
2. 在Android Studio中点击绿色的 `Run` 按钮 (▶️)
3. 选择你的模拟器
4. 等待应用安装并启动

#### 方法2: 使用自动化脚本
```bash
cd android_executor
./run_emulator_tests.sh --basic-only
```

### 4. 测试应用 (5-10分钟)

#### 4.1 权限设置
应用启动后会显示权限引导：
1. **存储权限**: 点击"设置" → 找到DSL Executor → 启用文件权限
2. **悬浮窗权限**: 点击"设置" → 找到DSL Executor → 启用"显示在其他应用上层"
3. **无障碍权限**: 点击"设置" → `Accessibility` → 启用"DSL Executor"
4. **屏幕捕捉权限**: 在弹出对话框中点击"立即开始"

#### 4.2 基础功能测试
1. **加载脚本**: 点击"选择脚本" → 选择示例脚本
2. **查看脚本信息**: 验证脚本详情显示正确
3. **执行脚本**: 点击"开始执行" → 观察执行过程
4. **查看日志**: 在执行日志区域查看详细信息
5. **测试设置**: 点击设置图标 → 调整参数 → 保存

## 🔧 常见问题解决

### 问题1: 模拟器启动失败
**解决方案**:
1. 确保电脑有足够内存（建议8GB+）
2. 在BIOS中启用虚拟化技术（Intel VT-x或AMD-V）
3. 关闭其他占用资源的程序

### 问题2: Gradle同步失败
**解决方案**:
1. 检查网络连接
2. 点击 `File` → `Invalidate Caches and Restart`
3. 删除 `.gradle` 文件夹后重新同步

### 问题3: 应用安装失败
**解决方案**:
1. 确保模拟器已完全启动
2. 在Terminal中运行 `adb devices` 确认连接
3. 手动安装APK: `adb install app/build/outputs/apk/debug/app-debug.apk`

### 问题4: 权限设置困难
**解决方案**:
1. 在模拟器中手动进入Settings
2. 搜索"DSL Executor"找到应用
3. 逐一启用所需权限

### 问题5: 应用崩溃
**解决方案**:
1. 查看Logcat日志: Android Studio → `View` → `Tool Windows` → `Logcat`
2. 过滤日志: 在Logcat中搜索"DSLExecutor"
3. 检查错误信息并报告问题

## 📱 推荐的测试流程

### 基础测试 (10分钟)
1. ✅ 应用启动和权限设置
2. ✅ 脚本加载和显示
3. ✅ 基本执行功能
4. ✅ 设置保存和恢复
5. ✅ 日志记录功能

### 进阶测试 (20分钟)
1. ✅ 不同类型的DSL指令测试
2. ✅ 错误处理测试
3. ✅ 屏幕旋转适配
4. ✅ 内存使用监控
5. ✅ 长时间运行测试

### 性能测试 (15分钟)
1. ✅ 使用Android Studio Profiler监控
2. ✅ 内存泄漏检测
3. ✅ CPU使用率监控
4. ✅ 启动时间测试

## 🎯 测试重点

### 必须验证的功能
- [ ] 应用能正常启动
- [ ] 权限申请流程正常
- [ ] 能加载和解析DSL脚本
- [ ] 基本的点击、滑动操作能执行
- [ ] 错误情况能正确处理
- [ ] 设置能保存和恢复

### 性能指标
- [ ] 内存使用 < 200MB
- [ ] 启动时间 < 5秒（模拟器）
- [ ] 无明显内存泄漏
- [ ] UI响应流畅

## 📊 自动化测试

### 运行完整测试套件
```bash
# 基础测试（推荐新手）
./run_emulator_tests.sh --basic-only

# 完整测试
./run_emulator_tests.sh

# 跳过构建的快速测试
./run_emulator_tests.sh --skip-build
```

### 查看测试报告
测试完成后查看 `emulator_test_reports/` 目录：
- `test_summary.txt` - 测试摘要
- `app_logs.txt` - 应用日志
- `memory_info.txt` - 内存信息

## 🔍 调试技巧

### 查看实时日志
```bash
# 查看应用日志
adb logcat | grep "DSLExecutor"

# 清除日志后查看
adb logcat -c && adb logcat | grep "DSLExecutor"
```

### 使用Android Studio调试
1. 设置断点
2. 点击Debug按钮（🐛）
3. 在断点处检查变量状态

### 性能监控
1. 打开 `View` → `Tool Windows` → `Profiler`
2. 选择应用进程
3. 监控CPU、内存、网络使用

## 📝 测试记录

建议记录以下信息：
- 测试日期和时间
- 模拟器配置（设备型号、Android版本）
- 测试的功能点
- 发现的问题
- 性能表现

## 🎉 完成测试

测试完成后，你应该能够：
1. ✅ 确认应用在模拟器上正常运行
2. ✅ 验证核心功能工作正常
3. ✅ 了解应用的性能表现
4. ✅ 发现并记录任何问题

---

**提示**: 模拟器测试是很好的开始，但建议在真实设备上进行最终验证，因为真实设备的性能和行为可能有所不同。
