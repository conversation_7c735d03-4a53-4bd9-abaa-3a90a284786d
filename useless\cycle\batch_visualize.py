import json
import os
from visualize_bbox import draw_bounding_box
from datetime import datetime

# 测试数据集
test_cases = [
    {
        "name": "ace_icon",
        "image": r"C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png",
        "result": {
            "element": {
                "bounding_box": {
                    "height": 82,
                    "width": 108,
                    "x": 330,
                    "y": 388
                },
                "description": "Application icon for ACE software",
                "text_content": ""
            }
        }
    }
    # 在这里添加更多测试案例
]

def batch_process():
    """批量处理所有测试案例"""
    # 创建输出文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"output_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理每个测试案例
    for i, case in enumerate(test_cases):
        print(f"\n处理测试案例 {i+1}: {case['name']}")
        
        # 检查图片是否存在
        if not os.path.exists(case['image']):
            print(f"警告: 图片不存在 - {case['image']}")
            continue
        
        # 生成输出文件名
        output_path = os.path.join(output_dir, f"{case['name']}_bbox.png")
        
        try:
            # 绘制边界框
            draw_bounding_box(case['image'], case['result'], output_path)
            
            # 保存JSON结果
            json_path = os.path.join(output_dir, f"{case['name']}_result.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(case['result'], f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"错误: 处理 {case['name']} 时出错 - {e}")
    
    print(f"\n所有结果已保存到: {output_dir}")
    
    # 生成HTML预览页面
    generate_html_preview(output_dir, test_cases)

def generate_html_preview(output_dir, test_cases):
    """生成HTML预览页面"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>边界框测试结果</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin-bottom: 30px; border: 1px solid #ccc; padding: 10px; }
        .images { display: flex; gap: 20px; margin-top: 10px; }
        .image-container { text-align: center; }
        img { max-width: 400px; border: 1px solid #ddd; }
        .bbox-info { background: #f0f0f0; padding: 10px; margin-top: 10px; }
        pre { background: #f5f5f5; padding: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>边界框可视化测试结果</h1>
"""
    
    for i, case in enumerate(test_cases):
        if not os.path.exists(case['image']):
            continue
            
        bbox = case['result']['element']['bounding_box']
        html_content += f"""
    <div class="test-case">
        <h2>测试案例 {i+1}: {case['name']}</h2>
        <div class="images">
            <div class="image-container">
                <h3>原始图片</h3>
                <img src="{os.path.abspath(case['image'])}" alt="原始图片">
            </div>
            <div class="image-container">
                <h3>带边界框的图片</h3>
                <img src="{case['name']}_bbox.png" alt="带边界框的图片">
            </div>
        </div>
        <div class="bbox-info">
            <h3>边界框信息</h3>
            <p><strong>描述:</strong> {case['result']['element']['description']}</p>
            <p><strong>文本内容:</strong> {case['result']['element']['text_content'] or '(无)'}</p>
            <p><strong>坐标:</strong> ({bbox['x']}, {bbox['y']})</p>
            <p><strong>尺寸:</strong> {bbox['width']} x {bbox['height']}</p>
            <details>
                <summary>完整JSON结果</summary>
                <pre>{json.dumps(case['result'], indent=2, ensure_ascii=False)}</pre>
            </details>
        </div>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    html_path = os.path.join(output_dir, "preview.html")
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML预览页面已生成: {html_path}")

if __name__ == "__main__":
    batch_process()