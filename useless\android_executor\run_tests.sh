#!/bin/bash

# Android DSL Executor 测试执行脚本
# 
# 此脚本用于执行完整的测试套件，包括单元测试、集成测试和性能测试

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查测试环境..."
    
    # 检查Android SDK
    if ! command -v adb &> /dev/null; then
        log_error "ADB not found. Please install Android SDK."
        exit 1
    fi
    
    # 检查设备连接
    DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device$" || true)
    if [ "$DEVICE_COUNT" -eq 0 ]; then
        log_error "No Android devices connected. Please connect a device or start an emulator."
        exit 1
    fi
    
    log_success "Found $DEVICE_COUNT connected device(s)"
    
    # 显示设备信息
    log_info "Connected devices:"
    adb devices -l
}

# 清理环境
clean_environment() {
    log_info "清理测试环境..."
    
    # 清理之前的测试数据
    adb shell pm clear com.showforai.executor 2>/dev/null || true
    
    # 清理测试报告目录
    rm -rf app/build/reports/tests/
    rm -rf app/build/reports/androidTests/
    rm -rf app/build/outputs/androidTest-results/
    
    log_success "Environment cleaned"
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    # 清理构建
    ./gradlew clean
    
    # 构建debug版本
    ./gradlew assembleDebug
    
    # 构建测试APK
    ./gradlew assembleDebugAndroidTest
    
    log_success "App built successfully"
}

# 安装应用
install_app() {
    log_info "安装应用..."
    
    # 卸载旧版本
    adb uninstall com.showforai.executor 2>/dev/null || true
    adb uninstall com.showforai.executor.test 2>/dev/null || true
    
    # 安装应用和测试APK
    adb install app/build/outputs/apk/debug/app-debug.apk
    adb install app/build/outputs/apk/androidTest/debug/app-debug-androidTest.apk
    
    log_success "App installed successfully"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    ./gradlew test --continue
    
    if [ $? -eq 0 ]; then
        log_success "Unit tests passed"
    else
        log_error "Unit tests failed"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    # 运行所有集成测试
    ./gradlew connectedAndroidTest --continue
    
    if [ $? -eq 0 ]; then
        log_success "Integration tests passed"
    else
        log_error "Integration tests failed"
        return 1
    fi
}

# 运行UI测试
run_ui_tests() {
    log_info "运行UI测试..."
    
    # 运行UI测试
    adb shell am instrument -w -r \
        -e class com.showforai.executor.ui.MainActivityTest \
        com.showforai.executor.test/androidx.test.runner.AndroidJUnitRunner
    
    if [ $? -eq 0 ]; then
        log_success "UI tests passed"
    else
        log_error "UI tests failed"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    # 运行性能测试
    adb shell am instrument -w -r \
        -e class com.showforai.executor.performance.PerformanceTest \
        com.showforai.executor.test/androidx.test.runner.AndroidJUnitRunner
    
    if [ $? -eq 0 ]; then
        log_success "Performance tests passed"
    else
        log_error "Performance tests failed"
        return 1
    fi
}

# 收集测试报告
collect_reports() {
    log_info "收集测试报告..."
    
    # 创建报告目录
    mkdir -p test_reports
    
    # 复制测试报告
    if [ -d "app/build/reports/tests/" ]; then
        cp -r app/build/reports/tests/ test_reports/unit_tests/
    fi
    
    if [ -d "app/build/reports/androidTests/" ]; then
        cp -r app/build/reports/androidTests/ test_reports/android_tests/
    fi
    
    # 收集设备日志
    adb logcat -d > test_reports/device_log.txt
    
    # 收集应用日志
    adb logcat -d | grep "DSLExecutor" > test_reports/app_log.txt
    
    log_success "Test reports collected in test_reports/"
}

# 生成测试摘要
generate_summary() {
    log_info "生成测试摘要..."
    
    SUMMARY_FILE="test_reports/test_summary.txt"
    
    cat > "$SUMMARY_FILE" << EOF
Android DSL Executor 测试摘要
=============================

测试执行时间: $(date)
设备信息:
$(adb shell getprop ro.product.model) - $(adb shell getprop ro.build.version.release)

测试结果:
- 单元测试: $UNIT_TEST_RESULT
- 集成测试: $INTEGRATION_TEST_RESULT
- UI测试: $UI_TEST_RESULT
- 性能测试: $PERFORMANCE_TEST_RESULT

详细报告请查看 test_reports/ 目录下的文件。
EOF

    log_success "Test summary generated: $SUMMARY_FILE"
}

# 检查测试覆盖率
check_coverage() {
    log_info "检查测试覆盖率..."
    
    # 生成覆盖率报告
    ./gradlew jacocoTestReport
    
    if [ -f "app/build/reports/jacoco/jacocoTestReport/html/index.html" ]; then
        log_success "Coverage report generated"
        cp -r app/build/reports/jacoco/ test_reports/coverage/
    else
        log_warning "Coverage report not found"
    fi
}

# 主函数
main() {
    log_info "开始执行Android DSL Executor测试套件"
    
    # 初始化结果变量
    UNIT_TEST_RESULT="SKIPPED"
    INTEGRATION_TEST_RESULT="SKIPPED"
    UI_TEST_RESULT="SKIPPED"
    PERFORMANCE_TEST_RESULT="SKIPPED"
    
    # 解析命令行参数
    RUN_UNIT_TESTS=true
    RUN_INTEGRATION_TESTS=true
    RUN_UI_TESTS=true
    RUN_PERFORMANCE_TESTS=true
    SKIP_BUILD=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-unit)
                RUN_UNIT_TESTS=false
                shift
                ;;
            --skip-integration)
                RUN_INTEGRATION_TESTS=false
                shift
                ;;
            --skip-ui)
                RUN_UI_TESTS=false
                shift
                ;;
            --skip-performance)
                RUN_PERFORMANCE_TESTS=false
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --skip-unit         Skip unit tests"
                echo "  --skip-integration  Skip integration tests"
                echo "  --skip-ui          Skip UI tests"
                echo "  --skip-performance Skip performance tests"
                echo "  --skip-build       Skip build step"
                echo "  --help             Show this help"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行测试流程
    check_environment
    clean_environment
    
    if [ "$SKIP_BUILD" = false ]; then
        build_app
        install_app
    fi
    
    # 运行测试
    if [ "$RUN_UNIT_TESTS" = true ]; then
        if run_unit_tests; then
            UNIT_TEST_RESULT="PASSED"
        else
            UNIT_TEST_RESULT="FAILED"
        fi
    fi
    
    if [ "$RUN_INTEGRATION_TESTS" = true ]; then
        if run_integration_tests; then
            INTEGRATION_TEST_RESULT="PASSED"
        else
            INTEGRATION_TEST_RESULT="FAILED"
        fi
    fi
    
    if [ "$RUN_UI_TESTS" = true ]; then
        if run_ui_tests; then
            UI_TEST_RESULT="PASSED"
        else
            UI_TEST_RESULT="FAILED"
        fi
    fi
    
    if [ "$RUN_PERFORMANCE_TESTS" = true ]; then
        if run_performance_tests; then
            PERFORMANCE_TEST_RESULT="PASSED"
        else
            PERFORMANCE_TEST_RESULT="FAILED"
        fi
    fi
    
    # 收集结果
    collect_reports
    check_coverage
    generate_summary
    
    # 显示最终结果
    echo ""
    log_info "测试执行完成！"
    echo "单元测试: $UNIT_TEST_RESULT"
    echo "集成测试: $INTEGRATION_TEST_RESULT"
    echo "UI测试: $UI_TEST_RESULT"
    echo "性能测试: $PERFORMANCE_TEST_RESULT"
    
    # 检查是否有失败的测试
    if [[ "$UNIT_TEST_RESULT" == "FAILED" || "$INTEGRATION_TEST_RESULT" == "FAILED" || 
          "$UI_TEST_RESULT" == "FAILED" || "$PERFORMANCE_TEST_RESULT" == "FAILED" ]]; then
        log_error "Some tests failed. Please check the reports."
        exit 1
    else
        log_success "All tests passed!"
        exit 0
    fi
}

# 执行主函数
main "$@"
