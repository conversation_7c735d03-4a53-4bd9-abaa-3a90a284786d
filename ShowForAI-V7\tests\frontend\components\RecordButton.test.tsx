import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import RecordButton from '@components/recording/RecordButton'
import { useRecordingStore } from '@stores/recording'

// Mock the store
vi.mock('@stores/recording', () => ({
  useRecordingStore: vi.fn()
}))

const mockUseRecordingStore = useRecordingStore as any

describe('RecordButton', () => {
  const mockStartRecording = vi.fn()
  const mockStopRecording = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseRecordingStore.mockReturnValue({
      isRecording: false,
      startRecording: mockStartRecording,
      stopRecording: mockStopRecording,
      error: null
    })
  })

  it('renders start recording button when not recording', () => {
    render(<RecordButton />)
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent(/start/i)
  })

  it('renders stop recording button when recording', () => {
    mockUseRecordingStore.mockReturnValue({
      isRecording: true,
      startRecording: mockStartRecording,
      stopRecording: mockStopRecording,
      error: null
    })

    render(<RecordButton />)
    
    const button = screen.getByRole('button')
    expect(button).toHaveTextContent(/stop/i)
  })

  it('calls startRecording when clicked and not recording', async () => {
    render(<RecordButton />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)

    await waitFor(() => {
      expect(mockStartRecording).toHaveBeenCalledOnce()
    })
  })

  it('calls stopRecording when clicked and recording', async () => {
    mockUseRecordingStore.mockReturnValue({
      isRecording: true,
      startRecording: mockStartRecording,
      stopRecording: mockStopRecording,
      error: null
    })

    render(<RecordButton />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)

    await waitFor(() => {
      expect(mockStopRecording).toHaveBeenCalledOnce()
    })
  })

  it('displays error message when error exists', () => {
    const errorMessage = 'Recording failed'
    mockUseRecordingStore.mockReturnValue({
      isRecording: false,
      startRecording: mockStartRecording,
      stopRecording: mockStopRecording,
      error: errorMessage
    })

    render(<RecordButton />)
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument()
  })

  it('disables button when recording is in progress', () => {
    mockUseRecordingStore.mockReturnValue({
      isRecording: true,
      startRecording: mockStartRecording,
      stopRecording: mockStopRecording,
      error: null
    })

    render(<RecordButton />)
    
    const button = screen.getByRole('button')
    expect(button).not.toBeDisabled() // Should be enabled to allow stopping
  })
})