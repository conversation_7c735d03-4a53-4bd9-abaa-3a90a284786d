"""
检查ShowForAI状态
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.sync.offline_manager import OfflineModeManager

def main():
    print("="*50)
    print("ShowForAI V3 状态检查")
    print("="*50)
    
    # 检查网络状态
    manager = OfflineModeManager()
    
    is_online = manager.is_online()
    can_record = manager.is_recording_allowed()
    can_execute = manager.is_execution_allowed()
    
    print(f"\n网络状态: {'✅ 在线' if is_online else '❌ 离线'}")
    print(f"录制功能: {'✅ 可用' if can_record else '❌ 不可用（需要网络）'}")
    print(f"执行功能: {'✅ 可用' if can_execute else '❌ 不可用'}")
    
    if is_online and can_record:
        print("\n🎉 恭喜！录制功能现在可以正常使用了！")
        print("\n使用说明：")
        print("1. 点击红色 Record 按钮开始录制")
        print("2. 执行你的操作")
        print("3. 按 F9 键停止录制")
        print("4. 点击 'Upload & Create Script' 生成脚本")
    else:
        print("\n⚠️ 录制功能仍然不可用，请检查网络连接")

if __name__ == "__main__":
    main()