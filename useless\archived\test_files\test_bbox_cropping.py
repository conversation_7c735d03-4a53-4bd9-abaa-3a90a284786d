"""
Test script for BBOX cropping system.

Tests the complete BBOX cropping workflow with 768x768 images.
"""

import io
import json
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import hashlib
from datetime import datetime

# Add parent directory to path
import sys
sys.path.insert(0, str(Path(__file__).parent))

from src.showforai.processing.bbox_cutter import BboxCutter
from src.showforai.storage.element_cache import ElementCacheManager
from src.showforai.processing.script_generator import ScriptGenerator


def create_test_image():
    """Create a test 768x768 image with UI elements."""
    img = Image.new('RGB', (768, 768), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw some UI elements
    elements = [
        {'bbox': {'x': 50, 'y': 50, 'width': 100, 'height': 40}, 'color': 'blue', 'text': 'Button 1'},
        {'bbox': {'x': 200, 'y': 50, 'width': 120, 'height': 40}, 'color': 'green', 'text': 'Button 2'},
        {'bbox': {'x': 50, 'y': 150, 'width': 200, 'height': 30}, 'color': 'red', 'text': 'Input Field'},
        {'bbox': {'x': 300, 'y': 300, 'width': 150, 'height': 150}, 'color': 'purple', 'text': 'Image'},
    ]
    
    for elem in elements:
        bbox = elem['bbox']
        # Draw rectangle
        draw.rectangle(
            [(bbox['x'], bbox['y']), 
             (bbox['x'] + bbox['width'], bbox['y'] + bbox['height'])],
            outline=elem['color'],
            width=2
        )
        # Add text
        text_x = bbox['x'] + bbox['width'] // 2 - len(elem['text']) * 3
        text_y = bbox['y'] + bbox['height'] // 2 - 5
        draw.text((text_x, text_y), elem['text'], fill='black')
    
    return img, elements


def test_bbox_cutter():
    """Test BboxCutter functionality."""
    print("Testing BboxCutter...")
    
    # Create test image
    img, elements = create_test_image()
    
    # Initialize cutter
    cutter = BboxCutter()
    
    # Convert image to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_data = img_bytes.getvalue()
    
    # Test single element cropping
    print("\n1. Testing single element cropping:")
    for i, elem in enumerate(elements):
        bbox = elem['bbox']
        element_id = f"test_element_{i}"
        
        cropped = cutter.crop_element_from_bbox(img_data, bbox, element_id)
        if cropped:
            print(f"   ✓ Cropped {elem['text']}: {bbox['width']}x{bbox['height']} pixels")
            
            # Save for visual inspection
            test_dir = Path("test_output")
            test_dir.mkdir(exist_ok=True)
            with open(test_dir / f"cropped_{element_id}.png", 'wb') as f:
                f.write(cropped)
        else:
            print(f"   ✗ Failed to crop {elem['text']}")
    
    # Test batch cropping
    print("\n2. Testing batch cropping:")
    bboxes_with_ids = [
        {**elem['bbox'], 'id': f"batch_{i}"} 
        for i, elem in enumerate(elements)
    ]
    
    results = cutter.batch_crop_elements(img_data, bboxes_with_ids)
    success_count = sum(1 for r in results if r is not None)
    print(f"   Batch cropped {success_count}/{len(results)} elements")
    
    # Test validation
    print("\n3. Testing BBOX validation:")
    test_cases = [
        {'x': 0, 'y': 0, 'width': 100, 'height': 100},  # Valid
        {'x': -10, 'y': 0, 'width': 100, 'height': 100},  # Negative x
        {'x': 700, 'y': 700, 'width': 100, 'height': 100},  # Out of bounds
        {'x': 0, 'y': 0, 'width': 768, 'height': 768},  # Full image
    ]
    
    for bbox in test_cases:
        validated = cutter.validate_and_adjust_bbox(bbox)
        if validated:
            print(f"   ✓ Valid BBOX: {bbox} -> {validated}")
        else:
            print(f"   ✗ Invalid BBOX: {bbox}")
    
    # Test cache stats
    print("\n4. Cache statistics:")
    stats = cutter.get_cache_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    return True


def test_element_cache():
    """Test ElementCacheManager functionality."""
    print("\nTesting ElementCacheManager...")
    
    # Initialize cache manager
    cache_mgr = ElementCacheManager()
    
    # Create test data
    script_id = "test_script_001"
    element_data = {
        "elem1": b"PNG_DATA_1",
        "elem2": b"PNG_DATA_2",
        "elem3": b"PNG_DATA_3",
    }
    
    # Test storing elements
    print("\n1. Testing element storage:")
    for elem_id, data in element_data.items():
        success = cache_mgr.store_element(script_id, elem_id, data)
        if success:
            print(f"   ✓ Stored element {elem_id}")
        else:
            print(f"   ✗ Failed to store {elem_id}")
    
    # Test retrieving elements
    print("\n2. Testing element retrieval:")
    for elem_id in element_data.keys():
        retrieved = cache_mgr.retrieve_element(script_id, elem_id)
        if retrieved == element_data[elem_id]:
            print(f"   ✓ Retrieved element {elem_id}")
        else:
            print(f"   ✗ Failed to retrieve {elem_id}")
    
    # Test batch storage
    print("\n3. Testing batch storage:")
    batch_data = {
        "batch1": b"BATCH_DATA_1",
        "batch2": b"BATCH_DATA_2",
    }
    results = cache_mgr.batch_store_elements(script_id, batch_data)
    print(f"   Stored {sum(results.values())}/{len(batch_data)} elements")
    
    # Test cache stats
    print("\n4. Cache statistics:")
    stats = cache_mgr.get_cache_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Test script cache clearing
    print("\n5. Testing cache clearing:")
    success = cache_mgr.clear_script_cache(script_id)
    if success:
        print(f"   ✓ Cleared cache for script {script_id}")
    else:
        print(f"   ✗ Failed to clear cache")
    
    return True


def test_script_generator_integration():
    """Test ScriptGenerator with BBOX cropping integration."""
    print("\nTesting ScriptGenerator Integration...")
    
    # Create test image and save it
    img, elements = create_test_image()
    test_dir = Path("test_output")
    test_dir.mkdir(exist_ok=True)
    screenshot_path = test_dir / "test_screenshot_768.png"
    img.save(screenshot_path)
    
    # Initialize generator
    generator = ScriptGenerator()
    
    # Create test recording data
    recording_data = {
        'metadata': {
            'session_id': 'test_session',
            'screen_resolution': '768x768',
            'device_id': 'test_device',
            'recorded_at': datetime.now().isoformat(),
            'duration_ms': 5000
        },
        'actions': []
    }
    
    # Add actions with BBOX
    for i, elem in enumerate(elements):
        action = {
            'type': 'click',
            'sequence': i + 1,
            'timestamp': i * 1000,
            'position': {'x': elem['bbox']['x'], 'y': elem['bbox']['y']},
            'screenshot': str(screenshot_path),
            'element_bbox': elem['bbox'],
            'detection_confidence': 0.95
        }
        recording_data['actions'].append(action)
    
    # Generate script with BBOX cropping
    print("\n1. Generating script with BBOX cropping:")
    script_id = hashlib.md5(b"test_script").hexdigest()[:16]
    script = generator.generate_script(
        recording_data,
        ai_data={'processed': True},
        script_id=script_id
    )
    
    print(f"   Generated script with {len(script['actions'])} actions")
    print(f"   Script ID: {script['metadata']['script_id']}")
    print(f"   Coordinate system: {script['metadata']['coordinate_system']}")
    
    # Check if elements were cropped and cached
    print("\n2. Checking cropped elements:")
    for action in script['actions']:
        if 'element_image_cached' in action:
            print(f"   ✓ Action {action['sequence']}: Element cached as {action['element_image_cached']}")
            if 'element_image_path' in action:
                print(f"     Path: {action['element_image_path']}")
        else:
            print(f"   ✗ Action {action['sequence']}: No cached element")
    
    # Test script validation
    print("\n3. Validating generated script:")
    is_valid, errors = generator.validate_script(script)
    if is_valid:
        print("   ✓ Script is valid")
    else:
        print(f"   ✗ Script validation failed: {errors}")
    
    # Save script
    output_file = test_dir / "generated_script.json"
    with open(output_file, 'w') as f:
        json.dump(script, f, indent=2)
    print(f"\n4. Script saved to: {output_file}")
    
    return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("BBOX Cropping System Test Suite")
    print("=" * 60)
    
    tests = [
        ("BboxCutter", test_bbox_cutter),
        ("ElementCacheManager", test_element_cache),
        ("ScriptGenerator Integration", test_script_generator_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'=' * 40}")
        print(f"Running: {test_name}")
        print('=' * 40)
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"\n✗ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    for test_name, success in results:
        status = "✓ PASSED" if success else "✗ FAILED"
        print(f"{test_name}: {status}")
    
    total_passed = sum(1 for _, s in results if s)
    print(f"\nTotal: {total_passed}/{len(results)} tests passed")
    
    if total_passed == len(results):
        print("\n🎉 All tests passed! BBOX cropping system is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please review the output above.")


if __name__ == "__main__":
    main()