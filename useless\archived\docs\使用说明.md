# ShowForAI V3 使用说明

## 快速开始

### 方法1：使用批处理文件（推荐）
1. 双击 `启动ShowForAI.bat`
2. 选择功能：
   - 输入 `1` 启动录制器
   - 输入 `2` 启动执行器

### 方法2：使用Python脚本
1. 双击 `run_showforai.py`
2. 按提示选择功能

### 方法3：命令行启动
```bash
# 启动录制器
python -m showforai recorder gui

# 启动执行器
python -m showforai executor gui
```

## 录制操作流程

1. **启动录制器**
   - 运行 `启动ShowForAI.bat` 并选择 `1`
   - 或直接运行 `start_recorder.bat`

2. **开始录制**
   - 点击红色 **Record** 按钮
   - 程序开始以10FPS捕获屏幕
   - 录制你的操作（点击、输入等）

3. **停止录制**
   - 按 **F9** 键停止录制
   - 或点击 **Stop** 按钮

4. **生成脚本**
   - 录制停止后会弹出对话框
   - 点击 **Upload & Create Script** 按钮
   - 系统会：
     - 上传录制数据
     - 调用AI识别界面元素
     - 生成包含BBOX的脚本
     - 裁切元素图像供执行使用

## 执行脚本流程

1. **启动执行器**
   - 运行 `启动ShowForAI.bat` 并选择 `2`

2. **选择脚本**
   - 从列表中选择要执行的脚本

3. **执行模式**
   - **主动模式**：基于录制间隔的智能等待
   - **辅助模式**：无限等待元素出现

4. **开始执行**
   - 点击 **Run** 按钮
   - 程序会自动执行录制的操作

## 产品特点

### 严格遵循产品原则
- ✅ **纯图像识别**：不使用其他定位方式
- ✅ **768×768标准化**：所有处理基于标准分辨率
- ✅ **智能等待**：使用录制时的操作间隔
- ✅ **离线执行**：脚本可离线执行
- ✅ **录制需在线**：AI识别需要网络连接

### 核心功能
1. **10FPS缓冲机制**：获取点击前第3帧，避免鼠标干扰
2. **AI识别集成**：自动识别UI元素并生成BBOX
3. **本地裁切**：基于768×768图像裁切元素
4. **智能等待**：主动模式使用录制间隔，失败后每秒重试

## 注意事项

1. **网络要求**
   - 录制功能需要网络连接（用于AI识别）
   - 离线状态下录制按钮会被禁用
   - 执行功能可以离线使用

2. **文件位置**
   - 录制文件保存在 `recordings/` 目录
   - 生成的脚本保存在 `scripts/` 目录
   - 裁切的元素图像保存在 `elements/` 目录

3. **故障排除**
   - 如果程序无法启动，检查Python版本（需要3.8+）
   - 如果录制按钮禁用，检查网络连接
   - 如果AI识别失败，检查AI服务是否可用

## 测试建议

1. **简单测试**
   - 录制打开记事本并输入文字
   - 录制打开计算器并点击数字

2. **验证功能**
   - 检查生成的脚本文件是否包含BBOX信息
   - 检查elements目录是否有裁切的图像
   - 执行脚本验证智能等待是否工作

## 已知问题

- 主GUI可能有一些初始化问题，建议使用录制器和执行器的独立GUI
- Supabase认证模块有警告但不影响基本功能

## 联系支持

如有问题，请查看：
- `docs/critical_fixes_summary.md` - 第一阶段修复记录
- `docs/phase2_fixes_summary.md` - 第二阶段修复记录

---

*基于产品原则开发，严格遵循图像识别核心理念*