"""
Test suite for Execution Engine
Tests multi-level matching, progress tracking, error recovery, and retry mechanisms
"""

import unittest
import time
import json
from unittest.mock import Mock, patch, MagicMock, call
import sys
from pathlib import Path
from PIL import Image
import numpy as np

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.execution_engine import ExecutionEngine, ExecutionResult, ExecutionStatus


class TestExecutionEngine(unittest.TestCase):
    """Test Execution Engine core functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.engine = ExecutionEngine()
        
        # Create test script
        self.test_script = {
            'name': 'test_script',
            'version': '1.0',
            'actions': [
                {
                    'type': 'click',
                    'element': {'id': 'button1', 'confidence': 0.9},
                    'position': (100, 100)
                },
                {
                    'type': 'type',
                    'element': {'id': 'input1', 'confidence': 0.85},
                    'text': 'test text'
                },
                {
                    'type': 'wait',
                    'duration': 1.0
                }
            ]
        }
        
        # Mock components
        self.mock_matcher = Mock()
        self.mock_executor = Mock()
        self.mock_monitor = Mock()
        
    def test_initialization(self):
        """Test engine initialization"""
        self.assertIsNotNone(self.engine)
        self.assertEqual(self.engine.status, ExecutionStatus.IDLE)
        self.assertEqual(self.engine.current_action_index, 0)
        self.assertIsNone(self.engine.current_script)
        
    def test_load_script(self):
        """Test loading execution script"""
        result = self.engine.load_script(self.test_script)
        
        self.assertTrue(result)
        self.assertEqual(self.engine.current_script, self.test_script)
        self.assertEqual(len(self.engine.actions), 3)
        self.assertEqual(self.engine.status, ExecutionStatus.READY)
        
    def test_execute_single_action(self):
        """Test executing a single action"""
        self.engine.load_script(self.test_script)
        
        # Mock successful execution
        self.mock_executor.execute.return_value = ExecutionResult(
            success=True,
            action_index=0,
            message="Click executed successfully"
        )
        
        result = self.engine.execute_action(
            action_index=0,
            executor=self.mock_executor
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.action_index, 0)
        
    def test_execute_full_script(self):
        """Test executing complete script"""
        self.engine.load_script(self.test_script)
        
        # Mock successful executions
        self.mock_executor.execute.return_value = ExecutionResult(success=True)
        
        results = self.engine.execute_all(executor=self.mock_executor)
        
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertTrue(result.success)
            
        self.assertEqual(self.engine.status, ExecutionStatus.COMPLETED)
        
    def test_multi_level_matching(self):
        """Test multi-level element matching"""
        # Create test element with multiple confidence levels
        element = {
            'id': 'test_element',
            'primary_confidence': 0.9,
            'secondary_confidence': 0.75,
            'fallback_confidence': 0.6
        }
        
        # Test primary match
        match_result = self.engine.match_element_multilevel(
            element,
            matcher=self.mock_matcher,
            level='primary'
        )
        
        self.mock_matcher.match.assert_called_with(
            element,
            confidence_threshold=0.85
        )
        
    def test_progress_tracking(self):
        """Test execution progress tracking"""
        self.engine.load_script(self.test_script)
        
        # Check initial progress
        progress = self.engine.get_progress()
        self.assertEqual(progress['completed'], 0)
        self.assertEqual(progress['total'], 3)
        self.assertEqual(progress['percentage'], 0)
        
        # Execute one action
        self.engine.current_action_index = 1
        progress = self.engine.get_progress()
        
        self.assertEqual(progress['completed'], 1)
        self.assertEqual(progress['percentage'], 33.33)
        
    def test_error_recovery(self):
        """Test error recovery mechanisms"""
        self.engine.load_script(self.test_script)
        
        # Mock execution with error
        self.mock_executor.execute.side_effect = [
            ExecutionResult(success=False, error="Element not found"),
            ExecutionResult(success=True)  # Success on retry
        ]
        
        # Enable error recovery
        self.engine.enable_recovery = True
        
        result = self.engine.execute_with_recovery(
            action_index=0,
            executor=self.mock_executor,
            max_retries=3
        )
        
        self.assertTrue(result.success)
        self.assertEqual(self.mock_executor.execute.call_count, 2)
        
    def test_retry_mechanism(self):
        """Test retry mechanism with backoff"""
        self.engine.load_script(self.test_script)
        
        # Mock failures then success
        self.mock_executor.execute.side_effect = [
            ExecutionResult(success=False),
            ExecutionResult(success=False),
            ExecutionResult(success=True)
        ]
        
        start_time = time.time()
        result = self.engine.execute_with_retry(
            action_index=0,
            executor=self.mock_executor,
            max_retries=3,
            backoff_factor=0.1
        )
        elapsed = time.time() - start_time
        
        self.assertTrue(result.success)
        self.assertEqual(result.retry_count, 2)
        self.assertGreater(elapsed, 0.2)  # Due to backoff
        
    def test_pause_and_resume(self):
        """Test pausing and resuming execution"""
        self.engine.load_script(self.test_script)
        
        # Start execution
        self.engine.status = ExecutionStatus.RUNNING
        
        # Pause
        self.engine.pause()
        self.assertEqual(self.engine.status, ExecutionStatus.PAUSED)
        
        # Resume
        self.engine.resume()
        self.assertEqual(self.engine.status, ExecutionStatus.RUNNING)
        
    def test_stop_execution(self):
        """Test stopping execution"""
        self.engine.load_script(self.test_script)
        self.engine.status = ExecutionStatus.RUNNING
        
        # Stop
        self.engine.stop()
        
        self.assertEqual(self.engine.status, ExecutionStatus.STOPPED)
        self.assertIsNone(self.engine.current_script)
        
    def test_execution_hooks(self):
        """Test pre and post execution hooks"""
        self.engine.load_script(self.test_script)
        
        # Set up hooks
        pre_hook = Mock()
        post_hook = Mock()
        
        self.engine.set_pre_action_hook(pre_hook)
        self.engine.set_post_action_hook(post_hook)
        
        # Execute action
        self.engine.execute_action(0, executor=self.mock_executor)
        
        # Verify hooks were called
        pre_hook.assert_called_once()
        post_hook.assert_called_once()
        
    def test_conditional_execution(self):
        """Test conditional action execution"""
        # Script with conditional actions
        conditional_script = {
            'name': 'conditional_test',
            'actions': [
                {
                    'type': 'click',
                    'condition': {'element_exists': 'button1'}
                },
                {
                    'type': 'type',
                    'condition': {'element_not_exists': 'popup'}
                }
            ]
        }
        
        self.engine.load_script(conditional_script)
        
        # Mock condition evaluator
        mock_evaluator = Mock()
        mock_evaluator.evaluate.side_effect = [True, False]
        
        results = self.engine.execute_conditional(
            executor=self.mock_executor,
            condition_evaluator=mock_evaluator
        )
        
        # First action executed, second skipped
        self.assertEqual(len(results), 2)
        self.assertTrue(results[0].executed)
        self.assertFalse(results[1].executed)


class TestExecutionMonitoring(unittest.TestCase):
    """Test execution monitoring and metrics"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.engine = ExecutionEngine()
        self.engine.enable_monitoring = True
        
    def test_performance_metrics(self):
        """Test performance metric collection"""
        script = {
            'name': 'perf_test',
            'actions': [
                {'type': 'click'},
                {'type': 'wait', 'duration': 0.1}
            ]
        }
        
        self.engine.load_script(script)
        
        # Execute with metrics
        mock_executor = Mock()
        mock_executor.execute.return_value = ExecutionResult(
            success=True,
            execution_time=0.05
        )
        
        self.engine.execute_all(executor=mock_executor)
        
        metrics = self.engine.get_metrics()
        
        self.assertIn('total_execution_time', metrics)
        self.assertIn('average_action_time', metrics)
        self.assertIn('success_rate', metrics)
        
    def test_execution_history(self):
        """Test execution history tracking"""
        self.engine.track_history = True
        
        # Execute multiple scripts
        for i in range(3):
            script = {'name': f'script_{i}', 'actions': [{'type': 'click'}]}
            self.engine.load_script(script)
            self.engine.execute_all(executor=Mock())
            
        history = self.engine.get_execution_history()
        
        self.assertEqual(len(history), 3)
        for entry in history:
            self.assertIn('script_name', entry)
            self.assertIn('timestamp', entry)
            self.assertIn('result', entry)
            
    def test_error_logging(self):
        """Test error logging during execution"""
        script = {
            'name': 'error_test',
            'actions': [{'type': 'click', 'element': 'missing'}]
        }
        
        self.engine.load_script(script)
        
        # Mock executor with error
        mock_executor = Mock()
        mock_executor.execute.return_value = ExecutionResult(
            success=False,
            error="Element not found: missing"
        )
        
        self.engine.execute_all(executor=mock_executor)
        
        errors = self.engine.get_error_log()
        
        self.assertEqual(len(errors), 1)
        self.assertIn('Element not found', errors[0]['error'])
        
    def test_resource_monitoring(self):
        """Test resource usage monitoring"""
        import psutil
        
        self.engine.monitor_resources = True
        
        script = {'name': 'resource_test', 'actions': [{'type': 'wait'}]}
        self.engine.load_script(script)
        
        # Execute with resource monitoring
        self.engine.execute_all(executor=Mock())
        
        resources = self.engine.get_resource_usage()
        
        self.assertIn('cpu_percent', resources)
        self.assertIn('memory_mb', resources)
        self.assertIn('peak_memory_mb', resources)


class TestAdvancedMatching(unittest.TestCase):
    """Test advanced multi-level matching strategies"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.engine = ExecutionEngine()
        
    def test_fallback_chain(self):
        """Test fallback chain matching"""
        element = {
            'primary_method': 'template',
            'secondary_method': 'ocr',
            'tertiary_method': 'coordinates'
        }
        
        # Mock matchers
        template_matcher = Mock()
        ocr_matcher = Mock()
        coord_matcher = Mock()
        
        # Template fails, OCR succeeds
        template_matcher.match.return_value = None
        ocr_matcher.match.return_value = {'position': (100, 100)}
        
        result = self.engine.match_with_fallback(
            element,
            matchers=[template_matcher, ocr_matcher, coord_matcher]
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result['position'], (100, 100))
        self.assertEqual(template_matcher.match.call_count, 1)
        self.assertEqual(ocr_matcher.match.call_count, 1)
        self.assertEqual(coord_matcher.match.call_count, 0)
        
    def test_confidence_aggregation(self):
        """Test confidence score aggregation"""
        # Multiple detection results
        detections = [
            {'confidence': 0.8, 'position': (100, 100)},
            {'confidence': 0.85, 'position': (102, 98)},
            {'confidence': 0.75, 'position': (99, 101)}
        ]
        
        aggregated = self.engine.aggregate_detections(detections)
        
        # Should use weighted average
        self.assertAlmostEqual(aggregated['confidence'], 0.8, delta=0.05)
        self.assertAlmostEqual(aggregated['position'][0], 100, delta=2)
        self.assertAlmostEqual(aggregated['position'][1], 100, delta=2)
        
    def test_adaptive_threshold(self):
        """Test adaptive confidence threshold adjustment"""
        self.engine.adaptive_threshold = True
        
        # Track successful matches
        for _ in range(5):
            self.engine.record_match_success(confidence=0.75)
            
        # Threshold should adapt
        new_threshold = self.engine.get_adaptive_threshold()
        self.assertLess(new_threshold, 0.85)  # Should lower if consistent success
        
        # Track failures
        for _ in range(5):
            self.engine.record_match_failure(confidence=0.6)
            
        # Threshold should increase
        newer_threshold = self.engine.get_adaptive_threshold()
        self.assertGreater(newer_threshold, new_threshold)


class TestExecutionOptimization(unittest.TestCase):
    """Test execution optimization features"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.engine = ExecutionEngine()
        
    def test_action_batching(self):
        """Test batching similar actions"""
        script = {
            'name': 'batch_test',
            'actions': [
                {'type': 'type', 'text': 'a'},
                {'type': 'type', 'text': 'b'},
                {'type': 'type', 'text': 'c'},
                {'type': 'click'},
                {'type': 'type', 'text': 'd'}
            ]
        }
        
        self.engine.load_script(script)
        batched = self.engine.batch_actions()
        
        # Should batch consecutive type actions
        self.assertEqual(len(batched), 3)  # [type batch, click, type]
        self.assertEqual(batched[0]['text'], 'abc')
        
    def test_parallel_execution(self):
        """Test parallel action execution where possible"""
        script = {
            'name': 'parallel_test',
            'actions': [
                {'type': 'screenshot', 'id': '1'},
                {'type': 'screenshot', 'id': '2'},
                {'type': 'click'},
                {'type': 'screenshot', 'id': '3'}
            ]
        }
        
        self.engine.load_script(script)
        
        # Identify parallelizable actions
        parallel_groups = self.engine.identify_parallel_actions()
        
        # First two screenshots can be parallel
        self.assertEqual(len(parallel_groups), 3)
        self.assertEqual(len(parallel_groups[0]), 2)  # Two screenshots
        
    def test_smart_wait_optimization(self):
        """Test smart wait time optimization"""
        self.engine.optimize_waits = True
        
        script = {
            'name': 'wait_test',
            'actions': [
                {'type': 'click'},
                {'type': 'wait', 'duration': 5.0},
                {'type': 'check_element', 'element': 'result'}
            ]
        }
        
        self.engine.load_script(script)
        
        # Mock element appears after 1 second
        mock_detector = Mock()
        mock_detector.detect.side_effect = [False, True]
        
        # Execute with optimized wait
        start_time = time.time()
        self.engine.execute_all(
            executor=Mock(),
            element_detector=mock_detector
        )
        elapsed = time.time() - start_time
        
        # Should complete faster than 5 seconds
        self.assertLess(elapsed, 3.0)
        
    def test_caching_optimization(self):
        """Test caching for repeated element detection"""
        self.engine.enable_cache = True
        
        script = {
            'name': 'cache_test',
            'actions': [
                {'type': 'click', 'element': 'button1'},
                {'type': 'wait', 'duration': 0.1},
                {'type': 'click', 'element': 'button1'},  # Same element
            ]
        }
        
        self.engine.load_script(script)
        
        mock_detector = Mock()
        mock_detector.detect.return_value = {'position': (100, 100)}
        
        self.engine.execute_all(
            executor=Mock(),
            element_detector=mock_detector
        )
        
        # Should only detect once due to caching
        self.assertEqual(mock_detector.detect.call_count, 1)


if __name__ == '__main__':
    # Run tests with coverage
    unittest.main(verbosity=2)