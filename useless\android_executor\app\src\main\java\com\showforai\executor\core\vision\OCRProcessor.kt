package com.showforai.executor.core.vision

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Rect
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * OCR处理器
 * 
 * 使用Google ML Kit进行文本识别：
 * - 图像文本识别
 * - 文本位置定位
 * - 置信度评估
 * - 文本过滤和匹配
 */
@Singleton
class OCRProcessor @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val MIN_CONFIDENCE = 0.7f
        private const val MIN_TEXT_LENGTH = 1
    }
    
    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    
    /**
     * 识别图像中的文本
     * @param bitmap 输入图像
     * @return OCR识别结果
     */
    suspend fun recognizeText(bitmap: Bitmap): OCRResult {
        return try {
            val inputImage = InputImage.fromBitmap(bitmap, 0)
            val visionText = textRecognizer.process(inputImage).await()
            
            val textBlocks = mutableListOf<TextBlock>()
            
            for (block in visionText.textBlocks) {
                val blockText = block.text
                val blockBounds = block.boundingBox
                val blockConfidence = block.confidence ?: 0f
                
                if (blockBounds != null && blockText.length >= MIN_TEXT_LENGTH) {
                    textBlocks.add(
                        TextBlock(
                            text = blockText,
                            boundingBox = blockBounds,
                            confidence = blockConfidence
                        )
                    )
                    
                    // 处理行级别的文本
                    for (line in block.lines) {
                        val lineText = line.text
                        val lineBounds = line.boundingBox
                        val lineConfidence = line.confidence ?: 0f
                        
                        if (lineBounds != null && lineText.length >= MIN_TEXT_LENGTH) {
                            textBlocks.add(
                                TextBlock(
                                    text = lineText,
                                    boundingBox = lineBounds,
                                    confidence = lineConfidence
                                )
                            )
                        }
                    }
                }
            }
            
            OCRResult(
                success = textBlocks.isNotEmpty(),
                textBlocks = textBlocks,
                fullText = visionText.text
            )
            
        } catch (e: Exception) {
            Timber.e(e, "OCR text recognition failed")
            OCRResult(
                success = false,
                textBlocks = emptyList(),
                fullText = ""
            )
        }
    }
    
    /**
     * 查找包含指定文本的区域
     * @param bitmap 输入图像
     * @param targetText 目标文本
     * @param exactMatch 是否精确匹配
     * @return 文本查找结果
     */
    suspend fun findText(
        bitmap: Bitmap,
        targetText: String,
        exactMatch: Boolean = false
    ): TextFindResult {
        val ocrResult = recognizeText(bitmap)
        
        if (!ocrResult.success) {
            return TextFindResult(
                found = false,
                confidence = 0f,
                location = android.graphics.Point(0, 0),
                boundingBox = Rect(),
                matchedText = ""
            )
        }
        
        // 查找最佳匹配
        var bestMatch: TextBlock? = null
        var bestScore = 0f
        
        for (textBlock in ocrResult.textBlocks) {
            val score = calculateTextMatchScore(textBlock.text, targetText, exactMatch)
            
            if (score > bestScore && score >= MIN_CONFIDENCE) {
                bestScore = score
                bestMatch = textBlock
            }
        }
        
        return if (bestMatch != null) {
            val centerX = bestMatch.boundingBox.centerX()
            val centerY = bestMatch.boundingBox.centerY()
            
            TextFindResult(
                found = true,
                confidence = bestScore,
                location = android.graphics.Point(centerX, centerY),
                boundingBox = bestMatch.boundingBox,
                matchedText = bestMatch.text
            )
        } else {
            TextFindResult(
                found = false,
                confidence = 0f,
                location = android.graphics.Point(0, 0),
                boundingBox = Rect(),
                matchedText = ""
            )
        }
    }
    
    /**
     * 在指定区域内查找文本
     * @param bitmap 输入图像
     * @param targetText 目标文本
     * @param searchArea 搜索区域
     * @param exactMatch 是否精确匹配
     * @return 文本查找结果
     */
    suspend fun findTextInArea(
        bitmap: Bitmap,
        targetText: String,
        searchArea: Rect,
        exactMatch: Boolean = false
    ): TextFindResult {
        // 裁剪搜索区域
        val croppedBitmap = try {
            Bitmap.createBitmap(
                bitmap,
                searchArea.left,
                searchArea.top,
                searchArea.width(),
                searchArea.height()
            )
        } catch (e: Exception) {
            Timber.e(e, "Failed to crop search area")
            return TextFindResult(
                found = false,
                confidence = 0f,
                location = android.graphics.Point(0, 0),
                boundingBox = Rect(),
                matchedText = ""
            )
        }
        
        // 在裁剪区域内查找文本
        val result = findText(croppedBitmap, targetText, exactMatch)
        
        // 调整坐标到原图像坐标系
        if (result.found) {
            val adjustedLocation = android.graphics.Point(
                result.location.x + searchArea.left,
                result.location.y + searchArea.top
            )
            
            val adjustedBoundingBox = Rect(
                result.boundingBox.left + searchArea.left,
                result.boundingBox.top + searchArea.top,
                result.boundingBox.right + searchArea.left,
                result.boundingBox.bottom + searchArea.top
            )
            
            return result.copy(
                location = adjustedLocation,
                boundingBox = adjustedBoundingBox
            )
        }
        
        croppedBitmap.recycle()
        return result
    }
    
    /**
     * 计算文本匹配分数
     */
    private fun calculateTextMatchScore(
        recognizedText: String,
        targetText: String,
        exactMatch: Boolean
    ): Float {
        if (exactMatch) {
            return if (recognizedText.equals(targetText, ignoreCase = true)) 1.0f else 0.0f
        }
        
        // 模糊匹配算法
        val recognized = recognizedText.lowercase().trim()
        val target = targetText.lowercase().trim()
        
        // 完全匹配
        if (recognized == target) {
            return 1.0f
        }
        
        // 包含匹配
        if (recognized.contains(target) || target.contains(recognized)) {
            val longer = if (recognized.length > target.length) recognized else target
            val shorter = if (recognized.length <= target.length) recognized else target
            return shorter.length.toFloat() / longer.length
        }
        
        // 编辑距离匹配
        val editDistance = calculateEditDistance(recognized, target)
        val maxLength = maxOf(recognized.length, target.length)
        
        if (maxLength == 0) return 0.0f
        
        val similarity = 1.0f - (editDistance.toFloat() / maxLength)
        return if (similarity > 0.6f) similarity else 0.0f
    }
    
    /**
     * 计算编辑距离（Levenshtein距离）
     */
    private fun calculateEditDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }
        
        for (i in 0..s1.length) {
            dp[i][0] = i
        }
        
        for (j in 0..s2.length) {
            dp[0][j] = j
        }
        
        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // 删除
                    dp[i][j - 1] + 1,      // 插入
                    dp[i - 1][j - 1] + cost // 替换
                )
            }
        }
        
        return dp[s1.length][s2.length]
    }
    
    /**
     * 获取图像中所有文本的边界框
     */
    suspend fun getAllTextBounds(bitmap: Bitmap): List<Rect> {
        val ocrResult = recognizeText(bitmap)
        return ocrResult.textBlocks.map { it.boundingBox }
    }
    
    /**
     * 检查图像中是否包含指定文本
     */
    suspend fun containsText(bitmap: Bitmap, targetText: String): Boolean {
        val result = findText(bitmap, targetText, exactMatch = false)
        return result.found
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            textRecognizer.close()
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup OCR processor")
        }
    }
}

/**
 * OCR识别结果
 */
data class OCRResult(
    val success: Boolean,
    val textBlocks: List<TextBlock>,
    val fullText: String
)

/**
 * 文本块
 */
data class TextBlock(
    val text: String,
    val boundingBox: Rect,
    val confidence: Float
)

/**
 * 文本查找结果
 */
data class TextFindResult(
    val found: Boolean,
    val confidence: Float,
    val location: android.graphics.Point,
    val boundingBox: Rect,
    val matchedText: String
)
