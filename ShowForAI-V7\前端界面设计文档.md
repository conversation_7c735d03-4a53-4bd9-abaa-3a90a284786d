# ShowForAI 前端界面设计文档 v2.0

## 设计哲学：物理分离原则

### 核心理念：分离关注点 (Separation of Concerns)
**"好的设计是消除特殊情况"** - 录制时的误操作是设计问题，不是用户问题

**物理分离的三大优势**：
1. **认知负载清零**: 用户永远知道自己在哪个界面
2. **操作安全性**: 录制时无法误触其他功能
3. **界面专注性**: 每个界面只解决一个核心问题

### 数据结构映射
```
录制界面 ← 录制状态 (recording state)
脚本管理界面 ← 脚本列表 + 执行状态 (scripts + execution state)
```

**消除复杂性**: 不再需要"当前是什么模式"的判断逻辑

## 技术架构

### 技术栈
```typescript
// 核心框架
Electron + React 18 + TypeScript
Ant Design Pro           // 企业级组件库
Zustand + Immer         // 轻量化状态管理
Tauri                   // Rust后端集成

// 新增依赖
react-beautiful-dnd     // 拖拽功能
framer-motion          // 动画效果
electron-store         // 持久化存储
```

### 项目结构
```
src/
├── app/
│   ├── RecordingApp.tsx      # 录制界面主组件
│   ├── ScriptManagerApp.tsx  # 脚本管理界面主组件
│   ├── Sidebar.tsx          # 高级侧边栏导航
│   └── MainWindow.tsx       # 主窗口容器
├── components/
│   ├── recording/           # 录制界面专用组件
│   │   ├── RecordButton.tsx
│   │   ├── NamingDialog.tsx
│   │   └── ProgressBar.tsx
│   ├── scripts/             # 脚本管理专用组件
│   │   ├── ScriptRow.tsx
│   │   ├── DragHandle.tsx
│   │   └── ColumnLayout.tsx
│   └── shared/              # 共享组件
│       ├── StatusIndicator.tsx
│       └── MinimizeToTray.tsx
├── store/
│   ├── recording-store.ts   # 录制状态
│   ├── script-store.ts      # 脚本管理状态
│   └── ui-store.ts          # 界面切换状态
└── types/
    ├── recording.ts
    ├── script.ts
    └── ui.ts
```

## 一、录制界面设计 (Recording Interface)

### 界面布局
```
┌─────────────────────────────────────────────────────┐
│                                                     │
│                                                     │
│                  [大红色录制按钮]                        │
│                    ●  开始录制                         │
│                                                     │
│                                                     │
│                                                     │
│                 (按F9停止录制)                          │
│                                                     │
│                                                     │
│                                                     │
│                                                     │
│                                                     │
└─────────────────────────────────────────────────────┘
```

### 录制状态设计
**点击录制按钮后**：
1. 按钮变为"录制中..."状态 (禁用)
2. F9提示更加醒目
3. **自动最小化到系统托盘**
4. 托盘图标变为红色录制状态

**按F9后的恢复流程**：
1. 从托盘自动恢复窗口
2. 显示命名对话框 (modal)
3. 等待用户操作

### 核心组件定义

#### RecordButton 组件
```typescript
interface RecordButtonProps {
  isRecording: boolean;
  onStartRecording: () => void;
}

function RecordButton({ isRecording, onStartRecording }: RecordButtonProps) {
  return (
    <motion.div
      className="record-button-container"
      initial={{ scale: 0.9 }}
      animate={{ scale: 1 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <Button
        type="primary"
        danger
        size="large"
        shape="circle"
        icon={<RecordingIcon />}
        loading={isRecording}
        onClick={onStartRecording}
        style={{
          width: '200px',
          height: '200px',
          fontSize: '24px',
          border: 'none',
          boxShadow: '0 8px 32px rgba(255, 77, 79, 0.3)'
        }}
        disabled={isRecording}
      >
        {isRecording ? '录制中...' : '开始录制'}
      </Button>
      
      {isRecording && (
        <motion.div 
          className="f9-hint"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          style={{ marginTop: '40px', fontSize: '18px', color: '#666' }}
        >
          按F9停止录制
        </motion.div>
      )}
    </motion.div>
  );
}
```

#### NamingDialog 组件
```typescript
interface NamingDialogProps {
  visible: boolean;
  onGenerate: (name: string, description: string) => void;
  onReRecord: () => void;
  onCancel: () => void;
}

function NamingDialog({ visible, onGenerate, onReRecord }: NamingDialogProps) {
  const { nextScriptNumber } = useAppStore(state => state.recording);
  const scriptName = `脚本${nextScriptNumber}`;  // 自动命名
  const [description, setDescription] = useState('');

  return (
    <Modal
      title="为脚本命名"
      open={visible}
      closable={false}
      maskClosable={false}
      width={600}
      footer={null}
    >
      <div className="naming-form">
        <div className="form-group">
          <label>脚本名称 *</label>
          <Input
            value={scriptName}
            disabled  // 自动命名，用户不可修改
            style={{ marginBottom: '16px' }}
          />
        </div>
        
        <div className="form-group">
          <label>详细描述</label>
          <TextArea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="描述这个脚本的功能和使用场景"
            rows={4}
            style={{ marginBottom: '24px' }}
          />
        </div>
        
        <div className="dialog-actions">
          <Button 
            onClick={onReRecord}
            style={{ marginRight: '12px' }}
          >
            重新录制
          </Button>
          <Button
            type="primary"
            onClick={() => onGenerate(scriptName, description)}
            disabled={!scriptName.trim()}
          >
            保存
          </Button>
        </div>
      </div>
    </Modal>
  );
}
```

#### ProgressBar 组件 (三阶段生成)
```typescript
interface ProgressBarProps {
  visible: boolean;
  onComplete: () => void;
}

function ProgressBar({ visible, onComplete }: ProgressBarProps) {
  const [currentStage, setCurrentStage] = useState(0);
  
  const stages = [
    { name: '分析录制数据', duration: 2000 },
    { name: '生成AI模板', duration: 3000 },
    { name: '保存脚本文件', duration: 1500 }
  ];

  useEffect(() => {
    if (!visible) return;
    
    let timer: NodeJS.Timeout;
    stages.forEach((stage, index) => {
      timer = setTimeout(() => {
        setCurrentStage(index + 1);
        if (index === stages.length - 1) {
          setTimeout(onComplete, 500);
        }
      }, stages.slice(0, index + 1).reduce((sum, s) => sum + s.duration, 0));
    });

    return () => clearTimeout(timer);
  }, [visible]);

  return (
    <Modal
      title="正在生成脚本"
      open={visible}
      closable={false}
      maskClosable={false}
      footer={null}
      width={500}
    >
      <div className="progress-container">
        {stages.map((stage, index) => (
          <div key={index} className="progress-stage">
            <div className="stage-indicator">
              {currentStage > index ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : currentStage === index ? (
                <LoadingOutlined />
              ) : (
                <div className="stage-dot" />
              )}
            </div>
            <span className={currentStage > index ? 'completed' : currentStage === index ? 'active' : 'pending'}>
              {stage.name}
            </span>
          </div>
        ))}
        
        <Progress
          percent={Math.min(100, (currentStage / stages.length) * 100)}
          showInfo={false}
          strokeColor="#1890ff"
        />
      </div>
    </Modal>
  );
}
```

### 录制状态管理
```typescript
interface RecordingState {
  isRecording: boolean;
  isGenerating: boolean;
  showNamingDialog: boolean;
  showProgress: boolean;
  recordedSteps: number;
  startTime: Date | null;
}

const useRecordingStore = create<RecordingState>((set, get) => ({
  isRecording: false,
  isGenerating: false,
  showNamingDialog: false,
  showProgress: false,
  recordedSteps: 0,
  startTime: null,

  startRecording: () => {
    set({ isRecording: true, startTime: new Date(), recordedSteps: 0 });
    // 自动最小化到托盘
    window.electronAPI?.minimizeToTray();
  },

  stopRecording: () => {
    set({ 
      isRecording: false, 
      showNamingDialog: true,
      startTime: null 
    });
    // 从托盘恢复窗口
    window.electronAPI?.restoreFromTray();
  },

  generateScript: (name: string, description: string) => {
    set({ 
      showNamingDialog: false, 
      showProgress: true, 
      isGenerating: true 
    });
  },

  completeGeneration: () => {
    set({ 
      showProgress: false, 
      isGenerating: false 
    });
  },

  reRecord: () => {
    set({ 
      showNamingDialog: false,
      isRecording: true,
      startTime: new Date(),
      recordedSteps: 0
    });
    window.electronAPI?.minimizeToTray();
  }
}));
```

## 二、脚本管理界面设计 (Script Management Interface)

### 界面布局 - 双栏系统
```
┌─────────────────────────────────────────────────────┐
│                                                     │
│  ⋮⋮  Script_A     |  登录脚本       循环: [5]    │ →
│                                                     │
│  ⋮⋮  Script_B     |  数据采集      循环: [∞]    │ → 
│                                                     │
│  ⋮⋮  Script_C     |  发送邮件      循环: [1]    │ →
│                                                     │
├─────────────────────────────────────────────────────┤
│           Active Mode (绿色主题)                        │
│  ⋮⋮  Script_D     登录脚本           循环: [3]        │
│  ⋮⋮  Script_E     数据处理           循环: [1]        │
│                                                     │
├─────────────────────────────────────────────────────┤
│          Auxiliary Mode (蓝色主题)                      │
│  ⋮⋮  Script_F     监控脚本           循环: [∞]        │
│                                                     │
└─────────────────────────────────────────────────────┘
```

### 脚本行设计
每一行脚本包含：
1. **拖拽手柄** (⋮⋮) - 左侧固定位置
2. **脚本名称** - 粗体显示
3. **描述文字** - 灰色小字
4. **循环次数设置** - 右侧对齐
5. **状态指示器** - 运行/停止/暂停

### 核心组件定义

#### ScriptRow 组件
```typescript
interface ScriptRowProps {
  script: Script;
  isDragging: boolean;
  mode: 'active' | 'auxiliary' | 'unassigned';
  onUpdateLoop: (scriptId: string, loopCount: number) => void;
  onExecute: (scriptId: string) => void;
  onEdit: (scriptId: string) => void;
  onDelete: (scriptId: string) => void;
}

function ScriptRow({ script, isDragging, mode, onUpdateLoop, onExecute }: ScriptRowProps) {
  const getModeStyle = () => {
    switch (mode) {
      case 'active':
        return { borderLeft: '4px solid #52c41a', backgroundColor: '#f6ffed' };
      case 'auxiliary':
        return { borderLeft: '4px solid #1890ff', backgroundColor: '#f0f9ff' };
      default:
        return { borderLeft: '4px solid #d9d9d9', backgroundColor: '#fafafa' };
    }
  };

  return (
    <div
      className={`script-row ${isDragging ? 'dragging' : ''}`}
      style={getModeStyle()}
    >
      <div className="drag-handle">
        <div className="grip-dots">
          <div className="dot" />
          <div className="dot" />
          <div className="dot" />
          <div className="dot" />
          <div className="dot" />
          <div className="dot" />
        </div>
      </div>

      <div className="script-info">
        <div className="script-name">{script.name}</div>
        <div className="script-description">{script.description}</div>
      </div>

      <div className="script-controls">
        <div className="loop-setting">
          <span>循环:</span>
          <InputNumber
            value={script.loopCount}
            onChange={(value) => onUpdateLoop(script.id, value || 1)}
            min={1}
            max={999}
            size="small"
            formatter={(value) => value === 999 ? '∞' : `${value}`}
            parser={(value) => value === '∞' ? 999 : parseInt(value || '1')}
          />
        </div>

        <div className="action-buttons">
          <Button
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => onExecute(script.id)}
            disabled={script.status === 'running'}
          >
            运行
          </Button>
          
          <Dropdown
            menu={{
              items: [
                { key: 'edit', label: '编辑脚本', icon: <EditOutlined /> },
                { key: 'duplicate', label: '复制脚本', icon: <CopyOutlined /> },
                { key: 'delete', label: '删除脚本', icon: <DeleteOutlined /> }
              ]
            }}
          >
            <Button size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </div>
      </div>

      {script.status === 'running' && (
        <div className="execution-indicator">
          <Progress
            type="line"
            percent={script.progress}
            size="small"
            status="active"
          />
          <span>第 {script.currentStep}/{script.totalSteps} 步</span>
        </div>
      )}
    </div>
  );
}
```

#### ColumnLayout 组件 (双栏拖拽)
```typescript
interface ColumnLayoutProps {
  scripts: Script[];
  onScriptMove: (scriptId: string, targetMode: 'active' | 'auxiliary' | 'unassigned') => void;
  onScriptReorder: (sourceIndex: number, targetIndex: number, mode: string) => void;
}

function ColumnLayout({ scripts, onScriptMove, onScriptReorder }: ColumnLayoutProps) {
  const unassignedScripts = scripts.filter(s => s.mode === 'unassigned');
  const activeScripts = scripts.filter(s => s.mode === 'active');
  const auxiliaryScripts = scripts.filter(s => s.mode === 'auxiliary');

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="column-layout">
        {/* 未分配脚本区域 */}
        <Droppable droppableId="unassigned">
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`unassigned-area ${snapshot.isDragOver ? 'drag-over' : ''}`}
            >
              <div className="area-header">
                <h3>脚本库</h3>
                <Button type="primary" icon={<PlusOutlined />}>
                  导入脚本
                </Button>
              </div>
              
              {unassignedScripts.map((script, index) => (
                <Draggable key={script.id} draggableId={script.id} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      <ScriptRow
                        script={script}
                        isDragging={snapshot.isDragging}
                        mode="unassigned"
                        onUpdateLoop={onUpdateLoop}
                        onExecute={onExecute}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>

        {/* Active Mode 列 */}
        <Droppable droppableId="active">
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`mode-column active-mode ${snapshot.isDragOver ? 'drag-over' : ''}`}
            >
              <div className="column-header">
                <div className="mode-indicator active" />
                <h3>Active Mode</h3>
                <span className="mode-description">主要执行任务</span>
                {activeScripts.some(s => s.status === 'running') && (
                  <Button size="small" danger>
                    全部停止
                  </Button>
                )}
              </div>
              
              {activeScripts.map((script, index) => (
                <Draggable key={script.id} draggableId={script.id} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      <ScriptRow
                        script={script}
                        isDragging={snapshot.isDragging}
                        mode="active"
                        onUpdateLoop={onUpdateLoop}
                        onExecute={onExecute}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>

        {/* Auxiliary Mode 列 */}
        <Droppable droppableId="auxiliary">
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`mode-column auxiliary-mode ${snapshot.isDragOver ? 'drag-over' : ''}`}
            >
              <div className="column-header">
                <div className="mode-indicator auxiliary" />
                <h3>Auxiliary Mode</h3>
                <span className="mode-description">辅助支持任务</span>
                {auxiliaryScripts.some(s => s.status === 'running') && (
                  <Button size="small" danger>
                    全部停止
                  </Button>
                )}
              </div>
              
              {auxiliaryScripts.map((script, index) => (
                <Draggable key={script.id} draggableId={script.id} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      <ScriptRow
                        script={script}
                        isDragging={snapshot.isDragging}
                        mode="auxiliary"
                        onUpdateLoop={onUpdateLoop}
                        onExecute={onExecute}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </div>
    </DragDropContext>
  );
}
```

### 脚本状态管理
```typescript
interface Script {
  id: string;
  name: string;
  description: string;
  mode: 'active' | 'auxiliary' | 'unassigned';
  loopCount: number;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error';
  progress: number;
  currentStep: number;
  totalSteps: number;
  createdAt: Date;
  lastExecuted?: Date;
}

interface ScriptState {
  scripts: Script[];
  executionQueue: string[];
  globalSettings: {
    maxConcurrent: number;
    autoRestart: boolean;
  };
}

const useScriptStore = create<ScriptState>((set, get) => ({
  scripts: [],
  executionQueue: [],
  globalSettings: {
    maxConcurrent: 3,
    autoRestart: false
  },

  moveScript: (scriptId: string, targetMode: 'active' | 'auxiliary' | 'unassigned') => {
    set(produce(state => {
      const script = state.scripts.find(s => s.id === scriptId);
      if (script) {
        script.mode = targetMode;
      }
    }));
  },

  reorderScripts: (sourceIndex: number, targetIndex: number, mode: string) => {
    set(produce(state => {
      const scriptsInMode = state.scripts.filter(s => s.mode === mode);
      const [removed] = scriptsInMode.splice(sourceIndex, 1);
      scriptsInMode.splice(targetIndex, 0, removed);
      
      // 重新排序整个数组
      const otherScripts = state.scripts.filter(s => s.mode !== mode);
      state.scripts = [...otherScripts, ...scriptsInMode];
    }));
  },

  executeScript: (scriptId: string) => {
    set(produce(state => {
      const script = state.scripts.find(s => s.id === scriptId);
      if (script) {
        script.status = 'running';
        script.progress = 0;
        script.lastExecuted = new Date();
      }
      state.executionQueue.push(scriptId);
    }));
  },

  updateScriptProgress: (scriptId: string, progress: number, currentStep: number) => {
    set(produce(state => {
      const script = state.scripts.find(s => s.id === scriptId);
      if (script) {
        script.progress = progress;
        script.currentStep = currentStep;
      }
    }));
  },

  completeScript: (scriptId: string) => {
    set(produce(state => {
      const script = state.scripts.find(s => s.id === scriptId);
      if (script) {
        script.status = 'completed';
        script.progress = 100;
      }
      state.executionQueue = state.executionQueue.filter(id => id !== scriptId);
    }));
  }
}));
```

## 三、高级侧边栏导航设计

### 设计理念
不是简单的标签页，而是**智能的界面切换器**：
- 显示每个界面的状态
- 提供快速预览信息
- 支持快捷键切换

### 侧边栏布局
```
┌─────────────┐
│             │
│  📹 录制      │ ← 当前界面
│  3 steps     │
│             │
├─────────────┤
│             │
│  📋 脚本管理   │
│  5 running   │
│             │
├─────────────┤
│             │
│  ⚙️ 设置      │
│             │
│             │
└─────────────┘
```

### Sidebar 组件
```typescript
interface SidebarProps {
  currentInterface: 'recording' | 'scripts' | 'settings';
  recordingStats: { steps: number; isRecording: boolean };
  scriptStats: { total: number; running: number; active: number; auxiliary: number };
  onSwitch: (interface: 'recording' | 'scripts' | 'settings') => void;
}

function Sidebar({ currentInterface, recordingStats, scriptStats, onSwitch }: SidebarProps) {
  return (
    <div className="advanced-sidebar">
      <div className="sidebar-nav">
        <motion.div
          className={`nav-item ${currentInterface === 'recording' ? 'active' : ''}`}
          onClick={() => onSwitch('recording')}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="nav-icon">
            {recordingStats.isRecording ? (
              <div className="recording-indicator" />
            ) : (
              <CameraOutlined />
            )}
          </div>
          <div className="nav-content">
            <h4>录制</h4>
            <span className="nav-stats">
              {recordingStats.isRecording ? '录制中...' : `${recordingStats.steps} steps`}
            </span>
          </div>
          {currentInterface === 'recording' && (
            <motion.div
              className="active-indicator"
              layoutId="sidebar-indicator"
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            />
          )}
        </motion.div>

        <motion.div
          className={`nav-item ${currentInterface === 'scripts' ? 'active' : ''}`}
          onClick={() => onSwitch('scripts')}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="nav-icon">
            <FileTextOutlined />
            {scriptStats.running > 0 && (
              <Badge count={scriptStats.running} size="small" />
            )}
          </div>
          <div className="nav-content">
            <h4>脚本管理</h4>
            <span className="nav-stats">
              {scriptStats.running > 0 
                ? `${scriptStats.running} 运行中` 
                : `${scriptStats.total} 脚本`
              }
            </span>
          </div>
          {currentInterface === 'scripts' && (
            <motion.div
              className="active-indicator"
              layoutId="sidebar-indicator"
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            />
          )}
        </motion.div>

        <motion.div
          className={`nav-item ${currentInterface === 'settings' ? 'active' : ''}`}
          onClick={() => onSwitch('settings')}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="nav-icon">
            <SettingOutlined />
          </div>
          <div className="nav-content">
            <h4>设置</h4>
            <span className="nav-stats">系统配置</span>
          </div>
          {currentInterface === 'settings' && (
            <motion.div
              className="active-indicator"
              layoutId="sidebar-indicator"
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            />
          )}
        </motion.div>
      </div>

      <div className="sidebar-footer">
        <div className="system-stats">
          <div className="stat-item">
            <span className="stat-label">内存</span>
            <span className="stat-value">256MB</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">状态</span>
            <span className={`stat-value ${scriptStats.running > 0 ? 'active' : 'idle'}`}>
              {scriptStats.running > 0 ? '运行中' : '空闲'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 四、主应用架构

### MainWindow 组件 (容器)
```typescript
interface MainWindowProps {}

function MainWindow() {
  const [currentInterface, setCurrentInterface] = useState<'recording' | 'scripts' | 'settings'>('recording');
  const recordingStats = useRecordingStore(state => ({ 
    steps: state.recordedSteps, 
    isRecording: state.isRecording 
  }));
  const scriptStats = useScriptStore(state => ({
    total: state.scripts.length,
    running: state.scripts.filter(s => s.status === 'running').length,
    active: state.scripts.filter(s => s.mode === 'active').length,
    auxiliary: state.scripts.filter(s => s.mode === 'auxiliary').length
  }));

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey) {
        switch (event.key) {
          case '1':
            setCurrentInterface('recording');
            break;
          case '2':
            setCurrentInterface('scripts');
            break;
          case '3':
            setCurrentInterface('settings');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const renderCurrentInterface = () => {
    switch (currentInterface) {
      case 'recording':
        return <RecordingApp />;
      case 'scripts':
        return <ScriptManagerApp />;
      case 'settings':
        return <SettingsApp />;
      default:
        return <RecordingApp />;
    }
  };

  return (
    <div className="main-window">
      <Sidebar
        currentInterface={currentInterface}
        recordingStats={recordingStats}
        scriptStats={scriptStats}
        onSwitch={setCurrentInterface}
      />
      
      <main className="interface-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentInterface}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="interface-content"
          >
            {renderCurrentInterface()}
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
}
```

## 五、用户交互流程

### 完整录制工作流
1. **启动**: 用户打开应用，默认显示录制界面
2. **录制**: 点击大红按钮开始录制，界面最小化到托盘
3. **停止**: 按F9停止录制，界面自动恢复
4. **命名**: 弹出命名对话框，填写脚本名称和描述
5. **生成**: 点击"生成脚本"，显示三阶段进度条
6. **完成**: 生成完成后自动切换到脚本管理界面

### 脚本管理工作流
1. **查看**: 在脚本管理界面查看所有脚本
2. **分类**: 将脚本拖拽到Active或Auxiliary模式列
3. **配置**: 设置循环次数和执行参数
4. **执行**: 点击运行按钮执行脚本
5. **监控**: 实时查看执行进度和状态

### 界面切换工作流
1. **快捷切换**: 使用Ctrl+1/2/3快速切换界面
2. **智能提示**: 侧边栏显示各界面的实时状态
3. **状态保持**: 切换界面不会丢失当前状态

## 六、视觉设计系统

### 色彩系统
```css
:root {
  /* 录制界面 */
  --recording-primary: #ff4d4f;    /* 录制红色 */
  --recording-bg: #ffffff;
  --recording-text: #262626;

  /* Active Mode */
  --active-primary: #52c41a;       /* 成功绿色 */
  --active-bg: #f6ffed;
  --active-border: #b7eb8f;

  /* Auxiliary Mode */
  --auxiliary-primary: #1890ff;    /* 主题蓝色 */
  --auxiliary-bg: #f0f9ff;
  --auxiliary-border: #91d5ff;

  /* 通用色彩 */
  --sidebar-bg: #fafafa;
  --sidebar-active: #e6f7ff;
  --text-primary: #262626;
  --text-secondary: #8c8c8c;
  --border-color: #f0f0f0;
}
```

### 动画效果
```css
/* 录制按钮脉冲效果 */
@keyframes recording-pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7); }
  70% { box-shadow: 0 0 0 20px rgba(255, 77, 79, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0); }
}

.record-button.recording {
  animation: recording-pulse 2s infinite;
}

/* 拖拽反馈 */
.script-row.dragging {
  transform: rotate(2deg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.column.drag-over {
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  border: 2px dashed #1890ff;
}

/* 界面切换动画 */
.interface-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 拖拽手柄设计
```css
.drag-handle {
  width: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.drag-handle:hover {
  opacity: 1;
}

.drag-handle:active {
  cursor: grabbing;
}

.grip-dots {
  display: grid;
  grid-template-columns: 4px 4px;
  grid-template-rows: 4px 4px 4px;
  gap: 2px;
}

.grip-dots .dot {
  width: 4px;
  height: 4px;
  background: #8c8c8c;
  border-radius: 50%;
}
```

## 七、技术实现细节

### 状态持久化
```typescript
// 使用 electron-store 持久化关键状态
interface PersistedState {
  lastInterface: 'recording' | 'scripts' | 'settings';
  scriptModes: Record<string, 'active' | 'auxiliary' | 'unassigned'>;
  scriptOrders: {
    active: string[];
    auxiliary: string[];
    unassigned: string[];
  };
  windowSettings: {
    width: number;
    height: number;
    x?: number;
    y?: number;
  };
}

const persistedStore = new Store<PersistedState>({
  defaults: {
    lastInterface: 'recording',
    scriptModes: {},
    scriptOrders: { active: [], auxiliary: [], unassigned: [] },
    windowSettings: { width: 1200, height: 800 }
  }
});
```

### 系统托盘集成
```typescript
// Electron 主进程
import { app, BrowserWindow, Tray, Menu, nativeImage } from 'electron';

class TrayManager {
  private tray: Tray | null = null;
  private mainWindow: BrowserWindow;

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow;
    this.createTray();
  }

  private createTray() {
    // 创建托盘图标
    const icon = nativeImage.createFromPath(path.join(__dirname, 'assets/tray-icon.png'));
    this.tray = new Tray(icon.resize({ width: 16, height: 16 }));

    // 设置上下文菜单
    const contextMenu = Menu.buildFromTemplate([
      { label: '显示窗口', click: () => this.showWindow() },
      { label: '开始录制', click: () => this.startRecording() },
      { type: 'separator' },
      { label: '退出', click: () => app.quit() }
    ]);

    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('ShowForAI');
    
    // 双击托盘图标恢复窗口
    this.tray.on('double-click', () => this.showWindow());
  }

  setRecordingMode(isRecording: boolean) {
    if (!this.tray) return;
    
    const iconName = isRecording ? 'tray-recording.png' : 'tray-icon.png';
    const icon = nativeImage.createFromPath(path.join(__dirname, 'assets', iconName));
    this.tray.setImage(icon.resize({ width: 16, height: 16 }));
    
    this.tray.setToolTip(isRecording ? 'ShowForAI - 录制中' : 'ShowForAI');
  }

  private showWindow() {
    this.mainWindow.show();
    this.mainWindow.focus();
  }

  private startRecording() {
    this.mainWindow.webContents.send('tray-start-recording');
  }
}
```

### 全局快捷键
```typescript
// Electron 主进程中注册全局快捷键
import { globalShortcut } from 'electron';

class ShortcutManager {
  constructor(private mainWindow: BrowserWindow) {
    this.registerShortcuts();
  }

  private registerShortcuts() {
    // F9 停止录制
    globalShortcut.register('F9', () => {
      this.mainWindow.webContents.send('global-shortcut', 'stop-recording');
    });

    // Ctrl+Shift+R 开始录制
    globalShortcut.register('CommandOrControl+Shift+R', () => {
      this.mainWindow.webContents.send('global-shortcut', 'start-recording');
    });

    // Ctrl+Shift+S 显示脚本管理
    globalShortcut.register('CommandOrControl+Shift+S', () => {
      this.mainWindow.webContents.send('global-shortcut', 'show-scripts');
      this.mainWindow.show();
      this.mainWindow.focus();
    });
  }

  unregisterAll() {
    globalShortcut.unregisterAll();
  }
}
```

## 八、性能优化策略

### React 组件优化
```typescript
// 使用 React.memo 优化重渲染
const ScriptRow = React.memo<ScriptRowProps>(({ script, isDragging, mode }) => {
  // 只有相关属性变化时才重新渲染
  return (
    // 组件内容
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.script.id === nextProps.script.id &&
    prevProps.script.status === nextProps.script.status &&
    prevProps.script.progress === nextProps.script.progress &&
    prevProps.isDragging === nextProps.isDragging &&
    prevProps.mode === nextProps.mode
  );
});

// 使用 useMemo 缓存计算结果
function ScriptManagerApp() {
  const { scripts } = useScriptStore();
  
  const scriptStats = useMemo(() => ({
    total: scripts.length,
    running: scripts.filter(s => s.status === 'running').length,
    active: scripts.filter(s => s.mode === 'active').length,
    auxiliary: scripts.filter(s => s.mode === 'auxiliary').length
  }), [scripts]);

  return (
    // 组件内容
  );
}
```

### 虚拟列表 (大量脚本优化)
```typescript
import { FixedSizeList as List } from 'react-window';

function VirtualizedScriptList({ scripts }: { scripts: Script[] }) {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <ScriptRow script={scripts[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={scripts.length}
      itemSize={80}
      overscanCount={5}
    >
      {Row}
    </List>
  );
}
```

## 九、可访问性设计

### 键盘导航
```typescript
// 键盘快捷键映射
const keyboardShortcuts = {
  'Ctrl+1': () => switchInterface('recording'),
  'Ctrl+2': () => switchInterface('scripts'),
  'Ctrl+3': () => switchInterface('settings'),
  'Ctrl+R': () => startRecording(),
  'Ctrl+Space': () => toggleSelectedScript(),
  'Ctrl+Enter': () => executeSelectedScript(),
  'Delete': () => deleteSelectedScript(),
  'F2': () => editSelectedScript()
};

// ARIA 标签和语义化
function ScriptRow({ script }: { script: Script }) {
  return (
    <div
      role="listitem"
      aria-label={`脚本 ${script.name}, ${script.description}, 状态: ${script.status}`}
      aria-selected={script.isSelected}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className="drag-handle" aria-label="拖拽手柄" role="button" tabIndex={-1}>
        {/* 拖拽手柄内容 */}
      </div>
      
      <button
        aria-label={`执行脚本 ${script.name}`}
        onClick={() => executeScript(script.id)}
        disabled={script.status === 'running'}
      >
        {script.status === 'running' ? '运行中...' : '运行'}
      </button>
    </div>
  );
}
```

### 高对比度主题支持
```css
/* 系统高对比度模式适配 */
@media (prefers-contrast: high) {
  :root {
    --recording-primary: #ff0000;
    --active-primary: #00aa00;
    --auxiliary-primary: #0066cc;
    --border-color: #000000;
    --text-primary: #000000;
    --text-secondary: #666666;
  }
}

/* 减少动画效果 */
@media (prefers-reduced-motion: reduce) {
  .interface-transition,
  .recording-pulse,
  .drag-handle {
    animation: none !important;
    transition: none !important;
  }
}
```

## 十、错误边界和异常处理

### React 错误边界
```typescript
class InterfaceErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('界面错误:', error, errorInfo);
    // 发送错误报告到日志系统
    window.electronAPI?.logError('界面渲染错误', { error: error.message, stack: error.stack });
  }

  render() {
    if (this.state.hasError) {
      return <this.props.fallback error={this.state.error!} />;
    }

    return this.props.children;
  }
}

// 错误回退界面
function ErrorFallback({ error }: { error: Error }) {
  return (
    <div className="error-boundary">
      <h2>界面加载失败</h2>
      <p>抱歉，当前界面遇到了问题。</p>
      <details>
        <summary>错误详情</summary>
        <pre>{error.message}</pre>
      </details>
      <Button onClick={() => window.location.reload()}>
        重新加载
      </Button>
    </div>
  );
}
```

### 网络和系统错误处理
```typescript
// 统一的错误处理机制
class ErrorHandler {
  static handle(error: Error, context: string) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    };

    // 根据错误类型决定处理方式
    if (error.name === 'NetworkError') {
      notification.error({
        message: '网络连接失败',
        description: '请检查网络连接后重试',
        duration: 5
      });
    } else if (error.name === 'FileSystemError') {
      notification.error({
        message: '文件系统错误',
        description: '无法访问脚本文件，请检查文件权限',
        duration: 5
      });
    } else {
      notification.error({
        message: '未知错误',
        description: error.message,
        duration: 5
      });
    }

    // 发送到日志系统
    window.electronAPI?.logError(context, errorInfo);
  }
}

// 在 Zustand store 中集成错误处理
const useScriptStore = create<ScriptState>((set, get) => ({
  // ... 其他状态

  executeScript: async (scriptId: string) => {
    try {
      set(produce(state => {
        const script = state.scripts.find(s => s.id === scriptId);
        if (script) script.status = 'running';
      }));

      await window.electronAPI?.executeScript(scriptId);
    } catch (error) {
      ErrorHandler.handle(error as Error, '脚本执行');
      
      set(produce(state => {
        const script = state.scripts.find(s => s.id === scriptId);
        if (script) {
          script.status = 'error';
          script.lastError = (error as Error).message;
        }
      }));
    }
  }
}));
```

## 总结

### 设计核心价值

**【关键洞察】**
- **物理分离消除认知负载**: 用户永远知道自己在哪个界面，不需要"当前是什么模式"的心理负担
- **录制安全性**: 录制时自动最小化，完全避免误操作
- **直观的模式选择**: 通过拖拽到物理位置来设置模式，比下拉菜单更直观

**【技术优势】**
1. **状态隔离**: 录制状态和脚本管理状态完全独立，降低耦合
2. **组件复用**: 共享组件库，但每个界面有专用组件
3. **性能优化**: 只渲染当前界面，减少内存占用

**【用户体验优势】**
1. **零学习成本**: 红色按钮=录制，拖拽=模式设置，直观明了
2. **错误预防**: 界面设计从根本上防止误操作
3. **专业感**: 高级侧边栏、流畅动画、智能状态指示

### 实施优先级

**Phase 1: 核心界面框架**
- 主窗口 + 侧边栏架构
- 录制界面基础功能
- 界面切换动画

**Phase 2: 录制完整流程**
- 大红按钮 + 自动最小化
- F9快捷键 + 托盘恢复
- 命名对话框 + 进度条

**Phase 3: 脚本管理系统**
- 双栏布局 (Active/Auxiliary)
- 拖拽功能 + 循环次数设置
- 脚本行设计 + 状态显示

**Phase 4: 执行引擎**
- Active 队列执行 (最小化界面)
- Auxiliary 后台监控 (保持界面)
- 冲突避让机制
- 进度反馈系统

## 十二、核心设计决策总结

### 关键设计点

1. **F9快捷键**
   - 固定使用F9停止录制
   - 全局注册，优先级最高
   - 简单明确，无备选方案

2. **自动命名简化**
   - 脚本自动命名为"脚本1"、"脚本2"...
   - 用户只需填写可选描述
   - 消除命名决策的认知负担

3. **执行模式本质区分**
   - **Active Mode**: 主动队列，执行时最小化，完成后停止
   - **Auxiliary Mode**: 后台守护，保持界面可见，持续监控
   - 冲突时 Auxiliary 主动避让，无需复杂调度

4. **零干扰录制**
   - 录制时界面完全消失
   - 没有脚本编辑功能（故意设计）
   - 强制用户一次录制正确

5. **物理分离原则**
   - 两个独立界面，不是模式切换
   - 通过位置（左右栏）区分模式
   - 消除所有特殊情况判断

### 最终产品形态

```
录制: 一个按钮 → 最小化 → F9结束 → 自动保存
管理: 拖拽分类 → 设置循环 → 一键执行/监控
```

**这是一个完全基于"好品味"设计原则的前端架构** - 简单、直观、无特殊情况。每个界面都专注于解决一个核心问题，用户永远知道自己在做什么。

"简单，但不残缺。" - Linus Torvalds