"""
Simple test for offline mode functionality.
"""
import sys
import socket
from unittest.mock import patch
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from showforai.sync.offline_manager import OfflineModeManager, get_offline_manager


def test_offline_manager():
    """Test offline manager functionality."""
    print("Testing Offline Manager...")
    
    # Test singleton
    manager1 = get_offline_manager()
    manager2 = get_offline_manager()
    assert manager1 is manager2, "Should return same instance"
    print("✓ Singleton pattern working")
    
    # Test network detection
    is_online = manager1.is_online()
    print(f"✓ Current network status: {'Online' if is_online else 'Offline'}")
    
    # Test permissions
    recording_allowed = manager1.is_recording_allowed()
    execution_allowed = manager1.is_execution_allowed()
    
    print(f"✓ Recording allowed: {recording_allowed}")
    print(f"✓ Execution allowed: {execution_allowed}")
    
    # Execution should always be allowed
    assert execution_allowed, "Execution should always be allowed"
    
    # Recording should match network status
    assert recording_allowed == is_online, "Recording should only be allowed when online"
    
    print("✓ Permission logic correct")


def test_offline_state():
    """Test offline state behavior."""
    print("\nTesting Offline State...")
    
    # Mock offline state
    with patch('socket.gethostbyname', side_effect=socket.gaierror):
        manager = OfflineModeManager()
        
        # Verify offline detection
        assert not manager.is_online(), "Should detect offline state"
        print("✓ Offline state detected")
        
        # Verify recording is disabled
        assert not manager.is_recording_allowed(), "Recording should be disabled offline"
        print("✓ Recording correctly disabled")
        
        # Verify execution is still allowed
        assert manager.is_execution_allowed(), "Execution should be allowed offline"
        print("✓ Execution still allowed")
        
        # Check messages
        offline_msg = manager.get_offline_message()
        assert "Recording is disabled" in offline_msg, "Should mention recording disabled"
        assert "execute existing scripts" in offline_msg, "Should mention execution allowed"
        print("✓ Offline message correct")
        
        recording_msg = manager.get_recording_disabled_message()
        assert "network connection" in recording_msg, "Should explain network requirement"
        assert "AI" in recording_msg, "Should mention AI services"
        print("✓ Recording disabled message correct")


def test_online_state():
    """Test online state behavior."""
    print("\nTesting Online State...")
    
    # Mock online state
    with patch('socket.gethostbyname', return_value='*******'):
        manager = OfflineModeManager()
        
        # Verify online detection
        assert manager.is_online(), "Should detect online state"
        print("✓ Online state detected")
        
        # Verify recording is allowed
        assert manager.is_recording_allowed(), "Recording should be allowed online"
        print("✓ Recording correctly enabled")
        
        # Verify execution is allowed
        assert manager.is_execution_allowed(), "Execution should be allowed online"
        print("✓ Execution allowed")
        
        # Check message
        online_msg = manager.get_offline_message()
        assert "all features enabled" in online_msg, "Should mention all features enabled"
        print("✓ Online message correct")


def test_status_callbacks():
    """Test network status change callbacks."""
    print("\nTesting Status Callbacks...")
    
    manager = OfflineModeManager()
    
    # Track callback invocations
    callback_results = []
    
    def test_callback(is_online):
        callback_results.append(is_online)
        print(f"  Callback invoked with status: {'Online' if is_online else 'Offline'}")
    
    # Add callback
    manager.add_status_callback(test_callback)
    print("✓ Callback added")
    
    # Simulate status change
    original_status = manager._is_online
    manager._is_online = not original_status
    manager._notify_status_change(not original_status)
    
    # Verify callback was invoked
    assert len(callback_results) == 1, "Callback should be invoked once"
    assert callback_results[0] == (not original_status), "Callback should receive correct status"
    print("✓ Callback invoked with correct status")
    
    # Remove callback
    manager.remove_status_callback(test_callback)
    print("✓ Callback removed")
    
    # Simulate another change
    manager._notify_status_change(original_status)
    
    # Verify callback was not invoked after removal
    assert len(callback_results) == 1, "Callback should not be invoked after removal"
    print("✓ Callback not invoked after removal")
    
    # Restore original status
    manager._is_online = original_status


def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Offline Mode Implementation")
    print("=" * 60)
    
    try:
        test_offline_manager()
        test_offline_state()
        test_online_state()
        test_status_callbacks()
        
        print("\n" + "=" * 60)
        print("All Offline Mode Tests PASSED! ✓")
        print("=" * 60)
        print("\nOffline mode has been successfully implemented:")
        print("• Removed all offline_queue functionality")
        print("• Recording is disabled when offline (requires AI)")
        print("• Script execution works offline")
        print("• Clear UI feedback for offline status")
        print("• Network status callbacks working")
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()