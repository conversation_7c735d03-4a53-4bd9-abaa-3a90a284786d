"""
临时修复方案 - 绕过网络检查
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("="*60)
print("ShowForAI V3 临时修复")
print("="*60)

# 修改offline_manager让它总是返回True
from showforai.sync import offline_manager

# 保存原始方法
original_is_recording_allowed = offline_manager.OfflineModeManager.is_recording_allowed

# 创建一个总是返回True的新方法
def always_allowed(self):
    print("[TEMP FIX] is_recording_allowed() 被调用 - 强制返回 True")
    return True

# 替换方法
offline_manager.OfflineModeManager.is_recording_allowed = always_allowed

print("\n✅ 已应用临时修复：")
print("  - is_recording_allowed() 现在总是返回 True")
print("  - 录制功能应该可以正常使用了")

# 运行录制器
print("\n启动录制器...")
from showforai.recorder.gui import main
main()