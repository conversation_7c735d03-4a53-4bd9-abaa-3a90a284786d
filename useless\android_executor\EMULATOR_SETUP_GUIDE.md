   # Android Studio 模拟器测试指南

## 📱 模拟器设置

### 1. 创建模拟器

#### 1.1 打开AVD Manager
1. 启动Android Studio
2. 点击 `Tools` → `AVD Manager`
3. 或者点击工具栏中的AVD Manager图标

#### 1.2 创建新的虚拟设备
1. 点击 `Create Virtual Device`
2. 选择设备类型：
   - **Phone**: Pixel 6 (推荐用于主要测试)
   - **Phone**: Pixel 3a (推荐用于低端设备测试)
   - **Tablet**: Pixel C (推荐用于平板测试)

#### 1.3 选择系统镜像
推荐下载以下系统镜像进行兼容性测试：
- **API 27 (Android 8.1)** - 最低支持版本测试
- **API 29 (Android 10.0)** - 主要目标版本
- **API 33 (Android 13.0)** - 最新版本测试

选择带有 `Google APIs` 的版本以获得完整功能。

#### 1.4 配置AVD
**基本配置**:
- AVD Name: `DSL_Executor_Test_API29`
- Startup orientation: Portrait
- Camera: Webcam0

**高级设置**:
- RAM: 4096 MB (推荐)
- VM heap: 512 MB
- Internal Storage: 6 GB
- SD card: 1 GB

**性能设置**:
- Graphics: Hardware - GLES 2.0
- Multi-Core CPU: 4 cores
- Enable Device Frame: ✓

### 2. 推荐的测试模拟器配置

#### 2.1 主要测试设备
```
名称: DSL_Executor_Main
设备: Pixel 6
API: 29 (Android 10.0)
RAM: 4096 MB
用途: 主要功能测试
```

#### 2.2 低端设备测试
```
名称: DSL_Executor_LowEnd
设备: Pixel 3a
API: 27 (Android 8.1)
RAM: 2048 MB
用途: 兼容性和性能测试
```

#### 2.3 高端设备测试
```
名称: DSL_Executor_HighEnd
设备: Pixel 6 Pro
API: 33 (Android 13.0)
RAM: 8192 MB
用途: 最新版本和性能测试
```

#### 2.4 平板测试
```
名称: DSL_Executor_Tablet
设备: Pixel C
API: 29 (Android 10.0)
RAM: 4096 MB
用途: 平板适配测试
```

## 🚀 启动和使用模拟器

### 1. 启动模拟器
1. 在AVD Manager中点击模拟器旁的 `▶️` 按钮
2. 等待模拟器启动（首次启动可能需要几分钟）
3. 模拟器启动后会显示Android桌面

### 2. 模拟器基本操作
- **返回**: 点击返回按钮或按 `Ctrl+Backspace`
- **主页**: 点击主页按钮或按 `Ctrl+H`
- **最近应用**: 点击最近应用按钮或按 `Ctrl+S`
- **音量**: 按 `Ctrl+Up/Down`
- **电源**: 按 `Ctrl+P`
- **旋转屏幕**: 按 `Ctrl+Left/Right`

### 3. 启用开发者选项
1. 打开 `Settings` → `About phone`
2. 连续点击 `Build number` 7次
3. 返回Settings，进入 `Developer options`
4. 启用以下选项：
   - USB debugging
   - Stay awake
   - Don't keep activities (用于测试内存管理)

## 🔧 项目导入和构建

### 1. 导入项目
1. 打开Android Studio
2. 选择 `Open an existing project`
3. 导航到 `android_executor` 目录并选择
4. 等待Gradle同步完成

### 2. 配置项目
1. 确保使用正确的SDK版本
2. 检查 `build.gradle` 文件中的依赖
3. 如果有错误，按照提示安装缺失的SDK组件

### 3. 构建项目
```bash
# 在Android Studio Terminal中执行
./gradlew clean
./gradlew assembleDebug
```

## 📱 安装和测试应用

### 1. 直接从Android Studio运行
1. 确保模拟器正在运行
2. 在Android Studio中点击 `Run` 按钮 (绿色三角形)
3. 选择目标模拟器
4. 应用将自动安装并启动

### 2. 手动安装APK
```bash
# 在Terminal中执行
adb devices  # 确认模拟器已连接
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 3. 启动应用
- 在模拟器中找到 "DSL Executor" 应用图标
- 点击启动应用

## 🧪 模拟器测试流程

### 1. 基础功能测试

#### 权限测试
1. 首次启动应用
2. 按照权限引导完成设置：
   - **存储权限**: 在设置中授予文件访问权限
   - **悬浮窗权限**: 在设置中允许显示在其他应用上层
   - **无障碍权限**: 在设置中启用DSL Executor服务
   - **屏幕捕捉权限**: 在弹出对话框中点击"立即开始"

#### 脚本加载测试
1. 点击"选择脚本"
2. 选择示例脚本或创建测试脚本
3. 验证脚本信息显示

#### 执行测试
1. 加载脚本后点击"开始执行"
2. 观察执行过程和日志
3. 测试暂停/恢复/停止功能

### 2. 模拟器特定测试

#### 屏幕旋转测试
1. 按 `Ctrl+Left` 旋转到横屏
2. 验证UI适配
3. 按 `Ctrl+Right` 旋转回竖屏

#### 内存压力测试
1. 在开发者选项中启用 "Don't keep activities"
2. 切换到其他应用再返回
3. 验证应用状态恢复

#### 网络状态测试
1. 在模拟器中关闭WiFi
2. 测试离线功能
3. 重新开启网络测试恢复

### 3. 性能监控

#### 使用Android Studio Profiler
1. 在Android Studio中点击 `View` → `Tool Windows` → `Profiler`
2. 选择运行中的应用
3. 监控以下指标：
   - **CPU使用率**
   - **内存使用**
   - **网络活动**
   - **电池消耗**

#### 内存泄漏检测
1. 在Profiler中选择Memory
2. 执行一些操作后点击垃圾回收
3. 观察内存使用变化

## 🔍 调试和日志

### 1. 查看应用日志
```bash
# 查看所有日志
adb logcat

# 只查看应用日志
adb logcat | grep "DSLExecutor"

# 清除日志后查看
adb logcat -c && adb logcat | grep "DSLExecutor"
```

### 2. 使用Android Studio Logcat
1. 在Android Studio底部点击 `Logcat` 标签
2. 选择设备和应用
3. 使用过滤器查看特定日志

### 3. 调试断点
1. 在代码中设置断点
2. 以Debug模式运行应用
3. 当执行到断点时检查变量状态

## 📊 自动化测试

### 1. 运行单元测试
```bash
./gradlew test
```

### 2. 运行UI测试
```bash
./gradlew connectedAndroidTest
```

### 3. 使用测试脚本
```bash
# 确保模拟器正在运行
./run_tests.sh --skip-build
```

## ⚠️ 模拟器限制和注意事项

### 限制
1. **传感器模拟**: 某些传感器功能可能不完全准确
2. **性能差异**: 模拟器性能可能与真实设备不同
3. **硬件功能**: 某些硬件特定功能无法测试

### 注意事项
1. **内存分配**: 给模拟器分配足够的RAM
2. **硬件加速**: 确保启用硬件加速以获得更好性能
3. **存储空间**: 确保有足够的磁盘空间
4. **系统资源**: 模拟器会消耗大量系统资源

### 性能优化
1. 关闭不必要的模拟器
2. 使用快照功能快速启动
3. 调整模拟器RAM分配
4. 启用硬件加速

## 🎯 测试重点

在模拟器上重点测试以下方面：
1. **UI适配**: 不同屏幕尺寸和方向
2. **权限管理**: 各种权限的申请和处理
3. **功能完整性**: 所有核心功能是否正常
4. **错误处理**: 各种异常情况的处理
5. **性能表现**: 内存和CPU使用情况

---

**使用建议**: 模拟器非常适合开发和初步测试，但在发布前建议在真实设备上进行最终验证。
