"""
测试点击录制按钮的逻辑
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_offline_manager_directly():
    """直接测试offline_manager"""
    print("="*60)
    print("直接测试 offline_manager")
    print("="*60)
    
    from showforai.sync.offline_manager import get_offline_manager
    
    manager = get_offline_manager()
    print(f"manager.is_online(): {manager.is_online()}")
    print(f"manager.is_recording_allowed(): {manager.is_recording_allowed()}")
    print(f"manager._is_online: {manager._is_online}")
    
    # 测试多次调用
    print("\n测试多次调用:")
    for i in range(3):
        result = manager.is_recording_allowed()
        print(f"  第{i+1}次调用 is_recording_allowed(): {result}")
    
    return manager

def test_gui_click():
    """测试GUI点击逻辑"""
    print("\n" + "="*60)
    print("测试GUI点击逻辑")
    print("="*60)
    
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from showforai.recorder.gui import RecorderWindow
    from showforai.config import Config
    from showforai.recorder.recorder import RecorderState
    
    app = QApplication([])
    config = Config()
    window = RecorderWindow(config)
    
    print(f"window.offline_manager: {window.offline_manager}")
    print(f"window.offline_manager.is_online(): {window.offline_manager.is_online()}")
    print(f"window.offline_manager.is_recording_allowed(): {window.offline_manager.is_recording_allowed()}")
    
    print(f"\nRecorder state: {window.recorder.get_state()}")
    print(f"RecorderState.IDLE: {RecorderState.IDLE}")
    print(f"Is IDLE? {window.recorder.get_state() == RecorderState.IDLE}")
    
    # 模拟点击逻辑（不实际显示弹窗）
    print("\n模拟点击逻辑:")
    if window.recorder.get_state() == RecorderState.IDLE:
        is_allowed = window.offline_manager.is_recording_allowed()
        print(f"  is_allowed = {is_allowed}")
        if not is_allowed:
            print("  将会显示离线消息弹窗")
        else:
            print("  将会开始录制")
    
    return window

def test_force_refresh():
    """强制刷新网络状态"""
    print("\n" + "="*60)
    print("强制刷新网络状态")
    print("="*60)
    
    from showforai.sync.offline_manager import get_offline_manager
    from datetime import datetime
    
    manager = get_offline_manager()
    
    print(f"刷新前:")
    print(f"  _is_online: {manager._is_online}")
    print(f"  _last_online_check: {manager._last_online_check}")
    
    # 强制刷新
    manager._last_online_check = datetime(2020, 1, 1)
    print(f"\n设置_last_online_check为2020年")
    
    result = manager.is_online()
    print(f"\n刷新后:")
    print(f"  is_online(): {result}")
    print(f"  _is_online: {manager._is_online}")
    print(f"  _last_online_check: {manager._last_online_check}")
    
    return manager

if __name__ == "__main__":
    # 启用所有日志
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    # 测试1：直接测试
    manager = test_offline_manager_directly()
    
    # 测试2：GUI点击
    window = test_gui_click()
    
    # 测试3：强制刷新
    test_force_refresh()
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)