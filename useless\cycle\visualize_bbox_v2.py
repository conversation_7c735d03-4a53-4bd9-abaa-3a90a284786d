"""
可视化Gemini返回的box_2d格式边界框
格式：[ymin, xmin, ymax, xmax] (0-1000归一化坐标)
"""

import json
import sys
from PIL import Image, ImageDraw, ImageFont
import os

def convert_box_2d_to_pixels(box_2d, img_width, img_height):
    """
    将Gemini的box_2d格式转换为像素坐标
    box_2d: [ymin, xmin, ymax, xmax] (0-1000归一化)
    返回: {x, y, width, height} 像素坐标
    """
    ymin, xmin, ymax, xmax = box_2d
    
    # 转换为实际像素坐标
    x = int(xmin * img_width / 1000)
    y = int(ymin * img_height / 1000)
    x2 = int(xmax * img_width / 1000)
    y2 = int(ymax * img_height / 1000)
    
    return {
        'x': x,
        'y': y,
        'width': x2 - x,
        'height': y2 - y,
        'x2': x2,
        'y2': y2
    }

def draw_bounding_box(image_path, bbox_data, output_path=None):
    """
    在图片上绘制边界框
    
    Args:
        image_path: 输入图片路径
        bbox_data: 包含box_2d的JSON数据
        output_path: 输出路径
    """
    # 加载图片
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    # 获取图片尺寸
    img_width, img_height = img.size
    
    # 解析数据
    if isinstance(bbox_data, str):
        data = json.loads(bbox_data)
    else:
        data = bbox_data
    
    # 提取box_2d
    box_2d = data['element']['box_2d']
    description = data['element'].get('description', '')
    text_content = data['element'].get('text_content', '')
    
    # 转换坐标
    coords = convert_box_2d_to_pixels(box_2d, img_width, img_height)
    
    print(f"原始box_2d: {box_2d}")
    print(f"图片尺寸: {img_width}x{img_height}")
    print(f"转换后像素坐标:")
    print(f"  左上角: ({coords['x']}, {coords['y']})")
    print(f"  右下角: ({coords['x2']}, {coords['y2']})")
    print(f"  尺寸: {coords['width']}x{coords['height']}")
    
    # 绘制边界框（红色，线宽3）
    draw.rectangle([coords['x'], coords['y'], coords['x2'], coords['y2']], 
                   outline='red', width=3)
    
    # 绘制中心点（绿色十字）
    center_x = coords['x'] + coords['width'] // 2
    center_y = coords['y'] + coords['height'] // 2
    cross_size = 10
    draw.line([center_x - cross_size, center_y, center_x + cross_size, center_y], 
              fill='green', width=2)
    draw.line([center_x, center_y - cross_size, center_x, center_y + cross_size], 
              fill='green', width=2)
    
    # 绘制图片中心点（黄色）- 用于对比
    img_center_x = img_width // 2
    img_center_y = img_height // 2
    draw.ellipse([img_center_x-5, img_center_y-5, img_center_x+5, img_center_y+5], 
                 fill='yellow', outline='orange', width=2)
    
    # 添加文字标注
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # 在边界框上方显示描述
    if description:
        text_y = max(coords['y'] - 25, 5)
        draw.text((coords['x'], text_y), description, fill='red', font=font)
    
    # 显示坐标信息
    info_text = f"Box: [{box_2d[0]}, {box_2d[1]}, {box_2d[2]}, {box_2d[3]}]\n"
    info_text += f"Pixels: ({coords['x']}, {coords['y']}) - ({coords['x2']}, {coords['y2']})"
    draw.text((10, 10), info_text, fill='black', font=font)
    
    # 检查元素是否包含中心点
    contains_center = (coords['x'] <= img_center_x <= coords['x2'] and 
                      coords['y'] <= img_center_y <= coords['y2'])
    
    status_text = "Contains center: " + ("YES" if contains_center else "NO")
    status_color = "green" if contains_center else "red"
    draw.text((10, 50), status_text, fill=status_color, font=font)
    
    # 保存结果
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = f"output_{base_name}_box2d.png"
    
    img.save(output_path)
    print(f"\n结果已保存到: {output_path}")
    print(f"元素是否包含中心点: {'是' if contains_center else '否'}")
    
    return output_path

def main():
    # 测试数据
    test_image = r"C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png"
    
    # 新的测试结果
    test_result = {
        "element": {
            "box_2d": [444, 601, 562, 748],
            "description": "icon in the taskbar",
            "text_content": ""
        }
    }
    
    # 绘制边界框
    draw_bounding_box(test_image, test_result)

if __name__ == "__main__":
    main()