<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <!-- Required for screen recording -->
  <key>com.apple.security.device.camera</key>
  <true/>
  
  <!-- Required for screen capture -->
  <key>com.apple.security.device.audio-input</key>
  <true/>
  
  <!-- Required for accessibility features -->
  <key>com.apple.security.automation.apple-events</key>
  <true/>
  
  <!-- Required for input monitoring -->
  <key>com.apple.security.device.usb</key>
  <true/>
  
  <!-- Network access for updates and API calls -->
  <key>com.apple.security.network.client</key>
  <true/>
  
  <!-- File system access -->
  <key>com.apple.security.files.user-selected.read-write</key>
  <true/>
  
  <!-- Downloads folder access -->
  <key>com.apple.security.files.downloads.read-write</key>
  <true/>
  
  <!-- Documents folder access -->
  <key>com.apple.security.files.user-selected.read-only</key>
  <true/>
  
  <!-- JIT compilation for better performance -->
  <key>com.apple.security.cs.allow-jit</key>
  <true/>
  
  <!-- Disable library validation for plugins -->
  <key>com.apple.security.cs.disable-library-validation</key>
  <true/>
  
  <!-- Runtime exceptions -->
  <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
  <true/>
  
  <!-- For system integration -->
  <key>com.apple.security.temporary-exception.apple-events</key>
  <array>
    <string>com.apple.systemevents</string>
    <string>com.apple.finder</string>
  </array>
</dict>
</plist>