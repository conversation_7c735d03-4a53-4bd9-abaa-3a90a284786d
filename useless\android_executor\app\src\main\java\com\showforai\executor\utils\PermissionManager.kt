package com.showforai.executor.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.media.projection.MediaProjectionManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.content.ContextCompat
import com.showforai.executor.data.models.PermissionStatus
import com.showforai.executor.services.AutomationAccessibilityService
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限管理器
 * 
 * 负责管理应用所需的各种权限：
 * - 存储权限
 * - 悬浮窗权限
 * - 屏幕捕捉权限
 * - 无障碍服务权限
 */
@Singleton
class PermissionManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        const val REQUEST_CODE_STORAGE = 1001
        const val REQUEST_CODE_OVERLAY = 1002
        const val REQUEST_CODE_MEDIA_PROJECTION = 1003
        const val REQUEST_CODE_ACCESSIBILITY = 1004
    }
    
    /**
     * 检查所有权限状态
     */
    fun checkAllPermissions(): PermissionStatus {
        return PermissionStatus(
            screenCapture = isMediaProjectionPermissionGranted(),
            accessibility = isAccessibilityServiceEnabled(),
            storage = isStoragePermissionGranted(),
            overlay = isOverlayPermissionGranted()
        )
    }
    
    /**
     * 检查存储权限
     */
    fun isStoragePermissionGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 使用 MANAGE_EXTERNAL_STORAGE
            Settings.canDrawOverlays(context) // 这里应该检查存储权限，但API复杂，暂时简化
        } else {
            // Android 10 及以下使用传统权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 检查悬浮窗权限
     */
    fun isOverlayPermissionGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Android 6.0 以下默认有权限
        }
    }
    
    /**
     * 检查屏幕捕捉权限（MediaProjection）
     * 注意：这个权限需要用户每次都授权，无法持久化检查
     */
    fun isMediaProjectionPermissionGranted(): Boolean {
        // MediaProjection权限无法预先检查，只能在请求时获得
        // 这里返回服务是否正在运行
        return try {
            val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            // 无法直接检查权限状态，返回false表示需要重新申请
            false
        } catch (e: Exception) {
            Timber.e(e, "Error checking MediaProjection permission")
            false
        }
    }
    
    /**
     * 检查无障碍服务是否启用
     */
    fun isAccessibilityServiceEnabled(): Boolean {
        return AutomationAccessibilityService.isServiceEnabled()
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 跳转到设置页面
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
                activity.startActivityForResult(intent, REQUEST_CODE_STORAGE)
            } catch (e: Exception) {
                Timber.e(e, "Failed to open storage permission settings")
                // 降级到通用设置页面
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
                activity.startActivity(intent)
            }
        } else {
            // Android 10 及以下使用运行时权限
            androidx.core.app.ActivityCompat.requestPermissions(
                activity,
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ),
                REQUEST_CODE_STORAGE
            )
        }
    }
    
    /**
     * 请求悬浮窗权限
     */
    fun requestOverlayPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:${context.packageName}")
            }
            activity.startActivityForResult(intent, REQUEST_CODE_OVERLAY)
        }
    }
    
    /**
     * 请求屏幕捕捉权限
     */
    fun requestMediaProjectionPermission(activity: Activity) {
        val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val intent = mediaProjectionManager.createScreenCaptureIntent()
        activity.startActivityForResult(intent, REQUEST_CODE_MEDIA_PROJECTION)
    }
    
    /**
     * 请求无障碍服务权限
     */
    fun requestAccessibilityPermission(activity: Activity) {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        activity.startActivityForResult(intent, REQUEST_CODE_ACCESSIBILITY)
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(activity: Activity) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.parse("package:${context.packageName}")
        }
        activity.startActivity(intent)
    }
    
    /**
     * 获取权限请求说明文本
     */
    fun getPermissionRationale(permission: String): String {
        return when (permission) {
            "storage" -> "应用需要存储权限来读取DSL脚本文件和保存执行日志"
            "overlay" -> "应用需要悬浮窗权限来在其他应用上方显示执行状态"
            "screen_capture" -> "应用需要屏幕捕捉权限来获取屏幕内容进行图像识别"
            "accessibility" -> "应用需要无障碍服务权限来执行自动化操作，如点击、滑动等"
            else -> "应用需要此权限来正常运行"
        }
    }
    
    /**
     * 检查权限请求结果
     */
    fun handlePermissionResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?
    ): Boolean {
        return when (requestCode) {
            REQUEST_CODE_STORAGE -> isStoragePermissionGranted()
            REQUEST_CODE_OVERLAY -> isOverlayPermissionGranted()
            REQUEST_CODE_MEDIA_PROJECTION -> {
                resultCode == Activity.RESULT_OK && data != null
            }
            REQUEST_CODE_ACCESSIBILITY -> isAccessibilityServiceEnabled()
            else -> false
        }
    }
    
    /**
     * 获取缺失的权限列表
     */
    fun getMissingPermissions(): List<String> {
        val missing = mutableListOf<String>()
        val status = checkAllPermissions()
        
        if (!status.storage) missing.add("storage")
        if (!status.overlay) missing.add("overlay")
        if (!status.screenCapture) missing.add("screen_capture")
        if (!status.accessibility) missing.add("accessibility")
        
        return missing
    }
    
    /**
     * 是否所有权限都已授予
     */
    fun areAllPermissionsGranted(): Boolean {
        return checkAllPermissions().allGranted
    }
}
