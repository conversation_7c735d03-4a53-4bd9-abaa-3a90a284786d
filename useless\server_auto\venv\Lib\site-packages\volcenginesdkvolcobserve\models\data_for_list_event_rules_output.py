# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListEventRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'contact_group_ids': 'list[str]',
        'contact_methods': 'list[str]',
        'created_at': 'int',
        'description': 'str',
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'enable_state': 'str',
        'endpoint': 'str',
        'event_bus_name': 'str',
        'event_type': 'list[str]',
        'filter_pattern': 'dict(str, object)',
        'level': 'str',
        'message_queue': 'MessageQueueForListEventRulesOutput',
        'region': 'str',
        'rule_id': 'str',
        'rule_name': 'str',
        'source': 'str',
        'tls_target': 'list[TLSTargetForListEventRulesOutput]',
        'updated_at': 'int',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'contact_group_ids': 'ContactGroupIds',
        'contact_methods': 'ContactMethods',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'enable_state': 'EnableState',
        'endpoint': 'Endpoint',
        'event_bus_name': 'EventBusName',
        'event_type': 'EventType',
        'filter_pattern': 'FilterPattern',
        'level': 'Level',
        'message_queue': 'MessageQueue',
        'region': 'Region',
        'rule_id': 'RuleId',
        'rule_name': 'RuleName',
        'source': 'Source',
        'tls_target': 'TLSTarget',
        'updated_at': 'UpdatedAt',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, account_id=None, contact_group_ids=None, contact_methods=None, created_at=None, description=None, effect_end_at=None, effect_start_at=None, enable_state=None, endpoint=None, event_bus_name=None, event_type=None, filter_pattern=None, level=None, message_queue=None, region=None, rule_id=None, rule_name=None, source=None, tls_target=None, updated_at=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """DataForListEventRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._contact_group_ids = None
        self._contact_methods = None
        self._created_at = None
        self._description = None
        self._effect_end_at = None
        self._effect_start_at = None
        self._enable_state = None
        self._endpoint = None
        self._event_bus_name = None
        self._event_type = None
        self._filter_pattern = None
        self._level = None
        self._message_queue = None
        self._region = None
        self._rule_id = None
        self._rule_name = None
        self._source = None
        self._tls_target = None
        self._updated_at = None
        self._webhook_ids = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if contact_methods is not None:
            self.contact_methods = contact_methods
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if effect_end_at is not None:
            self.effect_end_at = effect_end_at
        if effect_start_at is not None:
            self.effect_start_at = effect_start_at
        if enable_state is not None:
            self.enable_state = enable_state
        if endpoint is not None:
            self.endpoint = endpoint
        if event_bus_name is not None:
            self.event_bus_name = event_bus_name
        if event_type is not None:
            self.event_type = event_type
        if filter_pattern is not None:
            self.filter_pattern = filter_pattern
        if level is not None:
            self.level = level
        if message_queue is not None:
            self.message_queue = message_queue
        if region is not None:
            self.region = region
        if rule_id is not None:
            self.rule_id = rule_id
        if rule_name is not None:
            self.rule_name = rule_name
        if source is not None:
            self.source = source
        if tls_target is not None:
            self.tls_target = tls_target
        if updated_at is not None:
            self.updated_at = updated_at
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def account_id(self):
        """Gets the account_id of this DataForListEventRulesOutput.  # noqa: E501


        :return: The account_id of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListEventRulesOutput.


        :param account_id: The account_id of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this DataForListEventRulesOutput.  # noqa: E501


        :return: The contact_group_ids of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this DataForListEventRulesOutput.


        :param contact_group_ids: The contact_group_ids of this DataForListEventRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def contact_methods(self):
        """Gets the contact_methods of this DataForListEventRulesOutput.  # noqa: E501


        :return: The contact_methods of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_methods

    @contact_methods.setter
    def contact_methods(self, contact_methods):
        """Sets the contact_methods of this DataForListEventRulesOutput.


        :param contact_methods: The contact_methods of this DataForListEventRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._contact_methods = contact_methods

    @property
    def created_at(self):
        """Gets the created_at of this DataForListEventRulesOutput.  # noqa: E501


        :return: The created_at of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DataForListEventRulesOutput.


        :param created_at: The created_at of this DataForListEventRulesOutput.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this DataForListEventRulesOutput.  # noqa: E501


        :return: The description of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DataForListEventRulesOutput.


        :param description: The description of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this DataForListEventRulesOutput.  # noqa: E501


        :return: The effect_end_at of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this DataForListEventRulesOutput.


        :param effect_end_at: The effect_end_at of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this DataForListEventRulesOutput.  # noqa: E501


        :return: The effect_start_at of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this DataForListEventRulesOutput.


        :param effect_start_at: The effect_start_at of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._effect_start_at = effect_start_at

    @property
    def enable_state(self):
        """Gets the enable_state of this DataForListEventRulesOutput.  # noqa: E501


        :return: The enable_state of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_state

    @enable_state.setter
    def enable_state(self, enable_state):
        """Sets the enable_state of this DataForListEventRulesOutput.


        :param enable_state: The enable_state of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._enable_state = enable_state

    @property
    def endpoint(self):
        """Gets the endpoint of this DataForListEventRulesOutput.  # noqa: E501


        :return: The endpoint of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint

    @endpoint.setter
    def endpoint(self, endpoint):
        """Sets the endpoint of this DataForListEventRulesOutput.


        :param endpoint: The endpoint of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._endpoint = endpoint

    @property
    def event_bus_name(self):
        """Gets the event_bus_name of this DataForListEventRulesOutput.  # noqa: E501


        :return: The event_bus_name of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_bus_name

    @event_bus_name.setter
    def event_bus_name(self, event_bus_name):
        """Sets the event_bus_name of this DataForListEventRulesOutput.


        :param event_bus_name: The event_bus_name of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._event_bus_name = event_bus_name

    @property
    def event_type(self):
        """Gets the event_type of this DataForListEventRulesOutput.  # noqa: E501


        :return: The event_type of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this DataForListEventRulesOutput.


        :param event_type: The event_type of this DataForListEventRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._event_type = event_type

    @property
    def filter_pattern(self):
        """Gets the filter_pattern of this DataForListEventRulesOutput.  # noqa: E501


        :return: The filter_pattern of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: dict(str, object)
        """
        return self._filter_pattern

    @filter_pattern.setter
    def filter_pattern(self, filter_pattern):
        """Sets the filter_pattern of this DataForListEventRulesOutput.


        :param filter_pattern: The filter_pattern of this DataForListEventRulesOutput.  # noqa: E501
        :type: dict(str, object)
        """

        self._filter_pattern = filter_pattern

    @property
    def level(self):
        """Gets the level of this DataForListEventRulesOutput.  # noqa: E501


        :return: The level of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this DataForListEventRulesOutput.


        :param level: The level of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def message_queue(self):
        """Gets the message_queue of this DataForListEventRulesOutput.  # noqa: E501


        :return: The message_queue of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: MessageQueueForListEventRulesOutput
        """
        return self._message_queue

    @message_queue.setter
    def message_queue(self, message_queue):
        """Sets the message_queue of this DataForListEventRulesOutput.


        :param message_queue: The message_queue of this DataForListEventRulesOutput.  # noqa: E501
        :type: MessageQueueForListEventRulesOutput
        """

        self._message_queue = message_queue

    @property
    def region(self):
        """Gets the region of this DataForListEventRulesOutput.  # noqa: E501


        :return: The region of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListEventRulesOutput.


        :param region: The region of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def rule_id(self):
        """Gets the rule_id of this DataForListEventRulesOutput.  # noqa: E501


        :return: The rule_id of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this DataForListEventRulesOutput.


        :param rule_id: The rule_id of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def rule_name(self):
        """Gets the rule_name of this DataForListEventRulesOutput.  # noqa: E501


        :return: The rule_name of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this DataForListEventRulesOutput.


        :param rule_name: The rule_name of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    @property
    def source(self):
        """Gets the source of this DataForListEventRulesOutput.  # noqa: E501


        :return: The source of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this DataForListEventRulesOutput.


        :param source: The source of this DataForListEventRulesOutput.  # noqa: E501
        :type: str
        """

        self._source = source

    @property
    def tls_target(self):
        """Gets the tls_target of this DataForListEventRulesOutput.  # noqa: E501


        :return: The tls_target of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: list[TLSTargetForListEventRulesOutput]
        """
        return self._tls_target

    @tls_target.setter
    def tls_target(self, tls_target):
        """Sets the tls_target of this DataForListEventRulesOutput.


        :param tls_target: The tls_target of this DataForListEventRulesOutput.  # noqa: E501
        :type: list[TLSTargetForListEventRulesOutput]
        """

        self._tls_target = tls_target

    @property
    def updated_at(self):
        """Gets the updated_at of this DataForListEventRulesOutput.  # noqa: E501


        :return: The updated_at of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DataForListEventRulesOutput.


        :param updated_at: The updated_at of this DataForListEventRulesOutput.  # noqa: E501
        :type: int
        """

        self._updated_at = updated_at

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this DataForListEventRulesOutput.  # noqa: E501


        :return: The webhook_ids of this DataForListEventRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this DataForListEventRulesOutput.


        :param webhook_ids: The webhook_ids of this DataForListEventRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListEventRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListEventRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListEventRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
