# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListEventsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asc': 'bool',
        'end_time': 'int',
        'event_type': 'list[str]',
        'id': 'list[str]',
        'order_by': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'region': 'str',
        'source': 'list[str]',
        'start_time': 'int'
    }

    attribute_map = {
        'asc': 'Asc',
        'end_time': 'EndTime',
        'event_type': 'EventType',
        'id': 'ID',
        'order_by': 'OrderBy',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'region': 'Region',
        'source': 'Source',
        'start_time': 'StartTime'
    }

    def __init__(self, asc=None, end_time=None, event_type=None, id=None, order_by=None, page_number=None, page_size=None, region=None, source=None, start_time=None, _configuration=None):  # noqa: E501
        """ListEventsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asc = None
        self._end_time = None
        self._event_type = None
        self._id = None
        self._order_by = None
        self._page_number = None
        self._page_size = None
        self._region = None
        self._source = None
        self._start_time = None
        self.discriminator = None

        if asc is not None:
            self.asc = asc
        if end_time is not None:
            self.end_time = end_time
        if event_type is not None:
            self.event_type = event_type
        if id is not None:
            self.id = id
        if order_by is not None:
            self.order_by = order_by
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if region is not None:
            self.region = region
        if source is not None:
            self.source = source
        if start_time is not None:
            self.start_time = start_time

    @property
    def asc(self):
        """Gets the asc of this ListEventsRequest.  # noqa: E501


        :return: The asc of this ListEventsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._asc

    @asc.setter
    def asc(self, asc):
        """Sets the asc of this ListEventsRequest.


        :param asc: The asc of this ListEventsRequest.  # noqa: E501
        :type: bool
        """

        self._asc = asc

    @property
    def end_time(self):
        """Gets the end_time of this ListEventsRequest.  # noqa: E501


        :return: The end_time of this ListEventsRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ListEventsRequest.


        :param end_time: The end_time of this ListEventsRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def event_type(self):
        """Gets the event_type of this ListEventsRequest.  # noqa: E501


        :return: The event_type of this ListEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this ListEventsRequest.


        :param event_type: The event_type of this ListEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._event_type = event_type

    @property
    def id(self):
        """Gets the id of this ListEventsRequest.  # noqa: E501


        :return: The id of this ListEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ListEventsRequest.


        :param id: The id of this ListEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._id = id

    @property
    def order_by(self):
        """Gets the order_by of this ListEventsRequest.  # noqa: E501


        :return: The order_by of this ListEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this ListEventsRequest.


        :param order_by: The order_by of this ListEventsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["HappenTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                order_by not in allowed_values):
            raise ValueError(
                "Invalid value for `order_by` ({0}), must be one of {1}"  # noqa: E501
                .format(order_by, allowed_values)
            )

        self._order_by = order_by

    @property
    def page_number(self):
        """Gets the page_number of this ListEventsRequest.  # noqa: E501


        :return: The page_number of this ListEventsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListEventsRequest.


        :param page_number: The page_number of this ListEventsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListEventsRequest.  # noqa: E501


        :return: The page_size of this ListEventsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListEventsRequest.


        :param page_size: The page_size of this ListEventsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def region(self):
        """Gets the region of this ListEventsRequest.  # noqa: E501


        :return: The region of this ListEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListEventsRequest.


        :param region: The region of this ListEventsRequest.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def source(self):
        """Gets the source of this ListEventsRequest.  # noqa: E501


        :return: The source of this ListEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this ListEventsRequest.


        :param source: The source of this ListEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._source = source

    @property
    def start_time(self):
        """Gets the start_time of this ListEventsRequest.  # noqa: E501


        :return: The start_time of this ListEventsRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ListEventsRequest.


        :param start_time: The start_time of this ListEventsRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListEventsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListEventsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListEventsRequest):
            return True

        return self.to_dict() != other.to_dict()
