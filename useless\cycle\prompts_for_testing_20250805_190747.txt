
==================================================
提示词 A_precise_format
==================================================
Detect the UI element at the exact center of this image.

Return the bounding box in this exact format:
{
  "element": {
    "description": "brief description of the element",
    "bounding_box": {
      "ymin": top edge coordinate (0-1000),
      "xmin": left edge coordinate (0-1000),
      "ymax": bottom edge coordinate (0-1000),
      "xmax": right edge coordinate (0-1000)
    },
    "text_content": "visible text or empty string"
  }
}

Requirements:
- Coordinates must be normalized to 0-1000
- The element at the center is the clicked target
- Make the bounding box as tight as possible
- Use [ymin, xmin, ymax, xmax] order


==================================================
提示词 B_simple_array
==================================================
Find the UI element at the center of this screenshot.

Output format: [ymin, xmin, ymax, xmax, element_type, text_content]
- Coordinates normalized to 0-1000
- Tight bounding box around the center element
- Example: [245, 180, 290, 380, "button", "Submit"]


==================================================
提示词 C_center_anchored
==================================================
User clicked at the center of this image (500, 500 in 0-1000 coordinates).
Identify the clicked element and return its bounding box.

Format:
{
  "box_2d": [ymin, xmin, ymax, xmax],  // normalized 0-1000
  "label": "element description",
  "text": "text content if any"
}

The box should tightly fit the visual boundaries of the element.


==================================================
提示词 D_visual_boundary
==================================================
Detect the UI element at coordinates (500, 500) - the center of this 1000x1000 normalized image.

Requirements:
1. Find the element's visual boundaries including:
   - Background area
   - Border/shadow
   - Padding that's part of the clickable area
2. Return tight bounding box: [ymin, xmin, ymax, xmax]
3. All coordinates in 0-1000 range
4. Include element description and text

Output as JSON with box_2d array.


==================================================
提示词 E_wrong_order
==================================================
Find the UI element at the center of this screenshot.

Output format: [xmin, ymin, xmax, ymax, element_type, text_content]
- Coordinates normalized to 0-1000
- Note: Using different coordinate order for testing


==================================================
提示词 F_with_thinking
==================================================
Think step by step about the UI element at the center of this image.

First, analyze what type of element it might be.
Then, carefully determine its boundaries.
Finally, return the bounding box.

Format: [ymin, xmin, ymax, xmax] normalized to 0-1000

