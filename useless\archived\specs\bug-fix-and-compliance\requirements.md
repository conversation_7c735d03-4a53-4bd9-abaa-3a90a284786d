# Bug Fix and Product Compliance Requirements

## 1. Product Principles Core Summary

### 1.1 Image Recognition Core
- **Pure Image Recognition**: System must use ONLY image recognition for element location
- **Quality Thresholds**: Maintain high detection thresholds (0.85+), never compromise accuracy
- **Accept Failures**: Better to fail than to click wrong elements

### 1.2 Smart Wait Mechanism
- **Auxiliary Mode**: Infinite background wait for elements
- **Active Mode**: Use recorded intervals, then 1-second retries
- **Step Execution**: Each step must succeed before proceeding

### 1.3 Resolution Standardization
- **768×768 Standard**: ALL processing uses 768×768 resolution
- **BBOX Coordinates**: Based on 768×768 coordinate system
- **Client-side Cropping**: Server sends BBOX, client crops locally

### 1.4 Offline Mode
- **Recording Disabled**: MUST require network for AI recognition
- **Execution Allowed**: Scripts can run offline with cached data
- **Clear User Feedback**: Inform users why recording needs network

## 2. Current Compliance Analysis

### 2.1 Compliant Features ✅
- Resolution standardization implemented correctly
- CircularFrameBuffer (15 frames at 10 FPS) working
- Network check for recording implemented
- High-precision timestamps used throughout
- Smart wait strategies defined properly
- Multi-level image matching with proper thresholds

### 2.2 Non-Compliant Issues ❌
- TODO comments indicate incomplete features
- Missing error recovery in some scenarios
- Incomplete UI feedback implementation
- Share/marketplace features not fully integrated
- Some GUI dialogs show "TODO" messages
- Missing progress indicators in several places

## 3. Identified Bugs and Issues

### 3.1 Critical Bugs (P0)
1. **Share Module Integration**
   - api/adapter.py has TODO for share module implementation
   - Share functionality referenced but not fully connected

2. **GUI Progress Indicators**
   - enhanced_main_window.py missing progress display implementation
   - No visual feedback during long operations

3. **Script Management Features**
   - Batch rename shows "TODO" message instead of functioning
   - Permanent delete not implemented

### 3.2 Medium Priority Bugs (P1)
1. **Recording Status Display**
   - enhanced_main_window.py has TODO for actual recording status
   - Users can't see if recording is active

2. **Script Editor Actions**
   - Add/Edit action dialogs not implemented
   - Users can't modify scripts after creation

3. **Settings Dialog Missing**
   - main_window.py has TODO for settings dialog
   - No way to configure application settings

### 3.3 Low Priority Issues (P2)
1. **Documentation Links**
   - Help menu items not connected to actual docs
   - User guide incomplete

2. **Executor GUI Features**
   - Script card widgets not implemented
   - Auxiliary mode toggle not functional
   - Sync functionality incomplete

## 4. Feature Requirements

### 4.1 Must Fix (Product Principle Violations)
1. Complete network status UI indicators
2. Implement all progress feedback points
3. Fix recording status display
4. Complete offline mode user messaging

### 4.2 Should Fix (Functionality Gaps)
1. Implement share module integration
2. Complete script management dialogs
3. Add settings configuration UI
4. Implement script editor actions

### 4.3 Nice to Have (Enhancements)
1. Batch operations for scripts
2. Advanced filtering options
3. Keyboard shortcuts beyond F9
4. Export/Import functionality

## 5. Acceptance Criteria

### 5.1 Recording Module
- WHEN offline THEN recording button SHALL be disabled with tooltip explanation
- WHEN recording THEN status indicator SHALL show active state
- WHEN recording completes THEN user SHALL receive clear success/failure feedback

### 5.2 Execution Module  
- WHEN executing offline THEN cached scripts SHALL run without network
- WHEN element not found THEN system SHALL retry per smart wait rules
- WHEN threshold not met THEN execution SHALL fail cleanly with error message

### 5.3 UI Feedback
- WHEN operation takes >3 seconds THEN progress indicator SHALL appear
- WHEN AI processing THEN user SHALL see "正在识别界面元素..." message
- WHEN uploading THEN user SHALL see "正在上传录制数据..." message

### 5.4 Script Management
- WHEN user clicks rename THEN functional dialog SHALL appear
- WHEN user clicks delete THEN confirmation SHALL be required
- WHEN user clicks share THEN share dialog SHALL function properly

## 6. Testing Requirements

### 6.1 Unit Tests
- Verify all thresholds match product principles
- Test offline mode restrictions
- Validate 768×768 standardization

### 6.2 Integration Tests
- Test recording → upload → AI → download flow
- Test offline execution with cached scripts
- Test smart wait in both modes

### 6.3 UI Tests
- Verify all progress indicators appear
- Test all dialogs open without "TODO" messages
- Confirm offline mode messaging is clear

## 7. Success Metrics

- Zero TODO comments in production code
- All product principle requirements met
- All critical paths have error handling
- UI provides feedback for all operations >3 seconds
- Recording correctly disabled when offline
- Execution works reliably with cached scripts