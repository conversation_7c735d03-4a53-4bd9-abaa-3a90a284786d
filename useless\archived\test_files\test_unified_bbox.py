"""
Test script for unified BBOX processor.
Tests Task 5: Unify BBOX Cropping Flow
"""

import io
import sys
from pathlib import Path
from PIL import Image, ImageDraw
import numpy as np

# Add project to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from showforai.preprocessing.bbox_processor import UnifiedBboxProcessor, bbox_processor


def create_test_image(size=(768, 768), with_elements=True):
    """Create a test image with colored rectangles."""
    img = Image.new('RGB', size, color='white')
    
    if with_elements:
        draw = ImageDraw.Draw(img)
        # Draw some colored rectangles as "elements"
        draw.rectangle([100, 100, 200, 200], fill='red')
        draw.rectangle([300, 300, 450, 450], fill='blue')
        draw.rectangle([500, 150, 650, 300], fill='green')
        draw.rectangle([150, 400, 350, 600], fill='yellow')
    
    return img


def test_basic_cropping():
    """Test basic element cropping functionality."""
    print("\n=== Testing Basic Cropping ===")
    
    # Create test image
    img = create_test_image()
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_data = img_bytes.getvalue()
    
    # Define test BBOXes
    test_bboxes = [
        {'x': 100, 'y': 100, 'width': 100, 'height': 100},  # Red element
        {'x': 300, 'y': 300, 'width': 150, 'height': 150},  # Blue element
        {'x': 500, 'y': 150, 'width': 150, 'height': 150},  # Green element
    ]
    
    processor = UnifiedBboxProcessor(enable_caching=False)
    
    for i, bbox in enumerate(test_bboxes):
        cropped = processor.crop_element_768(img_data, bbox)
        assert cropped is not None, f"Failed to crop bbox {i}"
        
        # Verify cropped image size
        cropped_img = Image.open(io.BytesIO(cropped))
        assert cropped_img.size == (bbox['width'], bbox['height'])
        print(f"  ✓ Cropped element {i+1}: {bbox['width']}x{bbox['height']}")
    
    print("✓ Basic cropping test passed")


def test_coordinate_conversion():
    """Test coordinate conversion between resolutions."""
    print("\n=== Testing Coordinate Conversion ===")
    
    processor = UnifiedBboxProcessor()
    
    # Test BBOX conversion from original to 768x768
    original_bbox = {'x': 100, 'y': 100, 'width': 200, 'height': 200}
    original_res = (1920, 1080)
    
    # Convert to 768x768
    bbox_768 = processor.bbox_to_768(original_bbox, original_res)
    expected_x = int(100 * 768 / 1920)  # 40
    expected_y = int(100 * 768 / 1080)  # 71
    expected_w = int(200 * 768 / 1920)  # 80
    expected_h = int(200 * 768 / 1080)  # 142
    
    assert bbox_768['x'] == expected_x
    assert bbox_768['y'] == expected_y
    assert bbox_768['width'] == expected_w
    assert bbox_768['height'] == expected_h
    print(f"  ✓ Original {original_res} -> 768x768: {bbox_768}")
    
    # Convert back to original
    bbox_back = processor.bbox_from_768(bbox_768, original_res)
    # Due to integer rounding, values might be slightly different
    assert abs(bbox_back['x'] - original_bbox['x']) <= 2
    assert abs(bbox_back['y'] - original_bbox['y']) <= 2
    print(f"  ✓ 768x768 -> Original {original_res}: {bbox_back}")
    
    # Test click position conversion
    click_768 = processor.convert_click_to_768(960, 540, original_res)
    assert click_768['x'] == 384  # 960 * 768 / 1920
    assert click_768['y'] == 384  # 540 * 768 / 1080
    print(f"  ✓ Click conversion: (960, 540) -> {click_768}")
    
    print("✓ Coordinate conversion test passed")


def test_containment_rule():
    """Test BBOX containment rule enforcement."""
    print("\n=== Testing Containment Rule ===")
    
    processor = UnifiedBboxProcessor()
    
    # Test case 1: BBOX already contains click
    bbox = {'x': 100, 'y': 100, 'width': 200, 'height': 200}
    click_x, click_y = 150, 150
    
    adjusted = processor.ensure_bbox_contains_click(bbox, click_x, click_y)
    assert adjusted == bbox  # Should not change
    print("  ✓ BBOX already contains click - no adjustment needed")
    
    # Test case 2: Click is outside, needs adjustment
    bbox = {'x': 100, 'y': 100, 'width': 100, 'height': 100}
    click_x, click_y = 250, 250
    
    adjusted = processor.ensure_bbox_contains_click(bbox, click_x, click_y)
    assert processor.contains_point(adjusted, click_x, click_y)
    print(f"  ✓ BBOX adjusted to contain click: {bbox} -> {adjusted}")
    
    # Test case 3: Click near edge of image
    bbox = {'x': 700, 'y': 700, 'width': 50, 'height': 50}
    click_x, click_y = 760, 760
    
    adjusted = processor.ensure_bbox_contains_click(bbox, click_x, click_y)
    assert processor.contains_point(adjusted, click_x, click_y)
    assert adjusted['x'] + adjusted['width'] <= 768
    assert adjusted['y'] + adjusted['height'] <= 768
    print(f"  ✓ BBOX adjusted near edge: {bbox} -> {adjusted}")
    
    print("✓ Containment rule test passed")


def test_batch_processing():
    """Test batch element cropping."""
    print("\n=== Testing Batch Processing ===")
    
    # Create test image
    img = create_test_image()
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_data = img_bytes.getvalue()
    
    # Define multiple BBOXes
    bboxes = [
        {'x': 100, 'y': 100, 'width': 100, 'height': 100, 'id': 'elem1'},
        {'x': 300, 'y': 300, 'width': 150, 'height': 150, 'id': 'elem2'},
        {'x': 500, 'y': 150, 'width': 150, 'height': 150, 'id': 'elem3'},
        {'x': 150, 'y': 400, 'width': 200, 'height': 200, 'id': 'elem4'},
    ]
    
    processor = UnifiedBboxProcessor(enable_caching=True)
    
    # Batch crop
    results = processor.batch_crop_elements(img_data, bboxes)
    
    assert len(results) == len(bboxes)
    assert all(r is not None for r in results)
    print(f"  ✓ Batch cropped {len(results)} elements successfully")
    
    # Verify caching works
    cache_stats = processor.get_cache_stats()
    assert cache_stats['memory_cache_items'] > 0
    print(f"  ✓ Elements cached: {cache_stats['memory_cache_items']}")
    
    print("✓ Batch processing test passed")


def test_validation_and_adjustment():
    """Test BBOX validation and adjustment."""
    print("\n=== Testing Validation and Adjustment ===")
    
    processor = UnifiedBboxProcessor()
    
    # Test case 1: Valid BBOX
    bbox = {'x': 100, 'y': 100, 'width': 200, 'height': 200}
    validated = processor.validate_and_adjust_bbox(bbox)
    assert validated == bbox
    print("  ✓ Valid BBOX passes validation")
    
    # Test case 2: BBOX exceeds boundaries
    bbox = {'x': 700, 'y': 700, 'width': 100, 'height': 100}
    validated = processor.validate_and_adjust_bbox(bbox)
    assert validated['x'] + validated['width'] <= 768
    assert validated['y'] + validated['height'] <= 768
    print(f"  ✓ Out-of-bounds BBOX adjusted: {bbox} -> {validated}")
    
    # Test case 3: Negative coordinates
    bbox = {'x': -10, 'y': -10, 'width': 100, 'height': 100}
    validated = processor.validate_and_adjust_bbox(bbox)
    assert validated['x'] >= 0
    assert validated['y'] >= 0
    print(f"  ✓ Negative coordinates adjusted: {bbox} -> {validated}")
    
    # Test case 4: Too small BBOX
    bbox = {'x': 100, 'y': 100, 'width': 2, 'height': 2}
    validated = processor.validate_and_adjust_bbox(bbox)
    assert validated is None  # Should reject too small BBOX
    print("  ✓ Too small BBOX rejected")
    
    print("✓ Validation and adjustment test passed")


def test_caching():
    """Test element caching functionality."""
    print("\n=== Testing Caching ===")
    
    # Create processor with caching enabled
    processor = UnifiedBboxProcessor(enable_caching=True)
    
    # Create test image
    img = create_test_image()
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_data = img_bytes.getvalue()
    
    bbox = {'x': 100, 'y': 100, 'width': 100, 'height': 100}
    cache_key = processor.generate_cache_key(bbox, "test_image")
    
    # First crop - should cache
    result1 = processor.crop_element(img_data, bbox, cache_key=cache_key)
    assert result1 is not None
    print("  ✓ First crop successful")
    
    # Second crop - should hit cache
    result2 = processor.crop_element(img_data, bbox, cache_key=cache_key)
    assert result2 is not None
    assert result2 == result1  # Should be same data from cache
    print("  ✓ Cache hit on second request")
    
    # Check cache stats
    stats = processor.get_cache_stats()
    assert stats['memory_cache_items'] > 0
    print(f"  ✓ Cache stats: {stats['memory_cache_items']} items in memory")
    
    # Clear cache
    processor.clear_cache()
    stats = processor.get_cache_stats()
    assert stats['memory_cache_items'] == 0
    print("  ✓ Cache cleared successfully")
    
    print("✓ Caching test passed")


def test_utility_functions():
    """Test utility functions."""
    print("\n=== Testing Utility Functions ===")
    
    processor = UnifiedBboxProcessor()
    
    # Test IoU calculation
    bbox1 = {'x': 100, 'y': 100, 'width': 100, 'height': 100}
    bbox2 = {'x': 150, 'y': 150, 'width': 100, 'height': 100}
    
    iou = processor.calculate_iou(bbox1, bbox2)
    # Intersection: 50x50 = 2500, Union: 10000 + 10000 - 2500 = 17500
    expected_iou = 2500 / 17500
    assert abs(iou - expected_iou) < 0.001
    print(f"  ✓ IoU calculation: {iou:.3f}")
    
    # Test center calculation
    center = processor.calculate_center(bbox1)
    assert center == (150, 150)
    print(f"  ✓ Center calculation: {center}")
    
    # Test BBOX merging
    bboxes = [
        {'x': 100, 'y': 100, 'width': 100, 'height': 100},
        {'x': 150, 'y': 150, 'width': 100, 'height': 100},
    ]
    merged = processor.merge_bboxes(bboxes)
    assert merged == {'x': 100, 'y': 100, 'width': 150, 'height': 150}
    print(f"  ✓ BBOX merging: {merged}")
    
    # Test BBOX expansion
    expanded = processor.expand_bbox(bbox1, 10)
    assert expanded == {'x': 90, 'y': 90, 'width': 120, 'height': 120}
    print(f"  ✓ BBOX expansion: {expanded}")
    
    print("✓ Utility functions test passed")


def test_standardization():
    """Test 768x768 standardization."""
    print("\n=== Testing 768x768 Standardization ===")
    
    # Create images of different sizes
    sizes = [(1920, 1080), (1024, 768), (768, 768), (640, 480)]
    
    processor = UnifiedBboxProcessor()
    
    for size in sizes:
        img = create_test_image(size)
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_data = img_bytes.getvalue()
        
        # Define BBOX in original resolution
        original_bbox = {
            'x': size[0] // 4,
            'y': size[1] // 4,
            'width': size[0] // 4,
            'height': size[1] // 4
        }
        
        # Crop with original resolution specified
        cropped = processor.crop_element(
            img_data, 
            original_bbox,
            original_resolution=size
        )
        
        assert cropped is not None
        
        # Verify cropped size matches expected
        cropped_img = Image.open(io.BytesIO(cropped))
        assert cropped_img.size == (original_bbox['width'], original_bbox['height'])
        
        print(f"  ✓ Processed {size} image with BBOX cropping")
    
    print("✓ 768x768 standardization test passed")


def main():
    """Run all unified BBOX processor tests."""
    print("=" * 60)
    print("UNIFIED BBOX PROCESSOR TEST SUITE")
    print("Testing Task 5: Unify BBOX Cropping Flow")
    print("=" * 60)
    
    try:
        test_basic_cropping()
        test_coordinate_conversion()
        test_containment_rule()
        test_batch_processing()
        test_validation_and_adjustment()
        test_caching()
        test_utility_functions()
        test_standardization()
        
        print("\n" + "=" * 60)
        print("ALL TESTS PASSED ✓")
        print("\nTask 5 Implementation Summary:")
        print("  ✓ 5.1 Created centralized BBOX processor")
        print("  ✓ 5.2 Replaced scattered cropping code")
        print("  ✓ 5.3 Ensured 768x768 standardization")
        print("\nKey Features Implemented:")
        print("  ✓ Unified cropping interface")
        print("  ✓ Coordinate conversion between resolutions")
        print("  ✓ Containment rule enforcement")
        print("  ✓ Batch processing support")
        print("  ✓ BBOX validation and adjustment")
        print("  ✓ Element caching with LRU")
        print("  ✓ Utility functions (IoU, merge, expand)")
        print("  ✓ 768x768 standardization throughout")
        print("=" * 60)
        
        return True
        
    except AssertionError as e:
        print(f"\n❌ Test assertion failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)