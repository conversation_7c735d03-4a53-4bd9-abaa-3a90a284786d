"""
网络诊断脚本 - 详细检查网络连接问题
"""

import sys
import os
import socket
import time
from datetime import datetime

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_direct_connection():
    """直接测试网络连接"""
    print("\n" + "="*60)
    print("直接网络连接测试")
    print("="*60)
    
    # 1. 测试AI服务器
    print("\n1. 测试AI服务器 (*************:8080)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(("*************", 8080))
        sock.close()
        if result == 0:
            print("   ✅ AI服务器连接成功")
            return True
        else:
            print(f"   ❌ AI服务器连接失败 (错误码: {result})")
    except Exception as e:
        print(f"   ❌ 异常: {type(e).__name__}: {e}")
    
    # 2. 测试百度DNS
    print("\n2. 测试百度DNS...")
    try:
        ip = socket.gethostbyname("www.baidu.com")
        print(f"   ✅ 百度DNS解析成功: {ip}")
        return True
    except Exception as e:
        print(f"   ❌ 百度DNS解析失败: {type(e).__name__}: {e}")
    
    return False

def test_offline_manager():
    """测试OfflineModeManager"""
    print("\n" + "="*60)
    print("测试 OfflineModeManager")
    print("="*60)
    
    from showforai.sync.offline_manager import OfflineModeManager
    
    # 创建新实例
    manager = OfflineModeManager()
    
    print("\n初始状态:")
    print(f"  _is_online: {manager._is_online}")
    print(f"  _last_online_check: {manager._last_online_check}")
    
    print("\n调用 is_online()...")
    result = manager.is_online()
    print(f"  返回值: {result}")
    print(f"  _is_online: {manager._is_online}")
    
    print("\n调用 is_recording_allowed()...")
    allowed = manager.is_recording_allowed()
    print(f"  返回值: {allowed}")
    
    print("\n强制刷新网络状态...")
    manager._last_online_check = datetime(2020, 1, 1)  # 设置为很久以前
    result2 = manager.is_online()
    print(f"  返回值: {result2}")
    print(f"  _is_online: {manager._is_online}")
    
    return result

def test_singleton():
    """测试单例模式"""
    print("\n" + "="*60)
    print("测试单例模式 get_offline_manager")
    print("="*60)
    
    from showforai.sync.offline_manager import get_offline_manager
    
    manager1 = get_offline_manager()
    manager2 = get_offline_manager()
    
    print(f"\nmanager1 id: {id(manager1)}")
    print(f"manager2 id: {id(manager2)}")
    print(f"是否同一实例: {manager1 is manager2}")
    
    print(f"\nmanager1.is_online(): {manager1.is_online()}")
    print(f"manager2.is_online(): {manager2.is_online()}")
    
    return manager1.is_online()

def main():
    print("ShowForAI V3 网络诊断")
    print("="*60)
    print(f"时间: {datetime.now()}")
    
    # 1. 直接测试
    direct_ok = test_direct_connection()
    
    # 2. 测试OfflineModeManager
    manager_ok = test_offline_manager()
    
    # 3. 测试单例
    singleton_ok = test_singleton()
    
    # 总结
    print("\n" + "="*60)
    print("诊断结果总结")
    print("="*60)
    print(f"直接连接测试: {'✅ 成功' if direct_ok else '❌ 失败'}")
    print(f"OfflineModeManager: {'✅ 在线' if manager_ok else '❌ 离线'}")
    print(f"单例模式: {'✅ 在线' if singleton_ok else '❌ 离线'}")
    
    if direct_ok and not manager_ok:
        print("\n⚠️ 问题: 网络可达但OfflineModeManager报告离线!")
        print("可能原因: 初始化时网络检查失败，需要等待刷新")
    elif not direct_ok:
        print("\n❌ 网络确实不可达，请检查网络连接")
    else:
        print("\n✅ 一切正常，录制功能应该可用")

if __name__ == "__main__":
    main()