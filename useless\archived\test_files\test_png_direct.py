"""
Direct test of PNG format changes without full imports.
"""

import os
import sys
import tempfile
from pathlib import Path
from PIL import Image
import io

# Test 1: Check that image_processor defaults are correct
print("Testing image_processor.py defaults...")

# Read the file and check defaults
with open('src/showforai/ai/image_processor.py', 'r') as f:
    content = f.read()
    
    # Check default formats are PNG
    assert "target_format: str = 'PNG'" in content, "Default format should be PNG"
    print("✓ Default format is PNG in image_processor.py")
    
    # Check no JPEG as default
    assert "target_format: str = 'JPEG'" not in content, "Should not have JPEG as default"
    print("✓ No JPEG as default format")

print("\nTesting screen_capture.py...")

# Read screen_capture.py
with open('src/showforai/recorder/screen_capture.py', 'r') as f:
    content = f.read()
    
    # Check that quality parameter is removed from __init__
    assert "quality: int = 85" not in content, "Quality parameter should be removed"
    print("✓ JPEG quality parameter removed from __init__")
    
    # Check save operations use PNG
    assert "img.save(filepath, 'PNG', optimize=True)" in content, "Should save as PNG"
    print("✓ Files are saved as PNG format")
    
    # Check _compress_image is deprecated
    assert "_compress_image is deprecated" in content, "Compress method should be deprecated"
    print("✓ _compress_image method is deprecated")

print("\nTesting image_optimizer.py defaults...")

# Read image_optimizer.py
with open('src/showforai/optimizer/image_optimizer.py', 'r') as f:
    content = f.read()
    
    # Check default format is PNG
    assert "format: str = 'PNG'" in content, "Default format should be PNG"
    print("✓ Default format is PNG in image_optimizer.py")
    
    # Check PNG handling comes first
    assert "if format == 'PNG':" in content, "PNG should be handled first"
    print("✓ PNG format is handled first")

print("\nTesting practical PNG generation...")

# Create a simple test using just the core functionality
sys.path.insert(0, 'src')

# Test ImageProcessor directly
from showforai.ai.image_processor import ImageProcessor

# Create test image
img = Image.new('RGB', (100, 100), color='blue')
buffer = io.BytesIO()
img.save(buffer, format='PNG')
test_data = buffer.getvalue()

processor = ImageProcessor()

# Test that default processing outputs PNG
processed = processor.process_screenshot(test_data)
result_img = Image.open(io.BytesIO(processed))
assert result_img.format == 'PNG', f"Expected PNG, got {result_img.format}"
print("✓ ImageProcessor outputs PNG by default")

# Test compression outputs PNG
compressed = processor.compress_image(test_data)
result_img = Image.open(io.BytesIO(compressed))
assert result_img.format == 'PNG', f"Expected PNG, got {result_img.format}"
print("✓ ImageProcessor compression outputs PNG")

# Test format conversion defaults to PNG
converted = processor.convert_format(test_data)
result_img = Image.open(io.BytesIO(converted))
assert result_img.format == 'PNG', f"Expected PNG, got {result_img.format}"
print("✓ ImageProcessor conversion defaults to PNG")

print("\n" + "=" * 50)
print("ALL TESTS PASSED! ✓")
print("PNG format unification is complete.")
print("=" * 50)