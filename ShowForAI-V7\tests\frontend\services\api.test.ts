import { describe, it, expect, vi, beforeEach } from 'vitest'
import { invoke } from '@tauri-apps/api/tauri'
import * as api from '@services/api'

vi.mock('@tauri-apps/api/tauri')

const mockInvoke = invoke as any

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Recording API', () => {
    it('starts recording successfully', async () => {
      const expectedResult = { success: true, recordingId: 'rec_123' }
      mockInvoke.mockResolvedValue(expectedResult)

      const result = await api.startRecording()

      expect(mockInvoke).toHaveBeenCalledWith('start_recording')
      expect(result).toEqual(expectedResult)
    })

    it('stops recording successfully', async () => {
      const expectedResult = { 
        success: true, 
        actions: [
          { type: 'click', x: 100, y: 200, timestamp: 1234567890 }
        ]
      }
      mockInvoke.mockResolvedValue(expectedResult)

      const result = await api.stopRecording()

      expect(mockInvoke).toHaveBeenCalledWith('stop_recording')
      expect(result).toEqual(expectedResult)
    })

    it('gets recording status', async () => {
      const expectedStatus = { isRecording: true, duration: 5000 }
      mockInvoke.mockResolvedValue(expectedStatus)

      const result = await api.getRecordingStatus()

      expect(mockInvoke).toHaveBeenCalledWith('get_recording_status')
      expect(result).toEqual(expectedStatus)
    })
  })

  describe('Execution API', () => {
    it('executes steps successfully', async () => {
      const steps = [
        { type: 'click', x: 100, y: 200 },
        { type: 'type', text: 'hello world' }
      ]
      const expectedResult = { success: true, executionId: 'exec_456' }
      mockInvoke.mockResolvedValue(expectedResult)

      const result = await api.executeSteps(steps)

      expect(mockInvoke).toHaveBeenCalledWith('execute_steps', { steps })
      expect(result).toEqual(expectedResult)
    })

    it('gets execution status', async () => {
      const executionId = 'exec_456'
      const expectedStatus = { 
        status: 'running', 
        currentStep: 2, 
        totalSteps: 5 
      }
      mockInvoke.mockResolvedValue(expectedStatus)

      const result = await api.getExecutionStatus(executionId)

      expect(mockInvoke).toHaveBeenCalledWith('get_execution_status', { executionId })
      expect(result).toEqual(expectedStatus)
    })

    it('cancels execution', async () => {
      const executionId = 'exec_456'
      const expectedResult = { success: true }
      mockInvoke.mockResolvedValue(expectedResult)

      const result = await api.cancelExecution(executionId)

      expect(mockInvoke).toHaveBeenCalledWith('cancel_execution', { executionId })
      expect(result).toEqual(expectedResult)
    })
  })

  describe('Workflow API', () => {
    it('saves workflow successfully', async () => {
      const workflow = {
        name: 'Test Workflow',
        description: 'A test workflow',
        steps: [
          { type: 'click', x: 100, y: 200 }
        ]
      }
      const expectedResult = { success: true, workflowId: 'wf_789' }
      mockInvoke.mockResolvedValue(expectedResult)

      const result = await api.saveWorkflow(workflow)

      expect(mockInvoke).toHaveBeenCalledWith('save_workflow', { workflow })
      expect(result).toEqual(expectedResult)
    })

    it('loads workflow successfully', async () => {
      const workflowId = 'wf_789'
      const expectedWorkflow = {
        id: workflowId,
        name: 'Test Workflow',
        description: 'A test workflow',
        steps: [
          { type: 'click', x: 100, y: 200 }
        ]
      }
      mockInvoke.mockResolvedValue(expectedWorkflow)

      const result = await api.loadWorkflow(workflowId)

      expect(mockInvoke).toHaveBeenCalledWith('load_workflow', { workflowId })
      expect(result).toEqual(expectedWorkflow)
    })

    it('lists workflows successfully', async () => {
      const expectedWorkflows = [
        { id: 'wf_1', name: 'Workflow 1', description: 'First workflow' },
        { id: 'wf_2', name: 'Workflow 2', description: 'Second workflow' }
      ]
      mockInvoke.mockResolvedValue(expectedWorkflows)

      const result = await api.listWorkflows()

      expect(mockInvoke).toHaveBeenCalledWith('list_workflows')
      expect(result).toEqual(expectedWorkflows)
    })

    it('deletes workflow successfully', async () => {
      const workflowId = 'wf_789'
      const expectedResult = { success: true }
      mockInvoke.mockResolvedValue(expectedResult)

      const result = await api.deleteWorkflow(workflowId)

      expect(mockInvoke).toHaveBeenCalledWith('delete_workflow', { workflowId })
      expect(result).toEqual(expectedResult)
    })
  })

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      const errorMessage = 'Network error'
      mockInvoke.mockRejectedValue(new Error(errorMessage))

      await expect(api.startRecording()).rejects.toThrow(errorMessage)
    })

    it('handles timeout errors', async () => {
      mockInvoke.mockRejectedValue(new Error('Request timeout'))

      await expect(api.executeSteps([])).rejects.toThrow('Request timeout')
    })
  })
})