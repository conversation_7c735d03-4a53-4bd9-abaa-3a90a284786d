package com.showforai.executor.core.actions

import android.content.Context
import com.showforai.executor.data.models.*
import com.showforai.executor.services.AutomationAccessibilityService
import com.showforai.executor.utils.ScreenUtils
import kotlinx.coroutines.delay
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 动作执行器管理器
 * 
 * 负责执行各种自动化操作：
 * - 点击、长按、双击
 * - 滑动、滚动
 * - 文本输入
 * - 手势操作
 */
@Singleton
class ActionExecutorManager @Inject constructor(
    private val context: Context,
    private val screenUtils: ScreenUtils
) {
    
    companion object {
        private const val DEFAULT_CLICK_DURATION = 50L
        private const val DEFAULT_LONG_PRESS_DURATION = 1000L
        private const val DEFAULT_SWIPE_DURATION = 500L
        private const val DEFAULT_SCROLL_DISTANCE = 500f
        private const val DOUBLE_CLICK_INTERVAL = 100L
    }
    
    /**
     * 初始化动作执行器
     */
    fun initialize() {
        // 检查无障碍服务是否可用
        if (!AutomationAccessibilityService.isServiceEnabled()) {
            Timber.w("Accessibility service is not enabled")
        }
    }
    
    /**
     * 执行点击操作
     */
    suspend fun click(x: Float, y: Float): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            val result = service.performClick(x, y)
            if (result) {
                Timber.d("Click executed at ($x, $y)")
            } else {
                Timber.w("Click failed at ($x, $y)")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "Click execution failed")
            false
        }
    }
    
    /**
     * 执行长按操作
     */
    suspend fun longPress(x: Float, y: Float, duration: Long = DEFAULT_LONG_PRESS_DURATION): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            val result = service.performLongPress(x, y, duration)
            if (result) {
                Timber.d("Long press executed at ($x, $y) for ${duration}ms")
            } else {
                Timber.w("Long press failed at ($x, $y)")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "Long press execution failed")
            false
        }
    }
    
    /**
     * 执行双击操作
     */
    suspend fun doubleClick(x: Float, y: Float): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            val result = service.performDoubleClick(x, y)
            if (result) {
                Timber.d("Double click executed at ($x, $y)")
            } else {
                Timber.w("Double click failed at ($x, $y)")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "Double click execution failed")
            false
        }
    }
    
    /**
     * 执行滑动操作
     */
    suspend fun swipe(
        startX: Float,
        startY: Float,
        endX: Float,
        endY: Float,
        duration: Long = DEFAULT_SWIPE_DURATION
    ): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            val result = service.performSwipe(startX, startY, endX, endY, duration)
            if (result) {
                Timber.d("Swipe executed from ($startX, $startY) to ($endX, $endY)")
            } else {
                Timber.w("Swipe failed from ($startX, $startY) to ($endX, $endY)")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "Swipe execution failed")
            false
        }
    }
    
    /**
     * 执行方向性滑动
     */
    suspend fun swipeDirection(
        centerX: Float,
        centerY: Float,
        direction: SwipeDirection,
        distance: Float = DEFAULT_SCROLL_DISTANCE,
        duration: Long = DEFAULT_SWIPE_DURATION
    ): Boolean {
        val (startX, startY, endX, endY) = when (direction) {
            SwipeDirection.UP -> {
                val startY = centerY + distance / 2
                val endY = centerY - distance / 2
                arrayOf(centerX, startY, centerX, endY)
            }
            SwipeDirection.DOWN -> {
                val startY = centerY - distance / 2
                val endY = centerY + distance / 2
                arrayOf(centerX, startY, centerX, endY)
            }
            SwipeDirection.LEFT -> {
                val startX = centerX + distance / 2
                val endX = centerX - distance / 2
                arrayOf(startX, centerY, endX, centerY)
            }
            SwipeDirection.RIGHT -> {
                val startX = centerX - distance / 2
                val endX = centerX + distance / 2
                arrayOf(startX, centerY, endX, centerY)
            }
        }
        
        return swipe(startX, startY, endX, endY, duration)
    }
    
    /**
     * 执行滚动操作
     */
    suspend fun scroll(
        centerX: Float,
        centerY: Float,
        direction: ScrollDirection,
        amount: Int = 1,
        distance: Float = DEFAULT_SCROLL_DISTANCE
    ): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            // 将ScrollDirection转换为AutomationAccessibilityService的ScrollDirection
            val serviceDirection = when (direction) {
                ScrollDirection.UP -> com.showforai.executor.services.ScrollDirection.UP
                ScrollDirection.DOWN -> com.showforai.executor.services.ScrollDirection.DOWN
                ScrollDirection.LEFT -> com.showforai.executor.services.ScrollDirection.LEFT
                ScrollDirection.RIGHT -> com.showforai.executor.services.ScrollDirection.RIGHT
            }
            
            var success = true
            repeat(amount) {
                val result = service.performScroll(centerX, centerY, serviceDirection, distance)
                if (!result) {
                    success = false
                }
                // 滚动之间的短暂延迟
                if (it < amount - 1) {
                    kotlinx.coroutines.runBlocking { delay(100) }
                }
            }
            
            if (success) {
                Timber.d("Scroll executed at ($centerX, $centerY) direction=$direction amount=$amount")
            } else {
                Timber.w("Scroll failed at ($centerX, $centerY)")
            }
            
            success
        } catch (e: Exception) {
            Timber.e(e, "Scroll execution failed")
            false
        }
    }
    
    /**
     * 执行文本输入
     */
    suspend fun inputText(text: String): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            val result = service.inputText(text)
            if (result) {
                Timber.d("Text input executed: $text")
            } else {
                Timber.w("Text input failed: $text")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "Text input execution failed")
            false
        }
    }
    
    /**
     * 执行缩放手势
     */
    suspend fun pinch(
        centerX: Float,
        centerY: Float,
        startDistance: Float,
        endDistance: Float,
        duration: Long = DEFAULT_SWIPE_DURATION
    ): Boolean {
        return try {
            val service = AutomationAccessibilityService.getInstance()
            if (service == null) {
                Timber.e("Accessibility service not available")
                return false
            }
            
            val result = service.performPinch(centerX, centerY, startDistance, endDistance, duration)
            if (result) {
                Timber.d("Pinch executed at ($centerX, $centerY)")
            } else {
                Timber.w("Pinch failed at ($centerX, $centerY)")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "Pinch execution failed")
            false
        }
    }
    
    /**
     * 执行放大手势
     */
    suspend fun zoomIn(
        centerX: Float,
        centerY: Float,
        scale: Float = 2.0f,
        duration: Long = DEFAULT_SWIPE_DURATION
    ): Boolean {
        val startDistance = 100f
        val endDistance = startDistance * scale
        return pinch(centerX, centerY, startDistance, endDistance, duration)
    }
    
    /**
     * 执行缩小手势
     */
    suspend fun zoomOut(
        centerX: Float,
        centerY: Float,
        scale: Float = 0.5f,
        duration: Long = DEFAULT_SWIPE_DURATION
    ): Boolean {
        val startDistance = 200f
        val endDistance = startDistance * scale
        return pinch(centerX, centerY, startDistance, endDistance, duration)
    }
    
    /**
     * 等待指定时间
     */
    suspend fun wait(milliseconds: Long): Boolean {
        return try {
            delay(milliseconds)
            Timber.d("Wait executed for ${milliseconds}ms")
            true
        } catch (e: Exception) {
            Timber.e(e, "Wait execution failed")
            false
        }
    }
    
    /**
     * 检查无障碍服务是否可用
     */
    fun isAccessibilityServiceAvailable(): Boolean {
        return AutomationAccessibilityService.isServiceEnabled()
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        // 清理相关资源
        Timber.d("ActionExecutorManager cleanup completed")
    }
}
