"""
Enhanced main window for ShowForAI V3 with auto-login and status display
Integrates automatic login, offline mode, and enhanced status bar
"""

import asyncio
from typing import Optional, Dict, Any
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QMenu, QMessageBox,
    QSplitter, QToolBar, QProgressBar
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, pyqtSlot, QTimer, QSettings
)
from PyQt6.QtGui import QAction, QIcon, QKeySequence

from showforai.api.adapter import create_api_adapter, APIResponse
from showforai.config import Config
from showforai.logger import get_logger
from showforai.auth import AutoLoginManager
from showforai.utils.network_checker import get_network_checker, is_online

# Import GUI components
from .dashboard import DashboardWidget
from .script_editor import ScriptEditorWidget
from .execution_monitor import ExecutionMonitorWidget
from .auxiliary_panel import AuxiliaryPanel
from .async_worker import AsyncWorker
from .status_bar import EnhancedStatusBar, ConnectionStatusWidget
from .auth_dialog import AuthDialog


class EnhancedMainWindow(QMainWindow):
    """Enhanced main application window with auto-login support"""
    
    def __init__(self, config: Optional[Config] = None):
        super().__init__()
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Initialize API adapter
        self.api_adapter = create_api_adapter(self.config)
        
        # Initialize auto-login manager
        self.auto_login_manager = AutoLoginManager(self.config)
        
        # Settings
        self.settings = QSettings("ShowForAI", "V3")
        
        # User state
        self.current_user = None
        self.is_authenticated = False
        
        # Network checker
        self.network_checker = get_network_checker()
        self.network_checker.add_status_callback(self.on_network_status_changed)
        
        # Initialize UI
        self.init_ui()
        
        # Start auto-login process
        self.attempt_auto_login()
        
        # Setup status updates
        self.setup_timers()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("ShowForAI V3 - AI智能自动化平台")
        self.setGeometry(100, 100, 1200, 800)
        
        # Apply dark theme
        self.setStyleSheet(self.get_dark_theme())
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        
        # Add tabs
        self.dashboard_widget = DashboardWidget(self.api_adapter)
        self.script_editor_widget = ScriptEditorWidget(self.api_adapter)
        
        # Create execution tab with splitter
        execution_tab = QWidget()
        execution_layout = QVBoxLayout(execution_tab)
        execution_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create splitter for execution modes
        execution_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Active mode (execution monitor)
        self.execution_monitor_widget = ExecutionMonitorWidget(self.api_adapter)
        execution_splitter.addWidget(self.execution_monitor_widget)
        
        # Auxiliary mode panel
        self.auxiliary_panel = AuxiliaryPanel(self.api_adapter)
        execution_splitter.addWidget(self.auxiliary_panel)
        
        # Set splitter sizes (60% for active mode, 40% for auxiliary mode)
        execution_splitter.setSizes([480, 320])
        
        execution_layout.addWidget(execution_splitter)
        
        self.tab_widget.addTab(self.dashboard_widget, "仪表板")
        self.tab_widget.addTab(self.script_editor_widget, "脚本编辑器")
        self.tab_widget.addTab(execution_tab, "执行监控")
        
        main_layout.addWidget(self.tab_widget)
        
        # Create enhanced status bar
        self.create_enhanced_status_bar()
        
        # Restore window state
        self.restore_state()
    
    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("文件(&F)")
        
        # Login/Logout action
        self.login_action = QAction("登录(&L)", self)
        self.login_action.setShortcut(QKeySequence("Ctrl+L"))
        self.login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(self.login_action)
        
        self.logout_action = QAction("登出(&O)", self)
        self.logout_action.triggered.connect(self.logout)
        self.logout_action.setVisible(False)
        file_menu.addAction(self.logout_action)
        
        file_menu.addSeparator()
        
        new_action = QAction("新建脚本(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_script)
        file_menu.addAction(new_action)
        
        open_action = QAction("打开脚本(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_script)
        file_menu.addAction(open_action)
        
        save_action = QAction("保存脚本(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_script)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        settings_action = QAction("设置(&P)", self)
        settings_action.setShortcut(QKeySequence("Ctrl+,"))
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Recording menu
        recording_menu = menubar.addMenu("录制(&R)")
        
        self.start_recording_action = QAction("开始录制(&S)", self)
        self.start_recording_action.setShortcut(QKeySequence("Ctrl+R"))
        self.start_recording_action.triggered.connect(self.start_recording)
        recording_menu.addAction(self.start_recording_action)
        
        self.stop_recording_action = QAction("停止录制(&T)", self)
        self.stop_recording_action.setShortcut(QKeySequence("Ctrl+Shift+R"))
        self.stop_recording_action.triggered.connect(self.stop_recording)
        self.stop_recording_action.setEnabled(False)
        recording_menu.addAction(self.stop_recording_action)
        
        recording_menu.addSeparator()
        
        clear_actions_action = QAction("清除操作(&C)", self)
        clear_actions_action.triggered.connect(self.clear_recording)
        recording_menu.addAction(clear_actions_action)
        
        # Execution menu
        execution_menu = menubar.addMenu("执行(&E)")
        
        self.run_script_action = QAction("运行脚本(&R)", self)
        self.run_script_action.setShortcut(QKeySequence("F5"))
        self.run_script_action.triggered.connect(self.run_script)
        execution_menu.addAction(self.run_script_action)
        
        self.stop_execution_action = QAction("停止执行(&S)", self)
        self.stop_execution_action.setShortcut(QKeySequence("Shift+F5"))
        self.stop_execution_action.triggered.connect(self.stop_execution)
        self.stop_execution_action.setEnabled(False)
        execution_menu.addAction(self.stop_execution_action)
        
        # Help menu
        help_menu = menubar.addMenu("帮助(&H)")
        
        docs_action = QAction("文档(&D)", self)
        docs_action.setShortcut(QKeySequence.StandardKey.HelpContents)
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)
        
        help_menu.addSeparator()
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """Create the toolbar"""
        toolbar = QToolBar("主工具栏")
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # Add login button
        toolbar.addAction(self.login_action)
        toolbar.addAction(self.logout_action)
        toolbar.addSeparator()
        
        # Add other actions
        toolbar.addAction(self.start_recording_action)
        toolbar.addAction(self.stop_recording_action)
        toolbar.addSeparator()
        toolbar.addAction(self.run_script_action)
        toolbar.addAction(self.stop_execution_action)
    
    def create_enhanced_status_bar(self):
        """Create enhanced status bar with connection status and user info"""
        # Use custom enhanced status bar widget
        self.status_bar = EnhancedStatusBar()
        self.status_bar.logout_requested.connect(self.logout)
        
        # Create container widget
        status_container = QWidget()
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.addWidget(self.status_bar)
        
        # Set as status bar
        self.setStatusBar(status_container)
        
        # Set initial status
        self.update_connection_status(ConnectionStatusWidget.NOT_LOGGED)
    
    def attempt_auto_login(self):
        """Attempt automatic login using cached credentials"""
        self.logger.info("Attempting auto-login...")
        
        # Start async auto-login
        worker = AsyncWorker(self.auto_login_manager.auto_login())
        worker.result.connect(self.on_auto_login_complete)
        worker.error.connect(self.on_auto_login_error)
        worker.start()
        
        # Show immediately in offline mode
        self.update_connection_status(ConnectionStatusWidget.CONNECTING)
    
    @pyqtSlot(object)
    def on_auto_login_complete(self, result):
        """Handle auto-login completion"""
        success, user_data = result
        
        if success and user_data:
            self.current_user = user_data
            self.is_authenticated = True
            
            # Update UI
            email = user_data.get('email')
            self.status_bar.set_user(email)
            
            # Update menu items
            self.login_action.setVisible(False)
            self.logout_action.setVisible(True)
            
            if user_data.get('offline_mode'):
                self.update_connection_status(ConnectionStatusWidget.OFFLINE)
                self.show_info("离线模式", "已使用缓存凭据登录，在离线模式下运行")
            else:
                self.update_connection_status(ConnectionStatusWidget.ONLINE)
            
            # Initialize modules
            self.initialize_modules()
            
            # Start connection monitor
            asyncio.create_task(
                self.auto_login_manager.start_connection_monitor()
            )
            
            self.logger.info(f"Auto-login successful for {email}")
        else:
            self.update_connection_status(ConnectionStatusWidget.NOT_LOGGED)
            self.logger.info("No cached credentials, showing login dialog")
    
    @pyqtSlot(str)
    def on_auto_login_error(self, error: str):
        """Handle auto-login error"""
        self.logger.error(f"Auto-login error: {error}")
        self.update_connection_status(ConnectionStatusWidget.NOT_LOGGED)
    
    def show_login_dialog(self):
        """Show login dialog"""
        dialog = AuthDialog(self)
        if dialog.exec():
            email = dialog.get_email()
            password = dialog.get_password()
            
            # Perform login with caching
            worker = AsyncWorker(
                self.auto_login_manager.login_with_cache(email, password)
            )
            worker.result.connect(self.on_login_complete)
            worker.error.connect(self.on_login_error)
            worker.start()
            
            self.update_connection_status(ConnectionStatusWidget.CONNECTING)
    
    @pyqtSlot(object)
    def on_login_complete(self, result):
        """Handle login completion"""
        success, user_data = result
        
        if success and user_data:
            self.current_user = user_data
            self.is_authenticated = True
            
            # Update UI
            email = user_data.get('email')
            self.status_bar.set_user(email)
            
            # Update menu items
            self.login_action.setVisible(False)
            self.logout_action.setVisible(True)
            
            if user_data.get('offline_mode'):
                self.update_connection_status(ConnectionStatusWidget.OFFLINE)
                self.show_info("离线模式", "服务器连接失败，使用缓存凭据在离线模式下运行")
            else:
                self.update_connection_status(ConnectionStatusWidget.ONLINE)
                self.show_info("登录成功", f"欢迎回来，{email}")
            
            # Initialize modules
            self.initialize_modules()
            
            self.logger.info(f"Login successful for {email}")
        else:
            self.update_connection_status(ConnectionStatusWidget.NOT_LOGGED)
            self.show_error("登录失败", "请检查您的邮箱和密码")
    
    @pyqtSlot(str)
    def on_login_error(self, error: str):
        """Handle login error"""
        self.logger.error(f"Login error: {error}")
        self.update_connection_status(ConnectionStatusWidget.NOT_LOGGED)
        self.show_error("登录错误", str(error))
    
    def logout(self):
        """Logout current user"""
        # Clear cached credentials
        asyncio.create_task(self.auto_login_manager.clear_cache())
        
        # Logout from auth manager
        asyncio.create_task(self.auto_login_manager.auth_manager.logout())
        
        # Update state
        self.current_user = None
        self.is_authenticated = False
        
        # Update UI
        self.status_bar.set_user(None)
        self.update_connection_status(ConnectionStatusWidget.NOT_LOGGED)
        
        # Update menu items
        self.login_action.setVisible(True)
        self.logout_action.setVisible(False)
        
        self.show_info("已登出", "您已成功登出")
        self.logger.info("User logged out")
    
    def update_connection_status(self, status: str):
        """Update connection status in status bar"""
        self.status_bar.set_connection_status(status)
    
    def initialize_modules(self):
        """Initialize backend modules asynchronously"""
        self.show_progress("正在初始化模块...")
        
        worker = AsyncWorker(self.api_adapter.initialize())
        worker.result.connect(self.on_initialization_complete)
        worker.error.connect(self.on_initialization_error)
        worker.start()
    
    @pyqtSlot(object)
    def on_initialization_complete(self, response: APIResponse):
        """Handle initialization completion"""
        self.hide_progress()
        
        if response.success:
            self.logger.info("All modules initialized successfully")
            
            # Enable features
            self.enable_features()
            
            # Update connection status based on actual state
            if self.auto_login_manager.is_online:
                self.update_connection_status(ConnectionStatusWidget.ONLINE)
            else:
                self.update_connection_status(ConnectionStatusWidget.OFFLINE)
        else:
            self.show_error("初始化错误", 
                          f"部分模块初始化失败: {response.error}")
    
    @pyqtSlot(str)
    def on_initialization_error(self, error: str):
        """Handle initialization error"""
        self.hide_progress()
        self.show_error("初始化错误", error)
    
    def setup_timers(self):
        """Setup periodic update timers"""
        # Connection status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # Update every 5 seconds
        
        # Recording status timer
        self.recording_timer = QTimer()
        self.recording_timer.timeout.connect(self.update_recording_status)
        self.recording_timer.start(1000)  # Update every second
        
        # Network status check timer
        self.network_timer = QTimer()
        self.network_timer.timeout.connect(self.check_network_for_recording)
        self.network_timer.start(10000)  # Check every 10 seconds
    
    def update_status(self):
        """Update connection and user status"""
        if self.is_authenticated:
            # Get connection status from auto-login manager
            status = self.auto_login_manager.get_connection_status()
            
            if status['is_online']:
                self.update_connection_status(ConnectionStatusWidget.ONLINE)
            elif status['is_checking']:
                self.update_connection_status(ConnectionStatusWidget.CONNECTING)
            else:
                self.update_connection_status(ConnectionStatusWidget.OFFLINE)
    
    def update_recording_status(self):
        """Update recording status in status bar"""
        # TODO: Get actual recording status from recorder
        pass
    
    def enable_features(self):
        """Enable features after successful initialization"""
        # Enable recording and execution actions
        self.start_recording_action.setEnabled(True)
        self.run_script_action.setEnabled(True)
        
        # Enable tab widgets
        self.dashboard_widget.setEnabled(True)
        self.script_editor_widget.setEnabled(True)
        self.execution_monitor_widget.setEnabled(True)
    
    def show_progress(self, message: str):
        """Show progress message in status bar"""
        # TODO: Implement progress display
        pass
    
    def hide_progress(self):
        """Hide progress message"""
        # TODO: Implement progress hiding
        pass
    
    def show_info(self, title: str, message: str):
        """Show information message"""
        QMessageBox.information(self, title, message)
    
    def show_error(self, title: str, message: str):
        """Show error message"""
        QMessageBox.critical(self, title, message)
    
    def restore_state(self):
        """Restore window state from settings"""
        geometry = self.settings.value("window/geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        state = self.settings.value("window/state")
        if state:
            self.restoreState(state)
    
    def save_state(self):
        """Save window state to settings"""
        self.settings.setValue("window/geometry", self.saveGeometry())
        self.settings.setValue("window/state", self.saveState())
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Save state
        self.save_state()
        
        # Cleanup
        asyncio.create_task(self.auto_login_manager.cleanup())
        
        event.accept()
    
    def get_dark_theme(self) -> str:
        """Get dark theme stylesheet"""
        return """
        QMainWindow {
            background-color: #1e1e1e;
        }
        QTabWidget::pane {
            background-color: #2b2b2b;
            border: 1px solid #555;
        }
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ccc;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #2b2b2b;
            color: #fff;
        }
        QMenuBar {
            background-color: #2b2b2b;
            color: #ccc;
        }
        QMenuBar::item:selected {
            background-color: #3c3c3c;
        }
        QMenu {
            background-color: #2b2b2b;
            color: #ccc;
            border: 1px solid #555;
        }
        QMenu::item:selected {
            background-color: #3c3c3c;
        }
        QToolBar {
            background-color: #2b2b2b;
            border: none;
            spacing: 3px;
        }
        QPushButton {
            background-color: #3c3c3c;
            color: #ccc;
            border: 1px solid #555;
            padding: 5px 10px;
            border-radius: 3px;
        }
        QPushButton:hover {
            background-color: #484848;
        }
        QPushButton:pressed {
            background-color: #555;
        }
        """
    
    # Placeholder methods for actions
    def new_script(self):
        pass
    
    def open_script(self):
        pass
    
    def save_script(self):
        pass
    
    def show_settings(self):
        pass
    
    def start_recording(self):
        """Start recording - requires network connection for AI recognition"""
        # Check network status first
        if not is_online():
            QMessageBox.warning(
                self,
                "无法录制",
                "录制需要网络连接以进行AI识别\n\n请检查您的网络连接后重试。",
                QMessageBox.StandardButton.Ok
            )
            return
        
        # Check AI service
        if not self.network_checker.check_ai_service():
            QMessageBox.warning(
                self,
                "AI服务不可用",
                "AI识别服务暂时不可用\n\n请稍后重试。",
                QMessageBox.StandardButton.Ok
            )
            return
        
        # Start recording
        self.stop_recording_action.setEnabled(True)
        self.start_recording_action.setEnabled(False)
        self.status_bar.set_recording_status(True)
        self.status_bar.increment_scripts()
    
    def stop_recording(self):
        """Stop recording"""
        self.stop_recording_action.setEnabled(False)
        self.start_recording_action.setEnabled(True)
        self.status_bar.set_recording_status(False)
    
    def check_network_for_recording(self):
        """Check network status and update recording button state"""
        is_network_available = is_online()
        
        # Update recording action based on network status
        if hasattr(self, 'start_recording_action'):
            self.start_recording_action.setEnabled(is_network_available)
            
            if not is_network_available:
                self.start_recording_action.setToolTip(
                    "录制需要网络连接以进行AI识别"
                )
            else:
                self.start_recording_action.setToolTip(
                    "开始录制 (Ctrl+R)"
                )
    
    def on_network_status_changed(self, is_online: bool):
        """Handle network status change"""
        # Update recording button state
        if hasattr(self, 'start_recording_action'):
            self.start_recording_action.setEnabled(is_online)
            
            if not is_online:
                # If recording is in progress, stop it
                if hasattr(self, 'stop_recording_action') and self.stop_recording_action.isEnabled():
                    self.stop_recording()
                    QMessageBox.warning(
                        self,
                        "录制已停止",
                        "网络连接丢失，录制已自动停止。\n\n录制需要网络连接以进行AI识别。",
                        QMessageBox.StandardButton.Ok
                    )
    
    def clear_recording(self):
        pass
    
    def run_script(self):
        self.stop_execution_action.setEnabled(True)
        self.run_script_action.setEnabled(False)
        self.status_bar.increment_actions(10)  # Example
    
    def stop_execution(self):
        self.stop_execution_action.setEnabled(False)
        self.run_script_action.setEnabled(True)
    
    def show_documentation(self):
        pass
    
    def show_about(self):
        QMessageBox.about(self, "关于 ShowForAI V3", 
                         "ShowForAI V3 - AI智能自动化平台\n\n"
                         "版本: 3.0.0\n"
                         "© 2024 ShowForAI Team")