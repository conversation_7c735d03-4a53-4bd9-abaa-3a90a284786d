# 数据结构设计

> "Bad programmers worry about the code. Good programmers worry about data structures." - <PERSON><PERSON>ds

## 设计原则

1. **数据流清晰** - 每个结构有明确的用途
2. **无歧义** - 字段含义明确，不存在多重解释
3. **高效存储** - 合理的内存布局和序列化

## 核心数据结构

### 1. 录制数据结构

#### RawFrame - 原始帧
```rust
pub struct RawFrame {
    /// 原始截图数据（用户屏幕分辨率）
    pub data: Vec<u8>,
    /// 原始分辨率
    pub resolution: (u32, u32),
    /// 截图时间戳
    pub timestamp: u64,
    /// 帧序号
    pub frame_index: u32,
}
```

#### StandardFrame - 标准化帧
```rust
pub struct StandardFrame {
    /// 768×768 标准化图像数据（RGBA格式）
    pub image_768: Vec<u8>,
    /// 原始分辨率（用于执行时适配）
    pub original_resolution: (u32, u32),
    /// 时间戳
    pub timestamp: u64,
}
```

#### RecordedAction - 录制的动作
```rust
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RecordedAction {
    /// 动作类型
    pub action_type: ActionType,
    /// 动作发生的坐标（原始分辨率）
    pub x: f64,
    pub y: f64,
    /// 时间戳（毫秒）
    pub timestamp: u64,
    /// 与上一个动作的时间间隔（毫秒）
    pub interval_ms: u32,
    /// 额外参数（如输入文本、拖拽终点等）
    pub params: Option<ActionParams>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum ActionType {
    Click,
    DoubleClick,
    RightClick,
    Drag,
    Input,
    KeyPress,
    KeyRelease,
    Scroll,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum ActionParams {
    Text(String),           // 输入文本
    DragEnd { x: f64, y: f64 }, // 拖拽终点
    Key(String),            // 按键名称
    ScrollDelta(i32),       // 滚动量
}
```

### 2. AI处理数据结构

#### BoundingBox - AI识别的边界框
```rust
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BoundingBox {
    /// 左上角 x 坐标（基于768×768）
    pub x: u32,
    /// 左上角 y 坐标（基于768×768）
    pub y: u32,
    /// 宽度（基于768×768）
    pub width: u32,
    /// 高度（基于768×768）
    pub height: u32,
    /// 置信度 (0.0 - 1.0)
    pub confidence: f32,
    /// 元素类型（按钮、输入框等）
    pub element_type: Option<String>,
}
```

#### AIProcessingResult - AI处理结果
```rust
#[derive(Serialize, Deserialize)]
pub struct AIProcessingResult {
    /// 标准化图片ID
    pub image_id: String,
    /// 识别到的所有元素
    pub elements: Vec<BoundingBox>,
    /// 被点击元素的BBOX（包含点击坐标的那个）
    pub clicked_element: BoundingBox,
    /// 处理耗时（毫秒）
    pub processing_time: u32,
}
```

### 3. 脚本数据结构

#### Script - 脚本完整数据
```rust
#[derive(Serialize, Deserialize)]
pub struct Script {
    /// 脚本唯一ID
    pub id: String,
    /// 脚本名称（自动生成：脚本1、脚本2...）
    pub name: String,
    /// 用户描述
    pub description: Option<String>,
    /// 创建时间
    pub created_at: u64,
    /// 最后执行时间
    pub last_executed: Option<u64>,
    /// 原始录制分辨率
    pub original_resolution: (u32, u32),
    /// 动作序列
    pub actions: Vec<ScriptAction>,
    /// 脚本元数据
    pub metadata: ScriptMetadata,
}

#[derive(Serialize, Deserialize)]
pub struct ScriptAction {
    /// 动作索引
    pub index: u32,
    /// 裁切后的元素图像（Base64编码）
    pub element_image: String,
    /// 动作信息
    pub action: RecordedAction,
    /// 元素在768图像中的原始BBOX（用于调试）
    pub bbox: Option<BoundingBox>,
}

#[derive(Serialize, Deserialize)]
pub struct ScriptMetadata {
    /// 脚本版本
    pub version: u32,
    /// 总步骤数
    pub total_steps: u32,
    /// 预计执行时间（毫秒）
    pub estimated_duration: u32,
    /// 录制时的应用信息
    pub app_info: Option<String>,
}
```

### 4. 执行数据结构

#### ExecutionContext - 执行上下文
```rust
pub struct ExecutionContext {
    /// 当前脚本
    pub script: Script,
    /// 当前执行步骤
    pub current_step: u32,
    /// 执行模式
    pub mode: ExecutionMode,
    /// 循环次数
    pub loop_count: u32,
    /// 当前循环
    pub current_loop: u32,
    /// 执行状态
    pub status: ExecutionStatus,
    /// 执行开始时间
    pub start_time: u64,
}

#[derive(Clone, Debug)]
pub enum ExecutionMode {
    Active,     // 主动模式
    Auxiliary,  // 辅助模式
}

#[derive(Clone, Debug)]
pub enum ExecutionStatus {
    Running,
    Paused,
    Waiting,    // 等待元素出现
    Completed,
    Failed(String),
}
```

#### SharedExecutionState - 共享执行状态
```rust
use std::sync::atomic::{AtomicBool, AtomicU8};

/// 两个执行模式之间的共享状态
pub struct SharedExecutionState {
    /// Active模式是否正在运行
    pub active_running: AtomicBool,
    
    /// 鼠标键盘控制权归属
    /// 0 = 无人占用
    /// 1 = Active模式占用
    /// 2 = Auxiliary模式占用
    pub input_owner: AtomicU8,
    
    /// Active模式执行进度（用于UI显示）
    pub active_progress: AtomicU32,
    
    /// Auxiliary模式执行进度
    pub auxiliary_progress: AtomicU32,
}

#### MatchResult - 图像匹配结果
```rust
pub struct MatchResult {
    /// 匹配位置（屏幕坐标）
    pub position: (i32, i32),
    /// 匹配置信度
    pub confidence: f32,
    /// 匹配耗时
    pub match_time_ms: u32,
    /// 使用的匹配算法
    pub algorithm: MatchAlgorithm,
}

#[derive(Debug)]
pub enum MatchAlgorithm {
    TemplateMatching,   // 模板匹配
    ORB,               // ORB特征点
    SIFT,              // SIFT特征点
    MultiScale,        // 多尺度匹配
}
```

### 5. 通信数据结构

#### IpcMessage - 前后端通信消息
```typescript
// TypeScript 定义
interface IpcMessage {
  type: MessageType;
  payload: any;
  timestamp: number;
  requestId?: string;
}

enum MessageType {
  // 录制相关
  START_RECORDING = 'START_RECORDING',
  STOP_RECORDING = 'STOP_RECORDING',
  SAVE_RECORDING = 'SAVE_RECORDING',
  
  // 执行相关
  EXECUTE_SCRIPT = 'EXECUTE_SCRIPT',
  PAUSE_EXECUTION = 'PAUSE_EXECUTION',
  RESUME_EXECUTION = 'RESUME_EXECUTION',
  STOP_EXECUTION = 'STOP_EXECUTION',
  
  // 状态更新
  EXECUTION_PROGRESS = 'EXECUTION_PROGRESS',
  RECORDING_STATUS = 'RECORDING_STATUS',
  ERROR = 'ERROR',
}
```

#### ApiRequest - 服务端API请求
```rust
#[derive(Serialize, Deserialize)]
pub struct ApiRequest {
    /// 请求ID（用于防重放）
    pub request_id: String,
    /// 时间戳
    pub timestamp: u64,
    /// 签名（HMAC-SHA256）
    pub signature: String,
    /// 请求体
    pub body: ApiRequestBody,
}

#[derive(Serialize, Deserialize)]
pub enum ApiRequestBody {
    /// 上传录制数据
    UploadRecording {
        frames: Vec<StandardFrame>,
        actions: Vec<RecordedAction>,
        metadata: RecordingMetadata,
    },
    /// 获取脚本
    GetScript { script_id: String },
    /// 分享脚本
    ShareScript { script_id: String },
}

#[derive(Serialize, Deserialize)]
pub struct RecordingMetadata {
    /// 录制时长（毫秒）
    pub duration_ms: u32,
    /// 总帧数
    pub frame_count: u32,
    /// 原始分辨率
    pub resolution: (u32, u32),
    /// 客户端版本
    pub client_version: String,
}
```

### 6. 存储数据结构

#### LocalStorage - 本地存储架构
```rust
/// SQLite 数据库表结构
/// 
/// scripts 表
/// - id: TEXT PRIMARY KEY
/// - name: TEXT NOT NULL
/// - description: TEXT
/// - created_at: INTEGER NOT NULL
/// - last_executed: INTEGER
/// - data: BLOB (序列化的Script)
/// 
/// execution_history 表
/// - id: INTEGER PRIMARY KEY AUTOINCREMENT
/// - script_id: TEXT NOT NULL
/// - start_time: INTEGER NOT NULL
/// - end_time: INTEGER
/// - status: TEXT NOT NULL
/// - error_message: TEXT
/// 
/// settings 表
/// - key: TEXT PRIMARY KEY
/// - value: TEXT NOT NULL
```

#### CacheEntry - 缓存项
```rust
pub struct CacheEntry {
    /// 缓存键
    pub key: String,
    /// 裁切后的元素图像
    pub element_image: Vec<u8>,
    /// 创建时间
    pub created_at: u64,
    /// 最后访问时间
    pub last_accessed: u64,
    /// 访问次数
    pub access_count: u32,
}
```

## 内存布局优化

### 关键优化点

1. **768×768 标准化**
   - 固定大小：768 × 768 × 4 = 2,359,296 字节
   - 预分配缓冲区，避免动态分配

2. **元素图像缓存**
   - LRU缓存策略
   - 最大缓存大小：100MB
   - 缓存命中率目标：> 90%

3. **批量处理**
   - 动作批量上传（每批10个）
   - 帧批量处理（每批5帧）
   - 减少网络往返

## 序列化格式

### 本地存储
- 使用 MessagePack 序列化（比JSON小30-50%）
- 图像使用 PNG 压缩
- 元数据使用 SQLite

### 网络传输
- API通信：JSON + gzip
- 图像上传：multipart/form-data
- WebSocket：MessagePack

## 性能指标

| 操作 | 目标性能 |
|-----|---------|
| 768×768 缩放 | < 20ms |
| BBOX 裁切 | < 5ms |
| 模板匹配 | < 50ms |
| 脚本加载 | < 100ms |
| 元素缓存查询 | < 1ms |

## 错误处理

```rust
#[derive(Debug)]
pub enum DataError {
    /// 图像处理错误
    ImageProcessing(String),
    /// 序列化错误
    Serialization(String),
    /// 存储错误
    Storage(String),
    /// 网络错误
    Network(String),
    /// 数据验证错误
    Validation(String),
}

impl std::error::Error for DataError {}
```

## 版本兼容性

### 向前兼容策略
1. 脚本版本字段
2. 可选字段使用 Option<T>
3. 枚举预留 Unknown 变体

### 数据迁移
```rust
pub trait Migration {
    fn version(&self) -> u32;
    fn migrate(&self, data: &mut Script) -> Result<(), DataError>;
}
```

---

*"数据结构正确了，算法自然就对了。"*