"""
Test UI Optimizer - Verify UI response under 100ms
"""

import sys
import time
import unittest
from unittest.mock import Mock, MagicMock, patch
from PyQt5.QtWidgets import QApplication, QWidget, QListWidget, QPushButton
from PyQt5.QtCore import QRect, QTimer

# Create QApplication for testing
app = QApplication.instance()
if app is None:
    app = QApplication(sys.argv)

from src.showforai.optimization.ui_optimizer import (
    UIOptimizer, BatchProcessor, VirtualListRenderer,
    DirtyRegionTracker, EventQueueOptimizer, UIOperation,
    optimize_ui_response, get_ui_optimizer
)


class TestUIOptimizer(unittest.TestCase):
    """Test UI optimization features"""
    
    def setUp(self):
        """Set up test environment"""
        self.optimizer = UIOptimizer(target_response_ms=100)
        self.widget = QWidget()
        
    def tearDown(self):
        """Clean up"""
        self.optimizer.cleanup()
        
    def test_batch_processor_performance(self):
        """Test batch processor keeps operations under 100ms"""
        processor = BatchProcessor(batch_size=50, buffer_time_ms=100)
        
        # Add multiple operations
        start_time = time.time()
        mock_widget = Mock(spec=QWidget)
        
        for i in range(100):
            op = UIOperation(
                widget=mock_widget,
                operation='setText',
                args=(f'Text {i}',),
                priority=i % 3
            )
            processor.add_operation(op)
        
        # Wait for batch processing
        QTimer.singleShot(150, app.quit)
        app.exec_()
        
        # Check that batching happened within time limit
        elapsed = (time.time() - start_time) * 1000
        self.assertLess(elapsed, 300, f"Batch processing took {elapsed:.1f}ms")
        
    def test_virtual_list_rendering(self):
        """Test virtual list only renders visible items"""
        list_widget = QListWidget()
        list_widget.resize(300, 200)  # Show ~6 items at 30px height
        
        renderer = VirtualListRenderer(list_widget, item_height=30)
        
        # Set 1000 items
        items = [f"Item {i}" for i in range(1000)]
        renderer.set_items(items)
        
        # Check only visible items are rendered
        renderer.update_visible_range()
        
        # Should only have ~15 items in memory (visible + buffer)
        self.assertLess(len(renderer.visible_items), 20)
        self.assertGreater(len(renderer.visible_items), 0)
        
    def test_dirty_region_tracking(self):
        """Test dirty region merging and optimization"""
        tracker = DirtyRegionTracker(merge_threshold_ms=50)
        
        widget = QWidget()
        widget.resize(800, 600)
        
        # Mark multiple overlapping regions
        tracker.mark_dirty(widget, QRect(0, 0, 100, 100))
        tracker.mark_dirty(widget, QRect(50, 50, 100, 100))
        tracker.mark_dirty(widget, QRect(100, 100, 100, 100))
        
        # Should merge into one region
        self.assertEqual(len(tracker.dirty_regions.get(widget, [])), 3)
        
        # Process regions
        tracker.process_dirty_regions()
        
        # Should be cleared after processing
        self.assertEqual(len(tracker.dirty_regions), 0)
        
    def test_event_queue_optimization(self):
        """Test event queue coalescing and processing"""
        optimizer = EventQueueOptimizer(max_events_per_cycle=10)
        
        # Register handler
        handler_calls = []
        def test_handler(value):
            handler_calls.append(value)
        
        optimizer.register_handler('test_event', test_handler)
        
        # Queue multiple similar events
        for i in range(20):
            optimizer.queue_event('test_event', i)
        
        # Process events
        optimizer.process_events()
        
        # Should process max 10 events per cycle
        self.assertLessEqual(len(handler_calls), 10)
        
    def test_async_ui_operations(self):
        """Test async UI operations don't block"""
        def slow_operation():
            time.sleep(0.05)  # 50ms operation
            return "Result"
        
        result_received = []
        def on_result(result):
            result_received.append(result)
        
        # Run async
        start = time.time()
        self.optimizer.run_async(slow_operation, callback=on_result)
        
        # Should return immediately
        immediate_time = (time.time() - start) * 1000
        self.assertLess(immediate_time, 10, "Async operation blocked")
        
        # Wait for result
        QTimer.singleShot(100, app.quit)
        app.exec_()
        
        # Should have received result
        self.assertEqual(result_received, ["Result"])
        
    def test_ui_response_time_target(self):
        """Test overall UI response time meets target"""
        widget = QPushButton("Test")
        self.optimizer.optimize_widget(widget)
        
        # Simulate multiple UI updates
        start = time.time()
        
        for i in range(100):
            self.optimizer.batch_operation(
                widget, 'setText', f'Text {i}', priority=1
            )
        
        # Process batch
        self.optimizer.batch_processor.process_batch()
        
        elapsed = (time.time() - start) * 1000
        
        # Should complete within target time (with some tolerance)
        self.assertLess(elapsed, 150, f"UI operations took {elapsed:.1f}ms")
        
    def test_performance_metrics(self):
        """Test performance metrics tracking"""
        # Perform some operations
        mock_widget = Mock(spec=QWidget)
        
        for i in range(10):
            self.optimizer.batch_operation(
                mock_widget, 'update', priority=1
            )
        
        # Get metrics
        metrics = self.optimizer.get_metrics()
        
        self.assertEqual(metrics['total_operations'], 10)
        self.assertGreaterEqual(metrics['batches_processed'], 0)
        self.assertIn('average_response_ms', metrics)
        self.assertIn('slow_operations', metrics)
        
    def test_decorator_optimization(self):
        """Test optimize_ui_response decorator"""
        
        class TestWidget(QWidget):
            def __init__(self):
                super().__init__()
                self.update_count = 0
            
            @optimize_ui_response(target_ms=50)
            def slow_update(self):
                self.update_count += 1
                time.sleep(0.01)  # 10ms operation
        
        widget = TestWidget()
        
        # Call decorated method
        start = time.time()
        for i in range(10):
            widget.slow_update()
        elapsed = (time.time() - start) * 1000
        
        # Should batch operations
        self.assertLess(elapsed, 100, f"Decorated operations took {elapsed:.1f}ms")
        
    def test_memory_efficient_batching(self):
        """Test batch processor doesn't accumulate too many operations"""
        processor = BatchProcessor(batch_size=50, buffer_time_ms=100)
        
        # Add many operations
        mock_widget = Mock(spec=QWidget)
        
        for i in range(1000):
            op = UIOperation(
                widget=mock_widget,
                operation='update',
                args=(i,)
            )
            processor.add_operation(op)
        
        # Queue should not grow unbounded
        self.assertLessEqual(len(processor.operation_queue), 1000)
        
    def test_widget_cleanup(self):
        """Test proper cleanup of resources"""
        # Create and optimize widgets
        widgets = []
        for i in range(5):
            w = QWidget()
            self.optimizer.optimize_widget(w)
            widgets.append(w)
        
        # Perform operations
        for w in widgets:
            self.optimizer.batch_operation(w, 'update')
        
        # Cleanup
        self.optimizer.cleanup()
        
        # Timers should be stopped
        self.assertFalse(self.optimizer.batch_processor.timer.isActive())
        self.assertFalse(self.optimizer.dirty_tracker.timer.isActive())


def run_performance_benchmark():
    """Run performance benchmark"""
    print("\n=== UI Optimizer Performance Benchmark ===")
    
    optimizer = UIOptimizer(target_response_ms=100)
    
    # Test 1: Batch processing performance
    print("\n1. Batch Processing Test:")
    mock_widget = Mock(spec=QWidget)
    
    start = time.time()
    for i in range(1000):
        optimizer.batch_operation(
            mock_widget, 'setText', f'Item {i}'
        )
    
    # Force process all batches
    while optimizer.batch_processor.operation_queue:
        optimizer.batch_processor.process_batch()
    
    elapsed = (time.time() - start) * 1000
    print(f"   - 1000 operations: {elapsed:.1f}ms")
    print(f"   - Average per operation: {elapsed/1000:.2f}ms")
    print(f"   - Target met: {'✓' if elapsed/1000 < 0.1 else '✗'}")
    
    # Test 2: Virtual list performance
    print("\n2. Virtual List Rendering Test:")
    list_widget = QListWidget()
    renderer = optimizer.create_virtual_list(list_widget)
    
    start = time.time()
    items = [f"Item {i}" for i in range(10000)]
    renderer.set_items(items)
    elapsed = (time.time() - start) * 1000
    
    print(f"   - 10,000 items setup: {elapsed:.1f}ms")
    print(f"   - Visible items: {len(renderer.visible_items)}")
    print(f"   - Memory efficient: {'✓' if len(renderer.visible_items) < 50 else '✗'}")
    
    # Test 3: Event processing performance
    print("\n3. Event Processing Test:")
    event_optimizer = EventQueueOptimizer()
    
    processed_count = []
    def handler(val):
        processed_count.append(val)
    
    event_optimizer.register_handler('test', handler)
    
    start = time.time()
    for i in range(500):
        event_optimizer.queue_event('test', i)
    
    while event_optimizer.event_queue:
        event_optimizer.process_events()
    
    elapsed = (time.time() - start) * 1000
    print(f"   - 500 events: {elapsed:.1f}ms")
    print(f"   - Events processed: {len(processed_count)}")
    print(f"   - Performance target met: {'✓' if elapsed < 100 else '✗'}")
    
    # Get final metrics
    print("\n4. Overall Metrics:")
    metrics = optimizer.get_metrics()
    print(f"   - Total operations: {metrics['total_operations']}")
    print(f"   - Batches processed: {metrics['batches_processed']}")
    print(f"   - Average response: {metrics['average_response_ms']:.1f}ms")
    print(f"   - Slow operations: {metrics['slow_operations']}")
    
    # Overall assessment
    print("\n=== Performance Summary ===")
    target_met = (
        elapsed/1000 < 0.1 and 
        len(renderer.visible_items) < 50 and
        metrics['average_response_ms'] < 100
    )
    print(f"UI Response Target (<100ms): {'✓ ACHIEVED' if target_met else '✗ NOT MET'}")
    
    optimizer.cleanup()
    return target_met


if __name__ == '__main__':
    # Run unit tests
    print("Running UI Optimizer Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance benchmark
    success = run_performance_benchmark()
    
    if success:
        print("\n✓ UI Response Optimization Complete - Target Achieved!")
    else:
        print("\n✗ UI optimization needs further tuning")
    
    sys.exit(0 if success else 1)