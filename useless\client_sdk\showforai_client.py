"""
ShowForAI Client SDK
安全的客户端SDK，用于本地应用调用云端API
"""

import requests
import hashlib
import hmac
import time
import json
import base64
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import logging
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ShowForAIClient:
    """ShowForAI API客户端"""
    
    def __init__(
        self, 
        api_key: str,
        server_url: str = "http://*************:8080",
        timeout: int = 30,
        retry_count: int = 3,
        use_https: bool = False
    ):
        """
        初始化客户端
        
        Args:
            api_key: API密钥
            server_url: 服务器地址
            timeout: 请求超时时间（秒）
            retry_count: 重试次数
            use_https: 是否使用HTTPS
        """
        self.api_key = api_key
        self.server_url = server_url.rstrip('/')
        if use_https and self.server_url.startswith('http://'):
            self.server_url = self.server_url.replace('http://', 'https://')
        
        self.timeout = timeout
        self.retry_count = retry_count
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'ShowForAI-Client/2.0',
            'Accept': 'application/json'
        })
    
    def _generate_signature(self, method: str, path: str, timestamp: str, body: str = "") -> str:
        """
        生成请求签名（可选的额外安全层）
        
        Args:
            method: HTTP方法
            path: 请求路径
            timestamp: 时间戳
            body: 请求体
        
        Returns:
            签名字符串
        """
        message = f"{method}\n{path}\n{timestamp}\n{body}"
        signature = hmac.new(
            self.api_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict] = None,
        files: Optional[Dict] = None,
        use_signature: bool = False
    ) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            files: 文件数据
            use_signature: 是否使用签名验证
        
        Returns:
            响应数据
        """
        url = urljoin(self.server_url, endpoint)
        headers = {'Authorization': f'Bearer {self.api_key}'}
        
        # 可选：添加签名验证
        if use_signature:
            timestamp = str(int(time.time()))
            headers['X-Timestamp'] = timestamp
            
            body = json.dumps(data) if data and not files else ""
            signature = self._generate_signature(method, endpoint, timestamp, body)
            headers['X-Signature'] = signature
        
        # 重试机制
        for attempt in range(self.retry_count):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    json=data if not files else None,
                    data=data if files else None,
                    files=files,
                    headers=headers,
                    timeout=self.timeout
                )
                
                # 检查响应状态
                if response.status_code == 401:
                    raise Exception("认证失败：API密钥无效")
                elif response.status_code == 429:
                    raise Exception("请求过于频繁，请稍后再试")
                elif response.status_code >= 500:
                    if attempt < self.retry_count - 1:
                        wait_time = 2 ** attempt
                        logger.warning(f"服务器错误，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    raise Exception(f"服务器错误: {response.status_code}")
                
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.Timeout:
                if attempt < self.retry_count - 1:
                    logger.warning(f"请求超时，重试 {attempt + 1}/{self.retry_count}")
                    continue
                raise Exception("请求超时")
            except requests.exceptions.ConnectionError:
                if attempt < self.retry_count - 1:
                    logger.warning(f"连接错误，重试 {attempt + 1}/{self.retry_count}")
                    time.sleep(2)
                    continue
                raise Exception("无法连接到服务器")
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        return self._make_request('GET', '/health')
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计数据
        """
        return self._make_request('GET', '/api/v2/detection/stats')
    
    def detect_element(
        self,
        image_path: str,
        click_x: int,
        click_y: int,
        mode: str = "basic",
        save_result: bool = False,
        output_dir: str = "./results"
    ) -> Dict[str, Any]:
        """
        检测UI元素
        
        Args:
            image_path: 图片路径
            click_x: 点击X坐标
            click_y: 点击Y坐标
            mode: 检测模式 ("basic" 或 "enhanced")
            save_result: 是否保存结果
            output_dir: 结果保存目录
        
        Returns:
            检测结果
        """
        # 验证输入
        if mode not in ["basic", "enhanced"]:
            raise ValueError("mode必须是'basic'或'enhanced'")
        
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 检查文件大小
        file_size = image_path.stat().st_size
        if file_size > 10 * 1024 * 1024:  # 10MB
            raise ValueError("图片文件过大（最大10MB）")
        
        # 准备请求
        with open(image_path, 'rb') as f:
            files = {'screenshot': (image_path.name, f, 'image/png')}
            data = {
                'click_x': str(click_x),
                'click_y': str(click_y),
                'mode': mode
            }
            
            # 发送请求
            result = self._make_request(
                'POST',
                '/api/v2/detect',
                data=data,
                files=files
            )
        
        # 保存结果（可选）
        if save_result and result.get('success'):
            self._save_result(result, image_path.name, output_dir)
        
        return result
    
    def detect_batch(
        self,
        tasks: list,
        mode: str = "basic",
        max_workers: int = 3
    ) -> list:
        """
        批量检测
        
        Args:
            tasks: 任务列表，每个任务包含 (image_path, click_x, click_y)
            mode: 检测模式
            max_workers: 最大并发数
        
        Returns:
            结果列表
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        results = []
        
        def process_task(task):
            image_path, click_x, click_y = task
            try:
                return self.detect_element(image_path, click_x, click_y, mode)
            except Exception as e:
                return {"success": False, "error": str(e), "image": image_path}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_task, task): i 
                      for i, task in enumerate(tasks)}
            
            for future in as_completed(futures):
                idx = futures[future]
                try:
                    result = future.result()
                    results.append((idx, result))
                except Exception as e:
                    results.append((idx, {"success": False, "error": str(e)}))
        
        # 按原始顺序排序
        results.sort(key=lambda x: x[0])
        return [r[1] for r in results]
    
    def _save_result(self, result: Dict, image_name: str, output_dir: str):
        """保存检测结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 生成结果文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        result_file = output_path / f"result_{image_name}_{timestamp}.json"
        
        # 保存结果
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已保存: {result_file}")


class SecureShowForAIClient(ShowForAIClient):
    """增强安全性的客户端（支持加密传输）"""
    
    def __init__(self, api_key: str, encryption_key: Optional[str] = None, **kwargs):
        """
        初始化安全客户端
        
        Args:
            api_key: API密钥
            encryption_key: 加密密钥（可选）
            **kwargs: 其他参数
        """
        super().__init__(api_key, **kwargs)
        self.encryption_key = encryption_key
    
    def _encrypt_data(self, data: bytes) -> Tuple[bytes, bytes]:
        """
        加密数据（使用AES-256-GCM）
        
        Args:
            data: 原始数据
        
        Returns:
            (加密数据, nonce)
        """
        if not self.encryption_key:
            return data, b''
        
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        from cryptography.hazmat.backends import default_backend
        import os
        
        # 生成随机nonce
        nonce = os.urandom(12)
        
        # 创建加密器
        cipher = Cipher(
            algorithms.AES(self.encryption_key.encode()[:32].ljust(32, b'\0')),
            modes.GCM(nonce),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()
        
        # 加密数据
        ciphertext = encryptor.update(data) + encryptor.finalize()
        
        return ciphertext + encryptor.tag, nonce
    
    def detect_element_secure(self, image_path: str, click_x: int, click_y: int, **kwargs):
        """安全的元素检测（支持加密传输）"""
        if self.encryption_key:
            # 读取图片数据
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # 加密图片数据
            encrypted_data, nonce = self._encrypt_data(image_data)
            
            # 发送加密数据
            # ... 实现加密传输逻辑
        
        return self.detect_element(image_path, click_x, click_y, **kwargs)


# 使用示例
if __name__ == "__main__":
    # 创建客户端实例
    client = ShowForAIClient(
        api_key="your_api_key_here",
        server_url="http://*************:8080"
    )
    
    # 健康检查
    try:
        health = client.health_check()
        print(f"服务器状态: {health['status']}")
    except Exception as e:
        print(f"连接失败: {e}")
        exit(1)
    
    # 检测示例
    try:
        result = client.detect_element(
            image_path="screenshot.png",
            click_x=400,
            click_y=300,
            mode="basic"
        )
        
        if result['success']:
            print(f"检测成功!")
            print(f"边界框: {result['data']['bounding_box']}")
            print(f"置信度: {result['data']['confidence']}")
        else:
            print(f"检测失败: {result.get('error')}")
    
    except Exception as e:
        print(f"错误: {e}")