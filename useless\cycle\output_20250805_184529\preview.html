
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>边界框测试结果</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin-bottom: 30px; border: 1px solid #ccc; padding: 10px; }
        .images { display: flex; gap: 20px; margin-top: 10px; }
        .image-container { text-align: center; }
        img { max-width: 400px; border: 1px solid #ddd; }
        .bbox-info { background: #f0f0f0; padding: 10px; margin-top: 10px; }
        pre { background: #f5f5f5; padding: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>边界框可视化测试结果</h1>

    <div class="test-case">
        <h2>测试案例 1: ace_icon</h2>
        <div class="images">
            <div class="image-container">
                <h3>原始图片</h3>
                <img src="C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png" alt="原始图片">
            </div>
            <div class="image-container">
                <h3>带边界框的图片</h3>
                <img src="ace_icon_bbox.png" alt="带边界框的图片">
            </div>
        </div>
        <div class="bbox-info">
            <h3>边界框信息</h3>
            <p><strong>描述:</strong> Application icon for ACE software</p>
            <p><strong>文本内容:</strong> (无)</p>
            <p><strong>坐标:</strong> (330, 388)</p>
            <p><strong>尺寸:</strong> 108 x 82</p>
            <details>
                <summary>完整JSON结果</summary>
                <pre>{
  "element": {
    "bounding_box": {
      "height": 82,
      "width": 108,
      "x": 330,
      "y": 388
    },
    "description": "Application icon for ACE software",
    "text_content": ""
  }
}</pre>
            </details>
        </div>
    </div>

</body>
</html>
