package com.showforai.executor.core.location

import android.graphics.Bitmap
import com.showforai.executor.core.vision.OpenCVManager
import com.showforai.executor.core.vision.OCRProcessor
import com.showforai.executor.data.models.*
import com.showforai.executor.utils.ScreenUtils
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 定位策略接口
 */
interface LocationStrategy {
    suspend fun findTarget(
        screenshot: Bitmap,
        target: DSLTarget,
        timeoutSeconds: Int
    ): LocationResult
    
    fun getStrategyType(): LocationStrategy.Type
    
    enum class Type {
        VISUAL_MATCHING,
        OCR_TEXT,
        COORDINATE_FALLBACK
    }
}

/**
 * 视觉匹配策略
 * 使用OpenCV进行模板匹配
 */
class VisualMatchingStrategy @Inject constructor(
    private val openCVManager: OpenCVManager,
    private val screenUtils: ScreenUtils
) : LocationStrategy {
    
    companion object {
        private const val DEFAULT_THRESHOLD = 0.8
        private const val FALLBACK_THRESHOLD = 0.7
    }
    
    override suspend fun findTarget(
        screenshot: Bitmap,
        target: DSLTarget,
        timeoutSeconds: Int
    ): LocationResult {
        val visualHash = target.visualHash
        if (visualHash.isNullOrEmpty()) {
            return LocationResult(
                found = false,
                x = 0f,
                y = 0f,
                confidence = 0f,
                strategy = LocationStrategy.Type.VISUAL_MATCHING,
                boundingBox = null
            )
        }
        
        return try {
            // 确保OpenCV已初始化
            if (!openCVManager.isInitialized()) {
                val initialized = openCVManager.initialize()
                if (!initialized) {
                    Timber.e("OpenCV initialization failed")
                    return createFailedResult()
                }
            }
            
            // 加载模板图像
            val templateBitmap = loadTemplateBitmap(visualHash)
            if (templateBitmap == null) {
                Timber.w("Template image not found: $visualHash")
                return createFailedResult()
            }
            
            // 执行模板匹配
            val matchResult = withTimeoutOrNull((timeoutSeconds * 1000).toLong()) {
                openCVManager.multiScaleTemplateMatch(
                    sourceImage = screenshot,
                    templateImage = templateBitmap,
                    threshold = DEFAULT_THRESHOLD
                )
            }
            
            templateBitmap.recycle()
            
            if (matchResult?.found == true) {
                LocationResult(
                    found = true,
                    x = matchResult.location.x.toFloat(),
                    y = matchResult.location.y.toFloat(),
                    confidence = matchResult.confidence.toFloat(),
                    strategy = LocationStrategy.Type.VISUAL_MATCHING,
                    boundingBox = android.graphics.RectF(matchResult.boundingBox)
                )
            } else {
                createFailedResult()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Visual matching failed for target: $visualHash")
            createFailedResult()
        }
    }
    
    override fun getStrategyType(): LocationStrategy.Type {
        return LocationStrategy.Type.VISUAL_MATCHING
    }
    
    private fun createFailedResult(): LocationResult {
        return LocationResult(
            found = false,
            x = 0f,
            y = 0f,
            confidence = 0f,
            strategy = LocationStrategy.Type.VISUAL_MATCHING,
            boundingBox = null
        )
    }
    
    private fun loadTemplateBitmap(visualHash: String): Bitmap? {
        // TODO: 从FileManager加载模板图像
        // 这里需要实现从assets目录加载图像的逻辑
        return null
    }
}

/**
 * OCR文本识别策略
 * 使用ML Kit进行文本识别
 */
class OCRStrategy @Inject constructor(
    private val ocrProcessor: OCRProcessor,
    private val screenUtils: ScreenUtils
) : LocationStrategy {
    
    override suspend fun findTarget(
        screenshot: Bitmap,
        target: DSLTarget,
        timeoutSeconds: Int
    ): LocationResult {
        val textContent = target.textContent
        if (textContent.isNullOrEmpty()) {
            return createFailedResult()
        }
        
        return try {
            // 如果有边界框，在指定区域内搜索
            val searchResult = if (target.boundingBox != null && target.boundingBox.size == 4) {
                val searchArea = screenUtils.normalizedBoxToPixel(target.boundingBox)
                val searchRect = android.graphics.Rect(
                    searchArea.left.toInt(),
                    searchArea.top.toInt(),
                    searchArea.right.toInt(),
                    searchArea.bottom.toInt()
                )
                
                withTimeoutOrNull((timeoutSeconds * 1000).toLong()) {
                    ocrProcessor.findTextInArea(screenshot, textContent, searchRect)
                }
            } else {
                // 在整个屏幕中搜索
                withTimeoutOrNull((timeoutSeconds * 1000).toLong()) {
                    ocrProcessor.findText(screenshot, textContent)
                }
            }
            
            if (searchResult?.found == true) {
                LocationResult(
                    found = true,
                    x = searchResult.location.x.toFloat(),
                    y = searchResult.location.y.toFloat(),
                    confidence = searchResult.confidence,
                    strategy = LocationStrategy.Type.OCR_TEXT,
                    boundingBox = android.graphics.RectF(searchResult.boundingBox)
                )
            } else {
                createFailedResult()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "OCR text search failed for text: $textContent")
            createFailedResult()
        }
    }
    
    override fun getStrategyType(): LocationStrategy.Type {
        return LocationStrategy.Type.OCR_TEXT
    }
    
    private fun createFailedResult(): LocationResult {
        return LocationResult(
            found = false,
            x = 0f,
            y = 0f,
            confidence = 0f,
            strategy = LocationStrategy.Type.OCR_TEXT,
            boundingBox = null
        )
    }
}

/**
 * 坐标降级策略
 * 使用归一化坐标作为最后的备选方案
 */
class CoordinateFallbackStrategy @Inject constructor(
    private val screenUtils: ScreenUtils
) : LocationStrategy {
    
    override suspend fun findTarget(
        screenshot: Bitmap,
        target: DSLTarget,
        timeoutSeconds: Int
    ): LocationResult {
        val boundingBox = target.boundingBox
        if (boundingBox == null || boundingBox.size != 4) {
            return createFailedResult()
        }
        
        return try {
            // 计算边界框中心点
            val centerX = (boundingBox[0] + boundingBox[2]) / 2f
            val centerY = (boundingBox[1] + boundingBox[3]) / 2f
            
            // 转换为像素坐标
            val pixelPoint = screenUtils.normalizedToPixel(centerX, centerY)
            val pixelBox = screenUtils.normalizedBoxToPixel(boundingBox)
            
            // 检查坐标是否在屏幕范围内
            if (screenUtils.isPointInScreen(pixelPoint.x, pixelPoint.y)) {
                LocationResult(
                    found = true,
                    x = pixelPoint.x.toFloat(),
                    y = pixelPoint.y.toFloat(),
                    confidence = 1.0f, // 坐标降级总是"成功"的
                    strategy = LocationStrategy.Type.COORDINATE_FALLBACK,
                    boundingBox = pixelBox
                )
            } else {
                Timber.w("Coordinate fallback point is outside screen bounds: ($centerX, $centerY)")
                createFailedResult()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Coordinate fallback failed")
            createFailedResult()
        }
    }
    
    override fun getStrategyType(): LocationStrategy.Type {
        return LocationStrategy.Type.COORDINATE_FALLBACK
    }
    
    private fun createFailedResult(): LocationResult {
        return LocationResult(
            found = false,
            x = 0f,
            y = 0f,
            confidence = 0f,
            strategy = LocationStrategy.Type.COORDINATE_FALLBACK,
            boundingBox = null
        )
    }
}

/**
 * 定位策略管理器
 * 协调三层级联定位策略
 */
@Singleton
class LocationStrategyManager @Inject constructor(
    private val strategies: List<LocationStrategy>,
    @dagger.hilt.android.qualifiers.ApplicationContext private val context: android.content.Context
) {
    
    companion object {
        private const val RETRY_INTERVAL_MS = 500L
    }
    
    /**
     * 使用三层级联策略查找目标
     */
    suspend fun findTarget(target: DSLTarget, timeoutSeconds: Int): LocationResult {
        val startTime = System.currentTimeMillis()
        val timeoutMs = timeoutSeconds * 1000L
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            // 获取当前屏幕截图
            val screenshot = captureScreen()
            if (screenshot == null) {
                Timber.w("Failed to capture screen")
                kotlinx.coroutines.delay(RETRY_INTERVAL_MS)
                continue
            }
            
            // 按优先级尝试各种策略
            for (strategy in strategies) {
                try {
                    val result = strategy.findTarget(screenshot, target, 1) // 每个策略1秒超时
                    
                    if (result.found) {
                        Timber.d("Target found using ${strategy.getStrategyType()}: confidence=${result.confidence}")
                        screenshot.recycle()
                        return result
                    }
                    
                } catch (e: Exception) {
                    Timber.w(e, "Strategy ${strategy.getStrategyType()} failed")
                }
            }
            
            screenshot.recycle()
            
            // 等待一段时间后重试
            kotlinx.coroutines.delay(RETRY_INTERVAL_MS)
        }
        
        // 所有策略都失败了
        Timber.w("All location strategies failed for target: ${target.description}")
        return LocationResult(
            found = false,
            x = 0f,
            y = 0f,
            confidence = 0f,
            strategy = LocationStrategy.Type.COORDINATE_FALLBACK,
            boundingBox = null
        )
    }
    
    /**
     * 初始化所有策略
     */
    suspend fun initialize() {
        // 初始化OpenCV等组件
        strategies.forEach { strategy ->
            if (strategy is VisualMatchingStrategy) {
                // OpenCV初始化在策略内部处理
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        strategies.forEach { strategy ->
            if (strategy is OCRStrategy) {
                // OCR处理器清理
            }
        }
    }
    
    /**
     * 捕获屏幕截图
     * TODO: 需要与ScreenCaptureService集成
     */
    private suspend fun captureScreen(): Bitmap? {
        // 这里需要从ScreenCaptureService获取截图
        // 暂时返回null，需要在后续实现中完善
        return null
    }
}
