#!/usr/bin/env python3
"""
GUI launcher for ShowForAI V3
Starts the graphical user interface application
"""

import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def main():
    """Main entry point"""
    try:
        # Import here to avoid issues if PyQt6 is not installed
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # Enable high DPI scaling
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
        )
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
        
        # Import main window
        from showforai.gui import MainWindow
        from showforai.config import Config
        from showforai.logger import setup_logger
        
        # Setup logging
        setup_logger()
        
        # Create application
        app = QApplication(sys.argv)
        app.setApplicationName("ShowForAI V3")
        app.setOrganizationName("ShowForAI")
        
        # Load configuration
        config = Config()
        
        # Create and show main window
        window = MainWindow(config)
        window.show()
        
        # Run application
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"Error: Missing required dependency: {e}")
        print("\nPlease install PyQt6:")
        print("  pip install PyQt6")
        sys.exit(1)
        
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()