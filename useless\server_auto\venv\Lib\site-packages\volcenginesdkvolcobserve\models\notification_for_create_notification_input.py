# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NotificationForCreateNotificationInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_methods': 'list[str]',
        'contact_group_ids': 'list[str]',
        'level': 'str',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'alert_methods': 'AlertMethods',
        'contact_group_ids': 'ContactGroupIds',
        'level': 'Level',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, alert_methods=None, contact_group_ids=None, level=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """NotificationForCreateNotificationInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_methods = None
        self._contact_group_ids = None
        self._level = None
        self._webhook_ids = None
        self.discriminator = None

        if alert_methods is not None:
            self.alert_methods = alert_methods
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if level is not None:
            self.level = level
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def alert_methods(self):
        """Gets the alert_methods of this NotificationForCreateNotificationInput.  # noqa: E501


        :return: The alert_methods of this NotificationForCreateNotificationInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_methods

    @alert_methods.setter
    def alert_methods(self, alert_methods):
        """Sets the alert_methods of this NotificationForCreateNotificationInput.


        :param alert_methods: The alert_methods of this NotificationForCreateNotificationInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Email", "Phone", "SMS", "Webhook"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(alert_methods).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `alert_methods` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(alert_methods) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._alert_methods = alert_methods

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this NotificationForCreateNotificationInput.  # noqa: E501


        :return: The contact_group_ids of this NotificationForCreateNotificationInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this NotificationForCreateNotificationInput.


        :param contact_group_ids: The contact_group_ids of this NotificationForCreateNotificationInput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def level(self):
        """Gets the level of this NotificationForCreateNotificationInput.  # noqa: E501


        :return: The level of this NotificationForCreateNotificationInput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this NotificationForCreateNotificationInput.


        :param level: The level of this NotificationForCreateNotificationInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["notice", "warning", "critical", "recovery"]  # noqa: E501
        if (self._configuration.client_side_validation and
                level not in allowed_values):
            raise ValueError(
                "Invalid value for `level` ({0}), must be one of {1}"  # noqa: E501
                .format(level, allowed_values)
            )

        self._level = level

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this NotificationForCreateNotificationInput.  # noqa: E501


        :return: The webhook_ids of this NotificationForCreateNotificationInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this NotificationForCreateNotificationInput.


        :param webhook_ids: The webhook_ids of this NotificationForCreateNotificationInput.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NotificationForCreateNotificationInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NotificationForCreateNotificationInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NotificationForCreateNotificationInput):
            return True

        return self.to_dict() != other.to_dict()
