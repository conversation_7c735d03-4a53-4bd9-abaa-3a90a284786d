
# 快速坐标转换工具
def convert(ymin, xmin, ymax, xmax, img_width=512, img_height=512):
    """将Gemini坐标转换为像素坐标"""
    x = int(xmin * img_width / 1000)
    y = int(ymin * img_height / 1000)
    width = int((xmax - xmin) * img_width / 1000)
    height = int((ymax - ymin) * img_height / 1000)
    
    print(f"Gemini坐标: [{ymin}, {xmin}, {ymax}, {xmax}]")
    print(f"图片尺寸: {img_width}x{img_height}")
    print(f"转换后:")
    print(f"  X: {x}")
    print(f"  Y: {y}")
    print(f"  Width: {width}")
    print(f"  Height: {height}")
    print(f"  右下角: ({x + width}, {y + height})")
    
    return {'x': x, 'y': y, 'width': width, 'height': height}

# 使用示例:
# convert(388, 330, 470, 438, 512, 512)
