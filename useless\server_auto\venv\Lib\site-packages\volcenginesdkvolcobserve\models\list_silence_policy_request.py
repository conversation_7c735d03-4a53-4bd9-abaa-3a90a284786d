# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListSilencePolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_state': 'list[str]',
        'ids': 'list[str]',
        'name': 'str',
        'namespaces': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'silence_type': 'list[str]',
        'valid_state': 'str'
    }

    attribute_map = {
        'enable_state': 'EnableState',
        'ids': 'Ids',
        'name': 'Name',
        'namespaces': 'Namespaces',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'silence_type': 'SilenceType',
        'valid_state': 'ValidState'
    }

    def __init__(self, enable_state=None, ids=None, name=None, namespaces=None, page_number=None, page_size=None, silence_type=None, valid_state=None, _configuration=None):  # noqa: E501
        """ListSilencePolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_state = None
        self._ids = None
        self._name = None
        self._namespaces = None
        self._page_number = None
        self._page_size = None
        self._silence_type = None
        self._valid_state = None
        self.discriminator = None

        if enable_state is not None:
            self.enable_state = enable_state
        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name
        if namespaces is not None:
            self.namespaces = namespaces
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if silence_type is not None:
            self.silence_type = silence_type
        if valid_state is not None:
            self.valid_state = valid_state

    @property
    def enable_state(self):
        """Gets the enable_state of this ListSilencePolicyRequest.  # noqa: E501


        :return: The enable_state of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._enable_state

    @enable_state.setter
    def enable_state(self, enable_state):
        """Sets the enable_state of this ListSilencePolicyRequest.


        :param enable_state: The enable_state of this ListSilencePolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._enable_state = enable_state

    @property
    def ids(self):
        """Gets the ids of this ListSilencePolicyRequest.  # noqa: E501


        :return: The ids of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this ListSilencePolicyRequest.


        :param ids: The ids of this ListSilencePolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this ListSilencePolicyRequest.  # noqa: E501


        :return: The name of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListSilencePolicyRequest.


        :param name: The name of this ListSilencePolicyRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def namespaces(self):
        """Gets the namespaces of this ListSilencePolicyRequest.  # noqa: E501


        :return: The namespaces of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._namespaces

    @namespaces.setter
    def namespaces(self, namespaces):
        """Sets the namespaces of this ListSilencePolicyRequest.


        :param namespaces: The namespaces of this ListSilencePolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._namespaces = namespaces

    @property
    def page_number(self):
        """Gets the page_number of this ListSilencePolicyRequest.  # noqa: E501


        :return: The page_number of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListSilencePolicyRequest.


        :param page_number: The page_number of this ListSilencePolicyRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListSilencePolicyRequest.  # noqa: E501


        :return: The page_size of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListSilencePolicyRequest.


        :param page_size: The page_size of this ListSilencePolicyRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def silence_type(self):
        """Gets the silence_type of this ListSilencePolicyRequest.  # noqa: E501


        :return: The silence_type of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._silence_type

    @silence_type.setter
    def silence_type(self, silence_type):
        """Sets the silence_type of this ListSilencePolicyRequest.


        :param silence_type: The silence_type of this ListSilencePolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._silence_type = silence_type

    @property
    def valid_state(self):
        """Gets the valid_state of this ListSilencePolicyRequest.  # noqa: E501


        :return: The valid_state of this ListSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._valid_state

    @valid_state.setter
    def valid_state(self, valid_state):
        """Sets the valid_state of this ListSilencePolicyRequest.


        :param valid_state: The valid_state of this ListSilencePolicyRequest.  # noqa: E501
        :type: str
        """

        self._valid_state = valid_state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListSilencePolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListSilencePolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListSilencePolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
