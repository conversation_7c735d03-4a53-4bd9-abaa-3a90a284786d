"""
Test script for multi-level matching implementation.

This script tests the 4-level degradation strategy:
1. Direct template matching
2. ORB feature matching  
3. SIFT feature matching
4. Multi-scale template matching
"""

import cv2
import numpy as np
import time
from pathlib import Path
import json
from showforai.executor.element_matcher import ElementMatcher
from showforai.executor.multi_level_matcher import <PERSON>LevelMatcher, MatchLevel
from showforai.executor.match_result import MatchResult, MatchQuality

def create_test_images():
    """Create test images for matching."""
    # Create a simple test screenshot (800x600)
    screenshot = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # Add some UI elements
    # Button 1
    cv2.rectangle(screenshot, (100, 100), (250, 150), (0, 128, 255), -1)
    cv2.putText(screenshot, "Submit", (120, 130), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # Button 2
    cv2.rectangle(screenshot, (300, 200), (450, 250), (0, 255, 128), -1)
    cv2.putText(screenshot, "Cancel", (320, 230), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Create template (exact match)
    template_exact = screenshot[100:150, 100:250].copy()
    
    # Create template with slight modification (for testing degradation)
    template_modified = template_exact.copy()
    # Add some noise
    noise = np.random.randint(-20, 20, template_modified.shape, dtype=np.int16)
    template_modified = np.clip(template_modified.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Create rotated template (for feature matching)
    center = (template_exact.shape[1]//2, template_exact.shape[0]//2)
    matrix = cv2.getRotationMatrix2D(center, 5, 1.0)  # 5 degree rotation
    template_rotated = cv2.warpAffine(template_exact, matrix, (template_exact.shape[1], template_exact.shape[0]))
    
    # Create scaled template
    template_scaled = cv2.resize(template_exact, None, fx=1.1, fy=1.1, interpolation=cv2.INTER_CUBIC)
    
    return screenshot, template_exact, template_modified, template_rotated, template_scaled

def test_element_matcher():
    """Test the ElementMatcher with multi-level support."""
    print("="*50)
    print("Testing ElementMatcher with Multi-Level Support")
    print("="*50)
    
    # Create test images
    screenshot, template_exact, template_modified, template_rotated, template_scaled = create_test_images()
    
    # Initialize matcher with multi-level support
    matcher = ElementMatcher(
        confidence_threshold=0.85,
        use_multi_level=True,
        use_standardized_matching=False  # Disable for clearer testing
    )
    
    # Test 1: Exact template (should match at Level 1)
    print("\nTest 1: Exact Template Match")
    print("-"*30)
    result = matcher.find_element_multi_level(screenshot, template_exact)
    print(f"Found: {result.found}")
    print(f"Method: {result.method.value if result.method else 'None'}")
    print(f"Confidence: {result.confidence:.3f}")
    print(f"Quality: {result.quality.value if result.quality else 'None'}")
    print(f"Time: {result.time_ms:.2f}ms")
    if result.metadata and 'report' in result.metadata:
        print("\nDetailed Report:")
        print(result.metadata['report'])
    
    # Test 2: Modified template (might need Level 2 or 3)
    print("\n" + "="*50)
    print("Test 2: Modified Template (with noise)")
    print("-"*30)
    result = matcher.find_element_multi_level(screenshot, template_modified)
    print(f"Found: {result.found}")
    print(f"Method: {result.method.value if result.method else 'None'}")
    print(f"Confidence: {result.confidence:.3f}")
    print(f"Quality: {result.quality.value if result.quality else 'None'}")
    print(f"Time: {result.time_ms:.2f}ms")
    
    # Test 3: Rotated template (should need feature matching)
    print("\n" + "="*50)
    print("Test 3: Rotated Template (5 degrees)")
    print("-"*30)
    result = matcher.find_element_multi_level(screenshot, template_rotated)
    print(f"Found: {result.found}")
    print(f"Method: {result.method.value if result.method else 'None'}")
    print(f"Confidence: {result.confidence:.3f}")
    print(f"Quality: {result.quality.value if result.quality else 'None'}")
    print(f"Time: {result.time_ms:.2f}ms")
    
    # Test 4: Scaled template (might need Level 4)
    print("\n" + "="*50)
    print("Test 4: Scaled Template (110%)")
    print("-"*30)
    result = matcher.find_element_multi_level(screenshot, template_scaled)
    print(f"Found: {result.found}")
    print(f"Method: {result.method.value if result.method else 'None'}")
    print(f"Confidence: {result.confidence:.3f}")
    print(f"Quality: {result.quality.value if result.quality else 'None'}")
    print(f"Time: {result.time_ms:.2f}ms")
    
    # Print overall statistics
    print("\n" + "="*50)
    print("Overall Statistics")
    print("-"*30)
    stats = matcher.get_stats()
    print(f"Total matches: {stats['total_matches']}")
    print(f"Successful: {stats['successful_matches']}")
    print(f"Failed: {stats['failed_matches']}")
    print(f"Success rate: {stats['success_rate']:.1%}")
    print(f"Average time: {stats['average_time']:.2f}s")
    
    # Get multi-level statistics if available
    if matcher.multi_level_matcher:
        ml_stats = matcher.multi_level_matcher.get_stats()
        print("\nMulti-Level Statistics:")
        print(f"Overall success rate: {ml_stats['overall_success_rate']:.1%}")
        print("\nPer-level success rates:")
        for level, rate in ml_stats['level_success_rates'].items():
            attempts = ml_stats['level_attempts'].get(MatchLevel[level], 0)
            print(f"  {level}: {rate:.1%} ({attempts} attempts)")

def test_direct_multi_level():
    """Test the MultiLevelMatcher directly."""
    print("\n" + "="*50)
    print("Testing MultiLevelMatcher Directly")
    print("="*50)
    
    # Create test images
    screenshot, template_exact, template_modified, template_rotated, template_scaled = create_test_images()
    
    # Initialize multi-level matcher
    matcher = MultiLevelMatcher()
    
    # Test with increasingly difficult matches
    test_cases = [
        ("Exact match", template_exact),
        ("Noisy template", template_modified),
        ("Rotated template", template_rotated),
        ("Scaled template", template_scaled)
    ]
    
    for name, template in test_cases:
        print(f"\n{name}:")
        print("-"*30)
        
        result = matcher.match(screenshot, template)
        
        if result.found:
            print(f"✓ Matched at Level {result.level.value}: {result.level.name}")
            print(f"  Confidence: {result.confidence:.3f}")
            print(f"  Location: {result.location}")
            print(f"  Time: {result.total_time_ms:.2f}ms")
        else:
            print(f"✗ No match found")
            print(f"  Levels tried: {', '.join([l.name for l in result.levels_tried])}")
            print(f"  Total time: {result.total_time_ms:.2f}ms")
        
        # Show breakdown
        print(f"\n  Time breakdown:")
        for level, time_ms in result.level_times.items():
            status = "✓" if result.found and result.level == level else "✗"
            print(f"    {level.name}: {time_ms:.2f}ms {status}")

def test_quality_thresholds():
    """Test that quality thresholds are maintained."""
    print("\n" + "="*50)
    print("Testing Quality Thresholds")
    print("="*50)
    
    # Create a screenshot with multiple similar elements
    screenshot = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # Add multiple buttons with varying clarity
    buttons = [
        {"pos": (100, 100), "color": (0, 128, 255), "text": "Button1", "quality": 1.0},
        {"pos": (300, 100), "color": (0, 128, 255), "text": "Button2", "quality": 0.9},
        {"pos": (500, 100), "color": (0, 128, 255), "text": "Button3", "quality": 0.8},
    ]
    
    for btn in buttons:
        x, y = btn["pos"]
        # Draw button
        cv2.rectangle(screenshot, (x, y), (x+150, y+50), btn["color"], -1)
        
        # Add text with varying quality (blur)
        text_img = np.ones((50, 150, 3), dtype=np.uint8) * btn["color"][0]
        cv2.putText(text_img, btn["text"], (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Apply quality degradation (blur)
        if btn["quality"] < 1.0:
            blur_size = int((1.0 - btn["quality"]) * 10) * 2 + 1
            text_img = cv2.GaussianBlur(text_img, (blur_size, blur_size), 0)
        
        screenshot[y:y+50, x:x+150] = text_img
    
    # Create template from first button
    template = screenshot[100:150, 100:250].copy()
    
    # Test matching
    matcher = MultiLevelMatcher()
    result = matcher.match(screenshot, template)
    
    print(f"Match found: {result.found}")
    if result.found:
        print(f"Level: {result.level.name}")
        print(f"Confidence: {result.confidence:.3f}")
        print(f"Quality maintained: {result.confidence >= 0.85}")
    
    # Verify thresholds are maintained
    assert not result.found or result.confidence >= 0.80, "Quality threshold violated!"
    print("\n✓ Quality thresholds maintained - no degradation below configured limits")

def main():
    """Run all tests."""
    print("Multi-Level Matching Test Suite")
    print("="*50)
    print("Testing 4-level degradation strategy:")
    print("1. Direct template matching (≥0.85)")
    print("2. ORB feature matching (15+ features, ratio<0.7, inliers>60%)")
    print("3. SIFT feature matching (20+ features, ratio<0.65, inliers>70%)")
    print("4. Multi-scale template matching (≥0.80)")
    print("="*50)
    
    try:
        # Run tests
        test_element_matcher()
        test_direct_multi_level()
        test_quality_thresholds()
        
        print("\n" + "="*50)
        print("✓ All tests completed successfully")
        print("="*50)
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())