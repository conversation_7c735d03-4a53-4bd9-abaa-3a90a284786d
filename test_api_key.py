#!/usr/bin/env python3
"""
测试Replicate API Key和OmniParserV2模型

这个脚本会验证API key是否有效，并测试OmniParserV2模型的基本功能
"""

import os
import json
import time
import requests
from datetime import datetime

# 设置API Token
API_TOKEN = "****************************************"
os.environ['REPLICATE_API_TOKEN'] = API_TOKEN

def test_api_connection():
    """测试API连接"""
    print("🔑 测试API连接...")
    
    try:
        import replicate
        
        # 尝试获取账户信息
        headers = {
            "Authorization": f"Token {API_TOKEN}",
            "Content-Type": "application/json"
        }
        
        response = requests.get("https://api.replicate.com/v1/account", headers=headers)
        
        if response.status_code == 200:
            account_info = response.json()
            print(f"✅ API连接成功!")
            print(f"   账户: {account_info.get('username', 'N/A')}")
            print(f"   类型: {account_info.get('type', 'N/A')}")
            return True
        else:
            print(f"❌ API连接失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API连接测试失败: {str(e)}")
        return False

def test_omniparser_model():
    """测试OmniParserV2模型"""
    print("\n🧪 测试OmniParserV2模型...")
    
    try:
        import replicate
        
        # 使用一个简单的测试图像URL
        test_image_url = "https://replicate.delivery/pbxt/JrmRlKKLLaOxnlXfiFtnhwzGrKOc4Nv8Jws9DKMBhPt8hSQE/output.png"
        
        print(f"📸 测试图像: {test_image_url}")
        print("⏳ 正在处理，请稍候...")
        
        start_time = time.time()
        
        # 调用OmniParserV2模型 - 使用完整的版本号
        output = replicate.run(
            "microsoft/omniparser-v2:49cf3d41b8d3aca1360514e83be4c97131ce8f0d99abfc365526d8384caa88df",
            input={
                "image": test_image_url,
                "box_threshold": 0.05,
                "iou_threshold": 0.1,
                "imgsz": 640
            }
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 模型调用成功!")
        print(f"⏱️  处理时间: {processing_time:.2f} 秒")
        
        # 解析结果
        if "elements" in output and "img" in output:
            elements_data = json.loads(output["elements"])
            
            print(f"🔍 检测到 {len(elements_data)} 个UI元素")
            print(f"🖼️  标注图像: {output['img']}")
            
            # 显示前几个元素的详细信息
            print("\n📋 检测到的元素 (前5个):")
            for i, elem in enumerate(elements_data[:5]):
                print(f"   {i+1}. 文本: '{elem.get('text', 'N/A')}'")
                print(f"      位置: {elem.get('bbox', 'N/A')}")
                print(f"      类型: {elem.get('type', 'N/A')}")
                print()
            
            # 保存结果
            result = {
                "success": True,
                "processing_time": processing_time,
                "element_count": len(elements_data),
                "annotated_image_url": output["img"],
                "elements": elements_data,
                "test_timestamp": datetime.now().isoformat(),
                "test_image_url": test_image_url
            }
            
            # 保存到文件
            with open("api_test_result.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 结果已保存到 api_test_result.json")
            
            # 下载标注图像
            try:
                print("🖼️  下载标注图像...")
                img_response = requests.get(output["img"])
                img_response.raise_for_status()
                
                with open("annotated_test_image.png", "wb") as f:
                    f.write(img_response.content)
                
                print("✅ 标注图像已保存为 annotated_test_image.png")
                
            except Exception as e:
                print(f"⚠️  下载图像失败: {str(e)}")
            
            return True
            
        else:
            print("❌ 模型返回格式异常")
            print(f"   返回内容: {output}")
            return False
            
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        return False

def test_different_parameters():
    """测试不同参数设置"""
    print("\n🔬 测试不同参数设置...")
    
    try:
        import replicate
        
        test_image_url = "https://replicate.delivery/pbxt/JrmRlKKLLaOxnlXfiFtnhwzGrKOc4Nv8Jws9DKMBhPt8hSQE/output.png"
        
        # 不同的参数组合
        test_configs = [
            {"box_threshold": 0.03, "name": "高敏感度"},
            {"box_threshold": 0.1, "name": "低敏感度"},
            {"imgsz": 1024, "name": "高分辨率"}
        ]
        
        results = []
        
        for config in test_configs:
            print(f"\n测试配置: {config['name']}")
            
            try:
                start_time = time.time()
                
                input_params = {
                    "image": test_image_url,
                    "box_threshold": config.get("box_threshold", 0.05),
                    "iou_threshold": 0.1,
                    "imgsz": config.get("imgsz", 640)
                }
                
                print(f"   参数: {input_params}")
                
                output = replicate.run("microsoft/omniparser-v2:49cf3d41b8d3aca1360514e83be4c97131ce8f0d99abfc365526d8384caa88df", input=input_params)
                
                end_time = time.time()
                elements_data = json.loads(output["elements"])
                
                result = {
                    "config": config,
                    "success": True,
                    "processing_time": end_time - start_time,
                    "element_count": len(elements_data)
                }
                
                print(f"   ✅ 成功: {len(elements_data)} 个元素, {result['processing_time']:.2f}秒")
                
            except Exception as e:
                result = {
                    "config": config,
                    "success": False,
                    "error": str(e)
                }
                print(f"   ❌ 失败: {str(e)}")
            
            results.append(result)
            time.sleep(2)  # 避免API速率限制
        
        # 保存参数测试结果
        with open("parameter_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 参数测试结果已保存到 parameter_test_results.json")
        
        return results
        
    except Exception as e:
        print(f"❌ 参数测试失败: {str(e)}")
        return []

def main():
    """主测试函数"""
    print("🚀 Replicate API Key 和 OmniParserV2 模型测试")
    print("=" * 60)
    print(f"🔑 API Token: {API_TOKEN[:15]}...")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 安装依赖检查
    try:
        import replicate
        print("✅ replicate 库已安装")
    except ImportError:
        print("❌ 请先安装 replicate 库: pip install replicate")
        return
    
    # 1. 测试API连接
    api_ok = test_api_connection()
    
    if not api_ok:
        print("\n❌ API连接失败，请检查API Token是否正确")
        return
    
    # 2. 测试基本模型功能
    model_ok = test_omniparser_model()
    
    if not model_ok:
        print("\n❌ 模型测试失败")
        return
    
    # 3. 测试不同参数
    param_results = test_different_parameters()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   🔑 API连接: {'✅ 成功' if api_ok else '❌ 失败'}")
    print(f"   🧪 模型测试: {'✅ 成功' if model_ok else '❌ 失败'}")
    print(f"   🔬 参数测试: {len([r for r in param_results if r.get('success', False)])}/{len(param_results)} 成功")
    
    if api_ok and model_ok:
        print("\n🎉 恭喜！你的API Key工作正常，可以开始使用OmniParserV2了！")
        print("\n📁 生成的文件:")
        print("   - api_test_result.json (基本测试结果)")
        print("   - annotated_test_image.png (标注图像)")
        print("   - parameter_test_results.json (参数测试结果)")
    else:
        print("\n❌ 测试未完全通过，请检查错误信息")

if __name__ == "__main__":
    main()
