# Critical Fixes Technical Design

## P0级修复设计

### 1. 循环导入解决方案

#### 问题分析
```
ai.element_detector → gui.status_manager
gui.__init__ → gui.main_window
gui.main_window → api.adapter  
api.adapter → ai.ai_service
ai.ai_service → ai.element_detector
形成循环: ai → gui → api → ai
```

#### 解决方案：延迟导入 + 依赖注入
1. **移除ai模块对gui的直接依赖**
   - 将StatusManager的导入改为运行时注入
   - 使用回调函数替代直接调用

2. **重构导入结构**
   ```python
   # element_detector.py - 移除GUI导入
   class ElementDetector:
       def __init__(self, status_callback=None):
           self.status_callback = status_callback
       
       def update_status(self, message):
           if self.status_callback:
               self.status_callback(message)
   ```

3. **api.adapter延迟导入ai_service**
   ```python
   # api/adapter.py
   def create_api_adapter():
       # 延迟导入，避免循环
       from showforai.ai.ai_service import AIService
       return APIAdapter(AIService())
   ```

### 2. 类名统一方案

#### 标准化器修复
- 保持 `Standardizer` 作为正式类名
- 更新所有测试文件的导入

#### 缓冲区修复  
- 保持 `CircularFrameBuffer` 作为正式类名
- 更新测试和其他模块的引用

### 3. 网络状态与离线模式实现

#### 网络状态监控
```python
# utils/network_checker.py 增强
class NetworkChecker:
    def __init__(self):
        self.is_online = False
        self.callbacks = []
        self._start_monitoring()
    
    def register_callback(self, callback):
        self.callbacks.append(callback)
    
    def _check_network(self):
        # 检测网络连接
        # 触发回调通知状态变化
```

#### 录制按钮控制
```python
# gui/main_window.py
def update_record_button_state(self):
    if not self.network_checker.is_online:
        self.record_button.setEnabled(False)
        self.record_button.setToolTip("录制需要网络连接以进行AI识别")
    else:
        self.record_button.setEnabled(True)
        self.record_button.setToolTip("开始录制")
```

## P1级修复设计

### 1. 录制流程完善

#### 10FPS循环缓冲实现
```python
# recorder/recorder.py
class Recorder:
    def __init__(self):
        self.buffer_manager = FrameBufferManager()
        self.buffer_manager.start_capture()  # 启动10FPS捕获
    
    def on_click(self, x, y):
        # 获取点击前第3帧
        frame = self.buffer_manager.get_frame_before_click(3)
        if frame:
            self.process_click(frame, x, y)
```

#### 精确时间间隔记录
```python
# utils/time_utils.py 已实现
# 使用 time.perf_counter() 获取微秒级精度
def record_action(self):
    current_time = get_timestamp()
    if self.last_action_time:
        interval = calculate_interval(self.last_action_time, current_time)
    self.last_action_time = current_time
```

### 2. 智能等待机制实现

#### 主动模式
```python
# executor/smart_wait_manager.py
class SmartWaitManager:
    def wait_for_element(self, template, recorded_interval):
        # 首次等待录制时的间隔
        time.sleep(recorded_interval)
        
        # 持续识别直到成功
        while not self.match_element(template):
            time.sleep(1.0)  # 每秒重试
```

#### 辅助模式
```python
def auxiliary_mode_wait(self, template):
    # 无限等待
    while True:
        if self.match_element(template):
            return True
        time.sleep(0.1)  # 高频检测
```

### 3. BBOX统一处理

#### 集中裁切处理器
```python
# preprocessing/bbox_processor.py
class BBoxProcessor:
    @staticmethod
    def crop_element(image_768, bbox):
        """在768×768图像上裁切元素
        
        Args:
            image_768: 768×768标准化图像
            bbox: (x, y, width, height) 基于768坐标系
        
        Returns:
            裁切后的元素图像
        """
        x, y, w, h = bbox
        return image_768.crop((x, y, x+w, y+h))
```

## P2级修复设计

### 1. 分享模块集成
- 实现 share_script API方法
- 生成分享链接
- 实现导入逻辑

### 2. 对话框实现
- 批量重命名对话框
- 永久删除确认对话框
- 设置对话框

### 3. 帮助文档
- 创建markdown文档
- 实现文档查看器
- 添加帮助链接

## 实施顺序

### 第一步：解决循环导入（最紧急）
1. 重构element_detector.py - 移除GUI依赖
2. 修改api/adapter.py - 延迟导入
3. 验证程序能启动

### 第二步：修复类名不一致
1. 更新测试文件导入
2. 确保测试能运行

### 第三步：实现离线模式
1. 增强网络检测
2. 实现录制按钮控制
3. 添加状态提示

### 第四步：完善核心功能
1. 集成循环缓冲
2. 实现智能等待
3. 统一BBOX处理

### 第五步：功能完善
1. 完成分享模块
2. 实现对话框
3. 添加帮助文档