"""
Test script for enhanced API Security implementation
Tests rate limiting, audit logging, and encrypted storage
"""

import time
import json
from pathlib import Path
import threading
from showforai.security.api_security import APISecurityManager, RateLimiter, AuditLogger
from showforai.security.secure_config import SecureAP<PERSON><PERSON>eyManager

def test_rate_limiter():
    """Test rate limiting functionality"""
    print("\n=== Testing Rate Limiter ===")
    
    # Create rate limiter with 5 requests per 10 seconds for testing
    rate_limiter = RateLimiter(max_requests=5, window_seconds=10)
    
    client_id = "test_client_123"
    
    # Test normal requests within limit
    print("\nTesting requests within limit:")
    for i in range(5):
        allowed, info = rate_limiter.is_allowed(client_id)
        print(f"Request {i+1}: Allowed={allowed}, Remaining={info['remaining']}")
        assert allowed, f"Request {i+1} should be allowed"
    
    # Test rate limit exceeded
    print("\nTesting rate limit exceeded:")
    allowed, info = rate_limiter.is_allowed(client_id)
    print(f"Request 6: Allowed={allowed}, Reset after={info.get('reset_after', 0):.1f}s")
    assert not allowed, "Request 6 should be blocked"
    
    # Test multiple clients
    print("\nTesting multiple clients:")
    client2 = "test_client_456"
    allowed, info = rate_limiter.is_allowed(client2)
    print(f"Client 2 Request 1: Allowed={allowed}, Remaining={info['remaining']}")
    assert allowed, "Different client should be allowed"
    
    # Test sliding window
    print("\nTesting sliding window (waiting 5 seconds)...")
    time.sleep(5)
    
    # Should still be blocked as not enough time passed
    allowed, info = rate_limiter.is_allowed(client_id)
    print(f"After 5s: Allowed={allowed}")
    assert not allowed, "Should still be blocked after 5 seconds"
    
    print("\nWaiting another 5.5 seconds for window to slide...")
    time.sleep(5.5)
    
    # Now should be allowed again as oldest request expired
    allowed, info = rate_limiter.is_allowed(client_id)
    print(f"After 10.5s total: Allowed={allowed}, Remaining={info['remaining']}")
    assert allowed, "Should be allowed after window slides"
    
    # Test stats
    stats = rate_limiter.get_stats()
    print(f"\nRate limiter stats: {json.dumps(stats, indent=2)}")
    
    print("✓ Rate limiter tests passed")


def test_audit_logger():
    """Test audit logging functionality"""
    print("\n=== Testing Audit Logger ===")
    
    # Create audit logger with custom directory
    log_dir = Path("./test_audit_logs")
    audit_logger = AuditLogger(log_dir=log_dir, max_log_size_mb=1)
    
    # Test logging different event types
    print("\nLogging various events:")
    
    # Log successful request
    audit_logger.log_request(
        method="GET",
        url="https://api.example.com/data",
        client_id="client_123",
        success=True,
        response_time=0.125
    )
    print("✓ Logged successful request")
    
    # Log failed request
    audit_logger.log_request(
        method="POST",
        url="https://api.example.com/upload",
        client_id="client_456",
        success=False,
        response_time=0.050,
        error="Invalid signature"
    )
    print("✓ Logged failed request")
    
    # Log security events
    audit_logger.log_security_event(
        event="rate_limit_exceeded",
        client_id="client_789",
        details={"requests": 61, "limit": 60}
    )
    print("✓ Logged rate limit event")
    
    audit_logger.log_security_event(
        event="replay_attack",
        client_id="malicious_client",
        details={"nonce": "abc123", "timestamp": time.time()}
    )
    print("✓ Logged replay attack")
    
    # Test retrieving events
    print("\nRetrieving recent events:")
    recent_events = audit_logger.get_recent_events(hours=1)
    print(f"Found {len(recent_events)} recent events")
    
    for event in recent_events[:3]:  # Show first 3 events
        print(f"  - {event['event_type']}: {event['severity']} at {event['timestamp'][:19]}")
    
    # Clean up test directory
    import shutil
    if log_dir.exists():
        shutil.rmtree(log_dir)
        print(f"\n✓ Cleaned up test directory: {log_dir}")
    
    print("✓ Audit logger tests passed")


def test_integrated_security():
    """Test integrated API security with all features"""
    print("\n=== Testing Integrated API Security ===")
    
    # Create security manager with all features enabled
    security_manager = APISecurityManager(
        secret_key="test_secret_key_123",
        max_timestamp_age=300,
        enable_rate_limiting=True,
        enable_audit_logging=True
    )
    
    # Test 1: Valid request
    print("\nTest 1: Valid request")
    method = "POST"
    url = "https://api.example.com/process"
    body = json.dumps({"data": "test"})
    
    # Create secure headers
    headers = security_manager.create_secure_headers(method, url, body=body)
    headers['X-API-Key'] = "test_api_key_123"
    
    # Verify request
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=headers,
        body=body,
        client_id="test_client"
    )
    
    assert valid, f"Valid request should pass: {error}"
    print(f"✓ Valid request passed")
    
    # Test 2: Replay attack detection
    print("\nTest 2: Replay attack detection")
    # Try to use same nonce again
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=headers,
        body=body,
        client_id="test_client"
    )
    
    assert not valid, "Replay attack should be detected"
    assert "replay" in error.lower(), f"Error should mention replay: {error}"
    print(f"✓ Replay attack detected: {error}")
    
    # Test 3: Expired timestamp
    print("\nTest 3: Expired timestamp")
    old_timestamp = str(int(time.time()) - 400)  # 400 seconds ago
    bad_headers = headers.copy()
    bad_headers['X-Timestamp'] = old_timestamp
    bad_headers['X-Nonce'] = security_manager.generate_nonce()  # New nonce
    
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=bad_headers,
        body=body,
        client_id="test_client"
    )
    
    assert not valid, "Expired timestamp should fail"
    print(f"✓ Expired timestamp detected: {error}")
    
    # Test 4: Invalid signature
    print("\nTest 4: Invalid signature")
    bad_headers = security_manager.create_secure_headers(method, url, body=body)
    bad_headers['X-Signature'] = "invalid_signature_12345"
    bad_headers['X-API-Key'] = "test_api_key_123"
    
    valid, error = security_manager.verify_request(
        method=method,
        url=url,
        headers=bad_headers,
        body=body,
        client_id="test_client"
    )
    
    assert not valid, "Invalid signature should fail"
    assert "signature" in error.lower(), f"Error should mention signature: {error}"
    print(f"✓ Invalid signature detected: {error}")
    
    # Test 5: Rate limiting
    print("\nTest 5: Rate limiting (60 requests/minute)")
    print("Sending rapid requests to trigger rate limit...")
    
    # Create a fresh manager with lower limits for testing
    test_manager = APISecurityManager(
        secret_key="test_key",
        enable_rate_limiting=True,
        enable_audit_logging=False
    )
    test_manager.rate_limiter.max_requests = 3  # Lower limit for testing
    
    client_id = "rate_test_client"
    requests_sent = 0
    rate_limited = False
    
    for i in range(5):
        headers = test_manager.create_secure_headers("GET", "https://api.test.com/data")
        valid, error = test_manager.verify_request(
            method="GET",
            url="https://api.test.com/data",
            headers=headers,
            client_id=client_id
        )
        
        requests_sent += 1
        if not valid and "rate limit" in error.lower():
            rate_limited = True
            print(f"✓ Rate limited after {requests_sent} requests: {error}")
            break
    
    assert rate_limited, "Should hit rate limit"
    
    print("\n✓ All integrated security tests passed")


def test_secure_api_key_storage():
    """Test secure API key storage with encryption"""
    print("\n=== Testing Secure API Key Storage ===")
    
    # Create secure API key manager
    key_manager = SecureAPIKeyManager(fallback_path=Path("./test_secure_keys.enc"))
    
    # Initialize encryption
    key_manager.secure_config.initialize_encryption("test_master_password_123")
    
    # Test storing API key
    print("\nStoring API keys:")
    key_manager.store_api_key(
        "openai_key",
        "sk-test123456789",
        metadata={"service": "OpenAI", "created": time.time()}
    )
    print("✓ Stored OpenAI key")
    
    key_manager.store_api_key(
        "supabase_key",
        "sb-test987654321",
        metadata={"service": "Supabase", "created": time.time()}
    )
    print("✓ Stored Supabase key")
    
    # Test retrieving API key
    print("\nRetrieving API keys:")
    retrieved_key = key_manager.retrieve_api_key("openai_key")
    assert retrieved_key == "sk-test123456789", "Retrieved key should match"
    print(f"✓ Retrieved OpenAI key: {retrieved_key[:10]}...")
    
    # Test key rotation
    print("\nTesting key rotation:")
    new_key = key_manager.generate_api_secret()
    success = key_manager.rotate_api_key("openai_key", new_key, grace_period_hours=1)
    assert success, "Key rotation should succeed"
    print(f"✓ Rotated OpenAI key to: {new_key[:10]}...")
    
    # Verify new key
    rotated_key = key_manager.retrieve_api_key("openai_key")
    assert rotated_key == new_key, "Rotated key should be the new key"
    print("✓ Verified rotated key")
    
    # Clean up
    if Path("./test_secure_keys.enc").exists():
        Path("./test_secure_keys.enc").unlink()
        print("\n✓ Cleaned up test files")
    
    print("✓ Secure API key storage tests passed")


def run_performance_test():
    """Test performance of security operations"""
    print("\n=== Performance Testing ===")
    
    security_manager = APISecurityManager(
        secret_key="perf_test_key",
        enable_rate_limiting=True,
        enable_audit_logging=False  # Disable for performance test
    )
    
    # Test signature generation performance
    print("\nTesting signature generation performance:")
    start = time.perf_counter()
    iterations = 1000
    
    for i in range(iterations):
        headers = security_manager.create_secure_headers(
            method="POST",
            url=f"https://api.test.com/endpoint/{i}",
            body=json.dumps({"data": f"test_{i}"})
        )
    
    elapsed = time.perf_counter() - start
    per_request = (elapsed / iterations) * 1000
    print(f"Generated {iterations} signatures in {elapsed:.3f}s")
    print(f"Average time per signature: {per_request:.2f}ms")
    assert per_request < 10, "Signature generation should be fast (<10ms)"
    
    # Test verification performance
    print("\nTesting verification performance:")
    
    # Pre-generate headers for testing
    test_headers = []
    for i in range(100):
        headers = security_manager.create_secure_headers(
            method="GET",
            url=f"https://api.test.com/data/{i}"
        )
        test_headers.append(headers)
    
    start = time.perf_counter()
    
    for headers in test_headers:
        valid, error = security_manager.verify_request(
            method="GET",
            url="https://api.test.com/data/0",  # Use same URL for simplicity
            headers=headers,
            client_id=f"client_{hash(str(headers)) % 10}"  # Distribute across clients
        )
    
    elapsed = time.perf_counter() - start
    per_verification = (elapsed / len(test_headers)) * 1000
    print(f"Verified {len(test_headers)} requests in {elapsed:.3f}s")
    print(f"Average time per verification: {per_verification:.2f}ms")
    assert per_verification < 20, "Verification should be fast (<20ms)"
    
    print("\n✓ Performance tests passed")


def main():
    """Run all API security tests"""
    print("=" * 60)
    print("API SECURITY ENHANCED TEST SUITE")
    print("=" * 60)
    
    try:
        # Run individual component tests
        test_rate_limiter()
        test_audit_logger()
        test_integrated_security()
        test_secure_api_key_storage()
        run_performance_test()
        
        print("\n" + "=" * 60)
        print("✓ ALL TESTS PASSED SUCCESSFULLY!")
        print("=" * 60)
        
    except AssertionError as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())