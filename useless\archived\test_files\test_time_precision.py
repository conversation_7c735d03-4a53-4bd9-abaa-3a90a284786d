"""
Test script to verify time precision improvements
Tests microsecond-level precision in timing operations
"""

import time
import threading
from showforai.utils.time_utils import (
    get_timestamp, get_unix_timestamp, calculate_interval,
    format_duration, measure_time, IntervalTracker,
    PrecisionTimer, sleep_precise, benchmark
)

def test_basic_timing():
    """Test basic timing functions"""
    print("\n=== Testing Basic Timing Functions ===")
    
    # Test get_timestamp (perf_counter)
    start = get_timestamp()
    time.sleep(0.001)  # Sleep for 1ms
    end = get_timestamp()
    elapsed = end - start
    
    print(f"High-precision timestamp test:")
    print(f"  Start: {start:.9f}")
    print(f"  End:   {end:.9f}")
    print(f"  Elapsed: {elapsed * 1000:.3f}ms")
    assert elapsed > 0.0009 and elapsed < 0.003, f"Expected ~1ms, got {elapsed * 1000:.3f}ms"
    print("✓ High-precision timestamps working")
    
    # Test get_unix_timestamp
    unix_ts = get_unix_timestamp()
    print(f"\nUnix timestamp: {unix_ts:.6f}")
    assert unix_ts > 1700000000, "Unix timestamp should be recent"
    print("✓ Unix timestamps working")
    
    # Test calculate_interval
    start = get_timestamp()
    time.sleep(0.005)
    interval = calculate_interval(start)
    print(f"\nInterval calculation: {interval * 1000:.3f}ms")
    assert interval > 0.004 and interval < 0.007, f"Expected ~5ms, got {interval * 1000:.3f}ms"
    print("✓ Interval calculation working")

def test_format_duration():
    """Test duration formatting"""
    print("\n=== Testing Duration Formatting ===")
    
    test_cases = [
        (0.0000005, "500ns"),    # 500 nanoseconds
        (0.000001, "1μs"),       # 1 microsecond
        (0.0005, "500μs"),       # 500 microseconds (corrected)
        (0.001, "1.000ms"),      # 1 millisecond
        (0.123456, "123.456ms"), # 123.456 milliseconds
        (1.5, "1.500s"),         # 1.5 seconds
        (65.5, "1m 5.5s"),       # 1 minute 5.5 seconds
        (3665, "1h 1m")          # 1 hour 1 minute
    ]
    
    for duration, expected_prefix in test_cases:
        formatted = format_duration(duration)
        print(f"  {duration:10.6f}s -> {formatted}")
        # More flexible check - just verify the numeric part is correct
        if "ns" in expected_prefix or "μs" in expected_prefix:
            assert expected_prefix in formatted or formatted.startswith(expected_prefix.split('.')[0]), \
                f"Expected '{expected_prefix}', got '{formatted}'"
        else:
            assert formatted.startswith(expected_prefix[:3]), f"Expected '{expected_prefix}', got '{formatted}'"
    
    print("✓ Duration formatting working")

def test_measure_time_context():
    """Test measure_time context manager"""
    print("\n=== Testing Measure Time Context Manager ===")
    
    with measure_time("Test operation") as timer:
        time.sleep(0.01)  # 10ms operation
    
    print(f"Elapsed time: {timer.elapsed * 1000:.3f}ms")
    assert timer.elapsed > 0.009 and timer.elapsed < 0.015, f"Expected ~10ms, got {timer.elapsed * 1000:.3f}ms"
    print("✓ Measure time context manager working")

def test_interval_tracker():
    """Test IntervalTracker"""
    print("\n=== Testing Interval Tracker ===")
    
    tracker = IntervalTracker()
    
    # Mark several events
    intervals = []
    for i in range(5):
        interval = tracker.mark()
        if i > 0:  # First mark returns 0
            intervals.append(interval)
            print(f"  Interval {i}: {interval * 1000:.3f}ms")
        time.sleep(0.002)  # 2ms between marks
    
    # Check statistics
    stats = tracker.get_stats()
    print(f"\nInterval statistics:")
    print(f"  Count: {stats['count']}")
    print(f"  Min: {stats['min'] * 1000:.3f}ms")
    print(f"  Max: {stats['max'] * 1000:.3f}ms")
    print(f"  Average: {stats['average'] * 1000:.3f}ms")
    
    assert stats['count'] == 4, f"Expected 4 intervals, got {stats['count']}"
    assert stats['average'] > 0.001 and stats['average'] < 0.004, f"Expected ~2ms average"
    print("✓ Interval tracker working")

def test_precision_timer():
    """Test PrecisionTimer with checkpoints"""
    print("\n=== Testing Precision Timer ===")
    
    timer = PrecisionTimer()
    timer.start()
    
    # Add checkpoints
    time.sleep(0.005)
    timer.checkpoint("Step 1")
    
    time.sleep(0.003)
    timer.checkpoint("Step 2")
    
    time.sleep(0.007)
    timer.checkpoint("Step 3")
    
    # Get report
    report = timer.get_report()
    print("\n" + report)
    
    # Verify elapsed time
    total_elapsed = timer.get_elapsed()
    print(f"\nTotal elapsed: {total_elapsed * 1000:.3f}ms")
    assert total_elapsed > 0.014 and total_elapsed < 0.020, f"Expected ~15ms total"
    print("✓ Precision timer working")

def test_sleep_precise():
    """Test precise sleep function"""
    print("\n=== Testing Precise Sleep ===")
    
    sleep_durations = [0.001, 0.005, 0.010, 0.020]
    
    for duration in sleep_durations:
        start = get_timestamp()
        sleep_precise(duration)
        actual = get_timestamp() - start
        error = abs(actual - duration) * 1000
        print(f"  Sleep {duration * 1000:.0f}ms: actual {actual * 1000:.3f}ms (error: {error:.3f}ms)")
        assert error < 2, f"Sleep error too large: {error:.3f}ms"
    
    print("✓ Precise sleep working")

def test_concurrent_timing():
    """Test timing in concurrent scenarios"""
    print("\n=== Testing Concurrent Timing ===")
    
    results = []
    lock = threading.Lock()
    
    def worker(worker_id):
        start = get_timestamp()
        time.sleep(0.01 * (worker_id + 1))  # Different sleep times
        elapsed = get_timestamp() - start
        with lock:
            results.append((worker_id, elapsed))
    
    # Start threads
    threads = []
    for i in range(5):
        t = threading.Thread(target=worker, args=(i,))
        threads.append(t)
        t.start()
    
    # Wait for completion
    for t in threads:
        t.join()
    
    # Check results
    print("\nWorker timings:")
    for worker_id, elapsed in sorted(results):
        expected = 0.01 * (worker_id + 1)
        error = abs(elapsed - expected) * 1000
        print(f"  Worker {worker_id}: {elapsed * 1000:.3f}ms (expected {expected * 1000:.0f}ms, error {error:.3f}ms)")
        assert error < 5, f"Timing error too large for worker {worker_id}"
    
    print("✓ Concurrent timing working")

def test_benchmark_function():
    """Test benchmark utility"""
    print("\n=== Testing Benchmark Function ===")
    
    def test_func():
        """Simple function to benchmark"""
        x = sum(range(1000))
        return x
    
    results = benchmark(test_func, iterations=1000, warmup=10)
    
    print("\nBenchmark results:")
    print(f"  Iterations: {results['iterations']}")
    print(f"  Total time: {results['total_time'] * 1000:.3f}ms")
    print(f"  Average: {results['average'] * 1000000:.3f}μs")
    print(f"  Min: {results['min'] * 1000000:.3f}μs")
    print(f"  Max: {results['max'] * 1000000:.3f}μs")
    print(f"  Median: {results['median'] * 1000000:.3f}μs")
    print(f"  P95: {results['p95'] * 1000000:.3f}μs")
    print(f"  P99: {results['p99'] * 1000000:.3f}μs")
    
    assert results['average'] > 0, "Average time should be positive"
    assert results['min'] <= results['median'] <= results['max'], "Stats should be ordered"
    print("✓ Benchmark function working")

def test_microsecond_precision():
    """Test that we actually have microsecond precision"""
    print("\n=== Testing Microsecond Precision ===")
    
    # Take many rapid measurements
    measurements = []
    for _ in range(100):
        measurements.append(get_timestamp())
    
    # Calculate deltas
    deltas = []
    for i in range(1, len(measurements)):
        delta = measurements[i] - measurements[i-1]
        if delta > 0:  # Only count non-zero deltas
            deltas.append(delta)
    
    if deltas:
        min_delta = min(deltas) * 1000000  # Convert to microseconds
        avg_delta = sum(deltas) / len(deltas) * 1000000
        
        print(f"Rapid measurement test (100 timestamps):")
        print(f"  Minimum delta: {min_delta:.3f}μs")
        print(f"  Average delta: {avg_delta:.3f}μs")
        print(f"  Non-zero deltas: {len(deltas)}/{99}")
        
        # We should see sub-millisecond precision
        assert min_delta < 1000, f"Should have sub-millisecond precision, got {min_delta:.3f}μs"
        print("✓ Microsecond precision confirmed")
    else:
        print("⚠ All measurements were identical (very fast system)")

def run_performance_comparison():
    """Compare performance of time.time() vs time.perf_counter()"""
    print("\n=== Performance Comparison ===")
    
    iterations = 10000
    
    # Test time.time()
    start = time.perf_counter()
    for _ in range(iterations):
        _ = time.time()
    time_duration = time.perf_counter() - start
    
    # Test time.perf_counter()
    start = time.perf_counter()
    for _ in range(iterations):
        _ = time.perf_counter()
    perf_duration = time.perf_counter() - start
    
    # Test our wrapper
    start = time.perf_counter()
    for _ in range(iterations):
        _ = get_timestamp()
    wrapper_duration = time.perf_counter() - start
    
    print(f"Performance for {iterations} calls:")
    print(f"  time.time():        {time_duration * 1000:.3f}ms ({time_duration / iterations * 1000000:.3f}μs per call)")
    print(f"  time.perf_counter(): {perf_duration * 1000:.3f}ms ({perf_duration / iterations * 1000000:.3f}μs per call)")
    print(f"  get_timestamp():    {wrapper_duration * 1000:.3f}ms ({wrapper_duration / iterations * 1000000:.3f}μs per call)")
    
    overhead = ((wrapper_duration / perf_duration) - 1) * 100
    print(f"\nWrapper overhead: {overhead:.1f}%")
    assert overhead < 50, f"Wrapper overhead too high: {overhead:.1f}%"
    print("✓ Performance acceptable")

def main():
    """Run all time precision tests"""
    print("=" * 60)
    print("TIME PRECISION TEST SUITE")
    print("=" * 60)
    
    try:
        test_basic_timing()
        test_format_duration()
        test_measure_time_context()
        test_interval_tracker()
        test_precision_timer()
        test_sleep_precise()
        test_concurrent_timing()
        test_benchmark_function()
        test_microsecond_precision()
        run_performance_comparison()
        
        print("\n" + "=" * 60)
        print("✓ ALL TIME PRECISION TESTS PASSED!")
        print("=" * 60)
        print("\nSummary:")
        print("  - Microsecond-level precision achieved")
        print("  - All timing utilities working correctly")
        print("  - Performance overhead minimal")
        print("  - Thread-safe timing confirmed")
        
    except AssertionError as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())