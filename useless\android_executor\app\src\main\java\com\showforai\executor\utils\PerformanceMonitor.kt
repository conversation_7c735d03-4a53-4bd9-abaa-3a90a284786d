package com.showforai.executor.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Process
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 性能监控器
 * 
 * 监控应用性能指标：
 * - 内存使用情况
 * - CPU使用率
 * - 电池消耗
 * - 执行性能统计
 */
@Singleton
class PerformanceMonitor @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val MONITORING_INTERVAL_MS = 5000L // 5秒监控间隔
        private const val MEMORY_WARNING_THRESHOLD = 0.8f // 内存警告阈值
        private const val MEMORY_CRITICAL_THRESHOLD = 0.9f // 内存严重警告阈值
    }
    
    private val monitoringScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    
    // 性能指标流
    private val _performanceMetrics = MutableStateFlow(PerformanceMetrics())
    val performanceMetrics: StateFlow<PerformanceMetrics> = _performanceMetrics.asStateFlow()
    
    // 监控状态
    private var isMonitoring = false
    private var monitoringJob: Job? = null
    
    // 执行性能统计
    private val executionStats = mutableListOf<ExecutionPerformance>()
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) {
            return
        }
        
        isMonitoring = true
        monitoringJob = monitoringScope.launch {
            while (isActive && isMonitoring) {
                try {
                    val metrics = collectPerformanceMetrics()
                    _performanceMetrics.value = metrics
                    
                    // 检查内存警告
                    checkMemoryWarnings(metrics)
                    
                    delay(MONITORING_INTERVAL_MS)
                } catch (e: Exception) {
                    Timber.e(e, "Error collecting performance metrics")
                    delay(MONITORING_INTERVAL_MS)
                }
            }
        }
        
        Timber.i("Performance monitoring started")
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
        Timber.i("Performance monitoring stopped")
    }
    
    /**
     * 收集性能指标
     */
    private fun collectPerformanceMetrics(): PerformanceMetrics {
        val memoryInfo = getMemoryInfo()
        val cpuInfo = getCPUInfo()
        val batteryInfo = getBatteryInfo()
        
        return PerformanceMetrics(
            timestamp = System.currentTimeMillis(),
            memoryUsageMB = memoryInfo.usedMemoryMB,
            memoryTotalMB = memoryInfo.totalMemoryMB,
            memoryUsagePercent = memoryInfo.usagePercent,
            availableMemoryMB = memoryInfo.availableMemoryMB,
            cpuUsagePercent = cpuInfo.usagePercent,
            batteryLevel = batteryInfo.level,
            batteryTemperature = batteryInfo.temperature,
            isLowMemory = memoryInfo.isLowMemory,
            gcCount = Debug.getRuntimeStat("art.gc.gc-count")?.toLongOrNull() ?: 0L,
            gcTime = Debug.getRuntimeStat("art.gc.gc-time")?.toLongOrNull() ?: 0L
        )
    }
    
    /**
     * 获取内存信息
     */
    private fun getMemoryInfo(): MemoryInfo {
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        
        val usedMemoryMB = usedMemory / (1024 * 1024)
        val totalMemoryMB = maxMemory / (1024 * 1024)
        val availableMemoryMB = memoryInfo.availMem / (1024 * 1024)
        val usagePercent = usedMemory.toFloat() / maxMemory
        
        return MemoryInfo(
            usedMemoryMB = usedMemoryMB,
            totalMemoryMB = totalMemoryMB,
            availableMemoryMB = availableMemoryMB,
            usagePercent = usagePercent,
            isLowMemory = memoryInfo.lowMemory
        )
    }
    
    /**
     * 获取CPU信息
     */
    private fun getCPUInfo(): CPUInfo {
        // Android中获取CPU使用率比较复杂，这里提供简化版本
        // 实际应用中可能需要读取/proc/stat文件
        return CPUInfo(
            usagePercent = 0f // 暂时返回0，实际实现需要更复杂的逻辑
        )
    }
    
    /**
     * 获取电池信息
     */
    private fun getBatteryInfo(): BatteryInfo {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as? android.os.BatteryManager
        
        val level = batteryManager?.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_CAPACITY) ?: -1
        val temperature = batteryManager?.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_CURRENT_NOW) ?: -1
        
        return BatteryInfo(
            level = level,
            temperature = temperature
        )
    }
    
    /**
     * 检查内存警告
     */
    private fun checkMemoryWarnings(metrics: PerformanceMetrics) {
        when {
            metrics.memoryUsagePercent >= MEMORY_CRITICAL_THRESHOLD -> {
                Timber.w("Critical memory usage: ${(metrics.memoryUsagePercent * 100).toInt()}%")
                // 触发内存清理
                performMemoryCleanup()
            }
            metrics.memoryUsagePercent >= MEMORY_WARNING_THRESHOLD -> {
                Timber.w("High memory usage: ${(metrics.memoryUsagePercent * 100).toInt()}%")
            }
        }
        
        if (metrics.isLowMemory) {
            Timber.w("System reports low memory condition")
            performMemoryCleanup()
        }
    }
    
    /**
     * 执行内存清理
     */
    private fun performMemoryCleanup() {
        try {
            // 建议垃圾回收
            System.gc()
            
            // 可以在这里添加其他清理逻辑
            // 例如清理图像缓存、临时文件等
            
            Timber.i("Memory cleanup performed")
        } catch (e: Exception) {
            Timber.e(e, "Error during memory cleanup")
        }
    }
    
    /**
     * 记录执行性能
     */
    fun recordExecutionPerformance(
        stepIndex: Int,
        stepType: String,
        executionTimeMs: Long,
        success: Boolean,
        memoryUsageMB: Long = getCurrentMemoryUsage()
    ) {
        val performance = ExecutionPerformance(
            timestamp = System.currentTimeMillis(),
            stepIndex = stepIndex,
            stepType = stepType,
            executionTimeMs = executionTimeMs,
            success = success,
            memoryUsageMB = memoryUsageMB
        )
        
        executionStats.add(performance)
        
        // 保持最近1000条记录
        if (executionStats.size > 1000) {
            executionStats.removeAt(0)
        }
        
        Timber.d("Execution performance recorded: $stepType took ${executionTimeMs}ms")
    }
    
    /**
     * 获取当前内存使用量
     */
    private fun getCurrentMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
    }
    
    /**
     * 获取执行性能统计
     */
    fun getExecutionStatistics(): ExecutionStatistics {
        if (executionStats.isEmpty()) {
            return ExecutionStatistics()
        }
        
        val totalExecutions = executionStats.size
        val successfulExecutions = executionStats.count { it.success }
        val averageExecutionTime = executionStats.map { it.executionTimeMs }.average()
        val maxExecutionTime = executionStats.maxOfOrNull { it.executionTimeMs } ?: 0L
        val minExecutionTime = executionStats.minOfOrNull { it.executionTimeMs } ?: 0L
        
        // 按步骤类型分组统计
        val stepTypeStats = executionStats.groupBy { it.stepType }
            .mapValues { (_, performances) ->
                StepTypeStatistics(
                    count = performances.size,
                    successRate = performances.count { it.success }.toFloat() / performances.size,
                    averageTimeMs = performances.map { it.executionTimeMs }.average().toLong()
                )
            }
        
        return ExecutionStatistics(
            totalExecutions = totalExecutions,
            successfulExecutions = successfulExecutions,
            successRate = successfulExecutions.toFloat() / totalExecutions,
            averageExecutionTimeMs = averageExecutionTime.toLong(),
            maxExecutionTimeMs = maxExecutionTime,
            minExecutionTimeMs = minExecutionTime,
            stepTypeStatistics = stepTypeStats
        )
    }
    
    /**
     * 清除性能统计
     */
    fun clearStatistics() {
        executionStats.clear()
        Timber.i("Performance statistics cleared")
    }
    
    /**
     * 获取性能报告
     */
    fun generatePerformanceReport(): String {
        val currentMetrics = _performanceMetrics.value
        val executionStats = getExecutionStatistics()
        
        return buildString {
            appendLine("=== Performance Report ===")
            appendLine("Timestamp: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
            appendLine()
            
            appendLine("Memory Usage:")
            appendLine("  Used: ${currentMetrics.memoryUsageMB}MB / ${currentMetrics.memoryTotalMB}MB (${(currentMetrics.memoryUsagePercent * 100).toInt()}%)")
            appendLine("  Available: ${currentMetrics.availableMemoryMB}MB")
            appendLine("  Low Memory: ${currentMetrics.isLowMemory}")
            appendLine()
            
            appendLine("Execution Statistics:")
            appendLine("  Total Executions: ${executionStats.totalExecutions}")
            appendLine("  Success Rate: ${(executionStats.successRate * 100).toInt()}%")
            appendLine("  Average Time: ${executionStats.averageExecutionTimeMs}ms")
            appendLine("  Min/Max Time: ${executionStats.minExecutionTimeMs}ms / ${executionStats.maxExecutionTimeMs}ms")
            appendLine()
            
            appendLine("Step Type Statistics:")
            executionStats.stepTypeStatistics.forEach { (type, stats) ->
                appendLine("  $type: ${stats.count} executions, ${(stats.successRate * 100).toInt()}% success, ${stats.averageTimeMs}ms avg")
            }
        }
    }
}

// 数据类定义
data class PerformanceMetrics(
    val timestamp: Long = 0L,
    val memoryUsageMB: Long = 0L,
    val memoryTotalMB: Long = 0L,
    val memoryUsagePercent: Float = 0f,
    val availableMemoryMB: Long = 0L,
    val cpuUsagePercent: Float = 0f,
    val batteryLevel: Int = -1,
    val batteryTemperature: Int = -1,
    val isLowMemory: Boolean = false,
    val gcCount: Long = 0L,
    val gcTime: Long = 0L
)

data class MemoryInfo(
    val usedMemoryMB: Long,
    val totalMemoryMB: Long,
    val availableMemoryMB: Long,
    val usagePercent: Float,
    val isLowMemory: Boolean
)

data class CPUInfo(
    val usagePercent: Float
)

data class BatteryInfo(
    val level: Int,
    val temperature: Int
)

data class ExecutionPerformance(
    val timestamp: Long,
    val stepIndex: Int,
    val stepType: String,
    val executionTimeMs: Long,
    val success: Boolean,
    val memoryUsageMB: Long
)

data class ExecutionStatistics(
    val totalExecutions: Int = 0,
    val successfulExecutions: Int = 0,
    val successRate: Float = 0f,
    val averageExecutionTimeMs: Long = 0L,
    val maxExecutionTimeMs: Long = 0L,
    val minExecutionTimeMs: Long = 0L,
    val stepTypeStatistics: Map<String, StepTypeStatistics> = emptyMap()
)

data class StepTypeStatistics(
    val count: Int,
    val successRate: Float,
    val averageTimeMs: Long
)
