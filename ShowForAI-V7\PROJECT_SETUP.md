# ShowForAI Project Setup

This document provides instructions for setting up and running the ShowForAI Tauri application.

## Prerequisites

1. **Node.js** (v16 or later)
2. **Rust** (latest stable version)
3. **Tauri CLI** 
4. **Platform-specific requirements:**
   - **Windows**: Visual Studio Build Tools 2019 or later
   - **macOS**: Xcode Command Line Tools
   - **Linux**: WebKit2GTK and other development libraries

## Installation

### 1. Install Rust
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 2. Install Node.js Dependencies
```bash
npm install
```

### 3. Install Tauri CLI
```bash
npm install --save-dev @tauri-apps/cli
# or globally
npm install -g @tauri-apps/cli
```

## Development

### Start Development Server
```bash
npm run tauri dev
```

This command will:
- Start the React development server on port 3000
- Build the Rust backend
- Launch the Tauri desktop application

### Build for Production
```bash
npm run tauri build
```

## Project Structure

```
ShowForAI-V7/
├── src-tauri/                 # Rust backend
│   ├── src/
│   │   ├── modules/          # Core modules
│   │   │   ├── recording/    # Recording functionality
│   │   │   ├── execution/    # Execution engine
│   │   │   └── management/   # Workflow management
│   │   ├── api/             # Tauri command handlers
│   │   ├── utils/           # Utility functions
│   │   └── main.rs          # Application entry point
│   ├── Cargo.toml           # Rust dependencies
│   └── build.rs             # Build script
├── src/                      # React frontend
│   ├── components/          # React components
│   ├── pages/              # Application pages
│   ├── hooks/              # Custom React hooks
│   ├── stores/             # State management (Zustand)
│   ├── services/           # API services
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Frontend utilities
├── public/                  # Static assets
├── package.json            # Node.js dependencies
├── tauri.conf.json         # Tauri configuration
└── vite.config.ts          # Vite configuration
```

## Key Features

### Backend (Rust)
- **Recording Module**: Captures user interactions and screen activity
- **Execution Module**: Plays back recorded workflows
- **Management Module**: Stores and manages workflows in SQLite database
- **Cross-platform support** with platform-specific implementations

### Frontend (React + TypeScript)
- **Modern UI** built with Ant Design
- **State management** using Zustand
- **Responsive design** for desktop application
- **Real-time updates** for recording and execution status

## Dependencies

### Rust Dependencies
- `tauri`: Desktop application framework
- `serde`: Serialization framework
- `tokio`: Async runtime
- `rusqlite`: SQLite database
- `image`: Image processing
- `rdev`: Input device monitoring
- `screenshots`: Screen capture
- `parking_lot`: High-performance synchronization primitives

### Frontend Dependencies
- `react`: UI library
- `antd`: Component library
- `zustand`: State management
- `react-router-dom`: Routing
- `dayjs`: Date manipulation
- `uuid`: Unique ID generation

## Development Commands

```bash
# Start development
npm run tauri dev

# Build for production
npm run tauri build

# Lint frontend code
npm run lint

# Format frontend code
npm run lint:fix

# Run frontend only (for UI development)
npm run dev

# Preview production build
npm run preview
```

## Configuration

### Tauri Configuration (`tauri.conf.json`)
- Application metadata
- Window properties
- Security permissions
- Build settings

### Frontend Configuration
- `vite.config.ts`: Build tool configuration
- `tsconfig.json`: TypeScript configuration
- `.eslintrc.json`: Linting rules

## Troubleshooting

### Common Issues

1. **Build Errors on Windows**
   - Ensure Visual Studio Build Tools are installed
   - Install Windows SDK

2. **Permission Errors on macOS**
   - Grant necessary permissions in System Preferences
   - Code sign the application for distribution

3. **Missing Dependencies on Linux**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install libwebkit2gtk-4.0-dev build-essential curl wget libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
   
   # Fedora
   sudo dnf install webkit2gtk3-devel openssl-devel gtk3-devel libayatana-appindicator-gtk3-devel librsvg2-devel
   ```

### Performance Optimization

1. **Recording Performance**
   - Adjust screenshot interval in settings
   - Disable unnecessary capture features

2. **Execution Performance**
   - Increase step delays for slow systems
   - Use hardware acceleration when available

## Security Considerations

- All file operations are scoped to specific directories
- Network requests require explicit permissions
- Local database is encrypted
- Screen recording requires user consent

## Contributing

1. Follow the existing code structure
2. Use TypeScript for type safety
3. Write unit tests for new features
4. Follow Rust best practices for backend code
5. Use ESLint rules for frontend code

## License

MIT License - see LICENSE file for details