package com.showforai.executor.data.models

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * DSL脚本数据模型
 * 对应桌面版的DSL规范v3.1
 */

@JsonClass(generateAdapter = true)
@Parcelize
data class DSLScript(
    @Json(name = "version") val version: String,
    @Json(name = "metadata") val metadata: DSLMetadata?,
    @<PERSON>son(name = "steps") val steps: List<DSLStep>
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class DSLMetadata(
    @<PERSON><PERSON>(name = "session_id") val sessionId: String?,
    @<PERSON>son(name = "description") val description: String?,
    @Json(name = "created_at") val createdAt: String?,
    @<PERSON><PERSON>(name = "device_info") val deviceInfo: String?
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class DSLStep(
    @Json(name = "type") val type: String,
    @Json(name = "command") val command: CommandType,
    @Json(name = "timeout_seconds") val timeoutSeconds: Int = 30,
    @<PERSON><PERSON>(name = "target") val target: DSLTarget?,
    @Json(name = "parameters") val parameters: DSLParameters?
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class DSLTarget(
    @Json(name = "description") val description: String?,
    @Json(name = "visual_hash") val visualHash: String?,
    @Json(name = "text_content") val textContent: String?,
    @Json(name = "bounding_box") val boundingBox: List<Float>? // [x1, y1, x2, y2] normalized coordinates
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class DSLParameters(
    @Json(name = "text") val text: String?,
    @Json(name = "direction") val direction: SwipeDirection?,
    @Json(name = "distance") val distance: Float?,
    @Json(name = "duration") val duration: Long?,
    @Json(name = "start_point") val startPoint: List<Float>?,
    @Json(name = "end_point") val endPoint: List<Float>?,
    @Json(name = "scroll_direction") val scrollDirection: ScrollDirection?,
    @Json(name = "scroll_amount") val scrollAmount: Int?
) : Parcelable

/**
 * 支持的命令类型
 */
enum class CommandType {
    @Json(name = "CLICK") CLICK,
    @Json(name = "SWIPE") SWIPE,
    @Json(name = "INPUT_TEXT") INPUT_TEXT,
    @Json(name = "WAIT") WAIT,
    @Json(name = "SCROLL") SCROLL,
    @Json(name = "LONG_PRESS") LONG_PRESS,
    @Json(name = "DOUBLE_CLICK") DOUBLE_CLICK,
    @Json(name = "PINCH") PINCH,
    @Json(name = "ZOOM") ZOOM
}

/**
 * 滑动方向
 */
enum class SwipeDirection {
    @Json(name = "UP") UP,
    @Json(name = "DOWN") DOWN,
    @Json(name = "LEFT") LEFT,
    @Json(name = "RIGHT") RIGHT
}

/**
 * 滚动方向
 */
enum class ScrollDirection {
    @Json(name = "UP") UP,
    @Json(name = "DOWN") DOWN,
    @Json(name = "LEFT") LEFT,
    @Json(name = "RIGHT") RIGHT
}

/**
 * 执行结果
 */
@Parcelize
data class ExecutionResult(
    val success: Boolean,
    val completedSteps: Int,
    val totalSteps: Int,
    val errorMessage: String?,
    val executionTime: Long,
    val logs: List<ExecutionLog>
) : Parcelable

/**
 * 执行日志
 */
@Parcelize
data class ExecutionLog(
    val timestamp: Long,
    val level: LogLevel,
    val stepIndex: Int,
    val message: String,
    val details: String?
) : Parcelable

/**
 * 日志级别
 */
enum class LogLevel {
    DEBUG, INFO, WARNING, ERROR, SUCCESS
}

/**
 * 定位结果
 */
data class LocationResult(
    val found: Boolean,
    val x: Float,
    val y: Float,
    val confidence: Float,
    val strategy: LocationStrategy,
    val boundingBox: android.graphics.RectF?
)

/**
 * 定位策略
 */
enum class LocationStrategy {
    VISUAL_MATCHING,    // OpenCV模板匹配
    OCR_TEXT,          // OCR文本识别
    COORDINATE_FALLBACK // 坐标降级
}

/**
 * 屏幕信息
 */
data class ScreenInfo(
    val width: Int,
    val height: Int,
    val density: Float,
    val orientation: Int
)

/**
 * 点坐标
 */
@Parcelize
data class Point(
    val x: Float,
    val y: Float
) : Parcelable

/**
 * 矩形区域
 */
@Parcelize
data class Rectangle(
    val left: Float,
    val top: Float,
    val right: Float,
    val bottom: Float
) : Parcelable {
    val width: Float get() = right - left
    val height: Float get() = bottom - top
    val centerX: Float get() = left + width / 2
    val centerY: Float get() = top + height / 2
}

/**
 * 执行状态
 */
enum class ExecutionStatus {
    IDLE,           // 空闲
    PREPARING,      // 准备中
    RUNNING,        // 执行中
    PAUSED,         // 暂停
    COMPLETED,      // 完成
    FAILED,         // 失败
    CANCELLED       // 取消
}

/**
 * 权限状态
 */
data class PermissionStatus(
    val screenCapture: Boolean = false,
    val accessibility: Boolean = false,
    val storage: Boolean = false,
    val overlay: Boolean = false
) {
    val allGranted: Boolean
        get() = screenCapture && accessibility && storage && overlay
}
