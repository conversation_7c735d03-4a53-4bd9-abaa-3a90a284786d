package com.showforai.executor.core.engine

import android.content.Context
import com.showforai.executor.core.actions.ActionExecutorManager
import com.showforai.executor.core.location.LocationStrategyManager
import com.showforai.executor.data.models.*
import com.showforai.executor.utils.FileManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DSL执行引擎
 * 
 * 核心执行引擎，负责：
 * 1. DSL脚本解析和执行
 * 2. 三层级联定位策略协调
 * 3. 执行状态管理和监控
 * 4. 错误处理和恢复
 * 5. 执行日志记录
 */
@Singleton
class DSLExecutionEngine @Inject constructor(
    private val context: Context,
    private val locationManager: LocationStrategyManager,
    private val actionManager: ActionExecutorManager,
    private val fileManager: FileManager
) {
    
    private val executionScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 执行状态流
    private val _executionStatus = MutableStateFlow(ExecutionStatus.IDLE)
    val executionStatus: StateFlow<ExecutionStatus> = _executionStatus.asStateFlow()
    
    // 执行进度流
    private val _executionProgress = MutableStateFlow(ExecutionProgress())
    val executionProgress: StateFlow<ExecutionProgress> = _executionProgress.asStateFlow()
    
    // 执行日志流
    private val _executionLogs = MutableSharedFlow<ExecutionLog>()
    val executionLogs: SharedFlow<ExecutionLog> = _executionLogs.asSharedFlow()
    
    // 当前执行的脚本
    private var currentScript: DSLScript? = null
    private var currentJob: Job? = null
    
    /**
     * 执行DSL脚本
     */
    suspend fun executeScript(script: DSLScript): ExecutionResult {
        return withContext(Dispatchers.IO) {
            try {
                Timber.i("Starting script execution: ${script.metadata?.sessionId}")
                
                currentScript = script
                _executionStatus.value = ExecutionStatus.PREPARING
                
                // 验证脚本
                validateScript(script)
                
                // 准备执行环境
                prepareExecution(script)
                
                // 开始执行
                _executionStatus.value = ExecutionStatus.RUNNING
                val result = executeSteps(script.steps)
                
                _executionStatus.value = if (result.success) {
                    ExecutionStatus.COMPLETED
                } else {
                    ExecutionStatus.FAILED
                }
                
                Timber.i("Script execution completed: success=${result.success}")
                result
                
            } catch (e: Exception) {
                Timber.e(e, "Script execution failed")
                _executionStatus.value = ExecutionStatus.FAILED
                
                ExecutionResult(
                    success = false,
                    completedSteps = _executionProgress.value.currentStep,
                    totalSteps = script.steps.size,
                    errorMessage = e.message,
                    executionTime = System.currentTimeMillis() - _executionProgress.value.startTime,
                    logs = _executionProgress.value.logs
                )
            }
        }
    }
    
    /**
     * 暂停执行
     */
    fun pauseExecution() {
        if (_executionStatus.value == ExecutionStatus.RUNNING) {
            _executionStatus.value = ExecutionStatus.PAUSED
            logExecution(LogLevel.INFO, -1, "Execution paused by user")
        }
    }
    
    /**
     * 恢复执行
     */
    fun resumeExecution() {
        if (_executionStatus.value == ExecutionStatus.PAUSED) {
            _executionStatus.value = ExecutionStatus.RUNNING
            logExecution(LogLevel.INFO, -1, "Execution resumed by user")
        }
    }
    
    /**
     * 停止执行
     */
    fun stopExecution() {
        currentJob?.cancel()
        _executionStatus.value = ExecutionStatus.CANCELLED
        logExecution(LogLevel.INFO, -1, "Execution stopped by user")
    }
    
    /**
     * 验证脚本
     */
    private fun validateScript(script: DSLScript) {
        if (script.version != "3.1") {
            throw IllegalArgumentException("Unsupported DSL version: ${script.version}")
        }
        
        if (script.steps.isEmpty()) {
            throw IllegalArgumentException("Script has no steps to execute")
        }
        
        // 验证每个步骤
        script.steps.forEachIndexed { index, step ->
            validateStep(index, step)
        }
    }
    
    /**
     * 验证单个步骤
     */
    private fun validateStep(index: Int, step: DSLStep) {
        // 验证超时时间
        if (step.timeoutSeconds <= 0) {
            throw IllegalArgumentException("Step $index: Invalid timeout: ${step.timeoutSeconds}")
        }
        
        // 验证目标参数
        when (step.command) {
            CommandType.CLICK, CommandType.LONG_PRESS, CommandType.DOUBLE_CLICK -> {
                if (step.target == null) {
                    throw IllegalArgumentException("Step $index: ${step.command} requires target")
                }
            }
            CommandType.INPUT_TEXT -> {
                if (step.target == null || step.parameters?.text.isNullOrEmpty()) {
                    throw IllegalArgumentException("Step $index: INPUT_TEXT requires target and text parameter")
                }
            }
            CommandType.SWIPE -> {
                if (step.target == null && step.parameters?.startPoint == null) {
                    throw IllegalArgumentException("Step $index: SWIPE requires target or start/end points")
                }
            }
            else -> {
                // 其他命令的验证
            }
        }
    }
    
    /**
     * 准备执行环境
     */
    private suspend fun prepareExecution(script: DSLScript) {
        // 初始化进度
        _executionProgress.value = ExecutionProgress(
            totalSteps = script.steps.size,
            startTime = System.currentTimeMillis()
        )
        
        // 加载资源文件
        loadScriptAssets(script)
        
        // 初始化定位策略
        locationManager.initialize()
        
        // 初始化动作执行器
        actionManager.initialize()
        
        logExecution(LogLevel.INFO, -1, "Execution environment prepared")
    }
    
    /**
     * 加载脚本资源文件
     */
    private suspend fun loadScriptAssets(script: DSLScript) {
        val assetFiles = script.steps.mapNotNull { it.target?.visualHash }.distinct()
        
        for (assetFile in assetFiles) {
            try {
                fileManager.loadAsset(assetFile)
                Timber.d("Loaded asset: $assetFile")
            } catch (e: Exception) {
                Timber.w(e, "Failed to load asset: $assetFile")
            }
        }
    }
    
    /**
     * 执行步骤列表
     */
    private suspend fun executeSteps(steps: List<DSLStep>): ExecutionResult {
        val logs = mutableListOf<ExecutionLog>()
        var completedSteps = 0
        
        for ((index, step) in steps.withIndex()) {
            // 检查是否被取消
            if (_executionStatus.value == ExecutionStatus.CANCELLED) {
                break
            }
            
            // 等待暂停状态结束
            while (_executionStatus.value == ExecutionStatus.PAUSED) {
                delay(100)
            }
            
            try {
                // 更新进度
                _executionProgress.value = _executionProgress.value.copy(
                    currentStep = index + 1,
                    currentStepDescription = step.target?.description ?: step.command.name
                )
                
                logExecution(LogLevel.INFO, index, "Executing step: ${step.command}")
                
                // 执行步骤
                val stepResult = executeStep(index, step)
                
                if (stepResult.success) {
                    completedSteps++
                    logExecution(LogLevel.SUCCESS, index, "Step completed successfully")
                } else {
                    logExecution(LogLevel.ERROR, index, "Step failed: ${stepResult.errorMessage}")
                    
                    // 根据配置决定是否继续执行
                    if (!shouldContinueOnError(step)) {
                        break
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error executing step $index")
                logExecution(LogLevel.ERROR, index, "Step execution error: ${e.message}")
                
                if (!shouldContinueOnError(step)) {
                    break
                }
            }
        }
        
        val endTime = System.currentTimeMillis()
        val executionTime = endTime - _executionProgress.value.startTime
        
        return ExecutionResult(
            success = completedSteps == steps.size,
            completedSteps = completedSteps,
            totalSteps = steps.size,
            errorMessage = if (completedSteps < steps.size) "Execution incomplete" else null,
            executionTime = executionTime,
            logs = _executionProgress.value.logs
        )
    }
    
    /**
     * 执行单个步骤
     */
    private suspend fun executeStep(index: Int, step: DSLStep): StepExecutionResult {
        return try {
            when (step.command) {
                CommandType.CLICK -> executeClickStep(step)
                CommandType.SWIPE -> executeSwipeStep(step)
                CommandType.INPUT_TEXT -> executeInputTextStep(step)
                CommandType.WAIT -> executeWaitStep(step)
                CommandType.SCROLL -> executeScrollStep(step)
                CommandType.LONG_PRESS -> executeLongPressStep(step)
                CommandType.DOUBLE_CLICK -> executeDoubleClickStep(step)
                else -> {
                    StepExecutionResult(false, "Unsupported command: ${step.command}")
                }
            }
        } catch (e: Exception) {
            StepExecutionResult(false, e.message ?: "Unknown error")
        }
    }
    
    /**
     * 执行点击步骤
     */
    private suspend fun executeClickStep(step: DSLStep): StepExecutionResult {
        val target = step.target ?: return StepExecutionResult(false, "No target specified")

        // 使用三层级联定位策略查找目标
        val location = locationManager.findTarget(target, step.timeoutSeconds)

        return if (location.found) {
            val success = actionManager.click(location.x, location.y)
            if (success) {
                StepExecutionResult(true, "Click executed at (${location.x}, ${location.y})")
            } else {
                StepExecutionResult(false, "Click action failed")
            }
        } else {
            StepExecutionResult(false, "Target not found")
        }
    }

    /**
     * 执行滑动步骤
     */
    private suspend fun executeSwipeStep(step: DSLStep): StepExecutionResult {
        val parameters = step.parameters

        return if (parameters?.startPoint != null && parameters.endPoint != null) {
            // 使用指定的起始和结束点
            val startPoint = screenUtils.normalizedToPixel(parameters.startPoint[0], parameters.startPoint[1])
            val endPoint = screenUtils.normalizedToPixel(parameters.endPoint[0], parameters.endPoint[1])
            val duration = parameters.duration ?: 500L

            val success = actionManager.swipe(
                startPoint.x.toFloat(),
                startPoint.y.toFloat(),
                endPoint.x.toFloat(),
                endPoint.y.toFloat(),
                duration
            )

            if (success) {
                StepExecutionResult(true, "Swipe executed from $startPoint to $endPoint")
            } else {
                StepExecutionResult(false, "Swipe action failed")
            }
        } else if (step.target != null && parameters?.direction != null) {
            // 从目标位置开始滑动
            val location = locationManager.findTarget(step.target, step.timeoutSeconds)

            if (location.found) {
                val distance = parameters.distance ?: 500f
                val duration = parameters.duration ?: 500L

                val success = actionManager.swipeDirection(
                    location.x,
                    location.y,
                    parameters.direction,
                    distance,
                    duration
                )

                if (success) {
                    StepExecutionResult(true, "Swipe executed from (${location.x}, ${location.y}) direction=${parameters.direction}")
                } else {
                    StepExecutionResult(false, "Swipe action failed")
                }
            } else {
                StepExecutionResult(false, "Target not found for swipe")
            }
        } else {
            StepExecutionResult(false, "Invalid swipe parameters")
        }
    }

    /**
     * 执行文本输入步骤
     */
    private suspend fun executeInputTextStep(step: DSLStep): StepExecutionResult {
        val target = step.target ?: return StepExecutionResult(false, "No target specified")
        val text = step.parameters?.text ?: return StepExecutionResult(false, "No text specified")

        // 先点击目标位置以获得焦点
        val location = locationManager.findTarget(target, step.timeoutSeconds)

        return if (location.found) {
            // 点击获得焦点
            val clickSuccess = actionManager.click(location.x, location.y)
            if (!clickSuccess) {
                return StepExecutionResult(false, "Failed to click target for text input")
            }

            // 等待一下让输入框获得焦点
            delay(200)

            // 输入文本
            val inputSuccess = actionManager.inputText(text)
            if (inputSuccess) {
                StepExecutionResult(true, "Text input executed: $text")
            } else {
                StepExecutionResult(false, "Text input failed")
            }
        } else {
            StepExecutionResult(false, "Target not found for text input")
        }
    }

    /**
     * 执行等待步骤
     */
    private suspend fun executeWaitStep(step: DSLStep): StepExecutionResult {
        val duration = step.parameters?.duration ?: (step.timeoutSeconds * 1000L)

        val success = actionManager.wait(duration)
        return if (success) {
            StepExecutionResult(true, "Wait executed for ${duration}ms")
        } else {
            StepExecutionResult(false, "Wait failed")
        }
    }

    /**
     * 执行滚动步骤
     */
    private suspend fun executeScrollStep(step: DSLStep): StepExecutionResult {
        val parameters = step.parameters
        val scrollDirection = parameters?.scrollDirection ?: return StepExecutionResult(false, "No scroll direction specified")
        val scrollAmount = parameters.scrollAmount ?: 1

        return if (step.target != null) {
            // 在目标位置滚动
            val location = locationManager.findTarget(step.target, step.timeoutSeconds)

            if (location.found) {
                val success = actionManager.scroll(
                    location.x,
                    location.y,
                    scrollDirection,
                    scrollAmount
                )

                if (success) {
                    StepExecutionResult(true, "Scroll executed at (${location.x}, ${location.y}) direction=$scrollDirection amount=$scrollAmount")
                } else {
                    StepExecutionResult(false, "Scroll action failed")
                }
            } else {
                StepExecutionResult(false, "Target not found for scroll")
            }
        } else {
            // 在屏幕中心滚动
            val screenCenter = screenUtils.getScreenCenter()
            val success = actionManager.scroll(
                screenCenter.x.toFloat(),
                screenCenter.y.toFloat(),
                scrollDirection,
                scrollAmount
            )

            if (success) {
                StepExecutionResult(true, "Scroll executed at screen center direction=$scrollDirection amount=$scrollAmount")
            } else {
                StepExecutionResult(false, "Scroll action failed")
            }
        }
    }

    /**
     * 执行长按步骤
     */
    private suspend fun executeLongPressStep(step: DSLStep): StepExecutionResult {
        val target = step.target ?: return StepExecutionResult(false, "No target specified")
        val duration = step.parameters?.duration ?: 1000L

        // 使用三层级联定位策略查找目标
        val location = locationManager.findTarget(target, step.timeoutSeconds)

        return if (location.found) {
            val success = actionManager.longPress(location.x, location.y, duration)
            if (success) {
                StepExecutionResult(true, "Long press executed at (${location.x}, ${location.y}) for ${duration}ms")
            } else {
                StepExecutionResult(false, "Long press action failed")
            }
        } else {
            StepExecutionResult(false, "Target not found")
        }
    }

    /**
     * 执行双击步骤
     */
    private suspend fun executeDoubleClickStep(step: DSLStep): StepExecutionResult {
        val target = step.target ?: return StepExecutionResult(false, "No target specified")

        // 使用三层级联定位策略查找目标
        val location = locationManager.findTarget(target, step.timeoutSeconds)

        return if (location.found) {
            val success = actionManager.doubleClick(location.x, location.y)
            if (success) {
                StepExecutionResult(true, "Double click executed at (${location.x}, ${location.y})")
            } else {
                StepExecutionResult(false, "Double click action failed")
            }
        } else {
            StepExecutionResult(false, "Target not found")
        }
    }
    
    /**
     * 记录执行日志
     */
    private fun logExecution(level: LogLevel, stepIndex: Int, message: String, details: String? = null) {
        val log = ExecutionLog(
            timestamp = System.currentTimeMillis(),
            level = level,
            stepIndex = stepIndex,
            message = message,
            details = details
        )
        
        // 更新进度中的日志
        val currentProgress = _executionProgress.value
        _executionProgress.value = currentProgress.copy(
            logs = currentProgress.logs + log
        )
        
        // 发送日志事件
        executionScope.launch {
            _executionLogs.emit(log)
        }
        
        // 同时输出到Timber
        when (level) {
            LogLevel.DEBUG -> Timber.d("Step $stepIndex: $message")
            LogLevel.INFO -> Timber.i("Step $stepIndex: $message")
            LogLevel.WARNING -> Timber.w("Step $stepIndex: $message")
            LogLevel.ERROR -> Timber.e("Step $stepIndex: $message")
            LogLevel.SUCCESS -> Timber.i("Step $stepIndex: ✓ $message")
        }
    }
    
    /**
     * 判断是否在错误时继续执行
     */
    private fun shouldContinueOnError(step: DSLStep): Boolean {
        // 默认策略：遇到错误就停止
        // 可以根据步骤配置或全局配置来决定
        return false
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        currentJob?.cancel()
        executionScope.cancel()
        locationManager.cleanup()
        actionManager.cleanup()
    }
}

/**
 * 执行进度数据类
 */
data class ExecutionProgress(
    val totalSteps: Int = 0,
    val currentStep: Int = 0,
    val currentStepDescription: String = "",
    val startTime: Long = 0L,
    val logs: List<ExecutionLog> = emptyList()
)

/**
 * 步骤执行结果
 */
data class StepExecutionResult(
    val success: Boolean,
    val errorMessage: String? = null
)
