"""
对比不同坐标格式的工具
用于测试Gemini返回的坐标
"""

import json
from PIL import Image, ImageDraw, ImageFont
import os

def test_coordinate_formats(image_path, box_2d):
    """测试不同的坐标解释方式"""
    img = Image.open(image_path)
    img_width, img_height = img.size
    img_center_x = img_width // 2
    img_center_y = img_height // 2
    
    print(f"图片信息:")
    print(f"  尺寸: {img_width}x{img_height}")
    print(f"  中心点: ({img_center_x}, {img_center_y})")
    print(f"  归一化中心: (500, 500)")
    print(f"\n原始box_2d: {box_2d}")
    print("\n" + "="*60)
    
    # 测试不同的解释方式
    interpretations = []
    
    # 1. 标准格式: [ymin, xmin, ymax, xmax]
    ymin, xmin, ymax, xmax = box_2d
    x1 = int(xmin * img_width / 1000)
    y1 = int(ymin * img_height / 1000)
    x2 = int(xmax * img_width / 1000)
    y2 = int(ymax * img_height / 1000)
    
    contains_center = (x1 <= img_center_x <= x2 and y1 <= img_center_y <= y2)
    interpretations.append({
        "name": "标准格式 [ymin, xmin, ymax, xmax]",
        "coords": (x1, y1, x2, y2),
        "contains_center": contains_center,
        "color": "red"
    })
    
    # 2. 反向格式: [xmin, ymin, xmax, ymax]
    xmin2, ymin2, xmax2, ymax2 = box_2d
    x1_2 = int(xmin2 * img_width / 1000)
    y1_2 = int(ymin2 * img_height / 1000)
    x2_2 = int(xmax2 * img_width / 1000)
    y2_2 = int(ymax2 * img_height / 1000)
    
    contains_center2 = (x1_2 <= img_center_x <= x2_2 and y1_2 <= img_center_y <= y2_2)
    interpretations.append({
        "name": "反向格式 [xmin, ymin, xmax, ymax]",
        "coords": (x1_2, y1_2, x2_2, y2_2),
        "contains_center": contains_center2,
        "color": "blue"
    })
    
    # 3. 中心+尺寸格式: [center_y, center_x, height, width]
    cy, cx, h, w = box_2d
    cx_px = int(cx * img_width / 1000)
    cy_px = int(cy * img_height / 1000)
    w_px = int(w * img_width / 1000)
    h_px = int(h * img_height / 1000)
    x1_3 = cx_px - w_px // 2
    y1_3 = cy_px - h_px // 2
    x2_3 = cx_px + w_px // 2
    y2_3 = cy_px + h_px // 2
    
    contains_center3 = (x1_3 <= img_center_x <= x2_3 and y1_3 <= img_center_y <= y2_3)
    interpretations.append({
        "name": "中心格式 [center_y, center_x, height, width]",
        "coords": (x1_3, y1_3, x2_3, y2_3),
        "contains_center": contains_center3,
        "color": "green"
    })
    
    # 打印所有解释结果
    for i, interp in enumerate(interpretations):
        x1, y1, x2, y2 = interp["coords"]
        print(f"\n{i+1}. {interp['name']}:")
        print(f"   像素坐标: ({x1}, {y1}) - ({x2}, {y2})")
        print(f"   尺寸: {x2-x1}x{y2-y1}")
        print(f"   包含中心点: {'[YES]' if interp['contains_center'] else '[NO]'}")
        
        # 检查边界框中心
        bbox_center_x = (x1 + x2) // 2
        bbox_center_y = (y1 + y2) // 2
        distance = ((bbox_center_x - img_center_x)**2 + (bbox_center_y - img_center_y)**2)**0.5
        print(f"   边界框中心: ({bbox_center_x}, {bbox_center_y})")
        print(f"   距图片中心: {distance:.1f} 像素")
    
    # 创建可视化
    create_comparison_image(image_path, interpretations)
    
    # 返回最可能的格式
    valid_formats = [i for i in interpretations if i["contains_center"]]
    if valid_formats:
        print(f"\n[FOUND] 最可能的格式: {valid_formats[0]['name']}")
    else:
        print("\n[WARNING] 警告: 所有格式都不包含中心点!")

def create_comparison_image(image_path, interpretations):
    """创建对比图"""
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    # 绘制图片中心
    center_x = img.width // 2
    center_y = img.height // 2
    draw.ellipse([center_x-8, center_y-8, center_x+8, center_y+8], 
                 fill='yellow', outline='black', width=2)
    
    # 绘制所有解释的边界框
    for interp in interpretations:
        x1, y1, x2, y2 = interp["coords"]
        color = interp["color"]
        
        # 绘制边界框
        draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
        
        # 添加标签
        try:
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        label = interp["name"].split()[0]
        if interp["contains_center"]:
            label += " [Y]"
        draw.text((x1, y1-15), label, fill=color, font=font)
    
    # 保存
    output_path = "coordinate_comparison.png"
    img.save(output_path)
    print(f"\n对比图已保存: {output_path}")

def main():
    # 测试图片
    test_image = r"C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png"
    
    # 测试坐标
    box_2d = [444, 601, 562, 748]
    
    print("坐标格式对比测试")
    print("="*60)
    
    test_coordinate_formats(test_image, box_2d)
    
    print("\n" + "="*60)
    print("\n建议:")
    print("1. 检查Gemini返回的坐标格式文档")
    print("2. 尝试不同的提示词明确要求特定格式")
    print("3. 如果元素不在中心，可能是识别错误")

if __name__ == "__main__":
    main()