"""
为图片添加中心标记的工具
用于辅助Gemini识别图片中心位置
"""

from PIL import Image, ImageDraw
import os
import sys

def add_center_cross(image_path, output_path=None, cross_size=15, cross_color='red', line_width=2):
    """
    在图片中心添加十字标记
    
    Args:
        image_path: 输入图片路径
        output_path: 输出路径（默认添加_marked后缀）
        cross_size: 十字大小（像素）
        cross_color: 十字颜色
        line_width: 线条宽度
    """
    # 加载图片
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    # 计算中心点
    center_x = img.width // 2
    center_y = img.height // 2
    
    # 绘制十字
    # 水平线
    draw.line([center_x - cross_size, center_y, center_x + cross_size, center_y], 
              fill=cross_color, width=line_width)
    # 垂直线
    draw.line([center_x, center_y - cross_size, center_x, center_y + cross_size], 
              fill=cross_color, width=line_width)
    
    # 可选：添加中心点
    # draw.ellipse([center_x-2, center_y-2, center_x+2, center_y+2], fill=cross_color)
    
    # 保存
    if output_path is None:
        base, ext = os.path.splitext(image_path)
        output_path = f"{base}_marked{ext}"
    
    img.save(output_path)
    print(f"已添加中心标记: {output_path}")
    print(f"图片尺寸: {img.width}x{img.height}")
    print(f"中心坐标: ({center_x}, {center_y})")
    print(f"归一化中心: (500, 500) in 0-1000 system")
    
    return output_path

def add_center_dot(image_path, output_path=None, dot_size=5, dot_color='lime'):
    """
    在图片中心添加小圆点标记（更不显眼的方案）
    """
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    # 计算中心点
    center_x = img.width // 2
    center_y = img.height // 2
    
    # 绘制圆点
    draw.ellipse([center_x - dot_size, center_y - dot_size, 
                  center_x + dot_size, center_y + dot_size], 
                 fill=dot_color, outline='black', width=1)
    
    # 保存
    if output_path is None:
        base, ext = os.path.splitext(image_path)
        output_path = f"{base}_dotted{ext}"
    
    img.save(output_path)
    print(f"已添加中心点标记: {output_path}")
    
    return output_path

def add_corner_marks(image_path, output_path=None, mark_size=20, mark_color='blue'):
    """
    在图片四角添加标记，帮助AI理解图片边界
    """
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    # 四角坐标
    corners = [
        (0, 0),  # 左上
        (img.width - 1, 0),  # 右上
        (0, img.height - 1),  # 左下
        (img.width - 1, img.height - 1)  # 右下
    ]
    
    # 绘制角标记
    for x, y in corners:
        # L形标记
        if x == 0 and y == 0:  # 左上
            draw.line([x, y, x + mark_size, y], fill=mark_color, width=2)
            draw.line([x, y, x, y + mark_size], fill=mark_color, width=2)
        elif x > 0 and y == 0:  # 右上
            draw.line([x - mark_size, y, x, y], fill=mark_color, width=2)
            draw.line([x, y, x, y + mark_size], fill=mark_color, width=2)
        elif x == 0 and y > 0:  # 左下
            draw.line([x, y, x + mark_size, y], fill=mark_color, width=2)
            draw.line([x, y, x, y - mark_size], fill=mark_color, width=2)
        else:  # 右下
            draw.line([x - mark_size, y, x, y], fill=mark_color, width=2)
            draw.line([x, y, x, y - mark_size], fill=mark_color, width=2)
    
    # 保存
    if output_path is None:
        base, ext = os.path.splitext(image_path)
        output_path = f"{base}_corners{ext}"
    
    img.save(output_path)
    print(f"已添加角标记: {output_path}")
    
    return output_path

def create_test_prompts():
    """生成测试用的提示词"""
    prompts = {
        "with_cross": """
There is a small RED CROSS MARK at the exact center of this image indicating the click position.
Detect the UI element under this cross mark.

Instructions:
1. The cross mark itself is NOT the target - it's just a position indicator
2. Find the actual UI element beneath/around the cross mark
3. Return the complete bounding box of that UI element
4. The cross mark is at (500, 500) in 0-1000 coordinates

Output format:
{
  "boundingBox": [y_min, x_min, y_max, x_max],
  "elementDescription": "what this element is",
  "textContent": "any visible text"
}
""",
        
        "with_dot": """
There is a small GREEN DOT at the center of this image marking the click position.
Find the UI element at this position (ignoring the dot itself).

The dot is at coordinates (500, 500) in the 0-1000 system.
Return the bounding box of the clicked element.
""",
        
        "no_mark": """
IMPORTANT: The user clicked at the EXACT CENTER of this image.
The click position is at coordinates (500, 500) in the 0-1000 normalized coordinate system.

Task: Detect ONLY the UI element at the center of the image that was clicked.
"""
    }
    
    # 保存提示词
    with open('center_detection_prompts.txt', 'w', encoding='utf-8') as f:
        for name, prompt in prompts.items():
            f.write(f"\n{'='*50}\n")
            f.write(f"Prompt for {name}:\n")
            f.write(f"{'='*50}\n")
            f.write(prompt)
            f.write("\n\n")
    
    print("提示词已保存到: center_detection_prompts.txt")

def main():
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python add_center_mark.py <image_path> [mark_type]")
        print("\nmark_type 可选值:")
        print("  cross - 十字标记（默认）")
        print("  dot   - 圆点标记")
        print("  corners - 四角标记")
        print("  all   - 生成所有类型")
        return
    
    image_path = sys.argv[1]
    mark_type = sys.argv[2] if len(sys.argv) > 2 else "cross"
    
    if not os.path.exists(image_path):
        print(f"错误: 找不到图片 {image_path}")
        return
    
    if mark_type == "all":
        add_center_cross(image_path)
        add_center_dot(image_path)
        add_corner_marks(image_path)
    elif mark_type == "dot":
        add_center_dot(image_path)
    elif mark_type == "corners":
        add_corner_marks(image_path)
    else:
        add_center_cross(image_path)
    
    # 生成提示词
    create_test_prompts()

if __name__ == "__main__":
    main()