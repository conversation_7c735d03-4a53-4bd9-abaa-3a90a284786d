package com.showforai.executor.ui

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.showforai.executor.ui.main.MainActivity
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 主界面UI测试
 * 
 * 测试主界面的用户交互和功能
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class MainActivityTest {
    
    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)
    
    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()
    
    @Before
    fun setup() {
        hiltRule.inject()
    }
    
    @Test
    fun testMainActivityLaunch() {
        // 验证主界面启动
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
    }
    
    @Test
    fun testScriptSelectionButton() {
        // 测试脚本选择按钮
        composeTestRule.onNodeWithText("选择脚本").assertIsDisplayed()
        composeTestRule.onNodeWithText("选择脚本").assertIsEnabled()
    }
    
    @Test
    fun testExecutionButtonInitialState() {
        // 测试执行按钮初始状态（应该禁用）
        composeTestRule.onNodeWithText("开始执行").assertIsDisplayed()
        // 初始状态下应该禁用，因为没有加载脚本
        composeTestRule.onNodeWithText("开始执行").assertIsNotEnabled()
    }
    
    @Test
    fun testPermissionStatusDisplay() {
        // 测试权限状态显示
        composeTestRule.onNodeWithText("权限状态").assertIsDisplayed()
    }
    
    @Test
    fun testSettingsButton() {
        // 测试设置按钮
        composeTestRule.onNodeWithContentDescription("设置").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("设置").assertIsEnabled()
    }
    
    @Test
    fun testLogSection() {
        // 测试日志区域
        composeTestRule.onNodeWithText("执行日志").assertIsDisplayed()
    }
    
    @Test
    fun testNavigationToSettings() {
        // 测试导航到设置页面
        composeTestRule.onNodeWithContentDescription("设置").performClick()
        
        // 等待导航完成
        composeTestRule.waitForIdle()
        
        // 验证设置页面元素（如果在同一个Activity中）
        // 或者验证Intent是否被触发
    }
    
    @Test
    fun testScriptInfoDisplay() {
        // 测试脚本信息显示区域
        composeTestRule.onNodeWithText("当前脚本").assertIsDisplayed()
    }
    
    @Test
    fun testExecutionStatusDisplay() {
        // 测试执行状态显示
        composeTestRule.onNodeWithText("执行状态").assertIsDisplayed()
    }
    
    @Test
    fun testUIResponsiveness() {
        // 测试UI响应性
        composeTestRule.onNodeWithText("选择脚本").performClick()
        composeTestRule.waitForIdle()
        
        // 验证点击后UI状态
        composeTestRule.onNodeWithText("选择脚本").assertIsDisplayed()
    }
    
    @Test
    fun testPermissionGuideNavigation() {
        // 测试权限引导导航
        composeTestRule.onNodeWithText("权限设置").performClick()
        composeTestRule.waitForIdle()
        
        // 验证权限引导页面启动
        // 这里需要根据实际实现调整
    }
    
    @Test
    fun testLogScrolling() {
        // 测试日志滚动功能
        val logList = composeTestRule.onNodeWithTag("log_list")
        if (logList.isDisplayed()) {
            logList.performScrollToIndex(0)
            composeTestRule.waitForIdle()
        }
    }
    
    @Test
    fun testDarkModeSupport() {
        // 测试深色模式支持
        // 这个测试需要在不同主题下运行
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
    }
    
    @Test
    fun testScreenRotation() {
        // 测试屏幕旋转
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
        
        // 模拟屏幕旋转
        composeTestRule.activity.requestedOrientation = 
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        composeTestRule.waitForIdle()
        
        // 验证旋转后UI仍然正常
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
        
        // 恢复竖屏
        composeTestRule.activity.requestedOrientation = 
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
    }
    
    @Test
    fun testMemoryLeakPrevention() {
        // 测试内存泄漏预防
        // 多次创建和销毁Activity
        repeat(5) {
            composeTestRule.activityRule.scenario.recreate()
            composeTestRule.waitForIdle()
            composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
        }
    }
    
    @Test
    fun testAccessibilitySupport() {
        // 测试无障碍支持
        composeTestRule.onNodeWithText("选择脚本")
            .assertHasClickAction()
            .assertIsEnabled()
        
        composeTestRule.onNodeWithContentDescription("设置")
            .assertHasClickAction()
            .assertIsEnabled()
    }
    
    @Test
    fun testErrorStateHandling() {
        // 测试错误状态处理
        // 这里可以模拟各种错误状态
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
        
        // 验证错误状态下UI的表现
        // 需要根据具体的错误处理逻辑来实现
    }
    
    @Test
    fun testLoadingStateHandling() {
        // 测试加载状态处理
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
        
        // 验证加载状态下UI的表现
        // 需要根据具体的加载状态逻辑来实现
    }
    
    @Test
    fun testUserInteractionFlow() {
        // 测试完整的用户交互流程
        
        // 1. 启动应用
        composeTestRule.onNodeWithText("DSL Executor").assertIsDisplayed()
        
        // 2. 检查初始状态
        composeTestRule.onNodeWithText("开始执行").assertIsNotEnabled()
        
        // 3. 点击选择脚本
        composeTestRule.onNodeWithText("选择脚本").performClick()
        composeTestRule.waitForIdle()
        
        // 4. 检查设置按钮
        composeTestRule.onNodeWithContentDescription("设置").performClick()
        composeTestRule.waitForIdle()
        
        // 验证整个流程的连贯性
    }
}
