package com.showforai.executor.ui.main

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.showforai.executor.core.engine.DSLExecutionEngine
import com.showforai.executor.data.models.*
import com.showforai.executor.data.repositories.PermissionRepository
import com.showforai.executor.data.repositories.ScriptRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 主界面ViewModel
 * 
 * 管理主界面的状态和业务逻辑：
 * - 权限状态管理
 * - 脚本加载和管理
 * - 执行状态监控
 * - 日志记录
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val executionEngine: DSLExecutionEngine,
    private val scriptRepository: ScriptRepository,
    private val permissionRepository: PermissionRepository
) : ViewModel() {
    
    // 当前加载的脚本
    private val _currentScript = MutableStateFlow<DSLScript?>(null)
    val currentScript: StateFlow<DSLScript?> = _currentScript.asStateFlow()
    
    // 权限状态
    private val _permissionStatus = MutableStateFlow(PermissionStatus())
    val permissionStatus: StateFlow<PermissionStatus> = _permissionStatus.asStateFlow()
    
    // 最近的执行日志
    private val _recentLogs = MutableStateFlow<List<String>>(emptyList())
    val recentLogs: StateFlow<List<String>> = _recentLogs.asStateFlow()
    
    // UI状态
    val uiState: StateFlow<MainUiState> = combine(
        currentScript,
        permissionStatus,
        executionEngine.executionStatus,
        recentLogs
    ) { script, permissions, executionStatus, logs ->
        MainUiState(
            currentScript = script,
            permissionStatus = permissions,
            executionStatus = executionStatus,
            canExecute = script != null && permissions.allGranted && executionStatus == ExecutionStatus.IDLE,
            recentLogs = logs
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = MainUiState()
    )
    
    init {
        // 监听执行日志
        viewModelScope.launch {
            executionEngine.executionLogs.collect { log ->
                addLogMessage("${getLogLevelPrefix(log.level)} ${log.message}")
            }
        }
        
        // 初始检查权限
        checkPermissions()
    }
    
    /**
     * 检查权限状态
     */
    fun checkPermissions() {
        viewModelScope.launch {
            try {
                val status = permissionRepository.checkAllPermissions()
                _permissionStatus.value = status
                Timber.d("Permission status updated: $status")
            } catch (e: Exception) {
                Timber.e(e, "Failed to check permissions")
            }
        }
    }
    
    /**
     * 从URI加载脚本
     */
    fun loadScript(uri: Uri) {
        viewModelScope.launch {
            try {
                addLogMessage("📄 Loading script from URI...")
                
                val script = scriptRepository.loadScriptFromUri(uri)
                if (script != null) {
                    _currentScript.value = script
                    addLogMessage("✅ Script loaded successfully: ${script.metadata?.description ?: "Unknown"}")
                    Timber.i("Script loaded: ${script.metadata?.sessionId}")
                } else {
                    addLogMessage("❌ Failed to load script")
                    Timber.w("Failed to load script from URI: $uri")
                }
            } catch (e: Exception) {
                addLogMessage("❌ Script loading error: ${e.message}")
                Timber.e(e, "Error loading script from URI: $uri")
            }
        }
    }
    
    /**
     * 从文件路径加载脚本
     */
    fun loadScriptFromFile(filePath: String) {
        viewModelScope.launch {
            try {
                addLogMessage("📄 Loading script from file...")
                
                val script = scriptRepository.loadScriptFromFile(filePath)
                if (script != null) {
                    _currentScript.value = script
                    addLogMessage("✅ Script loaded successfully: ${script.metadata?.description ?: "Unknown"}")
                    Timber.i("Script loaded from file: $filePath")
                } else {
                    addLogMessage("❌ Failed to load script from file")
                    Timber.w("Failed to load script from file: $filePath")
                }
            } catch (e: Exception) {
                addLogMessage("❌ Script loading error: ${e.message}")
                Timber.e(e, "Error loading script from file: $filePath")
            }
        }
    }
    
    /**
     * 清除当前脚本
     */
    fun clearScript() {
        _currentScript.value = null
        addLogMessage("🗑️ Script cleared")
        Timber.d("Current script cleared")
    }
    
    /**
     * 开始执行脚本
     */
    fun startExecution() {
        val script = _currentScript.value
        if (script == null) {
            addLogMessage("❌ No script to execute")
            return
        }
        
        if (!_permissionStatus.value.allGranted) {
            addLogMessage("❌ Missing required permissions")
            return
        }
        
        viewModelScope.launch {
            try {
                addLogMessage("🚀 Starting script execution...")
                
                val result = executionEngine.executeScript(script)
                
                if (result.success) {
                    addLogMessage("✅ Script execution completed successfully")
                    addLogMessage("📊 Completed ${result.completedSteps}/${result.totalSteps} steps in ${result.executionTime}ms")
                } else {
                    addLogMessage("❌ Script execution failed: ${result.errorMessage}")
                    addLogMessage("📊 Completed ${result.completedSteps}/${result.totalSteps} steps")
                }
                
            } catch (e: Exception) {
                addLogMessage("❌ Execution error: ${e.message}")
                Timber.e(e, "Script execution error")
            }
        }
    }
    
    /**
     * 停止执行
     */
    fun stopExecution() {
        executionEngine.stopExecution()
        addLogMessage("⏹️ Execution stopped by user")
    }
    
    /**
     * 暂停执行
     */
    fun pauseExecution() {
        executionEngine.pauseExecution()
        addLogMessage("⏸️ Execution paused")
    }
    
    /**
     * 恢复执行
     */
    fun resumeExecution() {
        executionEngine.resumeExecution()
        addLogMessage("▶️ Execution resumed")
    }
    
    /**
     * 获取本地脚本列表
     */
    fun getLocalScripts() {
        viewModelScope.launch {
            try {
                val scripts = scriptRepository.getLocalScripts()
                addLogMessage("📋 Found ${scripts.size} local scripts")
                Timber.d("Local scripts: ${scripts.map { it.name }}")
            } catch (e: Exception) {
                addLogMessage("❌ Failed to get local scripts: ${e.message}")
                Timber.e(e, "Error getting local scripts")
            }
        }
    }
    
    /**
     * 保存当前脚本到本地
     */
    fun saveCurrentScript(fileName: String) {
        val script = _currentScript.value
        if (script == null) {
            addLogMessage("❌ No script to save")
            return
        }
        
        viewModelScope.launch {
            try {
                val success = scriptRepository.saveScript(script, fileName)
                if (success) {
                    addLogMessage("💾 Script saved as $fileName")
                } else {
                    addLogMessage("❌ Failed to save script")
                }
            } catch (e: Exception) {
                addLogMessage("❌ Save error: ${e.message}")
                Timber.e(e, "Error saving script")
            }
        }
    }
    
    /**
     * 添加日志消息
     */
    private fun addLogMessage(message: String) {
        val currentLogs = _recentLogs.value.toMutableList()
        val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date())
        
        currentLogs.add(0, "[$timestamp] $message")
        
        // 保持最近50条日志
        if (currentLogs.size > 50) {
            currentLogs.removeAt(currentLogs.size - 1)
        }
        
        _recentLogs.value = currentLogs
    }
    
    /**
     * 获取日志级别前缀
     */
    private fun getLogLevelPrefix(level: LogLevel): String {
        return when (level) {
            LogLevel.DEBUG -> "🔍"
            LogLevel.INFO -> "ℹ️"
            LogLevel.WARNING -> "⚠️"
            LogLevel.ERROR -> "❌"
            LogLevel.SUCCESS -> "✅"
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        executionEngine.cleanup()
    }
}

/**
 * 主界面UI状态
 */
data class MainUiState(
    val currentScript: DSLScript? = null,
    val permissionStatus: PermissionStatus = PermissionStatus(),
    val executionStatus: ExecutionStatus = ExecutionStatus.IDLE,
    val canExecute: Boolean = false,
    val recentLogs: List<String> = emptyList()
)
