# 768×768 Standardization Implementation

## Overview
All image processing in ShowForAI V3 has been standardized to use 768×768 resolution for AI processing, storage, and coordinate systems. This ensures consistency across the entire pipeline from recording to execution.

## Key Components Updated

### 1. ImageProcessor (`src/showforai/ai/image_processor.py`)
- **STANDARD_RESOLUTION**: Set to (768, 768)
- **resize_to_standard()**: Resizes any image to 768×768
- **original_to_standard()**: Converts coordinates from original to 768×768
- **standard_to_original()**: Converts coordinates from 768×768 to original
- **preprocess_for_ai()**: Prepares images for AI processing in 768×768

### 2. BboxProcessor (`src/showforai/ai/bbox_processor.py`)
- **STANDARD_RESOLUTION**: Set to (768, 768)
- **process_bboxes()**: Ensures all bboxes are in 768×768 coordinates
- **validate_bbox()**: Validates bboxes are within 768×768 bounds
- **bbox_to_original()**: Converts bbox from 768×768 to original resolution
- **bbox_to_standard()**: Converts bbox from original to 768×768

### 3. ElementDetector (`src/showforai/ai/element_detector.py`)
- **detect_element()**: Now accepts original_resolution parameter
- **detect_with_mode()**: Automatically resizes images to 768×768 before sending to AI
- Converts click positions to 768×768 coordinates
- Returns bboxes in 768×768 coordinate system
- Validates and clips bboxes to 768×768 bounds

### 4. Recorder (`src/showforai/recorder/recorder.py`)
- **_standardize_screenshot()**: New method to resize screenshots to 768×768
- All screenshots are standardized before storage
- Metadata includes 'standardized_resolution': '768x768'

### 5. ScriptGenerator (`src/showforai/processing/script_generator.py`)
- Metadata includes coordinate system information
- Validates bboxes are within 768×768 bounds
- Marks all coordinates as standardized

### 6. ElementMatcher (`src/showforai/executor/element_matcher.py`)
- **use_standardized_matching**: New parameter for 768×768 matching
- Performs template matching in 768×768 space
- Converts results back to original resolution

## Coordinate System

### Recording Phase
1. Screenshots captured at original resolution
2. Immediately resized to 768×768 for storage
3. Click positions stored in original resolution
4. Original resolution stored in metadata

### AI Processing Phase
1. Images already stored as 768×768
2. Click positions converted to 768×768 coordinates
3. AI server receives 768×768 images
4. AI returns bboxes in 768×768 coordinates

### Execution Phase
1. Templates are 768×768 element images
2. Current screen resized to 768×768 for matching
3. Match results converted back to original resolution
4. Actions executed in original resolution

## Benefits

1. **Consistency**: All AI processing uses the same resolution
2. **Efficiency**: Reduced image sizes for faster processing
3. **Accuracy**: Standardized coordinate system eliminates conversion errors
4. **Storage**: Smaller file sizes for recordings
5. **Compatibility**: Works across different screen resolutions

## Testing

Run the test suite to verify the implementation:
```bash
python test_768_standardization.py
```

The test suite verifies:
- Image resizing to 768×768
- Bidirectional coordinate conversion
- Bbox validation and conversion
- Consistent resolution across all components

## Migration Notes

For existing recordings:
1. Old recordings with different resolutions will be automatically converted
2. Coordinates will be mapped to the 768×768 system
3. No manual intervention required

## API Changes

### ImageProcessor
```python
# Resize to standard 768x768
resized_data, original_res = processor.resize_to_standard(image_data)

# Convert coordinates
std_x, std_y = processor.original_to_standard(x, y, orig_width, orig_height)
orig_x, orig_y = processor.standard_to_original(x, y, orig_width, orig_height)
```

### BboxProcessor
```python
# Process bboxes to 768x768
processed = processor.process_bboxes(bboxes, from_resolution=(1920, 1080))

# Validate bbox
is_valid = processor.validate_bbox(bbox)

# Convert bbox
bbox_orig = processor.bbox_to_original(bbox_768, orig_width, orig_height)
bbox_std = processor.bbox_to_standard(bbox_orig, orig_width, orig_height)
```

### ElementDetector
```python
# Detect with original resolution
result = detector.detect_element(image_data, click_pos, original_resolution=(1920, 1080))
```

## Product Compliance

This implementation fully complies with the product requirements:
- ✅ Recording screenshots standardized to 768×768
- ✅ AI service receives 768×768 images
- ✅ BBOX coordinates based on 768×768
- ✅ Server stores only 768×768 images
- ✅ Execution handles resolution adaptation