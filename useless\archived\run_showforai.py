"""
ShowForAI V3 快速启动脚本
使用方法：双击运行或在命令行执行 python run_showforai.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    print("=" * 50)
    print("ShowForAI V3 启动器")
    print("=" * 50)
    print()
    print("请选择要启动的功能：")
    print("1. 录制器 (Recorder)")
    print("2. 执行器 (Executor)")
    print("3. 主界面 (Main GUI)")
    print("4. 退出")
    print()
    
    choice = input("请输入选项 (1-4): ").strip()
    
    if choice == "1":
        print("\n正在启动录制器...")
        print("提示：")
        print("- 点击红色Record按钮开始录制")
        print("- 按F9停止录制")
        print("- 录制完成后点击'Upload & Create Script'生成脚本")
        print()
        
        from showforai.recorder.gui import main
        main()
        
    elif choice == "2":
        print("\n正在启动执行器...")
        print("提示：")
        print("- 选择要执行的脚本")
        print("- 点击Run按钮开始执行")
        print()
        
        from showforai.executor.gui import main
        main()
        
    elif choice == "3":
        print("\n正在启动主界面...")
        try:
            from showforai.gui_launcher import main
            main()
        except Exception as e:
            print(f"主界面启动失败: {e}")
            print("请尝试使用录制器或执行器")
            
    elif choice == "4":
        print("退出程序")
        sys.exit(0)
        
    else:
        print("无效选项，请重新运行")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序已退出")
    except Exception as e:
        print(f"\n错误: {e}")
        input("\n按Enter键退出...")