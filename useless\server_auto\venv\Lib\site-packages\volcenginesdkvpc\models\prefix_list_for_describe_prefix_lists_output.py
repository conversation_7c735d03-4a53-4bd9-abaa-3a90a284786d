# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PrefixListForDescribePrefixListsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'association_count': 'int',
        'cidrs': 'list[str]',
        'creation_time': 'str',
        'description': 'str',
        'ip_version': 'str',
        'max_entries': 'int',
        'prefix_list_id': 'str',
        'prefix_list_name': 'str',
        'project_name': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribePrefixListsOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'association_count': 'AssociationCount',
        'cidrs': 'Cidrs',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'ip_version': 'IpVersion',
        'max_entries': 'MaxEntries',
        'prefix_list_id': 'PrefixListId',
        'prefix_list_name': 'PrefixListName',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, association_count=None, cidrs=None, creation_time=None, description=None, ip_version=None, max_entries=None, prefix_list_id=None, prefix_list_name=None, project_name=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """PrefixListForDescribePrefixListsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._association_count = None
        self._cidrs = None
        self._creation_time = None
        self._description = None
        self._ip_version = None
        self._max_entries = None
        self._prefix_list_id = None
        self._prefix_list_name = None
        self._project_name = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if association_count is not None:
            self.association_count = association_count
        if cidrs is not None:
            self.cidrs = cidrs
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if ip_version is not None:
            self.ip_version = ip_version
        if max_entries is not None:
            self.max_entries = max_entries
        if prefix_list_id is not None:
            self.prefix_list_id = prefix_list_id
        if prefix_list_name is not None:
            self.prefix_list_name = prefix_list_name
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def association_count(self):
        """Gets the association_count of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The association_count of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: int
        """
        return self._association_count

    @association_count.setter
    def association_count(self, association_count):
        """Sets the association_count of this PrefixListForDescribePrefixListsOutput.


        :param association_count: The association_count of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: int
        """

        self._association_count = association_count

    @property
    def cidrs(self):
        """Gets the cidrs of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The cidrs of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cidrs

    @cidrs.setter
    def cidrs(self, cidrs):
        """Sets the cidrs of this PrefixListForDescribePrefixListsOutput.


        :param cidrs: The cidrs of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: list[str]
        """

        self._cidrs = cidrs

    @property
    def creation_time(self):
        """Gets the creation_time of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The creation_time of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this PrefixListForDescribePrefixListsOutput.


        :param creation_time: The creation_time of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The description of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this PrefixListForDescribePrefixListsOutput.


        :param description: The description of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def ip_version(self):
        """Gets the ip_version of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The ip_version of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this PrefixListForDescribePrefixListsOutput.


        :param ip_version: The ip_version of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._ip_version = ip_version

    @property
    def max_entries(self):
        """Gets the max_entries of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The max_entries of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_entries

    @max_entries.setter
    def max_entries(self, max_entries):
        """Sets the max_entries of this PrefixListForDescribePrefixListsOutput.


        :param max_entries: The max_entries of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: int
        """

        self._max_entries = max_entries

    @property
    def prefix_list_id(self):
        """Gets the prefix_list_id of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The prefix_list_id of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._prefix_list_id

    @prefix_list_id.setter
    def prefix_list_id(self, prefix_list_id):
        """Sets the prefix_list_id of this PrefixListForDescribePrefixListsOutput.


        :param prefix_list_id: The prefix_list_id of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._prefix_list_id = prefix_list_id

    @property
    def prefix_list_name(self):
        """Gets the prefix_list_name of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The prefix_list_name of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._prefix_list_name

    @prefix_list_name.setter
    def prefix_list_name(self, prefix_list_name):
        """Sets the prefix_list_name of this PrefixListForDescribePrefixListsOutput.


        :param prefix_list_name: The prefix_list_name of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._prefix_list_name = prefix_list_name

    @property
    def project_name(self):
        """Gets the project_name of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The project_name of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this PrefixListForDescribePrefixListsOutput.


        :param project_name: The project_name of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The status of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this PrefixListForDescribePrefixListsOutput.


        :param status: The status of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The tags of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: list[TagForDescribePrefixListsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this PrefixListForDescribePrefixListsOutput.


        :param tags: The tags of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: list[TagForDescribePrefixListsOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this PrefixListForDescribePrefixListsOutput.  # noqa: E501


        :return: The update_time of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this PrefixListForDescribePrefixListsOutput.


        :param update_time: The update_time of this PrefixListForDescribePrefixListsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrefixListForDescribePrefixListsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrefixListForDescribePrefixListsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PrefixListForDescribePrefixListsOutput):
            return True

        return self.to_dict() != other.to_dict()
