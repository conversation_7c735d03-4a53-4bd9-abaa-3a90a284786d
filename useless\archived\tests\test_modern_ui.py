"""
Test suite for Modern UI Components
Tests theme switching, component rendering, animations, and responsive layout
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QWidget, QPushButton, QLabel
from PyQt6.QtCore import Qt, QPropertyAnimation, QRect
from PyQt6.QtGui import QPalette, QColor
import time

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.ui.modern_ui import ModernUI, ThemeManager, AnimationManager, ResponsiveLayout


class TestThemeManager(unittest.TestCase):
    """Test Theme Manager functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
            
    def setUp(self):
        """Set up test fixtures"""
        self.theme_manager = ThemeManager()
        self.test_widget = QWidget()
        
    def test_default_theme(self):
        """Test default theme initialization"""
        current_theme = self.theme_manager.get_current_theme()
        self.assertIn(current_theme, ['light', 'dark'])
        
    def test_theme_switching(self):
        """Test switching between themes"""
        # Switch to dark theme
        self.theme_manager.set_theme('dark')
        self.assertEqual(self.theme_manager.get_current_theme(), 'dark')
        
        # Switch to light theme
        self.theme_manager.set_theme('light')
        self.assertEqual(self.theme_manager.get_current_theme(), 'light')
        
    def test_theme_application(self):
        """Test applying theme to widget"""
        # Apply dark theme
        self.theme_manager.set_theme('dark')
        self.theme_manager.apply_to_widget(self.test_widget)
        
        # Check style properties
        palette = self.test_widget.palette()
        bg_color = palette.color(QPalette.ColorRole.Window)
        
        # Dark theme should have dark background
        self.assertLess(bg_color.lightness(), 128)
        
    def test_custom_theme(self):
        """Test creating and applying custom theme"""
        custom_theme = {
            'name': 'custom',
            'colors': {
                'background': '#2b2b2b',
                'foreground': '#ffffff',
                'accent': '#007acc'
            },
            'fonts': {
                'default': 'Arial',
                'size': 12
            }
        }
        
        self.theme_manager.register_theme('custom', custom_theme)
        self.theme_manager.set_theme('custom')
        
        self.assertEqual(self.theme_manager.get_current_theme(), 'custom')
        
    def test_theme_persistence(self):
        """Test theme preference persistence"""
        # Set theme
        self.theme_manager.set_theme('dark')
        
        # Save preference
        self.theme_manager.save_preference()
        
        # Create new manager (should load saved preference)
        new_manager = ThemeManager()
        self.assertEqual(new_manager.get_current_theme(), 'dark')
        
    def test_theme_signals(self):
        """Test theme change signals"""
        signal_received = []
        
        def on_theme_changed(theme):
            signal_received.append(theme)
            
        self.theme_manager.theme_changed.connect(on_theme_changed)
        self.theme_manager.set_theme('dark')
        
        self.assertEqual(len(signal_received), 1)
        self.assertEqual(signal_received[0], 'dark')


class TestAnimationManager(unittest.TestCase):
    """Test Animation Manager functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.animation_manager = AnimationManager()
        self.test_widget = QPushButton("Test")
        self.test_widget.setGeometry(0, 0, 100, 50)
        
    def test_fade_animation(self):
        """Test fade in/out animations"""
        # Fade in
        animation = self.animation_manager.create_fade_animation(
            self.test_widget,
            duration=100,
            start_value=0.0,
            end_value=1.0
        )
        
        self.assertIsInstance(animation, QPropertyAnimation)
        self.assertEqual(animation.duration(), 100)
        
    def test_slide_animation(self):
        """Test slide animations"""
        # Slide right
        animation = self.animation_manager.create_slide_animation(
            self.test_widget,
            direction='right',
            distance=100,
            duration=200
        )
        
        animation.start()
        # Note: In unit tests, animations complete immediately
        
        # Check final position
        end_pos = animation.endValue()
        self.assertEqual(end_pos.x(), 100)
        
    def test_scale_animation(self):
        """Test scale animations"""
        animation = self.animation_manager.create_scale_animation(
            self.test_widget,
            scale_factor=1.5,
            duration=150
        )
        
        self.assertIsNotNone(animation)
        self.assertEqual(animation.duration(), 150)
        
    def test_animation_group(self):
        """Test grouped animations"""
        animations = [
            self.animation_manager.create_fade_animation(self.test_widget),
            self.animation_manager.create_slide_animation(self.test_widget)
        ]
        
        group = self.animation_manager.create_animation_group(animations)
        
        self.assertEqual(group.animationCount(), 2)
        
    def test_animation_completion(self):
        """Test animation completion callbacks"""
        completed = []
        
        def on_complete():
            completed.append(True)
            
        animation = self.animation_manager.create_fade_animation(
            self.test_widget,
            duration=10
        )
        animation.finished.connect(on_complete)
        
        animation.start()
        QApplication.processEvents()
        
        # Animation should complete
        self.assertEqual(len(completed), 1)
        
    def test_animation_interruption(self):
        """Test interrupting animations"""
        animation = self.animation_manager.create_slide_animation(
            self.test_widget,
            duration=1000
        )
        
        animation.start()
        self.assertEqual(animation.state(), QPropertyAnimation.State.Running)
        
        animation.stop()
        self.assertEqual(animation.state(), QPropertyAnimation.State.Stopped)


class TestResponsiveLayout(unittest.TestCase):
    """Test Responsive Layout functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.layout_manager = ResponsiveLayout()
        self.test_widget = QWidget()
        self.test_widget.resize(800, 600)
        
    def test_breakpoints(self):
        """Test responsive breakpoints"""
        # Test different screen sizes
        breakpoints = [
            (320, 'mobile'),
            (768, 'tablet'),
            (1024, 'desktop'),
            (1920, 'wide')
        ]
        
        for width, expected in breakpoints:
            category = self.layout_manager.get_size_category(width)
            self.assertEqual(category, expected)
            
    def test_layout_adjustment(self):
        """Test layout adjustment based on size"""
        # Create layout with test widgets
        button1 = QPushButton("Button 1")
        button2 = QPushButton("Button 2")
        
        self.layout_manager.add_widget(button1, responsive=True)
        self.layout_manager.add_widget(button2, responsive=True)
        
        # Test mobile layout (should stack vertically)
        self.layout_manager.adjust_for_size(320, 568)
        layout_type = self.layout_manager.get_current_layout()
        self.assertEqual(layout_type, 'vertical')
        
        # Test desktop layout (should be horizontal)
        self.layout_manager.adjust_for_size(1920, 1080)
        layout_type = self.layout_manager.get_current_layout()
        self.assertEqual(layout_type, 'horizontal')
        
    def test_dynamic_resizing(self):
        """Test dynamic widget resizing"""
        label = QLabel("Responsive Text")
        
        # Set responsive properties
        self.layout_manager.make_responsive(
            label,
            min_size=(100, 30),
            max_size=(400, 100),
            scale_with_parent=True
        )
        
        # Test scaling
        self.layout_manager.scale_widget(label, scale_factor=1.5)
        
        size = label.size()
        self.assertGreater(size.width(), 100)
        self.assertLessEqual(size.width(), 400)
        
    def test_aspect_ratio_preservation(self):
        """Test maintaining aspect ratio during resize"""
        widget = QWidget()
        widget.resize(200, 100)  # 2:1 ratio
        
        self.layout_manager.set_aspect_ratio(widget, 2.0)
        
        # Resize width
        new_size = self.layout_manager.calculate_size_with_aspect(
            widget, 
            new_width=300
        )
        
        self.assertEqual(new_size.width(), 300)
        self.assertEqual(new_size.height(), 150)  # Maintains 2:1
        
    def test_fluid_grid(self):
        """Test fluid grid layout"""
        # Create grid with widgets
        widgets = [QPushButton(f"Button {i}") for i in range(6)]
        
        grid = self.layout_manager.create_fluid_grid(
            widgets,
            columns_desktop=3,
            columns_tablet=2,
            columns_mobile=1
        )
        
        # Test desktop layout
        self.layout_manager.adjust_grid_for_size(grid, 1920, 1080)
        self.assertEqual(grid.columnCount(), 3)
        
        # Test mobile layout
        self.layout_manager.adjust_grid_for_size(grid, 320, 568)
        self.assertEqual(grid.columnCount(), 1)


class TestModernUIComponents(unittest.TestCase):
    """Test individual Modern UI components"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.modern_ui = ModernUI()
        
    def test_card_component(self):
        """Test card component creation"""
        card = self.modern_ui.create_card(
            title="Test Card",
            content="Card content",
            actions=["Action 1", "Action 2"]
        )
        
        self.assertIsNotNone(card)
        self.assertEqual(card.title, "Test Card")
        self.assertEqual(len(card.actions), 2)
        
    def test_toast_notification(self):
        """Test toast notification component"""
        toast = self.modern_ui.show_toast(
            message="Test notification",
            duration=1000,
            position='bottom-right'
        )
        
        self.assertIsNotNone(toast)
        self.assertTrue(toast.isVisible())
        
    def test_loading_spinner(self):
        """Test loading spinner component"""
        spinner = self.modern_ui.create_spinner(
            size=50,
            color='#007acc'
        )
        
        self.assertIsNotNone(spinner)
        self.assertTrue(spinner.is_spinning())
        
        # Stop spinner
        spinner.stop()
        self.assertFalse(spinner.is_spinning())
        
    def test_progress_indicator(self):
        """Test custom progress indicator"""
        progress = self.modern_ui.create_progress_indicator(
            style='circular',
            size=100
        )
        
        # Update progress
        progress.set_value(50)
        self.assertEqual(progress.value(), 50)
        
        # Animate to value
        progress.animate_to(100, duration=100)
        QApplication.processEvents()
        
    def test_floating_action_button(self):
        """Test floating action button"""
        fab = self.modern_ui.create_fab(
            icon='plus',
            position='bottom-right',
            color='#4CAF50'
        )
        
        self.assertIsNotNone(fab)
        
        # Test click
        clicked = []
        fab.clicked.connect(lambda: clicked.append(True))
        fab.click()
        
        self.assertEqual(len(clicked), 1)
        
    def test_material_button(self):
        """Test material design button"""
        button = self.modern_ui.create_material_button(
            text="Click Me",
            style='raised',
            ripple_effect=True
        )
        
        self.assertIsNotNone(button)
        self.assertTrue(button.has_ripple_effect)
        
    def test_custom_scrollbar(self):
        """Test custom scrollbar styling"""
        scrollbar = self.modern_ui.create_custom_scrollbar(
            width=10,
            color='#666',
            hover_color='#888'
        )
        
        self.assertIsNotNone(scrollbar)
        self.assertEqual(scrollbar.width(), 10)


class TestUIPerformance(unittest.TestCase):
    """Test UI performance and optimization"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def test_render_performance(self):
        """Test rendering performance"""
        modern_ui = ModernUI()
        
        # Create many widgets
        start_time = time.time()
        
        widgets = []
        for i in range(100):
            widget = modern_ui.create_card(f"Card {i}", "Content")
            widgets.append(widget)
            
        elapsed = time.time() - start_time
        
        # Should create 100 widgets quickly
        self.assertLess(elapsed, 1.0)
        
    def test_animation_performance(self):
        """Test animation performance"""
        manager = AnimationManager()
        widget = QWidget()
        
        # Create multiple animations
        animations = []
        for i in range(50):
            anim = manager.create_fade_animation(widget, duration=100)
            animations.append(anim)
            
        # Run all animations
        start_time = time.time()
        for anim in animations:
            anim.start()
            
        elapsed = time.time() - start_time
        
        # Should handle many animations efficiently
        self.assertLess(elapsed, 0.5)
        
    def test_memory_usage(self):
        """Test memory usage of UI components"""
        import tracemalloc
        
        tracemalloc.start()
        snapshot1 = tracemalloc.take_snapshot()
        
        # Create and destroy many widgets
        modern_ui = ModernUI()
        for _ in range(100):
            widget = modern_ui.create_card("Test", "Content")
            widget.deleteLater()
            
        QApplication.processEvents()
        
        snapshot2 = tracemalloc.take_snapshot()
        
        # Check memory difference
        stats = snapshot2.compare_to(snapshot1, 'lineno')
        total_diff = sum(stat.size_diff for stat in stats[:10])
        
        # Memory increase should be reasonable
        self.assertLess(total_diff / (1024 * 1024), 10)  # Less than 10MB
        
        tracemalloc.stop()
        
    def test_responsive_performance(self):
        """Test responsive layout performance"""
        layout = ResponsiveLayout()
        
        # Add many widgets
        widgets = [QPushButton(f"Button {i}") for i in range(100)]
        for widget in widgets:
            layout.add_widget(widget, responsive=True)
            
        # Test resize performance
        start_time = time.time()
        
        # Simulate multiple resize events
        for width in [320, 768, 1024, 1920]:
            layout.adjust_for_size(width, 600)
            
        elapsed = time.time() - start_time
        
        # Should handle resizes quickly
        self.assertLess(elapsed, 0.5)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)