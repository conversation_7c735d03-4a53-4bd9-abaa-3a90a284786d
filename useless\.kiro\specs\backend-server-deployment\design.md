# ShowForAI后端服务器设计文档

## 概述

ShowForAI后端服务器是一个基于现有server_auto的简化部署方案，专为个人开发者的小规模测试而设计。系统将现有的AI处理能力包装为Web服务，与前端平台和Supabase数据库集成，提供完整的session.zip处理和脚本生成功能。

### 项目现状分析
- **技术基础**: server_auto已完成90%，具备完整的AI处理流水线
- **前端平台**: showforai已完成80%，Supabase集成完成
- **用户规模**: 个人项目，初期测试阶段，预计<100用户
- **商业模式**: 免费创作者 + 付费创作者($20/月) + 脚本分享经济

### 设计目标
- **快速上线**: 基于现有server_auto，最小化开发工作量
- **成本控制**: 单台服务器部署，月成本<$50
- **功能完整**: 支持完整的AI处理流程和用户管理
- **易于维护**: 简单架构，便于个人开发者管理

## 架构

### 整体架构
系统采用微服务架构，主要包含以下核心服务：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Gateway   │    │  File Service   │    │  Auth Service   │
│   (API Gateway) │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
         │ Processing      │    │  Database       │    │  Message Queue  │
         │ Service         │    │  (PostgreSQL)   │    │  (Redis)        │
         └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 架构决策理由
1. **微服务架构**: 便于独立扩展和维护，降低单点故障风险
2. **API Gateway**: 统一入口，便于认证、限流和监控
3. **消息队列**: 异步处理AI任务，提高系统响应性
4. **Supabase PostgreSQL**: 与前端保持一致，复用现有数据库基础设施
5. **AWS S3存储**: 成本效益最优的文件存储方案，支持全球CDN分发

## 组件和接口

### 1. API Gateway (Web Gateway)
**职责**: 请求路由、认证验证、限流控制、日志记录

**核心接口**:
```python
# 用户认证
POST /api/auth/login
POST /api/auth/refresh

# 文件管理
POST /api/files/upload
GET /api/files/{file_id}/download
GET /api/files/user/{user_id}

# AI处理
POST /api/processing/submit
GET /api/processing/status/{task_id}
GET /api/processing/result/{task_id}

# 用户管理
GET /api/users/profile
PUT /api/users/profile
GET /api/users/quota
```

**技术栈**: FastAPI + Uvicorn
**设计理由**: FastAPI提供自动API文档生成和高性能异步处理

### 2. Authentication Service
**职责**: JWT token管理、用户权限验证、配额检查

**核心功能**:
- JWT token生成和验证
- 用户权限管理(免费用户/付费用户)
- API调用配额控制
- 异常访问检测和阻止

**集成方式**: 与Supabase Auth集成，支持多种登录方式
**设计理由**: 复用前端已有的认证体系，降低用户学习成本

### 3. File Management Service
**职责**: 文件上传下载、个性化打包、CDN分发

**存储策略**:
- **热数据**: Redis缓存(24小时内访问的文件)
- **温数据**: 本地SSD存储(7天内的处理结果)
- **冷数据**: AWS S3存储(长期文件存储和CDN分发)

**个性化打包功能**:
```python
class PackageGenerator:
    def generate_aeye_package(self, user_config):
        """生成包含用户配置的Aeye录制器SFX文件"""
        
    def generate_aihand_package(self, user_id, script_ids):
        """打包用户脚本和资产为AIhand执行器"""
        
    def generate_referral_package(self, referrer_id, script_id):
        """生成拉新专用下载程序"""
```

**设计理由**: 分层存储降低成本，个性化打包提升用户体验

### 4. AI Processing Service
**职责**: 任务队列管理、AI服务调用、结果处理

**四步AI处理流程** (基于server_auto实现):
1. **Step 1 - 事件提取**: 从session.zip中提取用户操作事件
2. **Step 2 - AI视觉标注**: 调用火山引擎视觉大模型API对UI元素进行标注
3. **Step 3 - 图像裁剪**: 裁剪UI元素图像，生成执行器所需资产
4. **Step 4 - DSL脚本生成**: 基于标注结果生成可执行的自动化脚本
5. **Step 5 - 概览GIF生成**: 生成操作流程的可视化概览

**队列管理**:
```python
class TaskQueue:
    def submit_task(self, user_id, file_path, priority="normal"):
        """提交AI处理任务"""
        
    def process_task(self, task_id):
        """处理单个任务"""
        
    def get_task_status(self, task_id):
        """获取任务状态"""
```

**设计理由**: 异步处理避免阻塞，优先级队列保证付费用户体验

### 5. Database Layer
**数据模型**:

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    subscription_type VARCHAR(50),
    quota_used INTEGER DEFAULT 0,
    quota_limit INTEGER DEFAULT 10,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 文件表
CREATE TABLE files (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    filename VARCHAR(255),
    file_type VARCHAR(50),
    file_size BIGINT,
    storage_path TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 处理任务表
CREATE TABLE processing_tasks (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    file_id UUID REFERENCES files(id),
    status VARCHAR(50),
    priority VARCHAR(20) DEFAULT 'normal',
    result_path TEXT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- 脚本表
CREATE TABLE scripts (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    task_id UUID REFERENCES processing_tasks(id),
    script_name VARCHAR(255),
    dsl_content TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);
```

**设计理由**: 规范化设计保证数据一致性，UUID主键支持分布式扩展

## 数据模型

### 文件生命周期管理
```python
class FileLifecycleManager:
    def __init__(self):
        self.retention_policies = {
            'session_zip': 30,  # 原始文件保留30天
            'processed_result': 90,  # 处理结果保留90天
            'user_scripts': 365,  # 用户脚本保留1年
            'temp_files': 1  # 临时文件1天后清理
        }
    
    def schedule_cleanup(self, file_type, file_path, created_at):
        """安排文件清理任务"""
        
    def backup_critical_data(self, file_id):
        """备份关键数据"""
```

### 配额管理系统
```python
class QuotaManager:
    def check_quota(self, user_id, operation_type):
        """检查用户配额"""
        
    def consume_quota(self, user_id, amount):
        """消费配额"""
        
    def reset_monthly_quota(self, user_id):
        """重置月度配额"""
```

## 错误处理

### 错误分类和处理策略

**1. 用户错误 (4xx)**
- 文件格式不支持: 返回详细格式要求
- 配额不足: 引导用户升级套餐
- 认证失败: 引导重新登录

**2. 系统错误 (5xx)**
- AI服务超时: 自动重试3次，失败后人工介入
- 存储服务故障: 切换备用存储
- 数据库连接失败: 使用连接池重连

**3. 第三方服务错误**
- 火山引擎API限流: 实现指数退避重试
- 云存储服务中断: 启用本地缓存模式

### 错误恢复机制
```python
class ErrorRecoveryManager:
    def handle_ai_processing_failure(self, task_id, error):
        """AI处理失败恢复"""
        if error.type == "timeout":
            self.retry_with_longer_timeout(task_id)
        elif error.type == "quota_exceeded":
            self.queue_for_next_cycle(task_id)
            
    def handle_storage_failure(self, operation, file_id):
        """存储失败恢复"""
        if operation == "upload":
            self.switch_to_backup_storage(file_id)
        elif operation == "download":
            self.serve_from_cache(file_id)
```

## 测试策略

### 单元测试
- **覆盖率目标**: 90%以上
- **重点模块**: 认证、文件处理、AI调用
- **测试框架**: pytest + pytest-asyncio

### 集成测试
```python
class IntegrationTests:
    def test_end_to_end_processing(self):
        """端到端AI处理流程测试"""
        
    def test_file_upload_download_cycle(self):
        """文件上传下载完整流程测试"""
        
    def test_authentication_flow(self):
        """认证授权流程测试"""
        
    def test_quota_management(self):
        """配额管理测试"""
```

### 性能测试
- **负载测试**: 模拟100并发用户
- **压力测试**: 逐步增加负载至系统极限
- **持久性测试**: 24小时连续运行测试

### 安全测试
- **认证绕过测试**: 验证JWT token安全性
- **文件上传安全**: 防止恶意文件上传
- **SQL注入测试**: 数据库查询安全性
- **API限流测试**: 防止DDoS攻击

## 部署和运维

### 容器化部署
```dockerfile
# 多阶段构建优化镜像大小
FROM python:3.11-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose配置
```yaml
version: '3.8'
services:
  api-gateway:
    build: ./api-gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    
  processing-service:
    build: ./processing-service
    environment:
      - VOLCANO_API_KEY=${VOLCANO_API_KEY}
      - AWS_ACCESS_KEY=${AWS_ACCESS_KEY}
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=showforai
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
```

### 监控和日志
```python
class MonitoringService:
    def setup_metrics(self):
        """设置Prometheus指标"""
        self.request_counter = Counter('api_requests_total')
        self.response_time = Histogram('api_response_time_seconds')
        self.error_counter = Counter('api_errors_total')
        
    def setup_logging(self):
        """配置结构化日志"""
        logging.config.dictConfig({
            'version': 1,
            'formatters': {
                'json': {
                    'format': '%(asctime)s %(name)s %(levelname)s %(message)s',
                    'class': 'pythonjsonlogger.jsonlogger.JsonFormatter'
                }
            }
        })
```

### 自动扩展策略
- **CPU使用率 > 70%**: 自动增加实例
- **内存使用率 > 80%**: 触发扩容警报
- **队列长度 > 50**: 增加处理节点
- **响应时间 > 3秒**: 启动性能优化模式

## 成本优化

### 资源使用优化
```python
class ResourceOptimizer:
    def optimize_ai_processing(self):
        """优化AI处理资源使用"""
        # 批量处理相似任务
        # GPU资源池化管理
        # 智能任务调度
        
    def optimize_storage_costs(self):
        """优化存储成本"""
        # 自动数据分层
        # 压缩算法优化
        # 冗余数据清理
        
    def optimize_compute_resources(self):
        """优化计算资源"""
        # 容器资源限制
        # 自动休眠机制
        # 负载均衡优化
```

### 成本监控
- **实时成本跟踪**: 按服务、按用户统计成本
- **预算警报**: 成本超出80%时发送警报
- **优化建议**: 基于使用模式提供成本优化建议

## 安全设计

### 数据安全
- **传输加密**: 全站HTTPS，API通信TLS 1.3
- **存储加密**: 敏感数据AES-256加密存储
- **访问控制**: 基于角色的权限管理(RBAC)

### API安全
```python
class SecurityMiddleware:
    def validate_jwt_token(self, token):
        """JWT token验证"""
        
    def rate_limiting(self, user_id, endpoint):
        """API限流"""
        
    def detect_anomaly(self, user_id, request_pattern):
        """异常访问检测"""
```

### 数据备份和恢复
- **实时备份**: 数据库WAL日志实时同步
- **定期备份**: 每日全量备份，每小时增量备份
- **异地备份**: 关键数据异地存储
- **恢复测试**: 月度备份恢复演练

## 简化部署方案

### 推荐配置 (个人开发者测试阶段)

#### 方案一：火山引擎云服务器 (推荐)
1. **计算资源**
   - **火山引擎ECS**: 1台 2核4GB服务器
   - **预估成本**: ¥100-150/月 ($15-20/月)

2. **数据库**
   - **Supabase免费版**: 500MB存储，50,000行数据
   - **预估成本**: 免费

3. **存储**
   - **火山引擎对象存储**: 10GB存储 + CDN
   - **预估成本**: ¥20-30/月 ($3-5/月)

4. **AI服务**
   - **火山引擎视觉大模型**: 按调用量计费
   - **预估成本**: ¥200-300/月 ($30-45/月)

**总预算**: ¥320-480/月 ($48-72/月)

#### 方案二：最小化部署
1. **计算资源**
   - **单台VPS**: 2核4GB (任意云服务商)
   - **预估成本**: $20-30/月

2. **数据库**
   - **Supabase免费版**: 与前端共享
   - **预估成本**: 免费

3. **存储**
   - **本地存储**: 服务器本地磁盘
   - **预估成本**: 包含在VPS费用中

4. **AI服务**
   - **火山引擎视觉大模型**: 按调用量计费
   - **预估成本**: $30-45/月

**总预算**: $50-75/月

### 部署架构简化

#### 单服务器部署
```
┌─────────────────────────────────────┐
│           单台服务器                  │
│  ┌─────────────┐  ┌─────────────┐   │
│  │   FastAPI   │  │    Redis    │   │
│  │   Gateway   │  │   (缓存)     │   │
│  └─────────────┘  └─────────────┘   │
│  ┌─────────────┐  ┌─────────────┐   │
│  │ Processing  │  │ File Storage│   │
│  │  Service    │  │  (本地磁盘)  │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
         │                    │
         ▼                    ▼
  ┌─────────────┐    ┌─────────────┐
  │  Supabase   │    │  火山引擎    │
  │ PostgreSQL  │    │  视觉API    │
  └─────────────┘    └─────────────┘
```

## 简化开发方案

### 基于server_auto的快速部署

#### 核心思路
直接基于现有的server_auto代码，添加必要的Web接口和用户管理功能，实现最小可行产品(MVP)。

#### 简化架构
```
┌─────────────────────────────────────┐
│        单体FastAPI应用               │
│  ┌─────────────────────────────────┐ │
│  │         Web API层               │ │
│  │  /api/upload/session           │ │
│  │  /api/processing/status        │ │
│  │  /api/download/script          │ │
│  └─────────────────────────────────┘ │
│  ┌─────────────────────────────────┐ │
│  │      现有server_auto核心         │ │
│  │  - PipelineService             │ │
│  │  - VisionAnnotator             │ │
│  │  - DslGenerator                │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
         │                    │
         ▼                    ▼
  ┌─────────────┐    ┌─────────────┐
  │  Supabase   │    │  火山引擎    │
  │ PostgreSQL  │    │  视觉API    │
  └─────────────┘    └─────────────┘
```

### 最小化代码结构

```
showforai-backend/
├── main.py                 # FastAPI主应用
├── api/
│   ├── __init__.py
│   ├── upload.py          # 文件上传接口
│   ├── processing.py      # 处理状态查询
│   └── download.py        # 结果下载接口
├── core/
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── database.py        # Supabase连接
│   └── auth.py            # 简单认证
├── server_auto/           # 复制现有server_auto代码
│   ├── processors/
│   ├── services/
│   └── models/
├── requirements.txt
└── Dockerfile
```

### 核心API设计

```python
# main.py - 简化版主应用
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
import shutil
from pathlib import Path

from server_auto.services.pipeline_service import PipelineService
from core.database import get_user_quota, update_user_quota
from core.auth import verify_user_token

app = FastAPI(title="ShowForAI Backend", version="1.0.0")
pipeline_service = PipelineService()

@app.post("/api/upload/session")
async def upload_session(
    file: UploadFile = File(...),
    user_token: str = Header(...)
):
    # 1. 验证用户token
    user_id = verify_user_token(user_token)
    
    # 2. 检查用户配额
    if not check_user_quota(user_id):
        raise HTTPException(status_code=403, detail="Quota exceeded")
    
    # 3. 保存文件并处理
    temp_path = save_uploaded_file(file)
    result_path = await pipeline_service.process_session_from_zip(temp_path)
    
    # 4. 更新配额并返回结果
    update_user_quota(user_id)
    return {"task_id": generate_task_id(), "status": "processing"}

@app.get("/api/download/script/{task_id}")
async def download_script(task_id: str, user_token: str = Header(...)):
    user_id = verify_user_token(user_token)
    script_path = get_script_path(task_id, user_id)
    return FileResponse(script_path, filename="generated_script.json")
```

### 部署配置

#### Docker配置
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 复制requirements并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 简化部署脚本
```bash
#!/bin/bash
# deploy.sh - 简单部署脚本

# 1. 构建Docker镜像
docker build -t showforai-backend .

# 2. 停止旧容器
docker stop showforai-backend || true
docker rm showforai-backend || true

# 3. 启动新容器
docker run -d \
  --name showforai-backend \
  -p 8000:8000 \
  -e SUPABASE_URL=$SUPABASE_URL \
  -e SUPABASE_KEY=$SUPABASE_KEY \
  -e VOLCANO_API_KEY=$VOLCANO_API_KEY \
  showforai-backend

echo "部署完成！"
```

### 开发流程

#### 本地开发
1. 复制server_auto代码到项目中
2. 添加FastAPI Web接口层
3. 集成Supabase用户认证
4. 本地测试完整流程

#### 生产部署
1. 购买火山引擎ECS服务器
2. 安装Docker
3. 配置环境变量
4. 运行部署脚本

### 成本控制

#### 最小化配置
- **服务器**: 火山引擎ECS 2核4GB ¥120/月
- **数据库**: Supabase免费版 ¥0/月
- **存储**: AWS S3 (10GB) ¥15/月
- **AI服务**: 火山引擎API ¥200-300/月

**总成本**: ¥335-435/月 ($50-65/月)

#### AWS S3的必要性
1. **前端集成**: showforai前端已完成S3集成，需要保持一致
2. **个性化分发**: 为用户生成专属的Aeye/AIhand下载包
3. **CDN加速**: 支持全球用户快速下载
4. **文件管理**: 处理结果文件的长期存储和分发

#### 优化策略
- 使用本地磁盘作为临时存储，S3作为长期存储
- Supabase免费版足够初期使用
- 按需使用AI API，控制调用频率
- 单服务器部署，简化运维成本

## 后端管理界面

### 管理界面需求
作为个人开发者，需要一个简单的可视化界面来监控和管理后端服务，包括：
- 用户管理和数据查看
- 系统状态监控
- AI处理任务管理
- 成本和使用量统计

### 推荐方案：Django Admin

#### 选择理由
1. **开箱即用**: Django Admin提供完整的管理界面，无需从零开发
2. **快速集成**: 可以轻松集成到现有FastAPI项目中
3. **功能完整**: 支持CRUD操作、用户权限、数据过滤等
4. **成本低**: 开源免费，维护成本低

#### 技术实现

**架构设计**:
```
┌─────────────────────────────────────┐
│        FastAPI主应用                 │
│  ┌─────────────────────────────────┐ │
│  │         API服务                 │ │
│  │  /api/upload/session           │ │
│  │  /api/processing/status        │ │
│  └─────────────────────────────────┘ │
│  ┌─────────────────────────────────┐ │
│  │      Django Admin模块           │ │
│  │  /admin/ (管理界面)             │ │
│  │  /admin/users/ (用户管理)       │ │
│  │  /admin/tasks/ (任务管理)       │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**代码结构**:
```
showforai-backend/
├── main.py                 # FastAPI主应用
├── api/                    # API接口
├── admin/                  # Django Admin模块
│   ├── __init__.py
│   ├── settings.py        # Django配置
│   ├── urls.py           # URL路由
│   ├── models.py         # 数据模型
│   ├── admin.py          # Admin配置
│   └── views.py          # 自定义视图
├── server_auto/           # AI处理核心
└── requirements.txt
```

**Django Admin配置**:
```python
# admin/models.py
from django.db import models

class User(models.Model):
    id = models.UUIDField(primary_key=True)
    email = models.EmailField(unique=True)
    subscription_type = models.CharField(max_length=50)
    quota_used = models.IntegerField(default=0)
    quota_limit = models.IntegerField(default=10)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.email

class ProcessingTask(models.Model):
    id = models.UUIDField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.user.email} - {self.status}"

# admin/admin.py
from django.contrib import admin
from .models import User, ProcessingTask

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ['email', 'subscription_type', 'quota_used', 'quota_limit', 'created_at']
    list_filter = ['subscription_type', 'created_at']
    search_fields = ['email']
    readonly_fields = ['id', 'created_at']

@admin.register(ProcessingTask)
class ProcessingTaskAdmin(admin.ModelAdmin):
    list_display = ['user', 'status', 'created_at', 'completed_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__email']
    readonly_fields = ['id', 'created_at']
```

**集成到FastAPI**:
```python
# main.py
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
import django
from django.conf import settings
from django.core.wsgi import get_wsgi_application
from django.urls import path, include

# 配置Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'admin.settings')
django.setup()

app = FastAPI(title="ShowForAI Backend", version="1.0.0")

# 挂载Django Admin
from django.http import HttpRequest
from starlette.applications import Starlette
from starlette.middleware.wsgi import WSGIMiddleware

django_app = get_wsgi_application()
app.mount("/admin", WSGIMiddleware(django_app))

# 原有的API路由
@app.post("/api/upload/session")
async def upload_session():
    # ... 现有代码
```

### 替代方案：FastAPI Admin

如果不想引入Django，可以使用FastAPI Admin：

```python
# 使用fastapi-admin
from fastapi_admin.app import app as admin_app
from fastapi_admin.resources import Model, Field

class UserResource(Model):
    label = "用户管理"
    model = User
    fields = [
        Field(name="email", label="邮箱"),
        Field(name="subscription_type", label="订阅类型"),
        Field(name="quota_used", label="已使用配额"),
        Field(name="quota_limit", label="配额限制"),
    ]

# 注册到主应用
app.mount('/admin', admin_app)
```

### 管理界面功能

#### 1. 用户管理
- 查看所有用户列表
- 用户详细信息查看
- 配额管理和调整
- 订阅状态管理

#### 2. 任务监控
- AI处理任务列表
- 任务状态实时监控
- 失败任务重试
- 处理时间统计

#### 3. 系统监控
- 服务器资源使用情况
- API调用统计
- 错误日志查看
- 性能指标监控

#### 4. 成本分析
- AI API调用成本统计
- 存储使用量分析
- 用户活跃度报告
- 收入统计（付费用户）

### 部署配置

**更新的requirements.txt**:
```
fastapi==0.104.0
uvicorn==0.24.0
django==4.2.0
psycopg2-binary==2.9.0
redis==5.0.0
boto3==1.29.0
volcenginesdkarkruntime==1.0.0
```

**更新的Dockerfile**:
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# Django数据库迁移
RUN python manage.py migrate

# 创建超级用户（可选）
# RUN echo "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('admin', '<EMAIL>', 'admin123')" | python manage.py shell

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

这样您就有了一个完整的后端管理界面，可以方便地监控用户、任务和系统状态，非常适合个人开发者的小规模项目管理需求。