# Android DSL Executor 快速测试指南

## 📋 概述

本指南提供了快速测试Android DSL Executor应用的步骤和方法。测试分为手动测试和自动化测试两部分，覆盖了功能、性能和兼容性测试。

## 🚀 准备工作

### 1. 环境准备
- 安装Android Studio
- 安装ADB工具
- 准备测试设备（真机或模拟器）
- 确保设备已启用USB调试

### 2. 获取测试版本
- 从项目根目录构建Debug版本：`./gradlew assembleDebug`
- 或使用预构建的APK：`app/build/outputs/apk/debug/app-debug.apk`

### 3. 安装应用
```bash
adb install app/build/outputs/apk/debug/app-debug.apk
```

## 🧪 自动化测试

### 1. 运行完整测试套件
```bash
cd android_executor
./run_tests.sh
```

### 2. 运行特定测试
```bash
# 只运行单元测试
./run_tests.sh --skip-integration --skip-ui --skip-performance

# 只运行UI测试
./run_tests.sh --skip-unit --skip-integration --skip-performance

# 只运行性能测试
./run_tests.sh --skip-unit --skip-integration --skip-ui
```

### 3. 查看测试报告
测试完成后，报告将生成在`test_reports/`目录下：
- 单元测试报告：`test_reports/unit_tests/`
- 集成测试报告：`test_reports/android_tests/`
- 测试摘要：`test_reports/test_summary.txt`

## 👨‍💻 手动测试

### 1. 基础功能测试

#### 1.1 安装和权限测试
1. 卸载应用（如已安装）
2. 重新安装应用
3. 启动应用，验证权限引导流程
4. 按照引导授予所有必要权限
5. 验证权限状态显示正确

#### 1.2 脚本管理测试
1. 点击"选择脚本"按钮
2. 选择示例脚本（`app/src/main/assets/sample_script.json`）
3. 验证脚本信息正确显示
4. 测试清除脚本功能

#### 1.3 执行测试
1. 加载示例脚本
2. 点击"开始执行"按钮
3. 观察执行过程和日志
4. 测试暂停/恢复/停止功能
5. 验证执行结果

#### 1.4 设置测试
1. 点击设置图标
2. 调整各项设置参数
3. 返回主界面，验证设置已保存
4. 重启应用，验证设置持久化

### 2. 高级功能测试

#### 2.1 定位策略测试
1. 创建包含不同定位方式的测试脚本：
   - 视觉匹配（使用图像）
   - OCR文本识别
   - 坐标定位
2. 执行脚本，观察不同策略的效果
3. 验证级联策略是否正常工作

#### 2.2 错误处理测试
1. 创建包含错误情况的测试脚本：
   - 不存在的目标
   - 无效的操作
   - 超时情况
2. 执行脚本，观察错误处理和恢复
3. 验证错误日志是否正确记录

#### 2.3 性能测试
1. 创建包含大量步骤的测试脚本
2. 执行脚本，监控性能指标：
   - 内存使用
   - CPU使用
   - 电池消耗
3. 长时间运行测试（>30分钟）

### 3. 兼容性测试

#### 3.1 设备兼容性
在不同设备上测试应用：
- 低端设备（2GB RAM）
- 中端设备（4GB RAM）
- 高端设备（8GB RAM）
- 平板设备（如有）

#### 3.2 Android版本兼容性
在不同Android版本上测试：
- Android 8.0
- Android 10.0
- Android 13.0

#### 3.3 屏幕适配测试
1. 在不同屏幕尺寸的设备上测试
2. 测试横竖屏切换
3. 测试深色模式支持

## 📝 测试记录

### 测试记录模板
```
测试日期：[日期]
测试设备：[设备型号, Android版本]
测试内容：[测试内容描述]

测试步骤：
1. [步骤1]
2. [步骤2]
3. ...

预期结果：[预期结果]
实际结果：[实际结果]
问题记录：[发现的问题]
```

### 问题报告模板
```
问题ID：BUG-[编号]
严重程度：[致命/严重/一般/轻微]
设备环境：[设备型号, Android版本]
复现步骤：
1. [步骤1]
2. [步骤2]
3. ...

预期结果：[预期结果]
实际结果：[实际结果]
附件：[截图或日志]
```

## 🔍 测试重点

### 功能测试重点
- 权限管理是否正确
- 脚本加载和解析是否准确
- 定位策略是否有效
- 动作执行是否准确
- 错误处理是否合理

### 性能测试重点
- 内存使用是否合理（<200MB）
- CPU使用是否适中（<50%）
- 电池消耗是否可接受（<10%/小时）
- 启动时间是否迅速（<3秒）
- 长时间运行是否稳定（>2小时无崩溃）

### 兼容性测试重点
- 在最低支持版本（Android 8.0）上是否可用
- 在不同屏幕尺寸上是否适配良好
- 在不同性能设备上是否表现合理

## 📊 测试完成标准

测试完成需满足以下条件：
1. 所有核心功能测试通过
2. 无严重或致命缺陷
3. 性能指标达到目标
4. 在所有目标设备上兼容
5. 测试报告完成并记录所有发现的问题

## 🛠️ 测试工具

### 内置工具
- Android Studio Profiler - 性能监控
- Logcat - 日志查看
- Layout Inspector - UI检查

### 第三方工具
- Firebase Test Lab - 兼容性测试
- Espresso - UI自动化测试
- JUnit - 单元测试
- Monkey - 压力测试

## 📱 测试设备推荐

### 最低配置
- Android 8.0
- 2GB RAM
- 720p屏幕

### 推荐配置
- Android 10.0+
- 4GB+ RAM
- 1080p屏幕

---

**文档版本**: 1.0  
**更新日期**: 2024年1月15日
