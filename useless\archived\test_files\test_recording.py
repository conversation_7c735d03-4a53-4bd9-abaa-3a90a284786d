"""
测试录制功能是否可用
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.sync.offline_manager import get_offline_manager
from showforai.recorder.gui import RecorderWindow
from showforai.config import Config
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def main():
    print("=" * 50)
    print("ShowForAI V3 录制功能测试")
    print("=" * 50)
    
    # 检查网络状态
    manager = get_offline_manager()
    is_online = manager.is_online()
    can_record = manager.is_recording_allowed()
    
    print(f"\n网络状态: {'✅ 在线' if is_online else '❌ 离线'}")
    print(f"录制功能: {'✅ 可用' if can_record else '❌ 不可用'}")
    
    if not can_record:
        print("\n❌ 录制功能不可用的原因：")
        print(manager.get_recording_disabled_message())
        return
    
    print("\n✅ 录制功能可用！")
    print("\n启动录制器界面...")
    
    # 创建应用
    app = QApplication([])
    
    # 创建窗口
    config = Config()
    window = RecorderWindow(config)
    
    # 检查UI状态
    print(f"\n界面状态检查：")
    print(f"- 离线警告是否隐藏: {window.offline_warning.isHidden()}")
    print(f"- 录制按钮是否启用: {window.record_button.isEnabled()}")
    print(f"- 录制按钮文字: {window.record_button.text()}")
    
    # 显示窗口
    window.show()
    
    # 添加一个定时器来显示状态
    def show_status():
        print(f"\n当前状态：")
        print(f"- 网络: {'在线' if window.offline_manager.is_online() else '离线'}")
        print(f"- 录制按钮启用: {window.record_button.isEnabled()}")
        print(f"- 离线警告隐藏: {window.offline_warning.isHidden()}")
    
    timer = QTimer()
    timer.timeout.connect(show_status)
    timer.start(10000)  # 每10秒显示一次状态
    
    print("\n录制器窗口已启动！")
    print("- 点击 'Start' 按钮开始录制")
    print("- 按 F9 键停止录制")
    print("- 点击 'Upload & Create Script' 生成脚本")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()