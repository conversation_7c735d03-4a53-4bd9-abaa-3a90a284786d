"""
Test suite for Integration Tests
Tests module interactions, complete workflows, performance benchmarks, and end-to-end scenarios
"""

import unittest
import time
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path
from PIL import Image
import numpy as np
import threading
import asyncio

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Import all major components for integration testing
from showforai.threshold_manager import ThresholdManager
from showforai.standardizer import Standardizer
from showforai.buffer_manager import BufferManager
from showforai.smart_wait import SmartWaitManager, WaitStrategy
from showforai.execution_engine import ExecutionEngine
from showforai.bug_fixes import CrashPrevention, MemoryManager


class TestModuleIntegration(unittest.TestCase):
    """Test integration between different modules"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.threshold_manager = ThresholdManager()
        self.standardizer = Standardizer768()
        self.buffer_manager = BufferManager()
        self.wait_manager = SmartWaitManager()
        self.execution_engine = ExecutionEngine()
        
    def test_standardizer_threshold_integration(self):
        """Test standardizer working with threshold manager"""
        # Create test image
        img = Image.new('RGB', (1920, 1080))
        
        # Standardize image
        standardized = self.standardizer.standardize_image(img)
        
        # Use threshold for validation
        confidence = 0.87
        valid, reason = self.threshold_manager.validate_confidence(
            confidence, 'primary'
        )
        
        # Test flow
        if valid:
            # Process standardized image
            result = {'image': standardized, 'confidence': confidence}
        else:
            # Use fallback
            result = {'image': standardized, 'fallback': True}
            
        self.assertIsNotNone(result)
        
    def test_buffer_execution_integration(self):
        """Test buffer manager with execution engine"""
        # Add frames to buffer
        for i in range(5):
            frame = Image.new('RGB', (100, 100), color=(i*50, i*50, i*50))
            frame_id = self.buffer_manager.add_frame(frame)
            
            # Add corresponding action
            action = {
                'type': 'click',
                'frame_id': frame_id,
                'position': (50, 50)
            }
            self.buffer_manager.add_action(action)
            
        # Load actions into execution engine
        script = {
            'name': 'buffer_test',
            'actions': self.buffer_manager.get_all_actions()
        }
        
        self.execution_engine.load_script(script)
        
        # Execute with frame validation
        for action in self.execution_engine.actions:
            if 'frame_id' in action:
                frame = self.buffer_manager.get_frame(action['frame_id'])
                self.assertIsNotNone(frame)
                
    def test_smart_wait_execution_integration(self):
        """Test smart wait with execution engine"""
        # Create script with wait points
        script = {
            'name': 'wait_test',
            'actions': [
                {'type': 'click', 'element': 'button1'},
                {'type': 'smart_wait', 'strategy': 'auxiliary'},
                {'type': 'type', 'text': 'test'},
                {'type': 'smart_wait', 'strategy': 'active'}
            ]
        }
        
        self.execution_engine.load_script(script)
        
        # Mock detectors
        mock_detector = Mock()
        mock_detector.detect_completion.side_effect = [True, True]
        
        # Execute with smart wait
        for action in self.execution_engine.actions:
            if action['type'] == 'smart_wait':
                result = self.wait_manager.wait_for_completion(
                    auxiliary_detector=mock_detector
                )
                self.assertTrue(result.success)
                
    def test_crash_prevention_integration(self):
        """Test crash prevention across modules"""
        crash_prevention = CrashPrevention()
        
        @crash_prevention.safe_execute
        def risky_operation():
            # Simulate risky standardization
            img = None  # Intentionally None
            return self.standardizer.standardize_image(img)
            
        # Should not crash
        result = risky_operation()
        self.assertIsNone(result)
        
        # Check crash was logged
        crashes = crash_prevention.get_crash_history()
        self.assertGreater(len(crashes), 0)


class TestCompleteWorkflow(unittest.TestCase):
    """Test complete application workflows"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.setup_components()
        
    def setup_components(self):
        """Initialize all components"""
        self.recorder = Mock()  # Mock recorder
        self.executor = Mock()  # Mock executor
        self.ai_service = Mock()  # Mock AI service
        
        # Real components
        self.threshold_manager = ThresholdManager()
        self.standardizer = Standardizer768()
        self.buffer_manager = BufferManager()
        self.execution_engine = ExecutionEngine()
        
    def test_recording_workflow(self):
        """Test complete recording workflow"""
        # 1. Start recording
        recording_data = {
            'start_time': time.time(),
            'actions': []
        }
        
        # 2. Capture actions
        for i in range(3):
            # Capture screenshot
            screenshot = Image.new('RGB', (1920, 1080))
            frame_id = self.buffer_manager.add_frame(screenshot)
            
            # Record action
            action = {
                'type': 'click',
                'position': (100 * i, 100 * i),
                'timestamp': time.time(),
                'frame_id': frame_id
            }
            recording_data['actions'].append(action)
            
        # 3. Process recording
        recording_data['end_time'] = time.time()
        recording_data['duration'] = recording_data['end_time'] - recording_data['start_time']
        
        # 4. Standardize screenshots
        for action in recording_data['actions']:
            if 'frame_id' in action:
                frame = self.buffer_manager.get_frame(action['frame_id'])
                standardized = self.standardizer.standardize_image(frame)
                action['standardized_frame'] = standardized
                
        # 5. Save recording
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(recording_data, f, default=str)
            recording_file = f.name
            
        # Verify workflow
        self.assertTrue(os.path.exists(recording_file))
        self.assertEqual(len(recording_data['actions']), 3)
        
        # Cleanup
        os.unlink(recording_file)
        
    def test_execution_workflow(self):
        """Test complete execution workflow"""
        # 1. Load script
        script = {
            'name': 'test_script',
            'actions': [
                {
                    'type': 'click',
                    'element': {'id': 'button1', 'confidence_threshold': 0.85}
                },
                {
                    'type': 'wait',
                    'duration': 0.1
                },
                {
                    'type': 'type',
                    'text': 'test input'
                }
            ]
        }
        
        # 2. Initialize execution
        self.execution_engine.load_script(script)
        
        # 3. Execute each action
        results = []
        for i, action in enumerate(self.execution_engine.actions):
            # Pre-execution checks
            if 'element' in action:
                # Validate confidence threshold
                threshold = action['element'].get('confidence_threshold', 0.85)
                self.assertGreaterEqual(threshold, 0.0)
                self.assertLessEqual(threshold, 1.0)
                
            # Execute action
            result = {
                'action_index': i,
                'action_type': action['type'],
                'success': True,
                'timestamp': time.time()
            }
            results.append(result)
            
            # Post-execution wait
            if action['type'] == 'wait':
                time.sleep(action['duration'])
                
        # 4. Verify execution
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertTrue(result['success'])
            
    def test_ai_enhancement_workflow(self):
        """Test AI enhancement workflow"""
        # 1. Capture screenshot
        screenshot = Image.new('RGB', (1920, 1080))
        
        # 2. Standardize for AI
        standardized = self.standardizer.standardize_image(screenshot)
        
        # 3. Mock AI detection
        self.ai_service.detect_elements.return_value = [
            {
                'type': 'button',
                'bbox': {'x': 100, 'y': 100, 'width': 200, 'height': 50},
                'confidence': 0.92,
                'text': 'Submit'
            },
            {
                'type': 'input',
                'bbox': {'x': 100, 'y': 200, 'width': 300, 'height': 40},
                'confidence': 0.88,
                'placeholder': 'Enter text'
            }
        ]
        
        elements = self.ai_service.detect_elements(standardized)
        
        # 4. Validate detections with threshold
        valid_elements = []
        for element in elements:
            confidence = element['confidence']
            level = self.threshold_manager.get_matching_level(confidence)
            
            if level:
                element['matching_level'] = level
                valid_elements.append(element)
                
        # 5. Convert coordinates back
        for element in valid_elements:
            bbox = element['bbox']
            original_bbox = self.standardizer.scale_bbox(
                bbox,
                from_resolution=(768, 768),
                to_resolution=(1920, 1080)
            )
            element['original_bbox'] = original_bbox
            
        # Verify workflow
        self.assertEqual(len(valid_elements), 2)
        self.assertIn('matching_level', valid_elements[0])
        self.assertIn('original_bbox', valid_elements[0])


class TestPerformanceBenchmarks(unittest.TestCase):
    """Test performance benchmarks for the system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.components = {
            'threshold': ThresholdManager(),
            'standardizer': Standardizer768(),
            'buffer': BufferManager(),
            'wait': SmartWaitManager(),
            'execution': ExecutionEngine()
        }
        
    def test_throughput_benchmark(self):
        """Test system throughput"""
        start_time = time.time()
        operations = 0
        
        # Run for 1 second
        while time.time() - start_time < 1.0:
            # Simulate typical operations
            img = Image.new('RGB', (1920, 1080))
            standardized = self.components['standardizer'].standardize_image(img)
            frame_id = self.components['buffer'].add_frame(standardized)
            
            confidence = np.random.random()
            self.components['threshold'].validate_confidence(confidence, 'primary')
            
            operations += 1
            
        # Should handle reasonable throughput
        self.assertGreater(operations, 10)  # At least 10 ops/second
        
    def test_latency_benchmark(self):
        """Test operation latency"""
        latencies = []
        
        for _ in range(100):
            start = time.perf_counter()
            
            # Typical operation sequence
            img = Image.new('RGB', (100, 100))
            self.components['buffer'].add_frame(img)
            
            end = time.perf_counter()
            latencies.append(end - start)
            
        # Calculate statistics
        avg_latency = np.mean(latencies)
        p95_latency = np.percentile(latencies, 95)
        p99_latency = np.percentile(latencies, 99)
        
        # Check latency requirements
        self.assertLess(avg_latency, 0.01)  # Avg < 10ms
        self.assertLess(p95_latency, 0.02)  # P95 < 20ms
        self.assertLess(p99_latency, 0.05)  # P99 < 50ms
        
    def test_memory_benchmark(self):
        """Test memory usage under load"""
        import tracemalloc
        import gc
        
        tracemalloc.start()
        snapshot1 = tracemalloc.take_snapshot()
        
        # Simulate heavy load
        frames = []
        for i in range(100):
            img = Image.new('RGB', (1920, 1080))
            standardized = self.components['standardizer'].standardize_image(img)
            frames.append(standardized)
            
        snapshot2 = tracemalloc.take_snapshot()
        
        # Check memory usage
        stats = snapshot2.compare_to(snapshot1, 'lineno')
        total_memory = sum(stat.size_diff for stat in stats)
        
        # Memory per frame
        memory_per_frame = total_memory / 100 / (1024 * 1024)  # MB
        
        # Should be reasonable
        self.assertLess(memory_per_frame, 10)  # Less than 10MB per frame
        
        # Cleanup
        frames.clear()
        gc.collect()
        tracemalloc.stop()
        
    def test_concurrent_operations(self):
        """Test concurrent operation handling"""
        results = []
        errors = []
        
        def worker(worker_id):
            try:
                for i in range(10):
                    img = Image.new('RGB', (100, 100))
                    frame_id = self.components['buffer'].add_frame(img)
                    results.append((worker_id, frame_id))
            except Exception as e:
                errors.append(e)
                
        # Create multiple threads
        threads = []
        for i in range(5):
            t = threading.Thread(target=worker, args=(i,))
            threads.append(t)
            t.start()
            
        # Wait for completion
        for t in threads:
            t.join()
            
        # Check results
        self.assertEqual(len(errors), 0)
        self.assertEqual(len(results), 50)  # 5 threads * 10 operations


class TestEndToEnd(unittest.TestCase):
    """Test end-to-end scenarios"""
    
    def test_record_and_replay(self):
        """Test recording and replaying a sequence"""
        # Record phase
        recording = {
            'name': 'e2e_test',
            'actions': []
        }
        
        # Simulate recording 3 actions
        for i in range(3):
            action = {
                'type': 'click',
                'position': (100 + i*50, 200 + i*30),
                'timestamp': time.time() + i
            }
            recording['actions'].append(action)
            time.sleep(0.1)
            
        # Save recording
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(recording, f)
            recording_file = f.name
            
        # Replay phase
        with open(recording_file, 'r') as f:
            loaded_recording = json.load(f)
            
        # Execute replay
        execution_engine = ExecutionEngine()
        execution_engine.load_script(loaded_recording)
        
        # Verify can execute
        self.assertEqual(len(execution_engine.actions), 3)
        
        # Cleanup
        os.unlink(recording_file)
        
    def test_error_recovery_e2e(self):
        """Test end-to-end error recovery"""
        crash_prevention = CrashPrevention()
        memory_manager = MemoryManager()
        
        # Simulate workflow with potential errors
        @crash_prevention.safe_execute(fallback=[])
        def risky_workflow():
            results = []
            
            # Step 1: May fail
            try:
                img = Image.new('RGB', (1920, 1080))
                results.append('image_created')
            except:
                return results
                
            # Step 2: May run out of memory
            if memory_manager.check_memory_limit():
                large_data = [0] * 1000000
                results.append('data_allocated')
            else:
                memory_manager.force_garbage_collection()
                results.append('memory_cleaned')
                
            # Step 3: Continue despite issues
            results.append('workflow_completed')
            return results
            
        results = risky_workflow()
        
        # Should complete with some results
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        
    def test_performance_under_stress(self):
        """Test system performance under stress"""
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        # Run for 2 seconds under stress
        while time.time() - start_time < 2.0:
            try:
                # Heavy operations
                img = Image.new('RGB', (3840, 2160))  # 4K image
                standardizer = Standardizer768()
                standardized = standardizer.standardize_image(img)
                
                # Multiple threshold checks
                threshold_manager = ThresholdManager()
                for _ in range(10):
                    confidence = np.random.random()
                    threshold_manager.validate_confidence(confidence, 'primary')
                    
                success_count += 1
                
            except Exception:
                error_count += 1
                
        # System should remain stable
        self.assertGreater(success_count, 0)
        self.assertLess(error_count / (success_count + error_count), 0.1)  # <10% errors


class TestAsyncIntegration(unittest.TestCase):
    """Test asynchronous operation integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
    def tearDown(self):
        """Clean up event loop"""
        self.loop.close()
        
    async def async_workflow(self):
        """Async workflow test"""
        results = []
        
        # Async operations
        async def process_frame(frame_id):
            await asyncio.sleep(0.01)  # Simulate async processing
            return f"processed_{frame_id}"
            
        # Process multiple frames concurrently
        tasks = []
        for i in range(5):
            task = process_frame(i)
            tasks.append(task)
            
        results = await asyncio.gather(*tasks)
        return results
        
    def test_async_execution(self):
        """Test async execution integration"""
        results = self.loop.run_until_complete(self.async_workflow())
        
        self.assertEqual(len(results), 5)
        for i, result in enumerate(results):
            self.assertEqual(result, f"processed_{i}")


if __name__ == '__main__':
    # Run tests with detailed output
    unittest.main(verbosity=2)