"""
Test suite for Feedback System
Tests status bar updates, progress bar functionality, toast notifications, and error message conversion
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow, QProgressBar, QStatusBar
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QColor

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.ui.feedback_system import (
    FeedbackSystem,
    StatusBarManager,
    ProgressManager,
    ToastNotificationManager,
    ErrorMessageConverter
)


class TestStatusBarManager(unittest.TestCase):
    """Test Status Bar Manager functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.main_window = QMainWindow()
        self.status_bar = self.main_window.statusBar()
        self.status_manager = StatusBarManager(self.status_bar)
        
    def test_status_message_display(self):
        """Test displaying status messages"""
        # Display message
        self.status_manager.show_message("Test message", duration=2000)
        
        current_message = self.status_bar.currentMessage()
        self.assertEqual(current_message, "Test message")
        
    def test_permanent_widget(self):
        """Test adding permanent widgets to status bar"""
        # Add progress indicator
        progress_widget = self.status_manager.add_permanent_widget(
            'progress',
            widget_type='progress_bar'
        )
        
        self.assertIsNotNone(progress_widget)
        self.assertIsInstance(progress_widget, QProgressBar)
        
    def test_status_levels(self):
        """Test different status message levels"""
        # Info message
        self.status_manager.show_info("Info message")
        self.assertIn("Info", self.status_bar.currentMessage())
        
        # Warning message
        self.status_manager.show_warning("Warning message")
        self.assertIn("Warning", self.status_bar.currentMessage())
        
        # Error message
        self.status_manager.show_error("Error message")
        self.assertIn("Error", self.status_bar.currentMessage())
        
    def test_status_queue(self):
        """Test message queueing"""
        # Queue multiple messages
        self.status_manager.queue_message("Message 1", priority=1)
        self.status_manager.queue_message("Message 2", priority=3)
        self.status_manager.queue_message("Message 3", priority=2)
        
        # Process queue
        self.status_manager.process_queue()
        
        # Highest priority should show first
        self.assertIn("Message 2", self.status_bar.currentMessage())
        
    def test_status_clear(self):
        """Test clearing status messages"""
        self.status_manager.show_message("Test message")
        self.status_manager.clear()
        
        self.assertEqual(self.status_bar.currentMessage(), "")
        
    def test_status_animation(self):
        """Test animated status updates"""
        # Start loading animation
        self.status_manager.show_loading("Loading data...")
        
        # Should have animation indicator
        self.assertTrue(self.status_manager.is_loading)
        
        # Stop loading
        self.status_manager.hide_loading()
        self.assertFalse(self.status_manager.is_loading)


class TestProgressManager(unittest.TestCase):
    """Test Progress Manager functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.progress_manager = ProgressManager()
        
    def test_progress_creation(self):
        """Test creating progress indicators"""
        # Create determinate progress
        progress = self.progress_manager.create_progress(
            min_value=0,
            max_value=100,
            initial_value=0
        )
        
        self.assertEqual(progress.minimum(), 0)
        self.assertEqual(progress.maximum(), 100)
        self.assertEqual(progress.value(), 0)
        
    def test_progress_update(self):
        """Test updating progress value"""
        progress = self.progress_manager.create_progress()
        
        # Update value
        self.progress_manager.update_progress(progress, 50)
        self.assertEqual(progress.value(), 50)
        
        # Update with increment
        self.progress_manager.increment_progress(progress, 10)
        self.assertEqual(progress.value(), 60)
        
    def test_indeterminate_progress(self):
        """Test indeterminate progress indicator"""
        progress = self.progress_manager.create_indeterminate_progress()
        
        # Should have no maximum (or very large)
        self.assertEqual(progress.maximum(), 0)
        
    def test_progress_tracking(self):
        """Test progress tracking across operations"""
        tracker = self.progress_manager.create_tracker(total_steps=10)
        
        # Track progress
        for i in range(5):
            tracker.step_completed()
            
        self.assertEqual(tracker.completed_steps, 5)
        self.assertEqual(tracker.percentage, 50)
        
    def test_multi_progress(self):
        """Test managing multiple progress indicators"""
        # Create multiple progress bars
        progress1 = self.progress_manager.create_progress(tag='download')
        progress2 = self.progress_manager.create_progress(tag='processing')
        
        # Update individually
        self.progress_manager.update_by_tag('download', 30)
        self.progress_manager.update_by_tag('processing', 60)
        
        self.assertEqual(progress1.value(), 30)
        self.assertEqual(progress2.value(), 60)
        
    def test_progress_estimation(self):
        """Test time remaining estimation"""
        estimator = self.progress_manager.create_estimator(total_items=100)
        
        # Process some items
        start_time = time.time()
        time.sleep(0.1)
        estimator.items_completed(10)
        
        # Get estimate
        remaining = estimator.time_remaining()
        self.assertGreater(remaining, 0)
        
    def test_progress_callback(self):
        """Test progress completion callbacks"""
        completed = []
        
        def on_complete():
            completed.append(True)
            
        progress = self.progress_manager.create_progress(
            on_complete=on_complete
        )
        
        # Complete progress
        self.progress_manager.update_progress(progress, 100)
        
        self.assertEqual(len(completed), 1)


class TestToastNotificationManager(unittest.TestCase):
    """Test Toast Notification Manager functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.toast_manager = ToastNotificationManager()
        
    def test_toast_creation(self):
        """Test creating toast notifications"""
        toast = self.toast_manager.show_toast(
            message="Test notification",
            duration=2000,
            position='bottom-right'
        )
        
        self.assertIsNotNone(toast)
        self.assertEqual(toast.message, "Test notification")
        self.assertTrue(toast.isVisible())
        
    def test_toast_positions(self):
        """Test different toast positions"""
        positions = [
            'top-left', 'top-center', 'top-right',
            'bottom-left', 'bottom-center', 'bottom-right'
        ]
        
        for position in positions:
            toast = self.toast_manager.show_toast(
                message=f"Toast at {position}",
                position=position
            )
            
            # Check position is set correctly
            self.assertEqual(toast.position, position)
            
    def test_toast_types(self):
        """Test different toast notification types"""
        # Success toast
        success = self.toast_manager.show_success("Operation successful")
        self.assertEqual(success.toast_type, 'success')
        
        # Error toast
        error = self.toast_manager.show_error("Operation failed")
        self.assertEqual(error.toast_type, 'error')
        
        # Warning toast
        warning = self.toast_manager.show_warning("Warning message")
        self.assertEqual(warning.toast_type, 'warning')
        
        # Info toast
        info = self.toast_manager.show_info("Information message")
        self.assertEqual(info.toast_type, 'info')
        
    def test_toast_auto_dismiss(self):
        """Test auto-dismiss functionality"""
        toast = self.toast_manager.show_toast(
            message="Auto dismiss test",
            duration=100  # 100ms for quick test
        )
        
        # Wait for auto-dismiss
        QTimer.singleShot(150, lambda: None)
        QApplication.processEvents()
        
        # Toast should be hidden
        self.assertFalse(toast.isVisible())
        
    def test_toast_stacking(self):
        """Test multiple toast stacking"""
        # Show multiple toasts
        toasts = []
        for i in range(3):
            toast = self.toast_manager.show_toast(
                message=f"Toast {i}",
                position='bottom-right'
            )
            toasts.append(toast)
            
        # Check stacking offsets
        for i in range(1, len(toasts)):
            self.assertNotEqual(
                toasts[i].y(),
                toasts[i-1].y()
            )
            
    def test_toast_actions(self):
        """Test toast with action buttons"""
        action_clicked = []
        
        def on_action():
            action_clicked.append(True)
            
        toast = self.toast_manager.show_toast_with_action(
            message="Toast with action",
            action_text="Undo",
            action_callback=on_action
        )
        
        # Click action
        toast.action_button.click()
        
        self.assertEqual(len(action_clicked), 1)
        
    def test_toast_queue(self):
        """Test toast notification queue"""
        # Queue multiple toasts
        for i in range(5):
            self.toast_manager.queue_toast(
                message=f"Queued toast {i}",
                priority=i
            )
            
        # Process queue
        self.toast_manager.process_toast_queue(max_visible=3)
        
        # Should show only max_visible toasts
        visible_count = len(self.toast_manager.get_visible_toasts())
        self.assertLessEqual(visible_count, 3)


class TestErrorMessageConverter(unittest.TestCase):
    """Test Error Message Converter functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.converter = ErrorMessageConverter()
        
    def test_technical_to_user_friendly(self):
        """Test converting technical errors to user-friendly messages"""
        # Database error
        technical = "psycopg2.OperationalError: could not connect to server"
        friendly = self.converter.convert(technical)
        self.assertIn("connection", friendly.lower())
        self.assertNotIn("psycopg2", friendly)
        
        # File not found
        technical = "FileNotFoundError: [Errno 2] No such file or directory: 'test.txt'"
        friendly = self.converter.convert(technical)
        self.assertIn("file", friendly.lower())
        self.assertNotIn("Errno", friendly)
        
    def test_error_categorization(self):
        """Test categorizing errors"""
        errors = [
            ("ConnectionRefusedError", "network"),
            ("PermissionError", "permission"),
            ("MemoryError", "resource"),
            ("KeyError", "data")
        ]
        
        for error, expected_category in errors:
            category = self.converter.categorize(error)
            self.assertEqual(category, expected_category)
            
    def test_error_suggestions(self):
        """Test providing helpful suggestions"""
        # Connection error
        error = "Connection timeout"
        suggestions = self.converter.get_suggestions(error)
        
        self.assertGreater(len(suggestions), 0)
        self.assertTrue(any("network" in s.lower() for s in suggestions))
        
    def test_error_localization(self):
        """Test error message localization"""
        # Set language
        self.converter.set_language('es')
        
        # Convert error
        technical = "File not found"
        localized = self.converter.convert(technical)
        
        # Should be in Spanish (mock implementation)
        self.assertIsNotNone(localized)
        
    def test_error_logging(self):
        """Test error logging for analytics"""
        # Log errors
        self.converter.log_error("Error 1", "category1")
        self.converter.log_error("Error 2", "category1")
        self.converter.log_error("Error 3", "category2")
        
        # Get statistics
        stats = self.converter.get_error_statistics()
        
        self.assertEqual(stats['total_errors'], 3)
        self.assertEqual(stats['by_category']['category1'], 2)
        
    def test_custom_error_mappings(self):
        """Test custom error message mappings"""
        # Add custom mapping
        self.converter.add_mapping(
            pattern="CUSTOM_ERROR_001",
            message="A specific custom error occurred",
            category="custom"
        )
        
        # Convert custom error
        technical = "CUSTOM_ERROR_001: Internal failure"
        friendly = self.converter.convert(technical)
        
        self.assertEqual(friendly, "A specific custom error occurred")


class TestFeedbackSystemIntegration(unittest.TestCase):
    """Test integrated feedback system"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for all tests"""
        if not QApplication.instance():
            cls.app = QApplication([])
            
    def setUp(self):
        """Set up test fixtures"""
        self.main_window = QMainWindow()
        self.feedback_system = FeedbackSystem(self.main_window)
        
    def test_coordinated_feedback(self):
        """Test coordinated feedback across components"""
        # Start operation with feedback
        self.feedback_system.start_operation(
            "Processing data",
            show_progress=True,
            show_status=True
        )
        
        # Update progress
        for i in range(10):
            self.feedback_system.update_progress(i * 10)
            
        # Complete operation
        self.feedback_system.complete_operation(
            "Processing complete",
            show_toast=True
        )
        
        # Check components updated
        self.assertFalse(self.feedback_system.is_busy)
        
    def test_error_handling_flow(self):
        """Test error handling feedback flow"""
        # Simulate error
        error = Exception("Database connection failed")
        
        self.feedback_system.handle_error(
            error,
            show_toast=True,
            show_status=True,
            user_friendly=True
        )
        
        # Check error was displayed
        self.assertTrue(self.feedback_system.has_error)
        
    def test_feedback_priorities(self):
        """Test feedback message priorities"""
        # Queue multiple feedback items
        self.feedback_system.queue_feedback("Low priority", priority=1)
        self.feedback_system.queue_feedback("High priority", priority=3)
        self.feedback_system.queue_feedback("Medium priority", priority=2)
        
        # Process queue
        displayed = self.feedback_system.process_feedback_queue()
        
        # Should process in priority order
        self.assertEqual(displayed[0], "High priority")
        
    def test_feedback_persistence(self):
        """Test feedback history and persistence"""
        # Generate feedback
        self.feedback_system.show_info("Info 1")
        self.feedback_system.show_warning("Warning 1")
        self.feedback_system.show_error("Error 1")
        
        # Get history
        history = self.feedback_system.get_feedback_history()
        
        self.assertEqual(len(history), 3)
        
        # Save history
        self.feedback_system.save_history("feedback_log.json")
        
        # Clear and reload
        self.feedback_system.clear_history()
        self.feedback_system.load_history("feedback_log.json")
        
        new_history = self.feedback_system.get_feedback_history()
        self.assertEqual(len(new_history), 3)
        
    def test_feedback_analytics(self):
        """Test feedback analytics"""
        # Generate various feedback
        for _ in range(5):
            self.feedback_system.show_info("Info")
        for _ in range(3):
            self.feedback_system.show_warning("Warning")
        for _ in range(2):
            self.feedback_system.show_error("Error")
            
        # Get analytics
        analytics = self.feedback_system.get_analytics()
        
        self.assertEqual(analytics['total_feedback'], 10)
        self.assertEqual(analytics['by_type']['info'], 5)
        self.assertEqual(analytics['by_type']['warning'], 3)
        self.assertEqual(analytics['by_type']['error'], 2)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)