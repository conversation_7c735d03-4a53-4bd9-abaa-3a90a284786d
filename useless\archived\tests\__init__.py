"""
ShowForAI-V3 Test Suite
Comprehensive testing for all modules and components
"""

# Import all test modules for easy access
from .test_threshold_manager import *
from .test_standardizer import *
from .test_buffer_manager import *
from .test_smart_wait import *
from .test_execution_engine import *
from .test_modern_ui import *
from .test_feedback_system import *
from .test_bug_fixes import *
from .test_integration import *
from .test_api_security import *

# Test suite metadata
__version__ = '1.0.0'
__author__ = 'ShowForAI Development Team'

# Define test categories
TEST_CATEGORIES = {
    'unit': [
        'test_threshold_manager',
        'test_standardizer',
        'test_buffer_manager',
        'test_smart_wait',
        'test_execution_engine',
        'test_bug_fixes',
    ],
    'ui': [
        'test_modern_ui',
        'test_feedback_system',
    ],
    'security': [
        'test_api_security',
    ],
    'integration': [
        'test_integration',
    ],
    'performance': [
        'test_integration.TestPerformanceBenchmarks',
    ],
    'e2e': [
        'test_integration.TestEndToEnd',
    ]
}

def get_test_suite(category=None):
    """
    Get test suite by category
    
    Args:
        category: Test category ('unit', 'ui', 'security', 'integration', 'performance', 'e2e')
                 If None, returns all tests
    
    Returns:
        List of test module names
    """
    if category is None:
        # Return all tests
        all_tests = []
        for tests in TEST_CATEGORIES.values():
            all_tests.extend(tests)
        return list(set(all_tests))
    
    return TEST_CATEGORIES.get(category, [])

def get_test_statistics():
    """
    Get statistics about the test suite
    
    Returns:
        Dictionary with test statistics
    """
    stats = {
        'total_test_modules': 0,
        'total_test_categories': len(TEST_CATEGORIES),
        'categories': {}
    }
    
    all_modules = set()
    for category, modules in TEST_CATEGORIES.items():
        stats['categories'][category] = len(modules)
        all_modules.update(modules)
    
    stats['total_test_modules'] = len(all_modules)
    
    return stats