# 编码规范

> "Code is read much more often than it is written." - <PERSON> van <PERSON>

## 核心原则

1. **可读性优先** - 代码是给人看的
2. **简单直接** - 不炫技，不过度设计
3. **一致性** - 统一的风格和模式
4. **零警告** - 编译器警告必须清零

## Rust 编码规范

### 命名规范

```rust
// 模块和文件：snake_case
mod screen_capture;
mod image_processor;

// 类型：PascalCase
struct ScreenCapture { }
enum ActionType { }
trait ImageMatcher { }

// 函数和方法：snake_case
fn capture_screen() -> Result<Frame> { }
fn process_image(image: &Image) -> Result<ProcessedImage> { }

// 常量：SCREAMING_SNAKE_CASE
const MAX_FRAME_SIZE: usize = 1920 * 1080 * 4;
const DEFAULT_FPS: u32 = 10;

// 变量：snake_case
let frame_buffer = Vec::new();
let current_step = 0;
```

### 代码组织

```rust
// 文件结构顺序
use std::collections::HashMap;  // 1. 标准库
use tokio::time::Duration;      // 2. 外部crate
use crate::types::Frame;        // 3. 内部模块

// 4. 常量定义
const BUFFER_SIZE: usize = 100;

// 5. 类型定义
pub struct RecordingModule {
    // 公有字段在前
    pub id: String,
    pub name: String,
    
    // 私有字段在后
    buffer: Vec<Frame>,
    is_running: bool,
}

// 6. Trait定义
pub trait Recorder {
    fn start(&mut self) -> Result<()>;
    fn stop(&mut self) -> Result<()>;
}

// 7. 实现块
impl RecordingModule {
    // 构造函数最先
    pub fn new(id: String) -> Self {
        Self {
            id,
            name: String::new(),
            buffer: Vec::with_capacity(BUFFER_SIZE),
            is_running: false,
        }
    }
    
    // 公有方法
    pub fn start_recording(&mut self) -> Result<()> {
        // 实现
    }
    
    // 私有方法
    fn internal_process(&self) -> Result<()> {
        // 实现
    }
}

// 8. Trait实现
impl Recorder for RecordingModule {
    fn start(&mut self) -> Result<()> {
        self.start_recording()
    }
    
    fn stop(&mut self) -> Result<()> {
        // 实现
    }
}

// 9. 测试
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_recording() {
        // 测试代码
    }
}
```

### 错误处理

```rust
// 使用Result，不panic
// ✅ 正确
pub fn read_file(path: &Path) -> Result<String, IoError> {
    fs::read_to_string(path)
}

// ❌ 错误
pub fn read_file(path: &Path) -> String {
    fs::read_to_string(path).unwrap()  // 不要在库代码中unwrap
}

// 自定义错误类型
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Parse error: {0}")]
    Parse(String),
    
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
}

// 错误传播
pub fn process_data() -> Result<Data, AppError> {
    let content = read_file("data.txt")?;  // 使用?操作符
    let parsed = parse_content(&content)?;
    Ok(parsed)
}
```

### 内存管理

```rust
// 优先借用，避免克隆
// ✅ 正确
fn process(data: &Data) -> Result<()> {
    // 使用引用
}

// ❌ 错误
fn process(data: Data) -> Result<()> {  // 不必要的所有权转移
    // 处理
}

// 使用Cow避免不必要的分配
use std::borrow::Cow;

fn format_message(msg: &str) -> Cow<str> {
    if msg.len() > 100 {
        Cow::Owned(format!("{}...", &msg[..100]))
    } else {
        Cow::Borrowed(msg)  // 不分配
    }
}

// 预分配容量
let mut vec = Vec::with_capacity(1000);  // 避免重复分配
```

### 并发安全

```rust
// 使用Arc<Mutex<>>时要小心
// ✅ 正确：最小化锁的范围
fn update_counter(counter: &Arc<Mutex<u32>>) {
    let mut count = counter.lock().unwrap();
    *count += 1;
    // 锁在这里自动释放
}

// ❌ 错误：长时间持有锁
fn bad_update(counter: &Arc<Mutex<Data>>) {
    let mut data = counter.lock().unwrap();
    expensive_operation();  // 持有锁时做耗时操作
    data.update();
}

// 使用通道而不是共享内存
use tokio::sync::mpsc;

async fn process_with_channels() {
    let (tx, mut rx) = mpsc::channel(100);
    
    // 生产者
    tokio::spawn(async move {
        tx.send(data).await.unwrap();
    });
    
    // 消费者
    while let Some(data) = rx.recv().await {
        process(data);
    }
}
```

## TypeScript/React 编码规范

### 命名规范

```typescript
// 组件：PascalCase
const RecordButton: React.FC = () => { };
const ScriptManager: React.FC = () => { };

// 函数和变量：camelCase
const handleClick = () => { };
let currentStep = 0;

// 常量：SCREAMING_SNAKE_CASE
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'https://api.showforai.com';

// 接口和类型：PascalCase，I前缀可选
interface Script {
  id: string;
  name: string;
}

type ExecutionMode = 'active' | 'auxiliary';

// 枚举：PascalCase，成员SCREAMING_SNAKE_CASE
enum ActionType {
  CLICK = 'CLICK',
  DOUBLE_CLICK = 'DOUBLE_CLICK',
  RIGHT_CLICK = 'RIGHT_CLICK',
}
```

### React 组件规范

```typescript
// 函数组件 + TypeScript
interface RecordButtonProps {
  isRecording: boolean;
  onStart: () => void;
  onStop: () => void;
}

// ✅ 正确：使用函数组件
const RecordButton: React.FC<RecordButtonProps> = ({ 
  isRecording, 
  onStart, 
  onStop 
}) => {
  // 使用hooks
  const [isPending, setIsPending] = useState(false);
  const timerRef = useRef<NodeJS.Timeout>();
  
  // 使用useCallback避免不必要的重渲染
  const handleClick = useCallback(() => {
    if (isRecording) {
      onStop();
    } else {
      onStart();
    }
  }, [isRecording, onStart, onStop]);
  
  // 使用useEffect处理副作用
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);
  
  return (
    <Button
      type="primary"
      danger
      loading={isPending}
      onClick={handleClick}
    >
      {isRecording ? '停止录制' : '开始录制'}
    </Button>
  );
};

// ❌ 错误：不要使用类组件
class RecordButton extends React.Component { }  // 避免
```

### 状态管理 (Zustand)

```typescript
// store定义
interface AppState {
  // 状态
  scripts: Script[];
  currentScript: Script | null;
  isRecording: boolean;
  
  // 动作
  addScript: (script: Script) => void;
  removeScript: (id: string) => void;
  setRecording: (recording: boolean) => void;
}

// ✅ 正确：使用immer进行不可变更新
const useAppStore = create<AppState>()(
  immer((set) => ({
    scripts: [],
    currentScript: null,
    isRecording: false,
    
    addScript: (script) => set((state) => {
      state.scripts.push(script);
    }),
    
    removeScript: (id) => set((state) => {
      state.scripts = state.scripts.filter(s => s.id !== id);
    }),
    
    setRecording: (recording) => set((state) => {
      state.isRecording = recording;
    }),
  }))
);

// 使用store
const ScriptList: React.FC = () => {
  const scripts = useAppStore(state => state.scripts);
  const removeScript = useAppStore(state => state.removeScript);
  
  return (
    <div>
      {scripts.map(script => (
        <ScriptItem
          key={script.id}
          script={script}
          onRemove={() => removeScript(script.id)}
        />
      ))}
    </div>
  );
};
```

### 异步处理

```typescript
// ✅ 正确：使用async/await
const uploadRecording = async (data: RecordingData): Promise<Script> => {
  try {
    const response = await api.uploadRecording(data);
    return response.data;
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};

// ❌ 错误：回调地狱
api.uploadRecording(data, (error, response) => {
  if (error) {
    handleError(error);
  } else {
    processResponse(response, (err, result) => {
      // 回调地狱
    });
  }
});

// React中的异步处理
const RecordingUploader: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleUpload = async () => {
    setUploading(true);
    setError(null);
    
    try {
      const result = await uploadRecording(data);
      // 处理成功
    } catch (err) {
      setError(err.message);
    } finally {
      setUploading(false);
    }
  };
  
  return (
    <div>
      <Button onClick={handleUpload} loading={uploading}>
        上传
      </Button>
      {error && <Alert type="error" message={error} />}
    </div>
  );
};
```

## 通用规范

### 注释规范

```rust
// Rust文档注释
/// 捕获屏幕的一帧
/// 
/// # Arguments
/// 
/// * `region` - 要捕获的屏幕区域
/// 
/// # Returns
/// 
/// 返回捕获的帧数据
/// 
/// # Errors
/// 
/// 如果屏幕捕获失败，返回`CaptureError`
pub fn capture_frame(region: &Region) -> Result<Frame, CaptureError> {
    // 实现注释：解释为什么，而不是做什么
    // 使用双缓冲避免撕裂
    let buffer = get_back_buffer();
    // ...
}
```

```typescript
/**
 * 上传录制数据到服务器
 * 
 * @param data - 录制的数据
 * @returns Promise<Script> - 生成的脚本
 * @throws {NetworkError} 网络错误
 * @throws {ValidationError} 数据验证失败
 * 
 * @example
 * ```typescript
 * const script = await uploadRecording({
 *   frames: [...],
 *   actions: [...]
 * });
 * ```
 */
export async function uploadRecording(data: RecordingData): Promise<Script> {
  // 实现
}
```

### Git 提交规范

```bash
# 格式：<type>(<scope>): <subject>

# 类型
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式（不影响功能）
refactor: 重构
perf: 性能优化
test: 测试相关
chore: 构建过程或辅助工具

# 示例
feat(recording): 添加屏幕录制功能
fix(matcher): 修复图像匹配精度问题
docs(api): 更新API文档
refactor(core): 重构数据结构
perf(capture): 优化屏幕捕获性能

# 提交消息正文（可选）
- 详细说明改动原因
- 列出主要改动点
- 提及相关issue

# Footer（可选）
BREAKING CHANGE: 描述不兼容的改动
Closes #123  # 关闭issue
```

### 代码审查清单

#### Rust代码审查
- [ ] 没有unwrap（除非在测试代码中）
- [ ] 没有panic
- [ ] 没有unsafe（除非必要且有注释）
- [ ] 错误处理完整
- [ ] 没有编译器警告
- [ ] 有单元测试
- [ ] 文档注释完整

#### TypeScript代码审查
- [ ] 没有any类型（除非必要）
- [ ] 没有console.log（生产代码）
- [ ] 组件有Props类型定义
- [ ] 异步错误处理
- [ ] 没有内存泄漏（清理定时器等）
- [ ] 有测试覆盖

### 性能规范

```rust
// Rust性能规范
// 1. 避免不必要的分配
let mut buffer = Vec::with_capacity(1000);  // 预分配

// 2. 使用迭代器而不是循环
let sum: i32 = numbers.iter().sum();  // 而不是for循环

// 3. 内联小函数
#[inline]
fn small_function() -> u32 { 42 }

// 4. 避免字符串分配
use std::fmt::Write;
let mut s = String::with_capacity(100);
write!(&mut s, "Hello {}", name).unwrap();  // 而不是format!
```

```typescript
// TypeScript性能规范
// 1. 使用React.memo避免不必要的重渲染
const ExpensiveComponent = React.memo(({ data }) => {
  // 组件内容
});

// 2. 使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// 3. 使用虚拟列表处理大数据
import { FixedSizeList } from 'react-window';

// 4. 懒加载组件
const HeavyComponent = React.lazy(() => import('./HeavyComponent'));
```

## 工具配置

### Rust (rustfmt.toml)
```toml
edition = "2021"
max_width = 100
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_field_init_shorthand = true
use_try_shorthand = true
```

### TypeScript (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  }
}
```

### ESLint (.eslintrc.json)
```json
{
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "no-console": "warn",
    "no-debugger": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react/prop-types": "off"
  }
}
```

---

*"Any fool can write code that a computer can understand. Good programmers write code that humans can understand."* - Martin Fowler