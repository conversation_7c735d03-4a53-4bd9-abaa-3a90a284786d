"""
Test suite for Buffer Manager
Tests frame buffering, circular buffer, thread safety, and memory management
"""

import unittest
import threading
import time
import numpy as np
from PIL import Image
from queue import Queue
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.buffer_manager import <PERSON>ufferManager, FrameBuffer, CircularFrameBuffer


class TestCircularBuffer(unittest.TestCase):
    """Test Circular Buffer implementation"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.buffer_size = 10
        self.buffer = CircularFrameBuffer(self.buffer_size)
        
    def test_initialization(self):
        """Test buffer initialization"""
        self.assertEqual(self.buffer.capacity, self.buffer_size)
        self.assertEqual(self.buffer.size, 0)
        self.assertTrue(self.buffer.is_empty())
        self.assertFalse(self.buffer.is_full())
        
    def test_add_and_get(self):
        """Test adding and getting items"""
        # Add items
        for i in range(5):
            self.buffer.add(f"item_{i}")
            
        self.assertEqual(self.buffer.size, 5)
        self.assertFalse(self.buffer.is_empty())
        self.assertFalse(self.buffer.is_full())
        
        # Get items
        item = self.buffer.get()
        self.assertEqual(item, "item_0")
        self.assertEqual(self.buffer.size, 4)
        
    def test_circular_behavior(self):
        """Test circular overwrite behavior"""
        # Fill buffer completely
        for i in range(self.buffer_size):
            self.buffer.add(f"item_{i}")
            
        self.assertTrue(self.buffer.is_full())
        
        # Add one more item (should overwrite oldest)
        self.buffer.add("new_item")
        
        # Still full
        self.assertTrue(self.buffer.is_full())
        
        # First item should now be item_1 (item_0 was overwritten)
        first = self.buffer.get()
        self.assertEqual(first, "item_1")
        
    def test_peek(self):
        """Test peeking without removing"""
        self.buffer.add("test_item")
        
        # Peek should return item without removing
        peeked = self.buffer.peek()
        self.assertEqual(peeked, "test_item")
        self.assertEqual(self.buffer.size, 1)
        
        # Get should return same item and remove it
        got = self.buffer.get()
        self.assertEqual(got, "test_item")
        self.assertEqual(self.buffer.size, 0)
        
    def test_clear(self):
        """Test clearing the buffer"""
        # Add items
        for i in range(5):
            self.buffer.add(f"item_{i}")
            
        # Clear buffer
        self.buffer.clear()
        
        self.assertEqual(self.buffer.size, 0)
        self.assertTrue(self.buffer.is_empty())
        
    def test_get_all(self):
        """Test getting all items"""
        items = ["a", "b", "c", "d", "e"]
        for item in items:
            self.buffer.add(item)
            
        all_items = self.buffer.get_all()
        self.assertEqual(all_items, items)
        self.assertEqual(self.buffer.size, 0)


class TestFrameBuffer(unittest.TestCase):
    """Test Frame Buffer for image frames"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.frame_buffer = FrameBuffer(max_frames=30)
        
        # Create test frames
        self.test_frames = []
        for i in range(5):
            frame = Image.new('RGB', (100, 100), color=(i*50, i*50, i*50))
            self.test_frames.append(frame)
            
    def test_add_frame(self):
        """Test adding frames to buffer"""
        for i, frame in enumerate(self.test_frames):
            frame_id = self.frame_buffer.add_frame(frame, metadata={'index': i})
            self.assertIsNotNone(frame_id)
            
        self.assertEqual(self.frame_buffer.frame_count(), len(self.test_frames))
        
    def test_get_frame(self):
        """Test retrieving frames by ID"""
        # Add frames and store IDs
        frame_ids = []
        for frame in self.test_frames:
            frame_id = self.frame_buffer.add_frame(frame)
            frame_ids.append(frame_id)
            
        # Retrieve frames
        for i, frame_id in enumerate(frame_ids):
            retrieved = self.frame_buffer.get_frame(frame_id)
            self.assertIsNotNone(retrieved)
            self.assertEqual(retrieved.size, self.test_frames[i].size)
            
    def test_get_latest_frame(self):
        """Test getting the latest frame"""
        # Add frames
        for frame in self.test_frames:
            self.frame_buffer.add_frame(frame)
            
        # Get latest
        latest = self.frame_buffer.get_latest_frame()
        self.assertIsNotNone(latest)
        
        # Should be the last added frame
        self.assertEqual(latest.size, self.test_frames[-1].size)
        
    def test_get_frame_range(self):
        """Test getting range of frames"""
        # Add frames with timestamps
        for i, frame in enumerate(self.test_frames):
            self.frame_buffer.add_frame(frame, metadata={'timestamp': i})
            
        # Get middle range
        frames = self.frame_buffer.get_frame_range(start_index=1, end_index=4)
        self.assertEqual(len(frames), 3)
        
    def test_buffer_overflow(self):
        """Test buffer behavior when exceeding max frames"""
        small_buffer = FrameBuffer(max_frames=3)
        
        # Add more frames than capacity
        for i in range(5):
            frame = Image.new('RGB', (50, 50))
            small_buffer.add_frame(frame, metadata={'index': i})
            
        # Should only have max_frames
        self.assertEqual(small_buffer.frame_count(), 3)
        
        # Oldest frames should be removed
        all_frames = small_buffer.get_all_frames()
        metadata = [f['metadata'] for f in all_frames]
        indices = [m['index'] for m in metadata]
        
        # Should have the last 3 frames
        self.assertEqual(indices, [2, 3, 4])
        
    def test_clear_buffer(self):
        """Test clearing the frame buffer"""
        # Add frames
        for frame in self.test_frames:
            self.frame_buffer.add_frame(frame)
            
        # Clear
        self.frame_buffer.clear()
        
        self.assertEqual(self.frame_buffer.frame_count(), 0)
        self.assertIsNone(self.frame_buffer.get_latest_frame())
        
    def test_frame_metadata(self):
        """Test frame metadata handling"""
        metadata = {
            'timestamp': time.time(),
            'resolution': (1920, 1080),
            'source': 'screen_capture'
        }
        
        frame_id = self.frame_buffer.add_frame(self.test_frames[0], metadata)
        
        # Retrieve with metadata
        frame_data = self.frame_buffer.get_frame_with_metadata(frame_id)
        
        self.assertIsNotNone(frame_data)
        self.assertIn('frame', frame_data)
        self.assertIn('metadata', frame_data)
        self.assertEqual(frame_data['metadata'], metadata)


class TestBufferManager(unittest.TestCase):
    """Test main Buffer Manager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.buffer_manager = BufferManager(
            frame_buffer_size=50,
            action_buffer_size=100
        )
        
    def test_initialization(self):
        """Test buffer manager initialization"""
        self.assertIsNotNone(self.buffer_manager.frame_buffer)
        self.assertIsNotNone(self.buffer_manager.action_buffer)
        self.assertEqual(self.buffer_manager.frame_buffer.max_frames, 50)
        
    def test_add_frame_and_action(self):
        """Test adding frames and actions"""
        # Add frame
        frame = Image.new('RGB', (100, 100))
        frame_id = self.buffer_manager.add_frame(frame)
        
        # Add action
        action = {
            'type': 'click',
            'position': (50, 50),
            'frame_id': frame_id
        }
        self.buffer_manager.add_action(action)
        
        # Verify
        self.assertEqual(self.buffer_manager.get_frame_count(), 1)
        self.assertEqual(self.buffer_manager.get_action_count(), 1)
        
    def test_synchronization(self):
        """Test frame-action synchronization"""
        # Add synchronized frame and action
        frame = Image.new('RGB', (200, 200))
        action = {'type': 'move', 'position': (100, 100)}
        
        frame_id, action_id = self.buffer_manager.add_synchronized(frame, action)
        
        self.assertIsNotNone(frame_id)
        self.assertIsNotNone(action_id)
        
        # Retrieve synchronized data
        sync_data = self.buffer_manager.get_synchronized(frame_id)
        
        self.assertIn('frame', sync_data)
        self.assertIn('action', sync_data)
        
    def test_thread_safety(self):
        """Test thread-safe operations"""
        errors = []
        
        def add_frames():
            try:
                for i in range(10):
                    frame = Image.new('RGB', (50, 50), color=(i*20, 0, 0))
                    self.buffer_manager.add_frame(frame)
                    time.sleep(0.01)
            except Exception as e:
                errors.append(e)
                
        def add_actions():
            try:
                for i in range(10):
                    action = {'type': 'test', 'index': i}
                    self.buffer_manager.add_action(action)
                    time.sleep(0.01)
            except Exception as e:
                errors.append(e)
                
        # Create threads
        frame_thread = threading.Thread(target=add_frames)
        action_thread = threading.Thread(target=add_actions)
        
        # Run concurrently
        frame_thread.start()
        action_thread.start()
        
        frame_thread.join()
        action_thread.join()
        
        # Should have no errors
        self.assertEqual(len(errors), 0)
        
        # Should have added all items
        self.assertEqual(self.buffer_manager.get_frame_count(), 10)
        self.assertEqual(self.buffer_manager.get_action_count(), 10)
        
    def test_memory_management(self):
        """Test memory management and cleanup"""
        import gc
        import tracemalloc
        
        tracemalloc.start()
        snapshot1 = tracemalloc.take_snapshot()
        
        # Add many large frames
        for i in range(100):
            frame = Image.new('RGB', (500, 500))
            self.buffer_manager.add_frame(frame)
            
        snapshot2 = tracemalloc.take_snapshot()
        
        # Clear buffers
        self.buffer_manager.clear_all()
        gc.collect()
        
        snapshot3 = tracemalloc.take_snapshot()
        
        # Memory should be mostly released
        stats2 = snapshot2.statistics('lineno')
        stats3 = snapshot3.statistics('lineno')
        
        total2 = sum(stat.size for stat in stats2[:10])
        total3 = sum(stat.size for stat in stats3[:10])
        
        # Memory after clear should be significantly less
        self.assertLess(total3, total2 * 0.5)
        
        tracemalloc.stop()
        
    def test_buffer_statistics(self):
        """Test buffer statistics and monitoring"""
        # Add some data
        for i in range(5):
            frame = Image.new('RGB', (100, 100))
            self.buffer_manager.add_frame(frame)
            self.buffer_manager.add_action({'index': i})
            
        stats = self.buffer_manager.get_statistics()
        
        # Check statistics structure
        self.assertIn('frame_buffer', stats)
        self.assertIn('action_buffer', stats)
        self.assertIn('memory_usage', stats)
        
        self.assertEqual(stats['frame_buffer']['count'], 5)
        self.assertEqual(stats['action_buffer']['count'], 5)
        
    def test_performance_metrics(self):
        """Test performance under load"""
        import time
        
        start_time = time.time()
        
        # Add many frames quickly
        for i in range(100):
            frame = Image.new('RGB', (200, 200))
            self.buffer_manager.add_frame(frame)
            
        elapsed = time.time() - start_time
        
        # Should handle 100 frames in reasonable time
        self.assertLess(elapsed, 2.0)
        
        # Calculate throughput
        throughput = 100 / elapsed
        self.assertGreater(throughput, 50)  # At least 50 frames/second
        
    def test_adaptive_buffer_sizing(self):
        """Test adaptive buffer resizing based on load"""
        # Simulate high load
        for i in range(200):
            frame = Image.new('RGB', (100, 100))
            self.buffer_manager.add_frame(frame)
            
        # Buffer should adapt (implementation dependent)
        # Check that it handles overflow gracefully
        self.assertLessEqual(
            self.buffer_manager.get_frame_count(),
            self.buffer_manager.frame_buffer.max_frames
        )
        
    def test_priority_frames(self):
        """Test priority frame handling"""
        # Add regular frames
        for i in range(3):
            frame = Image.new('RGB', (50, 50))
            self.buffer_manager.add_frame(frame, priority=False)
            
        # Add priority frame
        priority_frame = Image.new('RGB', (50, 50), color='red')
        priority_id = self.buffer_manager.add_frame(priority_frame, priority=True)
        
        # Priority frame should be retained even if buffer is full
        self.assertIsNotNone(self.buffer_manager.get_frame(priority_id))


class TestBufferIntegration(unittest.TestCase):
    """Integration tests for Buffer Manager with other components"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.buffer_manager = BufferManager()
        
    def test_recorder_integration(self):
        """Test integration with recorder component"""
        # Simulate recorder adding frames
        recorder_frames = []
        for i in range(10):
            frame = Image.new('RGB', (800, 600))
            frame_id = self.buffer_manager.add_frame(
                frame,
                metadata={'timestamp': time.time(), 'index': i}
            )
            recorder_frames.append(frame_id)
            
        # Verify all frames are accessible
        for frame_id in recorder_frames:
            frame = self.buffer_manager.get_frame(frame_id)
            self.assertIsNotNone(frame)
            
    def test_executor_integration(self):
        """Test integration with executor component"""
        # Add frames for execution
        execution_frames = []
        for i in range(5):
            frame = Image.new('RGB', (1920, 1080))
            frame_id = self.buffer_manager.add_frame(frame)
            execution_frames.append(frame_id)
            
            # Add corresponding action
            action = {
                'type': 'click',
                'frame_id': frame_id,
                'position': (100 * i, 100 * i)
            }
            self.buffer_manager.add_action(action)
            
        # Executor should be able to retrieve frame-action pairs
        for frame_id in execution_frames:
            sync_data = self.buffer_manager.get_synchronized(frame_id)
            self.assertIsNotNone(sync_data)
            self.assertIn('frame', sync_data)
            self.assertIn('action', sync_data)


if __name__ == '__main__':
    # Run tests with coverage
    unittest.main(verbosity=2)