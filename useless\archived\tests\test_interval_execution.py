"""
Test script to verify that intervals are properly used during execution.

This test ensures that the executor correctly uses the recorded intervals.
"""

import sys
import time
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.executor.executor import <PERSON>riptExecutor, ExecutorConfig, ExecutionMode
from showforai.executor.script_loader import Script, Action, ScriptMetadata
from showforai.executor.smart_wait_manager import SmartWaitManager, WaitMode, WaitConfig, WaitResult


def create_test_script():
    """Create a test script with specific delays."""
    metadata = ScriptMetadata(
        version="1.0",
        session_id="test-session",
        screen_resolution="1920x1080",
        device_id="test-device",
        recorded_at="2024-01-01T00:00:00",
        duration_ms=5000,
        platform="Windows",
        python_version="3.11"
    )
    
    actions = [
        Action(
            sequence=1,
            type="click",
            timestamp=0,
            delay_ms=0,  # First action, no delay
            bbox={'x': 100, 'y': 100, 'width': 50, 'height': 50},
            element="element1.png"
        ),
        Action(
            sequence=2,
            type="click",
            timestamp=500,
            delay_ms=500,  # 500ms delay from previous action
            bbox={'x': 200, 'y': 200, 'width': 50, 'height': 50},
            element="element2.png"
        ),
        Action(
            sequence=3,
            type="type",
            timestamp=1500,
            delay_ms=1000,  # 1000ms delay from previous action
            text="Hello World",
            bbox={'x': 300, 'y': 300, 'width': 100, 'height': 30},
            element="element3.png"
        ),
        Action(
            sequence=4,
            type="key_press",
            timestamp=2500,
            delay_ms=1000,  # 1000ms delay
            key="Enter"
        ),
        Action(
            sequence=5,
            type="scroll",
            timestamp=3000,
            delay_ms=500,  # 500ms delay
            direction="down"
        )
    ]
    
    return Script(metadata=metadata, actions=actions)


def test_executor_uses_delays():
    """Test that the executor properly uses recorded delays."""
    print("=" * 60)
    print("TESTING EXECUTOR INTERVAL USAGE")
    print("=" * 60)
    
    # Create test script
    script = create_test_script()
    
    # Create executor with config
    config = ExecutorConfig(
        mode=ExecutionMode.ACTIVE,
        delay_mode="recorded",  # Use recorded delays
        skip_on_failure=True
    )
    
    executor = ScriptExecutor(config)
    
    # Track calls to smart wait manager
    wait_calls = []
    original_wait_with_interval = executor.smart_wait_manager.wait_with_interval
    
    def mock_wait_with_interval(detection_func, recorded_interval_ms, mode):
        """Mock wait_with_interval to track calls."""
        wait_calls.append({
            'recorded_interval_ms': recorded_interval_ms,
            'mode': mode
        })
        # Simulate successful detection
        detection_func()  # Call once to simulate finding element
        return WaitResult(
            success=True,
            wait_time_ms=recorded_interval_ms,
            attempts=1,
            detected_at_ms=recorded_interval_ms
        )
    
    # Mock the wait manager
    executor.smart_wait_manager.wait_with_interval = mock_wait_with_interval
    
    # Mock action executor to avoid actual clicks
    with patch.object(executor.action_executor, 'execute') as mock_execute:
        mock_execute.return_value.success = True
        
        # Mock element matcher to avoid actual screenshots
        with patch.object(executor.element_matcher, 'match_element') as mock_match:
            mock_match.return_value = Mock(found=True, confidence=0.95, coordinates=(100, 100))
            
            # Track execution times
            action_times = []
            original_execute_action = executor.execute_action
            
            def track_execute_action(action):
                """Track when actions are executed."""
                action_times.append({
                    'action': action.type,
                    'delay_ms': action.delay_ms,
                    'time': time.time()
                })
                return original_execute_action(action)
            
            executor.execute_action = track_execute_action
            
            # Execute the script
            print("\nExecuting test script with recorded delays...")
            start_time = time.time()
            result = executor.execute_script(script)
            end_time = time.time()
            
            print(f"Execution completed in {(end_time - start_time):.2f} seconds")
            print(f"Result: success={result.success}, actions={result.successful_actions}/{result.total_actions}")
    
    # Verify delays were used
    print("\n=== Verification of Delay Usage ===")
    
    # Check wait calls for actions that use smart wait
    print("\nSmart Wait Calls (for element-based actions):")
    for i, call in enumerate(wait_calls):
        print(f"  Call {i+1}: recorded_interval_ms={call['recorded_interval_ms']}ms, mode={call['mode'].value}")
    
    # Expected delays for element-based actions (click, click, type)
    expected_delays = [0, 500, 1000]  # First 3 actions that use smart wait
    actual_delays = [call['recorded_interval_ms'] for call in wait_calls[:3]]
    
    print("\nDelay Verification:")
    for i, (expected, actual) in enumerate(zip(expected_delays, actual_delays)):
        match = "✅" if expected == actual else "❌"
        print(f"  Action {i+1}: Expected={expected}ms, Actual={actual}ms {match}")
        assert expected == actual, f"Action {i+1} delay mismatch: expected {expected}ms, got {actual}ms"
    
    print("\n✅ Executor correctly uses recorded intervals!")
    
    # Verify action execution
    print("\nAction Execution Summary:")
    for i, action_info in enumerate(action_times):
        print(f"  Action {i+1}: {action_info['action']:10} - delay_ms={action_info['delay_ms']:5}ms")
    
    return True


def test_delay_calculation_accuracy():
    """Test the accuracy of delay calculations."""
    print("\n" + "=" * 60)
    print("TESTING DELAY CALCULATION ACCURACY")
    print("=" * 60)
    
    from showforai.recorder.action_buffer import ActionBuffer
    
    buffer = ActionBuffer()
    
    # Create actions with precise timing
    base_time = 1000.0  # Start at 1000 seconds for easier calculation
    
    # Test various delay scenarios
    test_cases = [
        (0, "First action"),
        (0.001, "1ms delay"),
        (0.010, "10ms delay"),
        (0.100, "100ms delay"),
        (0.250, "250ms delay"),
        (0.500, "500ms delay"),
        (1.000, "1000ms delay"),
        (2.500, "2500ms delay"),
    ]
    
    for delay_seconds, description in test_cases:
        timestamp = base_time + sum(tc[0] for tc in test_cases[:test_cases.index((delay_seconds, description))])
        buffer.add_click(100, 100, timestamp)
    
    # Calculate delays
    buffer.calculate_delays()
    
    # Verify calculations
    print("\nDelay Calculation Results:")
    for i, action in enumerate(buffer.actions):
        expected_ms = int(test_cases[i][0] * 1000) if i > 0 else 0
        actual_ms = action.delay_ms
        description = test_cases[i][1]
        match = "✅" if expected_ms == actual_ms else "❌"
        print(f"  Action {i}: {description:15} - Expected={expected_ms:5}ms, Actual={actual_ms:5}ms {match}")
        
        # Allow 1ms tolerance for floating point precision
        assert abs(expected_ms - actual_ms) <= 1, f"Delay calculation error for {description}"
    
    print("\n✅ Delay calculations are accurate to millisecond precision!")
    return True


def main():
    """Run all interval execution tests."""
    try:
        # Test executor using delays
        test_executor_uses_delays()
        
        # Test delay calculation accuracy
        test_delay_calculation_accuracy()
        
        print("\n" + "=" * 60)
        print("✅ ALL EXECUTION TESTS PASSED!")
        print("=" * 60)
        print("\nConclusions:")
        print("- Executor correctly reads delay_ms from actions")
        print("- Smart wait manager receives correct interval values")
        print("- Delay calculations are accurate to millisecond precision")
        print("- The interval recording feature is fully functional!")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()