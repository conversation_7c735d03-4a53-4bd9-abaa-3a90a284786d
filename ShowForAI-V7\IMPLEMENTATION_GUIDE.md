# 技术实现指南

> "First, solve the problem. Then, write the code." - <PERSON>

## 概述

本指南提供ShowForAI核心功能的具体实现方案，包括代码示例和最佳实践。

## 1. 图像处理实现

### 1.1 屏幕捕获

#### Windows实现 (使用Windows Graphics Capture API)
```rust
use windows::{
    Win32::Graphics::Direct3D11::*,
    Win32::System::WinRT::*,
    Graphics::Capture::*,
};

pub struct ScreenCapture {
    device: ID3D11Device,
    context: ID3D11DeviceContext,
    capture_session: GraphicsCaptureSession,
}

impl ScreenCapture {
    pub fn new() -> Result<Self> {
        // 初始化D3D11设备
        let device = create_d3d11_device()?;
        let context = device.GetImmediateContext()?;
        
        // 创建捕获会话
        let capture_session = GraphicsCaptureSession::new()?;
        
        Ok(Self {
            device,
            context,
            capture_session,
        })
    }
    
    pub fn capture_frame(&self) -> Result<RawFrame> {
        // 捕获当前帧
        let frame = self.capture_session.TryGetNextFrame()?;
        
        if let Some(frame) = frame {
            // 获取纹理
            let texture = frame.Surface()?.as_d3d11_texture()?;
            
            // 读取像素数据
            let pixels = self.read_texture_pixels(texture)?;
            
            Ok(RawFrame {
                data: pixels,
                resolution: (1920, 1080), // 从texture获取实际分辨率
                timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64,
                frame_index: 0,
            })
        } else {
            Err(Error::NoFrameAvailable)
        }
    }
    
    fn read_texture_pixels(&self, texture: ID3D11Texture2D) -> Result<Vec<u8>> {
        // 创建暂存纹理
        let staging_texture = create_staging_texture(&self.device, &texture)?;
        
        // 复制到暂存纹理
        self.context.CopyResource(&staging_texture, &texture);
        
        // 映射并读取数据
        let mapped = self.context.Map(&staging_texture, 0, D3D11_MAP_READ, 0)?;
        let data = unsafe {
            std::slice::from_raw_parts(
                mapped.pData as *const u8,
                mapped.RowPitch as usize * 1080
            ).to_vec()
        };
        self.context.Unmap(&staging_texture, 0);
        
        Ok(data)
    }
}
```

#### 跨平台备选方案
```rust
// 使用 scrap 库作为跨平台方案
use scrap::{Capturer, Display};

pub struct CrossPlatformCapture {
    capturer: Capturer,
}

impl CrossPlatformCapture {
    pub fn new() -> Result<Self> {
        let display = Display::primary()?;
        let capturer = Capturer::new(display)?;
        Ok(Self { capturer })
    }
    
    pub fn capture_frame(&mut self) -> Result<Vec<u8>> {
        loop {
            match self.capturer.frame() {
                Ok(frame) => {
                    // 转换BGRA到RGBA
                    let rgba = convert_bgra_to_rgba(&frame);
                    return Ok(rgba);
                }
                Err(ref e) if e.kind() == std::io::ErrorKind::WouldBlock => {
                    // 等待下一帧
                    std::thread::sleep(Duration::from_millis(10));
                }
                Err(e) => return Err(e.into()),
            }
        }
    }
}
```

### 1.2 图像标准化（768×768）

```rust
use image::{DynamicImage, ImageBuffer, Rgba};

pub struct ImageStandardizer {
    target_size: (u32, u32),
}

impl ImageStandardizer {
    pub fn new() -> Self {
        Self {
            target_size: (768, 768),
        }
    }
    
    pub fn standardize(&self, raw_frame: &RawFrame) -> Result<StandardFrame> {
        // 将原始数据转换为图像
        let img = ImageBuffer::<Rgba<u8>, _>::from_raw(
            raw_frame.resolution.0,
            raw_frame.resolution.1,
            raw_frame.data.clone(),
        ).ok_or(Error::InvalidImageData)?;
        
        let dynamic_img = DynamicImage::ImageRgba8(img);
        
        // 缩放到768×768
        let resized = dynamic_img.resize_exact(
            self.target_size.0,
            self.target_size.1,
            image::imageops::FilterType::Lanczos3, // 高质量缩放
        );
        
        Ok(StandardFrame {
            image_768: resized.into_rgba8().into_raw(),
            original_resolution: raw_frame.resolution,
            timestamp: raw_frame.timestamp,
        })
    }
    
    /// 批量标准化（性能优化）
    pub fn standardize_batch(&self, frames: Vec<RawFrame>) -> Result<Vec<StandardFrame>> {
        use rayon::prelude::*;
        
        frames
            .par_iter()  // 并行处理
            .map(|frame| self.standardize(frame))
            .collect()
    }
}
```

### 1.3 BBOX裁切实现

```rust
pub struct BboxCropper;

impl BboxCropper {
    /// 从768×768图像中裁切BBOX区域
    pub fn crop_element(
        &self,
        image_768: &[u8],
        bbox: &BoundingBox,
    ) -> Result<Vec<u8>> {
        // 验证BBOX边界
        if bbox.x + bbox.width > 768 || bbox.y + bbox.height > 768 {
            return Err(Error::BboxOutOfBounds);
        }
        
        // 创建图像缓冲区
        let img = ImageBuffer::<Rgba<u8>, _>::from_raw(
            768,
            768,
            image_768.to_vec(),
        ).ok_or(Error::InvalidImageData)?;
        
        // 裁切指定区域
        let cropped = image::imageops::crop_imm(
            &img,
            bbox.x,
            bbox.y,
            bbox.width,
            bbox.height,
        );
        
        Ok(cropped.to_image().into_raw())
    }
    
    /// 批量裁切（用于处理服务器返回的数据）
    pub fn crop_batch(
        &self,
        frames: &[Vec<u8>],
        bboxes: &[BoundingBox],
    ) -> Result<Vec<Vec<u8>>> {
        if frames.len() != bboxes.len() {
            return Err(Error::FrameBboxMismatch);
        }
        
        frames
            .iter()
            .zip(bboxes.iter())
            .map(|(frame, bbox)| self.crop_element(frame, bbox))
            .collect()
    }
}
```

## 2. 图像匹配实现

### 2.1 模板匹配（主要算法）

```rust
use opencv::{
    core::{Mat, Point, Rect, Scalar, Size, CV_32F},
    imgcodecs,
    imgproc::{match_template, min_max_loc, TemplateMatchModes},
};

pub struct TemplateMatcher {
    threshold: f64,
}

impl TemplateMatcher {
    pub fn new() -> Self {
        Self {
            threshold: 0.85, // 高置信度要求
        }
    }
    
    pub fn match_template(
        &self,
        screen: &[u8],
        template: &[u8],
        screen_size: (u32, u32),
        template_size: (u32, u32),
    ) -> Option<MatchResult> {
        // 转换为OpenCV Mat
        let screen_mat = Mat::from_slice(screen).unwrap();
        let template_mat = Mat::from_slice(template).unwrap();
        
        // 创建结果矩阵
        let mut result = Mat::default();
        
        // 执行模板匹配
        match_template(
            &screen_mat,
            &template_mat,
            &mut result,
            TemplateMatchModes::TM_CCOEFF_NORMED as i32,
            &Mat::default(),
        ).ok()?;
        
        // 查找最佳匹配
        let mut min_val = 0.0;
        let mut max_val = 0.0;
        let mut min_loc = Point::default();
        let mut max_loc = Point::default();
        
        min_max_loc(
            &result,
            Some(&mut min_val),
            Some(&mut max_val),
            Some(&mut min_loc),
            Some(&mut max_loc),
            &Mat::default(),
        ).ok()?;
        
        // 检查置信度
        if max_val >= self.threshold {
            Some(MatchResult {
                position: (max_loc.x, max_loc.y),
                confidence: max_val as f32,
                match_time_ms: 0, // 实际测量
                algorithm: MatchAlgorithm::TemplateMatching,
            })
        } else {
            None
        }
    }
}
```

### 2.2 ORB特征点匹配（降级方案）

```rust
use opencv::{
    features2d::{ORB, BFMatcher, DrawMatchesFlags, DMatch},
    core::{KeyPoint, Mat, Scalar, Vector},
};

pub struct OrbMatcher {
    orb: ORB,
    matcher: BFMatcher,
    min_matches: usize,
}

impl OrbMatcher {
    pub fn new() -> Result<Self> {
        let orb = ORB::create(
            500,    // nfeatures
            1.2,    // scaleFactor
            8,      // nlevels
            31,     // edgeThreshold
            0,      // firstLevel
            2,      // WTA_K
            opencv::features2d::ORB_ScoreType::HARRIS_SCORE,
            31,     // patchSize
            20,     // fastThreshold
        )?;
        
        let matcher = BFMatcher::create(
            opencv::core::NORM_HAMMING,
            true, // crossCheck
        )?;
        
        Ok(Self {
            orb,
            matcher,
            min_matches: 15,
        })
    }
    
    pub fn match_orb(
        &mut self,
        screen: &Mat,
        template: &Mat,
    ) -> Option<MatchResult> {
        // 提取特征点
        let mut kp1 = Vector::<KeyPoint>::new();
        let mut desc1 = Mat::default();
        self.orb.detect_and_compute(screen, &Mat::default(), &mut kp1, &mut desc1, false).ok()?;
        
        let mut kp2 = Vector::<KeyPoint>::new();
        let mut desc2 = Mat::default();
        self.orb.detect_and_compute(template, &Mat::default(), &mut kp2, &mut desc2, false).ok()?;
        
        // 特征匹配
        let mut matches = Vector::<DMatch>::new();
        self.matcher.match_(&desc1, &desc2, &mut matches, &Mat::default()).ok()?;
        
        // 筛选好的匹配
        let good_matches: Vec<DMatch> = matches
            .iter()
            .filter(|m| m.distance < 30.0)  // 距离阈值
            .collect();
        
        if good_matches.len() >= self.min_matches {
            // 计算匹配中心位置
            let sum_x: f32 = good_matches.iter().map(|m| kp1.get(m.query_idx as usize).unwrap().pt.x).sum();
            let sum_y: f32 = good_matches.iter().map(|m| kp1.get(m.query_idx as usize).unwrap().pt.y).sum();
            let count = good_matches.len() as f32;
            
            Some(MatchResult {
                position: ((sum_x / count) as i32, (sum_y / count) as i32),
                confidence: (good_matches.len() as f32 / self.min_matches as f32).min(1.0),
                match_time_ms: 0,
                algorithm: MatchAlgorithm::ORB,
            })
        } else {
            None
        }
    }
}
```

### 2.3 多尺度匹配（兜底方案）

```rust
pub struct MultiScaleMatcher {
    base_matcher: TemplateMatcher,
    scale_range: Vec<f32>,
}

impl MultiScaleMatcher {
    pub fn new() -> Self {
        Self {
            base_matcher: TemplateMatcher::new(),
            scale_range: vec![0.8, 0.85, 0.9, 0.95, 1.0, 1.05, 1.1, 1.15, 1.2],
        }
    }
    
    pub fn match_multiscale(
        &self,
        screen: &[u8],
        template: &[u8],
        template_size: (u32, u32),
    ) -> Option<MatchResult> {
        let mut best_match: Option<MatchResult> = None;
        let mut best_confidence = 0.0;
        
        for scale in &self.scale_range {
            // 缩放模板
            let scaled_size = (
                (template_size.0 as f32 * scale) as u32,
                (template_size.1 as f32 * scale) as u32,
            );
            
            let scaled_template = resize_image(template, template_size, scaled_size)?;
            
            // 尝试匹配
            if let Some(result) = self.base_matcher.match_template(
                screen,
                &scaled_template,
                (1920, 1080), // 假设屏幕大小
                scaled_size,
            ) {
                if result.confidence > best_confidence {
                    best_confidence = result.confidence;
                    best_match = Some(result);
                }
            }
        }
        
        best_match
    }
}
```

## 3. 动作执行实现

### 3.1 鼠标操作

```rust
use winapi::um::winuser::{
    SetCursorPos, mouse_event,
    MOUSEEVENTF_LEFTDOWN, MOUSEEVENTF_LEFTUP,
    MOUSEEVENTF_RIGHTDOWN, MOUSEEVENTF_RIGHTUP,
    MOUSEEVENTF_MIDDLEDOWN, MOUSEEVENTF_MIDDLEUP,
    MOUSEEVENTF_WHEEL,
};

pub struct MouseController;

impl MouseController {
    /// 移动鼠标到指定位置
    pub fn move_to(&self, x: i32, y: i32) -> Result<()> {
        unsafe {
            if SetCursorPos(x, y) == 0 {
                return Err(Error::MouseOperationFailed);
            }
        }
        Ok(())
    }
    
    /// 点击
    pub fn click(&self, x: i32, y: i32) -> Result<()> {
        self.move_to(x, y)?;
        std::thread::sleep(Duration::from_millis(50)); // 短暂延迟
        
        unsafe {
            mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
            std::thread::sleep(Duration::from_millis(50));
            mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
        }
        Ok(())
    }
    
    /// 双击
    pub fn double_click(&self, x: i32, y: i32) -> Result<()> {
        self.click(x, y)?;
        std::thread::sleep(Duration::from_millis(100));
        self.click(x, y)?;
        Ok(())
    }
    
    /// 右键点击
    pub fn right_click(&self, x: i32, y: i32) -> Result<()> {
        self.move_to(x, y)?;
        std::thread::sleep(Duration::from_millis(50));
        
        unsafe {
            mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
            std::thread::sleep(Duration::from_millis(50));
            mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
        }
        Ok(())
    }
    
    /// 拖拽
    pub fn drag(&self, from: (i32, i32), to: (i32, i32)) -> Result<()> {
        self.move_to(from.0, from.1)?;
        std::thread::sleep(Duration::from_millis(100));
        
        unsafe {
            mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
            std::thread::sleep(Duration::from_millis(100));
        }
        
        // 平滑移动
        let steps = 20;
        for i in 1..=steps {
            let x = from.0 + (to.0 - from.0) * i / steps;
            let y = from.1 + (to.1 - from.1) * i / steps;
            self.move_to(x, y)?;
            std::thread::sleep(Duration::from_millis(20));
        }
        
        unsafe {
            mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
        }
        Ok(())
    }
    
    /// 滚轮
    pub fn scroll(&self, delta: i32) -> Result<()> {
        unsafe {
            mouse_event(MOUSEEVENTF_WHEEL, 0, 0, delta as u32, 0);
        }
        Ok(())
    }
}
```

### 3.2 键盘操作

```rust
use winapi::um::winuser::{
    keybd_event, VkKeyScanA,
    KEYEVENTF_KEYUP, KEYEVENTF_UNICODE,
};

pub struct KeyboardController;

impl KeyboardController {
    /// 按键
    pub fn key_press(&self, key: VirtualKey) -> Result<()> {
        unsafe {
            keybd_event(key as u8, 0, 0, 0);
            std::thread::sleep(Duration::from_millis(50));
            keybd_event(key as u8, 0, KEYEVENTF_KEYUP, 0);
        }
        Ok(())
    }
    
    /// 组合键
    pub fn key_combination(&self, keys: &[VirtualKey]) -> Result<()> {
        // 按下所有键
        for key in keys {
            unsafe {
                keybd_event(*key as u8, 0, 0, 0);
            }
            std::thread::sleep(Duration::from_millis(20));
        }
        
        // 释放所有键（反序）
        for key in keys.iter().rev() {
            unsafe {
                keybd_event(*key as u8, 0, KEYEVENTF_KEYUP, 0);
            }
            std::thread::sleep(Duration::from_millis(20));
        }
        
        Ok(())
    }
    
    /// 输入文本
    pub fn type_text(&self, text: &str) -> Result<()> {
        for ch in text.chars() {
            self.type_char(ch)?;
            std::thread::sleep(Duration::from_millis(30)); // 模拟打字速度
        }
        Ok(())
    }
    
    fn type_char(&self, ch: char) -> Result<()> {
        if ch.is_ascii() {
            // ASCII字符
            let vk = unsafe { VkKeyScanA(ch as i8) };
            let key_code = (vk & 0xFF) as u8;
            let shift = (vk & 0x100) != 0;
            
            if shift {
                unsafe { keybd_event(0x10, 0, 0, 0); } // Shift down
            }
            
            unsafe {
                keybd_event(key_code, 0, 0, 0);
                keybd_event(key_code, 0, KEYEVENTF_KEYUP, 0);
            }
            
            if shift {
                unsafe { keybd_event(0x10, 0, KEYEVENTF_KEYUP, 0); } // Shift up
            }
        } else {
            // Unicode字符
            let mut utf16 = [0u16; 2];
            let len = ch.encode_utf16(&mut utf16).len();
            
            for i in 0..len {
                unsafe {
                    keybd_event(0, 0, KEYEVENTF_UNICODE, utf16[i] as usize);
                    keybd_event(0, 0, KEYEVENTF_UNICODE | KEYEVENTF_KEYUP, utf16[i] as usize);
                }
            }
        }
        
        Ok(())
    }
}
```

## 4. 执行引擎实现

### 4.1 智能等待机制

```rust
pub struct SmartWaiter {
    base_interval: u32,
    retry_interval: u32,
    max_retries: u32,
}

impl SmartWaiter {
    pub fn new() -> Self {
        Self {
            base_interval: 1000,  // 基础间隔1秒
            retry_interval: 1000,  // 重试间隔1秒
            max_retries: 30,       // 最多重试30次
        }
    }
    
    /// 智能等待元素出现
    pub async fn wait_for_element(
        &self,
        matcher: &mut ImageMatcher,
        template: &[u8],
        mode: ExecutionMode,
        recorded_interval: u32,
    ) -> Result<MatchResult> {
        // 首次等待使用录制时的间隔
        tokio::time::sleep(Duration::from_millis(recorded_interval as u64)).await;
        
        let mut retry_count = 0;
        
        loop {
            // 截取当前屏幕
            let screen = capture_current_screen()?;
            
            // 尝试匹配
            if let Some(result) = matcher.match_all(&screen, template).await? {
                return Ok(result);
            }
            
            retry_count += 1;
            
            // 根据模式决定是否继续
            match mode {
                ExecutionMode::Active => {
                    if retry_count >= self.max_retries {
                        return Err(Error::ElementNotFound);
                    }
                }
                ExecutionMode::Auxiliary => {
                    // 辅助模式无限等待
                }
            }
            
            // 等待后重试
            tokio::time::sleep(Duration::from_millis(self.retry_interval as u64)).await;
        }
    }
}
```

### 4.2 执行队列管理（独立双线程架构）

```rust
use tokio::sync::mpsc;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU8, Ordering};

/// 共享执行状态
pub struct SharedExecutionState {
    /// Active模式是否正在运行
    pub active_running: AtomicBool,
    /// 鼠标键盘控制权（0=无，1=Active，2=Auxiliary）
    pub input_owner: AtomicU8,
}

impl SharedExecutionState {
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            active_running: AtomicBool::new(false),
            input_owner: AtomicU8::new(0),
        })
    }
}

/// Active模式执行器（独立线程）
pub struct ActiveExecutor {
    rx: mpsc::Receiver<Script>,
    shared_state: Arc<SharedExecutionState>,
    matcher: ImageMatcher,
    executor: ActionExecutor,
}

impl ActiveExecutor {
    pub fn new(
        rx: mpsc::Receiver<Script>,
        shared_state: Arc<SharedExecutionState>,
    ) -> Self {
        Self {
            rx,
            shared_state,
            matcher: ImageMatcher::new(),
            executor: ActionExecutor::new(),
        }
    }
    
    pub async fn run(mut self) {
        while let Some(script) = self.rx.recv().await {
            // 设置Active运行状态
            self.shared_state.active_running.store(true, Ordering::SeqCst);
            self.shared_state.input_owner.store(1, Ordering::SeqCst);
            
            // 最小化界面
            minimize_window();
            
            // 执行脚本
            if let Err(e) = self.execute_script(script).await {
                eprintln!("Active execution failed: {:?}", e);
            }
            
            // 清除Active运行状态
            self.shared_state.active_running.store(false, Ordering::SeqCst);
            self.shared_state.input_owner.store(0, Ordering::SeqCst);
            
            // 恢复界面
            restore_window();
        }
    }
    
    async fn execute_script(&mut self, script: Script) -> Result<()> {
        for _ in 0..script.loop_count {
            for action in &script.actions {
                // 等待元素出现
                let element = self.wait_for_element(&action.element_image).await?;
                
                // 执行动作（Active模式总是有控制权）
                self.executor.execute(&action.action, element.position)?;
                
                // 智能等待
                tokio::time::sleep(Duration::from_millis(action.interval_ms as u64)).await;
            }
        }
        Ok(())
    }
}

/// Auxiliary模式执行器（独立线程）
pub struct AuxiliaryExecutor {
    rx: mpsc::Receiver<Script>,
    shared_state: Arc<SharedExecutionState>,
    matcher: ImageMatcher,
    executor: ActionExecutor,
}

impl AuxiliaryExecutor {
    pub fn new(
        rx: mpsc::Receiver<Script>,
        shared_state: Arc<SharedExecutionState>,
    ) -> Self {
        Self {
            rx,
            shared_state,
            matcher: ImageMatcher::new(),
            executor: ActionExecutor::new(),
        }
    }
    
    pub async fn run(mut self) {
        while let Some(script) = self.rx.recv().await {
            // 界面保持可见，持续执行
            loop {
                if let Err(e) = self.execute_script_once(&script).await {
                    eprintln!("Auxiliary execution error: {:?}", e);
                    // 辅助模式错误后继续重试
                    tokio::time::sleep(Duration::from_secs(5)).await;
                }
                
                // 检查是否需要继续循环
                if script.loop_count != 999 {  // 999表示无限循环
                    break;
                }
            }
        }
    }
    
    async fn execute_script_once(&mut self, script: &Script) -> Result<()> {
        for action in &script.actions {
            // 等待元素出现
            let element = self.wait_for_element(&action.element_image).await?;
            
            // 检查是否需要避让Active模式
            if self.shared_state.active_running.load(Ordering::Relaxed) {
                // Active正在运行，跳过本次动作但继续识别
                println!("Auxiliary: 检测到Active运行中，暂停动作执行");
                tokio::time::sleep(Duration::from_secs(1)).await;
                continue;  // 跳过本次动作，进入下一个动作
            }
            
            // 尝试获取控制权
            let expected = 0;
            if self.shared_state.input_owner.compare_exchange(
                expected,
                2,  // Auxiliary = 2
                Ordering::SeqCst,
                Ordering::Relaxed,
            ).is_ok() {
                // 获得控制权，执行动作
                self.executor.execute(&action.action, element.position)?;
                
                // 释放控制权
                self.shared_state.input_owner.store(0, Ordering::SeqCst);
            } else {
                // 无法获得控制权，等待
                println!("Auxiliary: 等待控制权...");
                tokio::time::sleep(Duration::from_millis(500)).await;
                continue;
            }
            
            // 智能等待
            tokio::time::sleep(Duration::from_millis(action.interval_ms as u64)).await;
        }
        Ok(())
    }
}

/// 执行管理器 - 启动两个独立执行器
pub struct ExecutionManager {
    active_tx: mpsc::Sender<Script>,
    auxiliary_tx: mpsc::Sender<Script>,
    shared_state: Arc<SharedExecutionState>,
}

impl ExecutionManager {
    pub fn new() -> Self {
        let (active_tx, active_rx) = mpsc::channel(100);
        let (auxiliary_tx, auxiliary_rx) = mpsc::channel(100);
        let shared_state = SharedExecutionState::new();
        
        // 启动Active执行器（独立线程）
        let active_executor = ActiveExecutor::new(active_rx, shared_state.clone());
        tokio::spawn(async move {
            active_executor.run().await;
        });
        
        // 启动Auxiliary执行器（独立线程）
        let auxiliary_executor = AuxiliaryExecutor::new(auxiliary_rx, shared_state.clone());
        tokio::spawn(async move {
            auxiliary_executor.run().await;
        });
        
        Self {
            active_tx,
            auxiliary_tx,
            shared_state,
        }
    }
    
    pub async fn add_script(&self, script: Script, mode: ExecutionMode) -> Result<()> {
        match mode {
            ExecutionMode::Active => {
                self.active_tx.send(script).await?;
            }
            ExecutionMode::Auxiliary => {
                self.auxiliary_tx.send(script).await?;
            }
        }
        Ok(())
    }
    
    pub fn get_status(&self) -> ExecutionStatus {
        ExecutionStatus {
            active_running: self.shared_state.active_running.load(Ordering::Relaxed),
            input_owner: self.shared_state.input_owner.load(Ordering::Relaxed),
        }
    }
}
```

## 5. 性能优化实现

### 5.1 缓存系统

```rust
use lru::LruCache;
use std::sync::Mutex;

pub struct ElementCache {
    cache: Mutex<LruCache<String, Vec<u8>>>,
    max_size: usize,
}

impl ElementCache {
    pub fn new(max_size: usize) -> Self {
        Self {
            cache: Mutex::new(LruCache::new(max_size)),
            max_size,
        }
    }
    
    pub fn get(&self, key: &str) -> Option<Vec<u8>> {
        let mut cache = self.cache.lock().unwrap();
        cache.get(key).cloned()
    }
    
    pub fn put(&self, key: String, value: Vec<u8>) {
        let mut cache = self.cache.lock().unwrap();
        cache.put(key, value);
    }
    
    pub fn clear(&self) {
        let mut cache = self.cache.lock().unwrap();
        cache.clear();
    }
    
    pub fn stats(&self) -> CacheStats {
        let cache = self.cache.lock().unwrap();
        CacheStats {
            size: cache.len(),
            capacity: self.max_size,
            hit_rate: 0.0, // 需要实际统计
        }
    }
}

pub struct CacheStats {
    pub size: usize,
    pub capacity: usize,
    pub hit_rate: f32,
}
```

### 5.2 并行处理

```rust
use rayon::prelude::*;
use tokio::task;

pub struct ParallelProcessor;

impl ParallelProcessor {
    /// 并行处理多个帧
    pub async fn process_frames_parallel(
        &self,
        frames: Vec<RawFrame>,
    ) -> Result<Vec<StandardFrame>> {
        let handles: Vec<_> = frames
            .into_iter()
            .map(|frame| {
                task::spawn_blocking(move || {
                    ImageStandardizer::new().standardize(&frame)
                })
            })
            .collect();
        
        let mut results = Vec::new();
        for handle in handles {
            results.push(handle.await??);
        }
        
        Ok(results)
    }
    
    /// 并行匹配多个模板
    pub fn match_templates_parallel(
        &self,
        screen: &[u8],
        templates: &[Vec<u8>],
    ) -> Vec<Option<MatchResult>> {
        templates
            .par_iter()
            .map(|template| {
                TemplateMatcher::new().match_template(
                    screen,
                    template,
                    (1920, 1080),
                    (100, 100), // 假设模板大小
                )
            })
            .collect()
    }
}
```

## 6. 错误恢复机制

### 6.1 断点续执行

```rust
#[derive(Serialize, Deserialize)]
pub struct ExecutionCheckpoint {
    script_id: String,
    current_step: u32,
    current_loop: u32,
    timestamp: u64,
    state: HashMap<String, String>,
}

pub struct CheckpointManager {
    checkpoint_dir: PathBuf,
}

impl CheckpointManager {
    pub fn save_checkpoint(&self, checkpoint: &ExecutionCheckpoint) -> Result<()> {
        let path = self.checkpoint_dir.join(format!("{}.checkpoint", checkpoint.script_id));
        let data = serde_json::to_string(checkpoint)?;
        fs::write(path, data)?;
        Ok(())
    }
    
    pub fn load_checkpoint(&self, script_id: &str) -> Result<Option<ExecutionCheckpoint>> {
        let path = self.checkpoint_dir.join(format!("{}.checkpoint", script_id));
        
        if !path.exists() {
            return Ok(None);
        }
        
        let data = fs::read_to_string(path)?;
        let checkpoint = serde_json::from_str(&data)?;
        Ok(Some(checkpoint))
    }
    
    pub fn clear_checkpoint(&self, script_id: &str) -> Result<()> {
        let path = self.checkpoint_dir.join(format!("{}.checkpoint", script_id));
        if path.exists() {
            fs::remove_file(path)?;
        }
        Ok(())
    }
}
```

### 6.2 错误重试策略

```rust
pub struct RetryStrategy {
    max_retries: u32,
    backoff: Duration,
}

impl RetryStrategy {
    pub async fn execute_with_retry<F, T, E>(
        &self,
        mut operation: F,
    ) -> Result<T, E>
    where
        F: FnMut() -> Result<T, E>,
        E: std::fmt::Debug,
    {
        let mut retries = 0;
        
        loop {
            match operation() {
                Ok(result) => return Ok(result),
                Err(e) => {
                    retries += 1;
                    
                    if retries >= self.max_retries {
                        eprintln!("Operation failed after {} retries: {:?}", retries, e);
                        return Err(e);
                    }
                    
                    eprintln!("Retry {} after error: {:?}", retries, e);
                    
                    // 指数退避
                    let wait_time = self.backoff * 2u32.pow(retries - 1);
                    tokio::time::sleep(wait_time).await;
                }
            }
        }
    }
}
```

## 7. 测试实现

### 7.1 单元测试示例

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_image_standardization() {
        let raw_frame = RawFrame {
            data: vec![0; 1920 * 1080 * 4],
            resolution: (1920, 1080),
            timestamp: 0,
            frame_index: 0,
        };
        
        let standardizer = ImageStandardizer::new();
        let result = standardizer.standardize(&raw_frame).unwrap();
        
        assert_eq!(result.image_768.len(), 768 * 768 * 4);
        assert_eq!(result.original_resolution, (1920, 1080));
    }
    
    #[test]
    fn test_bbox_cropping() {
        let image_768 = vec![0u8; 768 * 768 * 4];
        let bbox = BoundingBox {
            x: 100,
            y: 100,
            width: 200,
            height: 150,
            confidence: 0.95,
            element_type: Some("button".to_string()),
        };
        
        let cropper = BboxCropper;
        let result = cropper.crop_element(&image_768, &bbox).unwrap();
        
        assert_eq!(result.len(), 200 * 150 * 4);
    }
    
    #[tokio::test]
    async fn test_smart_waiter() {
        let waiter = SmartWaiter::new();
        let mut matcher = ImageMatcher::new();
        let template = vec![0u8; 100 * 100 * 4];
        
        // 模拟测试
        let result = waiter.wait_for_element(
            &mut matcher,
            &template,
            ExecutionMode::Active,
            1000,
        ).await;
        
        // 验证结果
        assert!(result.is_ok() || matches!(result, Err(Error::ElementNotFound)));
    }
}
```

### 7.2 集成测试示例

```rust
#[cfg(test)]
mod integration_tests {
    use super::*;
    
    #[tokio::test]
    async fn test_full_recording_flow() {
        // 1. 开始录制
        let mut recorder = RecordingModule::new();
        recorder.start_recording().unwrap();
        
        // 2. 模拟用户操作
        tokio::time::sleep(Duration::from_secs(2)).await;
        
        // 3. 停止录制
        let data = recorder.stop_recording().unwrap();
        assert!(!data.frames.is_empty());
        assert!(!data.actions.is_empty());
        
        // 4. 标准化
        let standardizer = ImageStandardizer::new();
        let standard_frames = standardizer.standardize_batch(data.frames).unwrap();
        
        // 5. 验证
        for frame in &standard_frames {
            assert_eq!(frame.image_768.len(), 768 * 768 * 4);
        }
    }
    
    #[tokio::test]
    async fn test_script_execution() {
        // 加载测试脚本
        let script = load_test_script();
        
        // 创建执行器
        let mut executor = ExecutionModule::new();
        
        // 执行脚本
        let result = executor.execute_script(script, ExecutionMode::Active).await;
        
        // 验证结果
        assert!(result.is_ok());
    }
}
```

## 8. 部署配置

### 8.1 Cargo.toml配置

```toml
[package]
name = "showforai"
version = "1.0.0"
edition = "2021"

[dependencies]
# 核心依赖
tokio = { version = "1.35", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 图像处理
image = "0.24"
opencv = { version = "0.88", features = ["opencv-4"] }

# Windows API
winapi = { version = "0.3", features = ["winuser", "wingdi"] }
windows = "0.52"

# 跨平台
scrap = "0.5"

# 工具库
rayon = "1.8"
lru = "0.12"
base64 = "0.21"
thiserror = "1.0"

[build-dependencies]
cc = "1.0"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
strip = true
panic = "abort"
```

### 8.2 构建脚本

```bash
#!/bin/bash
# build.sh

# 设置环境变量
export RUST_BACKTRACE=1
export RUSTFLAGS="-C target-cpu=native"

# 清理
cargo clean

# 构建Release版本
cargo build --release

# 运行测试
cargo test --release

# 打包
mkdir -p dist
cp target/release/showforai.exe dist/
cp -r assets dist/

# 创建安装包
# makensis installer.nsi

echo "Build completed successfully!"
```

---

*"实现细节决定产品质量，但架构决定产品生死。"*