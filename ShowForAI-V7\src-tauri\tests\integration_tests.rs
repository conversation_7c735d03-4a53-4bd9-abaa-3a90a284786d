use std::time::Duration;
use tempfile::TempDir;
use tokio::time::sleep;
use serial_test::serial;

use showforai::modules::{
    recording::action_recorder::ActionRecorder,
    image_processing::{
        matcher::ImageMatcher,
        screenshot::ScreenshotCapture,
    },
    execution::active::ActiveExecutor,
};

#[tokio::test]
#[serial]
async fn test_full_recording_workflow() {
    let temp_dir = TempDir::new().unwrap();
    let mut recorder = ActionRecorder::new(temp_dir.path().to_path_buf());
    
    // Start recording
    recorder.start_recording().unwrap();
    assert!(recorder.is_recording());
    
    // Record some actions
    recorder.record_click_action(100, 200, "left").unwrap();
    recorder.record_type_action("Hello, World!").unwrap();
    recorder.record_key_action("Enter", true).unwrap();
    
    sleep(Duration::from_millis(100)).await;
    
    // Stop recording
    let actions = recorder.stop_recording().unwrap();
    assert!(!recorder.is_recording());
    assert_eq!(actions.len(), 3);
    
    // Verify action types
    assert_eq!(actions[0].action_type, "click");
    assert_eq!(actions[1].action_type, "type");
    assert_eq!(actions[2].action_type, "key");
}

#[tokio::test]
async fn test_image_matching_workflow() {
    let matcher = ImageMatcher::new();
    
    // Create test images
    let template = image::ImageBuffer::from_fn(50, 50, |x, y| {
        image::Rgb([((x + y) % 255) as u8, 100, 150])
    });
    
    let screenshot = image::ImageBuffer::from_fn(200, 200, |x, y| {
        if x >= 50 && x < 100 && y >= 50 && y < 100 {
            // Embed template at position (50, 50)
            let tx = x - 50;
            let ty = y - 50;
            image::Rgb([((tx + ty) % 255) as u8, 100, 150])
        } else {
            image::Rgb([0, 0, 0])
        }
    });
    
    // Test matching
    let result = matcher.find_match(&template, &screenshot, 0.8);
    assert!(result.is_some());
    
    let match_result = result.unwrap();
    assert!((match_result.x - 50).abs() < 5); // Allow small tolerance
    assert!((match_result.y - 50).abs() < 5);
    assert!(match_result.confidence > 0.8);
}

#[tokio::test]
async fn test_screenshot_capture() {
    let capture = ScreenshotCapture::new().unwrap();
    
    // Test full screen capture
    let screenshot = capture.capture_full_screen().await;
    assert!(screenshot.is_ok());
    
    let image = screenshot.unwrap();
    assert!(image.width() > 0);
    assert!(image.height() > 0);
}

#[tokio::test]
#[serial]
async fn test_execution_workflow() {
    let temp_dir = TempDir::new().unwrap();
    let executor = ActiveExecutor::new(temp_dir.path().to_path_buf());
    
    // Create test workflow
    let workflow = vec![
        serde_json::json!({
            "type": "click",
            "x": 100,
            "y": 200,
            "timeout": 1000
        }),
        serde_json::json!({
            "type": "wait",
            "duration": 500
        }),
        serde_json::json!({
            "type": "type",
            "text": "Test input",
            "timeout": 1000
        })
    ];
    
    // Test workflow validation
    let validation_result = executor.validate_workflow(&workflow);
    assert!(validation_result.is_ok());
    
    // Note: We can't test actual execution in a headless environment
    // but we can test the setup and validation
}

#[tokio::test]
async fn test_cache_functionality() {
    let matcher = ImageMatcher::new();
    
    let template = image::ImageBuffer::from_fn(50, 50, |x, y| {
        image::Rgb([x as u8, y as u8, 100])
    });
    
    let template_hash = "test_template_hash";
    
    // Test caching
    matcher.cache_template(template_hash, &template);
    
    // Test retrieval
    let cached = matcher.get_cached_template(template_hash);
    assert!(cached.is_some());
    
    let cached_template = cached.unwrap();
    assert_eq!(cached_template.width(), template.width());
    assert_eq!(cached_template.height(), template.height());
}

#[tokio::test]
async fn test_error_handling() {
    let temp_dir = TempDir::new().unwrap();
    let recorder = ActionRecorder::new(temp_dir.path().to_path_buf());
    
    // Test stopping recording when not started
    let result = recorder.stop_recording();
    assert!(result.is_err());
    
    // Test invalid action recording
    let result = recorder.record_click_action(-1, -1, "invalid_button");
    assert!(result.is_err());
}

#[tokio::test]
async fn test_concurrent_operations() {
    let temp_dir = TempDir::new().unwrap();
    let matcher = ImageMatcher::new();
    
    let template = image::ImageBuffer::from_fn(50, 50, |_, _| {
        image::Rgb([100, 100, 100])
    });
    
    // Test concurrent template caching
    let handles: Vec<_> = (0..10)
        .map(|i| {
            let matcher = matcher.clone();
            let template = template.clone();
            tokio::spawn(async move {
                let hash = format!("template_{}", i);
                matcher.cache_template(&hash, &template);
                matcher.get_cached_template(&hash).is_some()
            })
        })
        .collect();
    
    let results = futures::future::join_all(handles).await;
    
    // All operations should succeed
    for result in results {
        assert!(result.unwrap());
    }
}

#[tokio::test]
async fn test_performance_under_load() {
    let matcher = ImageMatcher::new();
    
    let template = image::ImageBuffer::from_fn(20, 20, |x, y| {
        image::Rgb([x as u8, y as u8, 100])
    });
    
    let screenshot = image::ImageBuffer::from_fn(100, 100, |x, y| {
        image::Rgb([x as u8, y as u8, 100])
    });
    
    let start = std::time::Instant::now();
    
    // Perform 100 matching operations
    for _ in 0..100 {
        let _ = matcher.find_match(&template, &screenshot, 0.8);
    }
    
    let duration = start.elapsed();
    
    // Should complete within reasonable time (adjust threshold as needed)
    assert!(duration < Duration::from_secs(10));
}