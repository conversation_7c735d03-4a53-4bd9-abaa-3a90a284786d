package com.showforai.executor.core.error

import com.showforai.executor.data.models.ExecutionLog
import com.showforai.executor.data.models.LogLevel
import kotlinx.coroutines.delay
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 错误处理和恢复机制
 * 
 * 提供智能的错误处理和恢复策略：
 * - 错误分类和分析
 * - 自动重试机制
 * - 错误恢复策略
 * - 错误报告生成
 */
@Singleton
class ErrorHandler @Inject constructor() {
    
    companion object {
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY_BASE_MS = 1000L
        private const val RETRY_DELAY_MULTIPLIER = 2.0
    }
    
    /**
     * 处理执行错误
     */
    suspend fun handleExecutionError(
        error: Throwable,
        stepIndex: Int,
        retryCount: Int = 0
    ): ErrorHandlingResult {
        val errorType = classifyError(error)
        val errorInfo = ErrorInfo(
            type = errorType,
            message = error.message ?: "Unknown error",
            stackTrace = error.stackTraceToString(),
            stepIndex = stepIndex,
            retryCount = retryCount,
            timestamp = System.currentTimeMillis()
        )
        
        Timber.e(error, "Execution error at step $stepIndex (retry $retryCount): ${error.message}")
        
        return when (errorType) {
            ErrorType.NETWORK_ERROR -> handleNetworkError(errorInfo)
            ErrorType.PERMISSION_ERROR -> handlePermissionError(errorInfo)
            ErrorType.TIMEOUT_ERROR -> handleTimeoutError(errorInfo)
            ErrorType.TARGET_NOT_FOUND -> handleTargetNotFoundError(errorInfo)
            ErrorType.ACTION_FAILED -> handleActionFailedError(errorInfo)
            ErrorType.OPENCV_ERROR -> handleOpenCVError(errorInfo)
            ErrorType.OCR_ERROR -> handleOCRError(errorInfo)
            ErrorType.SYSTEM_ERROR -> handleSystemError(errorInfo)
            ErrorType.UNKNOWN_ERROR -> handleUnknownError(errorInfo)
        }
    }
    
    /**
     * 错误分类
     */
    private fun classifyError(error: Throwable): ErrorType {
        return when {
            error is java.net.SocketTimeoutException ||
            error is java.net.ConnectException ||
            error is java.net.UnknownHostException -> ErrorType.NETWORK_ERROR
            
            error is SecurityException ||
            error.message?.contains("permission", ignoreCase = true) == true -> ErrorType.PERMISSION_ERROR
            
            error is java.util.concurrent.TimeoutException ||
            error.message?.contains("timeout", ignoreCase = true) == true -> ErrorType.TIMEOUT_ERROR
            
            error.message?.contains("target not found", ignoreCase = true) == true ||
            error.message?.contains("element not found", ignoreCase = true) == true -> ErrorType.TARGET_NOT_FOUND
            
            error.message?.contains("action failed", ignoreCase = true) == true ||
            error.message?.contains("click failed", ignoreCase = true) == true -> ErrorType.ACTION_FAILED
            
            error.message?.contains("opencv", ignoreCase = true) == true ||
            error is UnsatisfiedLinkError -> ErrorType.OPENCV_ERROR
            
            error.message?.contains("ocr", ignoreCase = true) == true ||
            error.message?.contains("text recognition", ignoreCase = true) == true -> ErrorType.OCR_ERROR
            
            error is OutOfMemoryError ||
            error is IllegalStateException -> ErrorType.SYSTEM_ERROR
            
            else -> ErrorType.UNKNOWN_ERROR
        }
    }
    
    /**
     * 处理网络错误
     */
    private suspend fun handleNetworkError(errorInfo: ErrorInfo): ErrorHandlingResult {
        if (errorInfo.retryCount < MAX_RETRY_ATTEMPTS) {
            val delayMs = calculateRetryDelay(errorInfo.retryCount)
            delay(delayMs)
            
            return ErrorHandlingResult(
                shouldRetry = true,
                shouldContinue = false,
                recoveryAction = RecoveryAction.RETRY_WITH_DELAY,
                errorLog = createErrorLog(errorInfo, "Network error, retrying in ${delayMs}ms"),
                suggestedDelay = delayMs
            )
        }
        
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = false,
            recoveryAction = RecoveryAction.FAIL_EXECUTION,
            errorLog = createErrorLog(errorInfo, "Network error: Max retries exceeded"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理权限错误
     */
    private suspend fun handlePermissionError(errorInfo: ErrorInfo): ErrorHandlingResult {
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = false,
            recoveryAction = RecoveryAction.REQUEST_PERMISSIONS,
            errorLog = createErrorLog(errorInfo, "Permission error: Please grant required permissions"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理超时错误
     */
    private suspend fun handleTimeoutError(errorInfo: ErrorInfo): ErrorHandlingResult {
        if (errorInfo.retryCount < MAX_RETRY_ATTEMPTS) {
            val delayMs = calculateRetryDelay(errorInfo.retryCount)
            delay(delayMs)
            
            return ErrorHandlingResult(
                shouldRetry = true,
                shouldContinue = false,
                recoveryAction = RecoveryAction.RETRY_WITH_LONGER_TIMEOUT,
                errorLog = createErrorLog(errorInfo, "Timeout error, retrying with longer timeout"),
                suggestedDelay = delayMs
            )
        }
        
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = true, // 可以继续执行下一步
            recoveryAction = RecoveryAction.SKIP_STEP,
            errorLog = createErrorLog(errorInfo, "Timeout error: Skipping step after max retries"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理目标未找到错误
     */
    private suspend fun handleTargetNotFoundError(errorInfo: ErrorInfo): ErrorHandlingResult {
        if (errorInfo.retryCount < MAX_RETRY_ATTEMPTS) {
            val delayMs = calculateRetryDelay(errorInfo.retryCount)
            delay(delayMs)
            
            return ErrorHandlingResult(
                shouldRetry = true,
                shouldContinue = false,
                recoveryAction = RecoveryAction.RETRY_WITH_DIFFERENT_STRATEGY,
                errorLog = createErrorLog(errorInfo, "Target not found, trying different location strategy"),
                suggestedDelay = delayMs
            )
        }
        
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = true, // 可以继续执行下一步
            recoveryAction = RecoveryAction.SKIP_STEP,
            errorLog = createErrorLog(errorInfo, "Target not found: Skipping step"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理动作执行失败错误
     */
    private suspend fun handleActionFailedError(errorInfo: ErrorInfo): ErrorHandlingResult {
        if (errorInfo.retryCount < 2) { // 动作失败重试次数较少
            val delayMs = calculateRetryDelay(errorInfo.retryCount)
            delay(delayMs)
            
            return ErrorHandlingResult(
                shouldRetry = true,
                shouldContinue = false,
                recoveryAction = RecoveryAction.RETRY_ACTION,
                errorLog = createErrorLog(errorInfo, "Action failed, retrying"),
                suggestedDelay = delayMs
            )
        }
        
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = true,
            recoveryAction = RecoveryAction.SKIP_STEP,
            errorLog = createErrorLog(errorInfo, "Action failed: Skipping step"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理OpenCV错误
     */
    private suspend fun handleOpenCVError(errorInfo: ErrorInfo): ErrorHandlingResult {
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = true,
            recoveryAction = RecoveryAction.USE_FALLBACK_STRATEGY,
            errorLog = createErrorLog(errorInfo, "OpenCV error: Using fallback location strategy"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理OCR错误
     */
    private suspend fun handleOCRError(errorInfo: ErrorInfo): ErrorHandlingResult {
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = true,
            recoveryAction = RecoveryAction.USE_FALLBACK_STRATEGY,
            errorLog = createErrorLog(errorInfo, "OCR error: Using coordinate fallback"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理系统错误
     */
    private suspend fun handleSystemError(errorInfo: ErrorInfo): ErrorHandlingResult {
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = false,
            recoveryAction = RecoveryAction.FAIL_EXECUTION,
            errorLog = createErrorLog(errorInfo, "System error: Execution cannot continue"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 处理未知错误
     */
    private suspend fun handleUnknownError(errorInfo: ErrorInfo): ErrorHandlingResult {
        if (errorInfo.retryCount < 1) { // 未知错误只重试一次
            val delayMs = calculateRetryDelay(errorInfo.retryCount)
            delay(delayMs)
            
            return ErrorHandlingResult(
                shouldRetry = true,
                shouldContinue = false,
                recoveryAction = RecoveryAction.RETRY_WITH_DELAY,
                errorLog = createErrorLog(errorInfo, "Unknown error, retrying once"),
                suggestedDelay = delayMs
            )
        }
        
        return ErrorHandlingResult(
            shouldRetry = false,
            shouldContinue = false,
            recoveryAction = RecoveryAction.FAIL_EXECUTION,
            errorLog = createErrorLog(errorInfo, "Unknown error: Execution failed"),
            suggestedDelay = 0L
        )
    }
    
    /**
     * 计算重试延迟
     */
    private fun calculateRetryDelay(retryCount: Int): Long {
        return (RETRY_DELAY_BASE_MS * Math.pow(RETRY_DELAY_MULTIPLIER, retryCount.toDouble())).toLong()
    }
    
    /**
     * 创建错误日志
     */
    private fun createErrorLog(errorInfo: ErrorInfo, message: String): ExecutionLog {
        return ExecutionLog(
            timestamp = errorInfo.timestamp,
            level = LogLevel.ERROR,
            stepIndex = errorInfo.stepIndex,
            message = message,
            details = "${errorInfo.type}: ${errorInfo.message}"
        )
    }
}

/**
 * 错误类型
 */
enum class ErrorType {
    NETWORK_ERROR,
    PERMISSION_ERROR,
    TIMEOUT_ERROR,
    TARGET_NOT_FOUND,
    ACTION_FAILED,
    OPENCV_ERROR,
    OCR_ERROR,
    SYSTEM_ERROR,
    UNKNOWN_ERROR
}

/**
 * 恢复动作
 */
enum class RecoveryAction {
    RETRY_WITH_DELAY,
    RETRY_WITH_LONGER_TIMEOUT,
    RETRY_WITH_DIFFERENT_STRATEGY,
    RETRY_ACTION,
    USE_FALLBACK_STRATEGY,
    SKIP_STEP,
    REQUEST_PERMISSIONS,
    FAIL_EXECUTION
}

/**
 * 错误信息
 */
data class ErrorInfo(
    val type: ErrorType,
    val message: String,
    val stackTrace: String,
    val stepIndex: Int,
    val retryCount: Int,
    val timestamp: Long
)

/**
 * 错误处理结果
 */
data class ErrorHandlingResult(
    val shouldRetry: Boolean,
    val shouldContinue: Boolean,
    val recoveryAction: RecoveryAction,
    val errorLog: ExecutionLog,
    val suggestedDelay: Long
)
