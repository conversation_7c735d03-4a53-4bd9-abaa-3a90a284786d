package com.showforai.executor.ui.permissions

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.showforai.executor.ui.theme.DSLExecutorTheme
import com.showforai.executor.utils.PermissionManager
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 权限引导界面
 * 
 * 引导用户授予应用所需的权限：
 * - 存储权限
 * - 悬浮窗权限
 * - 屏幕捕捉权限
 * - 无障碍服务权限
 */
@AndroidEntryPoint
class PermissionGuideActivity : ComponentActivity() {
    
    private val viewModel: PermissionGuideViewModel by viewModels()
    
    // 权限请求结果处理
    private val permissionResultLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        viewModel.handlePermissionResults(permissions)
    }
    
    private val activityResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        viewModel.handleActivityResult(result.resultCode, result.data)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            DSLExecutorTheme {
                PermissionGuideScreen(
                    viewModel = viewModel,
                    onRequestPermission = { permissionType ->
                        requestPermission(permissionType)
                    },
                    onFinish = { finish() }
                )
            }
        }
        
        // 初始检查权限状态
        viewModel.checkPermissions()
    }
    
    private fun requestPermission(permissionType: String) {
        when (permissionType) {
            "storage" -> {
                viewModel.requestStoragePermission(this)
            }
            "overlay" -> {
                viewModel.requestOverlayPermission(this)
            }
            "screen_capture" -> {
                viewModel.requestMediaProjectionPermission(this)
            }
            "accessibility" -> {
                viewModel.requestAccessibilityPermission(this)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionGuideScreen(
    viewModel: PermissionGuideViewModel,
    onRequestPermission: (String) -> Unit,
    onFinish: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("权限设置") },
                navigationIcon = {
                    IconButton(onClick = onFinish) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明卡片
            item {
                IntroductionCard()
            }
            
            // 权限列表
            items(uiState.permissionItems) { item ->
                PermissionItemCard(
                    item = item,
                    onRequestPermission = { onRequestPermission(item.type) }
                )
            }
            
            // 完成按钮
            item {
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onFinish,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = uiState.allPermissionsGranted
                ) {
                    if (uiState.allPermissionsGranted) {
                        Icon(Icons.Default.Check, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("完成设置")
                    } else {
                        Text("需要授予所有权限")
                    }
                }
            }
        }
    }
}

@Composable
fun IntroductionCard() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Security,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "权限说明",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "DSL Executor需要以下权限来正常运行自动化脚本。请按照提示授予相应权限。",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun PermissionItemCard(
    item: PermissionItem,
    onRequestPermission: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        item.icon,
                        contentDescription = null,
                        tint = if (item.granted) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.error
                        }
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Column {
                        Text(
                            text = item.title,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = if (item.granted) "已授予" else "未授予",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (item.granted) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.error
                            }
                        )
                    }
                }
                
                if (item.granted) {
                    Icon(
                        Icons.Default.CheckCircle,
                        contentDescription = "已授予",
                        tint = MaterialTheme.colorScheme.primary
                    )
                } else {
                    Button(
                        onClick = onRequestPermission,
                        size = ButtonDefaults.SmallButtonSize
                    ) {
                        Text("授予")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = item.description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            if (item.steps.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "授权步骤：",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium
                )
                
                item.steps.forEachIndexed { index, step ->
                    Text(
                        text = "${index + 1}. $step",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                    )
                }
            }
        }
    }
}

/**
 * 权限项数据类
 */
data class PermissionItem(
    val type: String,
    val title: String,
    val description: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val granted: Boolean,
    val steps: List<String> = emptyList()
)
