package com.showforai.executor.ui.main

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.showforai.executor.data.models.ExecutionStatus
import com.showforai.executor.data.models.PermissionStatus
import com.showforai.executor.ui.execution.ExecutionActivity
import com.showforai.executor.ui.permissions.PermissionGuideActivity
import com.showforai.executor.ui.settings.SettingsActivity
import com.showforai.executor.ui.theme.DSLExecutorTheme
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 主界面Activity
 * 
 * 功能包括：
 * - 显示应用状态和权限状态
 * - 脚本管理和选择
 * - 执行控制
 * - 设置入口
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    private val viewModel: MainViewModel by viewModels()
    
    // 文件选择器
    private val scriptPickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { viewModel.loadScript(it) }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            DSLExecutorTheme {
                MainScreen(
                    viewModel = viewModel,
                    onPickScript = { scriptPickerLauncher.launch("application/json") },
                    onNavigateToPermissions = { startPermissionGuide() },
                    onNavigateToSettings = { startSettings() },
                    onNavigateToExecution = { startExecution() }
                )
            }
        }
        
        // 检查权限状态
        viewModel.checkPermissions()
    }
    
    private fun startPermissionGuide() {
        val intent = Intent(this, PermissionGuideActivity::class.java)
        startActivity(intent)
    }
    
    private fun startSettings() {
        val intent = Intent(this, SettingsActivity::class.java)
        startActivity(intent)
    }
    
    private fun startExecution() {
        val intent = Intent(this, ExecutionActivity::class.java)
        startActivity(intent)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onPickScript: () -> Unit,
    onNavigateToPermissions: () -> Unit,
    onNavigateToSettings: () -> Unit,
    onNavigateToExecution: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("DSL Executor") },
                actions = {
                    IconButton(onClick = onNavigateToSettings) {
                        Icon(Icons.Default.Settings, contentDescription = "Settings")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 权限状态卡片
            item {
                PermissionStatusCard(
                    permissionStatus = uiState.permissionStatus,
                    onRequestPermissions = onNavigateToPermissions
                )
            }
            
            // 脚本管理卡片
            item {
                ScriptManagementCard(
                    currentScript = uiState.currentScript?.metadata?.description,
                    onPickScript = onPickScript,
                    onClearScript = { viewModel.clearScript() }
                )
            }
            
            // 执行控制卡片
            item {
                ExecutionControlCard(
                    executionStatus = uiState.executionStatus,
                    canExecute = uiState.canExecute,
                    onStartExecution = onNavigateToExecution,
                    onStopExecution = { viewModel.stopExecution() }
                )
            }
            
            // 最近执行日志
            if (uiState.recentLogs.isNotEmpty()) {
                item {
                    RecentLogsCard(logs = uiState.recentLogs)
                }
            }
        }
    }
}

@Composable
fun PermissionStatusCard(
    permissionStatus: PermissionStatus,
    onRequestPermissions: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "权限状态",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                if (permissionStatus.allGranted) {
                    Icon(
                        Icons.Default.CheckCircle,
                        contentDescription = "All permissions granted",
                        tint = MaterialTheme.colorScheme.primary
                    )
                } else {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = "Permissions needed",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            PermissionItem("屏幕捕捉", permissionStatus.screenCapture)
            PermissionItem("无障碍服务", permissionStatus.accessibility)
            PermissionItem("存储访问", permissionStatus.storage)
            PermissionItem("悬浮窗", permissionStatus.overlay)
            
            if (!permissionStatus.allGranted) {
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onRequestPermissions,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("授予权限")
                }
            }
        }
    }
}

@Composable
fun PermissionItem(name: String, granted: Boolean) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = name)
        Icon(
            if (granted) Icons.Default.Check else Icons.Default.Close,
            contentDescription = if (granted) "Granted" else "Not granted",
            tint = if (granted) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
        )
    }
}

@Composable
fun ScriptManagementCard(
    currentScript: String?,
    onPickScript: () -> Unit,
    onClearScript: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "脚本管理",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (currentScript != null) {
                Text(
                    text = "当前脚本: $currentScript",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = onPickScript,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("更换脚本")
                    }
                    
                    OutlinedButton(
                        onClick = onClearScript,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("清除脚本")
                    }
                }
            } else {
                Text(
                    text = "未选择脚本",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Button(
                    onClick = onPickScript,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("选择脚本")
                }
            }
        }
    }
}

@Composable
fun ExecutionControlCard(
    executionStatus: ExecutionStatus,
    canExecute: Boolean,
    onStartExecution: () -> Unit,
    onStopExecution: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "执行控制",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "状态: ${getStatusText(executionStatus)}",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            when (executionStatus) {
                ExecutionStatus.IDLE -> {
                    Button(
                        onClick = onStartExecution,
                        enabled = canExecute,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.PlayArrow, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("开始执行")
                    }
                }
                ExecutionStatus.RUNNING, ExecutionStatus.PAUSED -> {
                    Button(
                        onClick = onStopExecution,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Icon(Icons.Default.Stop, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("停止执行")
                    }
                }
                else -> {
                    // 其他状态显示状态信息
                }
            }
        }
    }
}

@Composable
fun RecentLogsCard(logs: List<String>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "最近日志",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            logs.take(5).forEach { log ->
                Text(
                    text = log,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

private fun getStatusText(status: ExecutionStatus): String {
    return when (status) {
        ExecutionStatus.IDLE -> "空闲"
        ExecutionStatus.PREPARING -> "准备中"
        ExecutionStatus.RUNNING -> "执行中"
        ExecutionStatus.PAUSED -> "已暂停"
        ExecutionStatus.COMPLETED -> "已完成"
        ExecutionStatus.FAILED -> "执行失败"
        ExecutionStatus.CANCELLED -> "已取消"
    }
}
