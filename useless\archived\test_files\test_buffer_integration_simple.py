"""Simple test for CircularFrameBuffer integration."""

import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_circular_buffer():
    """Test the CircularFrameBuffer directly."""
    from showforai.buffer_manager import CircularFrameBuffer
    
    print("\n=== Testing CircularFrameBuffer ===\n")
    
    # Create buffer
    buffer = CircularFrameBuffer(max_frames=15, fps=10)
    print(f"✓ Created CircularFrameBuffer with 15 frames at 10 FPS")
    
    # Start capture
    buffer.start_capture()
    print("✓ Started frame capture")
    
    # Let it capture frames for 2 seconds (should capture ~20 frames)
    print("\nCapturing frames for 2 seconds...")
    time.sleep(2)
    
    # Get buffer statistics
    stats = buffer.get_buffer_stats()
    print(f"\nBuffer Statistics:")
    print(f"  Buffer size: {stats['buffer_size']}/{stats['max_frames']}")
    print(f"  Total frames captured: {stats['total_captured']}")
    print(f"  Is capturing: {stats['is_capturing']}")
    print(f"  Target FPS: {stats['fps']}")
    
    if stats['buffer_size'] > 0:
        print(f"  Oldest frame: #{stats['oldest_frame_number']}")
        print(f"  Newest frame: #{stats['newest_frame_number']}")
        print(f"  Time span: {stats['time_span']:.2f} seconds")
    
    # Test frame retrieval - get frame from 0.3 seconds before
    print("\n=== Testing Frame Retrieval ===")
    
    click_time = time.perf_counter()
    frame = buffer.get_frame_before_click(click_time, seconds_before=0.3)
    
    if frame:
        time_diff = click_time - frame['timestamp']
        print(f"✓ Retrieved frame from {time_diff:.3f} seconds before click")
        print(f"  Frame number: {frame['frame_number']}")
        print(f"  Target was 0.3s, actual: {time_diff:.3f}s")
        
        # The frame should be approximately 0.3 seconds old
        if 0.2 <= time_diff <= 0.4:
            print("  ✓ Frame timing is within acceptable range (0.2-0.4s)")
        else:
            print(f"  ⚠ Frame timing is outside expected range")
    else:
        print("✗ No frame available")
    
    # Test multiple frame retrieval
    print("\n=== Testing Multiple Frame Retrieval ===")
    frames = buffer.get_frames_before_click(click_time, num_frames=3)
    print(f"Retrieved {len(frames)} frames before click")
    
    for i, frame_data in enumerate(frames):
        time_diff = click_time - frame_data['timestamp']
        print(f"  Frame {i+1}: #{frame_data['frame_number']} ({time_diff:.3f}s ago)")
    
    # Continue capturing for another second
    print("\n=== Testing Circular Buffer Behavior ===")
    initial_total = stats['total_captured']
    time.sleep(1)
    
    new_stats = buffer.get_buffer_stats()
    new_frames = new_stats['total_captured'] - initial_total
    print(f"Captured {new_frames} more frames in 1 second")
    print(f"Buffer still contains {new_stats['buffer_size']} frames (max: {new_stats['max_frames']})")
    
    if new_stats['buffer_size'] == new_stats['max_frames']:
        print("✓ Buffer is at maximum capacity (circular behavior active)")
    
    # Stop capture
    buffer.stop_capture()
    print("\n✓ Stopped frame capture")
    
    # Final stats
    final_stats = buffer.get_buffer_stats()
    print(f"\nFinal Statistics:")
    print(f"  Total frames captured: {final_stats['total_captured']}")
    print(f"  Expected frames (3s @ 10fps): ~30")
    print(f"  Buffer maintained: {final_stats['buffer_size']} frames")
    
    # Verify the buffer maintained exactly 15 frames
    if final_stats['buffer_size'] == 15 and final_stats['total_captured'] >= 30:
        print("\n✓ Circular buffer correctly maintained 15 frames while capturing more")
    
    print("\n=== Test Complete ===")
    return True

def test_frame_buffer_manager():
    """Test the FrameBufferManager."""
    from showforai.buffer_manager import FrameBufferManager
    
    print("\n\n=== Testing FrameBufferManager ===\n")
    
    # Create manager
    manager = FrameBufferManager(buffer_size=15)
    print(f"✓ Created FrameBufferManager with buffer size 15")
    
    # Add some dummy frames
    print("\nAdding test frames...")
    for i in range(20):
        # Create a minimal valid PNG as dummy data
        dummy_png = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x0cIDATx\x9cc\xf8\x0f\x00\x00\x01\x01\x00\x05\x18\xd8+E\x00\x00\x00\x00IEND\xaeB`\x82'
        frame_num = manager.add_frame(dummy_png, resolution=(1920, 1080))
        if i % 5 == 0:
            print(f"  Added frame {frame_num}")
        time.sleep(0.01)  # Small delay to create time differences
    
    # Get statistics
    stats = manager.get_statistics()
    print(f"\nManager Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Test frame retrieval
    frame_before = manager.get_frame_before_click(offset=3)
    if frame_before:
        print(f"\n✓ Retrieved frame from 3 frames back: #{frame_before.frame_number}")
    
    latest = manager.get_latest_frame()
    if latest:
        print(f"✓ Latest frame: #{latest.frame_number}")
    
    oldest = manager.get_oldest_frame()
    if oldest:
        print(f"✓ Oldest frame: #{oldest.frame_number}")
    
    print("\n✓ FrameBufferManager test complete")
    return True

if __name__ == "__main__":
    success = True
    
    try:
        # Test CircularFrameBuffer
        if not test_circular_buffer():
            success = False
    except Exception as e:
        print(f"\n✗ CircularFrameBuffer test failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    try:
        # Test FrameBufferManager
        if not test_frame_buffer_manager():
            success = False
    except Exception as e:
        print(f"\n✗ FrameBufferManager test failed: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    if success:
        print("\n\n✅ All buffer integration tests passed!")
    else:
        print("\n\n❌ Some tests failed")
        sys.exit(1)