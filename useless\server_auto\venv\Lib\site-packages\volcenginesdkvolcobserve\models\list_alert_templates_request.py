# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAlertTemplatesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'namespaces': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'template_ids': 'list[str]'
    }

    attribute_map = {
        'name': 'Name',
        'namespaces': 'Namespaces',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'template_ids': 'TemplateIds'
    }

    def __init__(self, name=None, namespaces=None, page_number=None, page_size=None, template_ids=None, _configuration=None):  # noqa: E501
        """ListAlertTemplatesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._namespaces = None
        self._page_number = None
        self._page_size = None
        self._template_ids = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if namespaces is not None:
            self.namespaces = namespaces
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if template_ids is not None:
            self.template_ids = template_ids

    @property
    def name(self):
        """Gets the name of this ListAlertTemplatesRequest.  # noqa: E501


        :return: The name of this ListAlertTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListAlertTemplatesRequest.


        :param name: The name of this ListAlertTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def namespaces(self):
        """Gets the namespaces of this ListAlertTemplatesRequest.  # noqa: E501


        :return: The namespaces of this ListAlertTemplatesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._namespaces

    @namespaces.setter
    def namespaces(self, namespaces):
        """Sets the namespaces of this ListAlertTemplatesRequest.


        :param namespaces: The namespaces of this ListAlertTemplatesRequest.  # noqa: E501
        :type: list[str]
        """

        self._namespaces = namespaces

    @property
    def page_number(self):
        """Gets the page_number of this ListAlertTemplatesRequest.  # noqa: E501


        :return: The page_number of this ListAlertTemplatesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListAlertTemplatesRequest.


        :param page_number: The page_number of this ListAlertTemplatesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListAlertTemplatesRequest.  # noqa: E501


        :return: The page_size of this ListAlertTemplatesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListAlertTemplatesRequest.


        :param page_size: The page_size of this ListAlertTemplatesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def template_ids(self):
        """Gets the template_ids of this ListAlertTemplatesRequest.  # noqa: E501


        :return: The template_ids of this ListAlertTemplatesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._template_ids

    @template_ids.setter
    def template_ids(self, template_ids):
        """Sets the template_ids of this ListAlertTemplatesRequest.


        :param template_ids: The template_ids of this ListAlertTemplatesRequest.  # noqa: E501
        :type: list[str]
        """

        self._template_ids = template_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAlertTemplatesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAlertTemplatesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAlertTemplatesRequest):
            return True

        return self.to_dict() != other.to_dict()
