# ShowForAI 测试策略文档

## 概述

本文档定义了ShowForAI项目的全面测试策略，确保通过TDD方法开发出高质量、可靠的软件。

## 测试金字塔

```
                /\
               /  \
              / E2E \       (5%)
             /______\
            /        \
           / 集成测试  \     (20%)
          /____________\
         /              \
        /   单元测试      \   (75%)
       /__________________\
```

## 测试类型定义

### 1. 单元测试 (Unit Tests)

#### 定义
测试单个函数、方法或类的行为，与外部依赖隔离。

#### 特征
- 执行速度快（< 1ms）
- 完全隔离，使用mock/stub
- 高覆盖率（> 90%）
- 失败时能精确定位问题

#### 示例
```python
def test_ring_buffer_push():
    """测试环形缓冲区的push操作"""
    buffer = RingBuffer(size=3)
    buffer.push("item1")
    assert buffer.size() == 1
    assert buffer.peek() == "item1"
```

#### 测试工具
- **框架**: pytest
- **Mock**: unittest.mock, pytest-mock
- **覆盖率**: pytest-cov
- **参数化**: pytest.mark.parametrize

### 2. 集成测试 (Integration Tests)

#### 定义
测试多个组件之间的交互，验证接口契约和数据流。

#### 特征
- 执行速度中等（< 100ms）
- 真实组件，模拟外部服务
- 测试主要的集成点
- 验证组件间通信

#### 示例
```python
def test_recorder_with_screenshot_thread():
    """测试录制器与截图线程的集成"""
    recorder = Recorder()
    recorder.start()
    time.sleep(0.1)
    
    # 验证截图线程正在工作
    assert recorder.screenshot_thread.is_alive()
    assert recorder.buffer.size() > 0
    
    recorder.stop()
```

#### 测试工具
- **数据库**: pytest-postgresql
- **网络**: responses, httpretty
- **文件系统**: pytest-fs
- **时间**: freezegun

### 3. 端到端测试 (E2E Tests)

#### 定义
测试完整的用户场景，从UI到后端的全链路。

#### 特征
- 执行速度慢（秒级）
- 使用真实环境
- 覆盖关键用户路径
- 验证系统整体行为

#### 示例
```python
def test_record_and_playback_scenario():
    """测试完整的录制和回放场景"""
    app = ShowForAIApp()
    app.start()
    
    # 录制操作
    app.start_recording()
    simulate_user_actions()
    script = app.stop_recording()
    
    # 回放验证
    app.execute_script(script)
    assert verify_actions_executed()
```

#### 测试工具
- **UI自动化**: pytest-qt (PySide6)
- **浏览器测试**: Selenium, Playwright
- **API测试**: pytest-httpx
- **性能测试**: locust

## 测试环境策略

### 1. 本地开发环境

#### 配置
```yaml
test_env:
  python: "3.9+"
  dependencies: "requirements-dev.txt"
  database: "sqlite::memory:"
  mocks:
    - ai_service: true
    - file_system: tmpdir
```

#### 快速测试命令
```bash
# 运行快速单元测试
pytest tests/unit -m "not slow"

# 运行特定模块测试
pytest tests/unit/test_recorder.py

# 带覆盖率运行
pytest --cov=showforai --cov-report=html
```

### 2. CI/CD环境

#### 测试矩阵
```yaml
strategy:
  matrix:
    os: [ubuntu-latest, windows-latest, macos-latest]
    python: ["3.9", "3.10", "3.11"]
```

#### 测试阶段
1. **Lint & Format**: 代码质量检查
2. **Unit Tests**: 快速反馈
3. **Integration Tests**: 组件验证
4. **E2E Tests**: 完整场景
5. **Performance Tests**: 性能基准

### 3. 预生产环境

#### 特点
- 与生产环境相似的配置
- 真实的外部服务集成
- 性能和压力测试
- 安全扫描

## Mock策略

### 1. 外部服务Mock

#### AI服务Mock
```python
class MockAIService:
    """火山引擎AI服务Mock"""
    
    def recognize_element(self, image, click_pos):
        # 返回预定义的识别结果
        return {
            "element_type": "button",
            "bounds": {"x": 100, "y": 100, "w": 80, "h": 30},
            "confidence": 0.95
        }
```

#### 文件系统Mock
```python
@pytest.fixture
def mock_fs(tmp_path):
    """模拟文件系统"""
    scripts_dir = tmp_path / "scripts"
    scripts_dir.mkdir()
    return scripts_dir
```

### 2. 硬件Mock

#### 鼠标/键盘Mock
```python
class MockMouse:
    """模拟鼠标操作"""
    def __init__(self):
        self.clicks = []
        
    def click(self, x, y, button='left'):
        self.clicks.append((x, y, button))
```

#### 屏幕Mock
```python
class MockScreen:
    """模拟屏幕截图"""
    def __init__(self, width=1920, height=1080):
        self.width = width
        self.height = height
        
    def grab(self, region=None):
        # 返回模拟的截图数据
        return self._generate_test_image(region)
```

## 测试数据管理

### 1. 测试固件 (Fixtures)

#### 基础固件
```python
@pytest.fixture
def sample_script():
    """示例脚本数据"""
    return Script(
        name="测试脚本",
        steps=[
            ClickStep(x=100, y=100),
            InputStep(text="Hello"),
            WaitStep(duration=1.0)
        ]
    )

@pytest.fixture
def mock_config():
    """测试配置"""
    return Config({
        "recording": {"fps": 30},
        "execution": {"speed": 1.0}
    })
```

#### 参数化固件
```python
@pytest.fixture(params=[
    {"os": "windows", "dpi": 1.0},
    {"os": "windows", "dpi": 1.5},
    {"os": "macos", "dpi": 2.0}
])
def platform_config(request):
    """多平台配置"""
    return request.param
```

### 2. 测试数据文件

#### 目录结构
```
tests/
├── fixtures/
│   ├── scripts/        # 测试脚本
│   ├── images/         # 测试图片
│   └── sessions/       # 测试会话
├── mocks/
│   ├── api_responses/  # API响应
│   └── events/         # 事件数据
└── golden/            # 黄金标准文件
```

## 性能测试策略

### 1. 基准测试

#### 录制性能
```python
@pytest.mark.benchmark
def test_recording_performance(benchmark):
    """录制性能基准测试"""
    recorder = Recorder()
    
    def record_session():
        recorder.start()
        # 模拟1分钟录制
        simulate_user_activity(duration=60)
        recorder.stop()
    
    result = benchmark(record_session)
    assert result.stats.mean < 1.0  # 平均时间小于1秒
```

#### 执行性能
```python
@pytest.mark.benchmark
def test_execution_performance(benchmark):
    """执行性能基准测试"""
    executor = Executor()
    script = load_test_script("complex_script.json")
    
    result = benchmark(executor.execute, script)
    assert result.stats.max < 0.1  # 最大延迟小于100ms
```

### 2. 压力测试

#### 内存压力
```python
def test_memory_under_load():
    """内存压力测试"""
    recorder = Recorder()
    initial_memory = get_memory_usage()
    
    # 运行10分钟
    recorder.start()
    time.sleep(600)
    recorder.stop()
    
    final_memory = get_memory_usage()
    assert final_memory - initial_memory < 50 * 1024 * 1024  # 小于50MB增长
```

#### CPU压力
```python
def test_cpu_usage():
    """CPU使用率测试"""
    monitor = CPUMonitor()
    recorder = Recorder()
    
    recorder.start()
    monitor.start()
    time.sleep(60)
    
    avg_cpu = monitor.get_average()
    assert avg_cpu < 10  # 平均CPU使用率小于10%
```

## 测试质量指标

### 1. 覆盖率目标

| 模块类型 | 目标覆盖率 | 说明 |
|---------|-----------|------|
| 核心业务逻辑 | > 95% | 录制、执行引擎 |
| UI组件 | > 80% | 界面交互逻辑 |
| 工具函数 | > 90% | 通用工具类 |
| 集成代码 | > 70% | 第三方集成 |

### 2. 测试稳定性

#### 不稳定测试处理
```python
@pytest.mark.flaky(reruns=3, reruns_delay=1)
def test_window_detection():
    """可能不稳定的窗口检测测试"""
    # 最多重试3次，每次延迟1秒
    pass
```

#### 隔离策略
- 每个测试独立的临时目录
- 测试后自动清理资源
- 并行测试时的进程隔离

### 3. 测试性能

#### 目标
- 单元测试套件: < 30秒
- 集成测试套件: < 5分钟
- E2E测试套件: < 15分钟

#### 优化策略
- 并行执行测试
- 智能测试选择
- 缓存测试依赖
- 按需运行E2E测试

## 测试报告

### 1. 覆盖率报告

#### HTML报告
```bash
pytest --cov=showforai --cov-report=html
# 生成 htmlcov/index.html
```

#### 控制台报告
```bash
pytest --cov=showforai --cov-report=term-missing
```

### 2. 测试结果报告

#### JUnit XML
```bash
pytest --junit-xml=report.xml
```

#### Allure报告
```bash
pytest --alluredir=allure-results
allure generate allure-results -o allure-report
```

## 持续改进

### 1. 测试审查

- 每个PR必须包含测试
- 测试代码同样需要code review
- 定期审查测试覆盖率
- 识别和消除冗余测试

### 2. 测试重构

- 提取通用测试工具
- 优化测试执行时间
- 改进测试可读性
- 更新过时的测试

### 3. 测试文档

- 为复杂测试添加注释
- 维护测试最佳实践
- 记录已知问题和解决方案
- 分享测试经验

## 测试检查清单

### PR提交前
- [ ] 所有新代码都有对应测试
- [ ] 测试覆盖率达到目标
- [ ] 没有被跳过的测试
- [ ] 测试在本地通过
- [ ] 测试命名清晰明确

### 发布前
- [ ] 完整测试套件通过
- [ ] 性能测试达标
- [ ] 跨平台测试通过
- [ ] 无已知的不稳定测试
- [ ] 测试报告已生成

---

*本文档是活文档，将根据项目发展持续更新*