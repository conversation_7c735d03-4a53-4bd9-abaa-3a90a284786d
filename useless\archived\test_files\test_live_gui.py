"""
实时测试GUI网络状态
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QPushButton
from PyQt6.QtCore import QTimer
from showforai.recorder.gui import RecorderWindow
from showforai.config import Config
from loguru import logger

def main():
    app = QApplication([])
    config = Config()
    window = RecorderWindow(config)
    
    # 添加测试按钮
    test_button = QPushButton("测试网络状态", window)
    test_button.move(10, 10)
    test_button.clicked.connect(lambda: test_network_status(window))
    test_button.show()
    
    # 初始检查
    print("="*60)
    print("初始状态检查")
    print("="*60)
    test_network_status(window)
    
    # 定时检查
    timer = QTimer()
    timer.timeout.connect(lambda: periodic_check(window))
    timer.start(10000)  # 每10秒检查一次
    
    window.show()
    sys.exit(app.exec())

def test_network_status(window):
    """测试网络状态"""
    print(f"\n时间: {__import__('datetime').datetime.now()}")
    print(f"offline_manager id: {id(window.offline_manager)}")
    print(f"offline_manager._is_online: {window.offline_manager._is_online}")
    print(f"offline_manager.is_online(): {window.offline_manager.is_online()}")
    print(f"offline_manager.is_recording_allowed(): {window.offline_manager.is_recording_allowed()}")
    print(f"offline_warning.isHidden(): {window.offline_warning.isHidden()}")
    print(f"record_button.isEnabled(): {window.record_button.isEnabled()}")
    print(f"recorder.get_state(): {window.recorder.get_state()}")
    
    # 测试点击逻辑
    from showforai.recorder.recorder import RecorderState
    if window.recorder.get_state() == RecorderState.IDLE:
        is_allowed = window.offline_manager.is_recording_allowed()
        print(f"点击录制按钮将会: {'开始录制' if is_allowed else '显示离线弹窗'}")

def periodic_check(window):
    """定期检查"""
    print("\n[定期检查]", end=" ")
    print(f"online={window.offline_manager.is_online()}", end=" ")
    print(f"warning_hidden={window.offline_warning.isHidden()}", end=" ")
    print(f"button_enabled={window.record_button.isEnabled()}")

if __name__ == "__main__":
    main()