"""
测试网络状态更新
"""

import sys
import os
import time
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.sync.offline_manager import get_offline_manager

def main():
    print("测试网络状态更新...")
    
    # 获取离线管理器
    manager = get_offline_manager()
    
    # 检查当前状态
    print(f"当前网络状态: {manager.is_online()}")
    print(f"录制是否允许: {manager.is_recording_allowed()}")
    
    # 强制刷新网络状态
    print("\n强制刷新网络状态...")
    old_status = manager._is_online
    manager._is_online = manager._check_connectivity()
    manager._last_online_check = datetime.now()
    
    print(f"刷新后网络状态: {manager._is_online}")
    
    # 如果状态改变，触发回调
    if old_status != manager._is_online:
        print("状态已改变，触发回调...")
        manager._notify_status_change(manager._is_online)
    
    # 再次检查
    print(f"\n最终网络状态: {manager.is_online()}")
    print(f"录制是否允许: {manager.is_recording_allowed()}")
    
    # 显示回调数量
    print(f"\n注册的回调数量: {len(manager._status_callbacks)}")

if __name__ == "__main__":
    from datetime import datetime
    main()