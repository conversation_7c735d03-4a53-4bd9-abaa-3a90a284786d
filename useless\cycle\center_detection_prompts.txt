
==================================================
Prompt for with_cross:
==================================================

There is a small RED CROSS MARK at the exact center of this image indicating the click position.
Detect the UI element under this cross mark.

Instructions:
1. The cross mark itself is NOT the target - it's just a position indicator
2. Find the actual UI element beneath/around the cross mark
3. Return the complete bounding box of that UI element
4. The cross mark is at (500, 500) in 0-1000 coordinates

Output format:
{
  "boundingBox": [y_min, x_min, y_max, x_max],
  "elementDescription": "what this element is",
  "textContent": "any visible text"
}



==================================================
Prompt for with_dot:
==================================================

There is a small GREEN DOT at the center of this image marking the click position.
Find the UI element at this position (ignoring the dot itself).

The dot is at coordinates (500, 500) in the 0-1000 system.
Return the bounding box of the clicked element.



==================================================
Prompt for no_mark:
==================================================

IMPORTANT: The user clicked at the EXACT CENTER of this image.
The click position is at coordinates (500, 500) in the 0-1000 normalized coordinate system.

Task: Detect ONLY the UI element at the center of the image that was clicked.


