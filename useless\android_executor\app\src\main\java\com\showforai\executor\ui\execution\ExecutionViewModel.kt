package com.showforai.executor.ui.execution

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.showforai.executor.core.engine.DSLExecutionEngine
import com.showforai.executor.data.models.*
import com.showforai.executor.data.repositories.ScriptRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 执行监控ViewModel
 * 
 * 管理脚本执行的状态和控制：
 * - 执行状态监控
 * - 执行控制（开始/暂停/停止）
 * - 执行日志管理
 * - 执行进度跟踪
 */
@HiltViewModel
class ExecutionViewModel @Inject constructor(
    private val executionEngine: DSLExecutionEngine,
    private val scriptRepository: ScriptRepository
) : ViewModel() {
    
    // 当前脚本
    private val _currentScript = MutableStateFlow<DSLScript?>(null)
    val currentScript: StateFlow<DSLScript?> = _currentScript.asStateFlow()
    
    // 执行日志
    private val _executionLogs = MutableStateFlow<List<ExecutionLog>>(emptyList())
    val executionLogs: StateFlow<List<ExecutionLog>> = _executionLogs.asStateFlow()
    
    // 日志展开状态
    private val _isLogExpanded = MutableStateFlow(false)
    val isLogExpanded: StateFlow<Boolean> = _isLogExpanded.asStateFlow()
    
    // 执行开始时间
    private val _executionStartTime = MutableStateFlow(0L)
    
    // UI状态
    val uiState: StateFlow<ExecutionUiState> = combine(
        currentScript,
        executionEngine.executionStatus,
        executionEngine.executionProgress,
        executionLogs,
        isLogExpanded,
        _executionStartTime
    ) { script, status, progress, logs, logExpanded, startTime ->
        val currentTime = System.currentTimeMillis()
        val executionTime = if (startTime > 0 && status == ExecutionStatus.RUNNING) {
            currentTime - startTime
        } else {
            progress.startTime.let { if (it > 0) currentTime - it else 0L }
        }
        
        ExecutionUiState(
            scriptName = script?.metadata?.description ?: "未知脚本",
            scriptDescription = script?.metadata?.sessionId,
            executionStatus = status,
            currentStep = progress.currentStep,
            totalSteps = progress.totalSteps,
            currentStepDescription = progress.currentStepDescription,
            executionTime = executionTime,
            logs = logs,
            isLogExpanded = logExpanded
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = ExecutionUiState()
    )
    
    init {
        // 监听执行日志
        viewModelScope.launch {
            executionEngine.executionLogs.collect { log ->
                val currentLogs = _executionLogs.value.toMutableList()
                currentLogs.add(0, log) // 新日志添加到顶部
                
                // 保持最近100条日志
                if (currentLogs.size > 100) {
                    currentLogs.removeAt(currentLogs.size - 1)
                }
                
                _executionLogs.value = currentLogs
            }
        }
        
        // 监听执行状态变化
        viewModelScope.launch {
            executionEngine.executionStatus.collect { status ->
                when (status) {
                    ExecutionStatus.RUNNING -> {
                        if (_executionStartTime.value == 0L) {
                            _executionStartTime.value = System.currentTimeMillis()
                        }
                    }
                    ExecutionStatus.COMPLETED, ExecutionStatus.FAILED, ExecutionStatus.CANCELLED -> {
                        // 执行结束，记录总时间
                        Timber.i("Execution finished with status: $status")
                    }
                    else -> {
                        // 其他状态
                    }
                }
            }
        }
    }
    
    /**
     * 加载指定脚本
     */
    fun loadScript(scriptId: String) {
        viewModelScope.launch {
            try {
                // 这里应该根据scriptId加载脚本
                // 暂时使用文件路径加载
                val script = scriptRepository.loadScriptFromFile(scriptId)
                if (script != null) {
                    _currentScript.value = script
                    clearLogs()
                    Timber.i("Script loaded for execution: ${script.metadata?.sessionId}")
                } else {
                    Timber.w("Failed to load script: $scriptId")
                }
            } catch (e: Exception) {
                Timber.e(e, "Error loading script: $scriptId")
            }
        }
    }
    
    /**
     * 使用当前已加载的脚本
     */
    fun useCurrentScript() {
        // 这里可以从其他地方获取当前脚本
        // 例如从SharedPreferences或全局状态
        Timber.d("Using current script for execution")
    }
    
    /**
     * 开始执行脚本
     */
    fun startExecution() {
        val script = _currentScript.value
        if (script == null) {
            Timber.w("No script to execute")
            return
        }
        
        viewModelScope.launch {
            try {
                clearLogs()
                _executionStartTime.value = System.currentTimeMillis()
                
                Timber.i("Starting script execution")
                val result = executionEngine.executeScript(script)
                
                if (result.success) {
                    Timber.i("Script execution completed successfully")
                } else {
                    Timber.w("Script execution failed: ${result.errorMessage}")
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Script execution error")
            }
        }
    }
    
    /**
     * 暂停/恢复执行
     */
    fun togglePauseResume() {
        val currentStatus = executionEngine.executionStatus.value
        
        when (currentStatus) {
            ExecutionStatus.RUNNING -> {
                executionEngine.pauseExecution()
                Timber.d("Execution paused")
            }
            ExecutionStatus.PAUSED -> {
                executionEngine.resumeExecution()
                Timber.d("Execution resumed")
            }
            else -> {
                Timber.w("Cannot pause/resume in current status: $currentStatus")
            }
        }
    }
    
    /**
     * 停止执行
     */
    fun stopExecution() {
        executionEngine.stopExecution()
        _executionStartTime.value = 0L
        Timber.d("Execution stopped")
    }
    
    /**
     * 重新执行脚本
     */
    fun restartExecution() {
        stopExecution()
        
        // 等待一下确保停止完成
        viewModelScope.launch {
            kotlinx.coroutines.delay(500)
            startExecution()
        }
    }
    
    /**
     * 切换日志展开状态
     */
    fun toggleLogExpanded() {
        _isLogExpanded.value = !_isLogExpanded.value
    }
    
    /**
     * 清除日志
     */
    fun clearLogs() {
        _executionLogs.value = emptyList()
    }
    
    /**
     * 检查是否正在执行
     */
    fun isExecuting(): Boolean {
        val status = executionEngine.executionStatus.value
        return status == ExecutionStatus.RUNNING || status == ExecutionStatus.PAUSED
    }
    
    /**
     * 获取执行统计信息
     */
    fun getExecutionStats(): ExecutionStats {
        val progress = executionEngine.executionProgress.value
        val logs = _executionLogs.value
        
        return ExecutionStats(
            totalSteps = progress.totalSteps,
            completedSteps = progress.currentStep,
            successfulSteps = logs.count { it.level == LogLevel.SUCCESS },
            failedSteps = logs.count { it.level == LogLevel.ERROR },
            executionTime = if (_executionStartTime.value > 0) {
                System.currentTimeMillis() - _executionStartTime.value
            } else {
                0L
            }
        )
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        if (isExecuting()) {
            stopExecution()
        }
    }
}

/**
 * 执行UI状态
 */
data class ExecutionUiState(
    val scriptName: String = "",
    val scriptDescription: String? = null,
    val executionStatus: ExecutionStatus = ExecutionStatus.IDLE,
    val currentStep: Int = 0,
    val totalSteps: Int = 0,
    val currentStepDescription: String = "",
    val executionTime: Long = 0L,
    val logs: List<ExecutionLog> = emptyList(),
    val isLogExpanded: Boolean = false
)

/**
 * 执行统计信息
 */
data class ExecutionStats(
    val totalSteps: Int,
    val completedSteps: Int,
    val successfulSteps: Int,
    val failedSteps: Int,
    val executionTime: Long
)
