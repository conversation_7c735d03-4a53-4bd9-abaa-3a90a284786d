#!/usr/bin/env python3
"""
诊断网络检查问题
找出为什么录制功能仍然显示网络错误
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("="*60)
print("ShowForAI V3 网络问题诊断")
print("="*60)

# 1. 测试 network_checker 模块
print("\n[1] 测试 network_checker 模块...")
from showforai.utils.network_checker import is_online, check_ai_service, get_network_checker

checker = get_network_checker()
online = is_online()
ai_service = check_ai_service()

print(f"  is_online(): {online}")
print(f"  check_ai_service(): {ai_service}")

# 详细检查 check_ai_service
print("\n[2] 详细分析 check_ai_service 失败原因...")
import urllib.request
import socket

# 检查 api.anthropic.com (当前实现)
print("  测试 api.anthropic.com:")
try:
    request = urllib.request.Request(
        "https://api.anthropic.com",
        headers={'User-Agent': 'ShowForAI/3.0 AIServiceCheck'}
    )
    with urllib.request.urlopen(request, timeout=5) as response:
        print(f"    ✓ 连接成功，状态码: {response.status}")
except Exception as e:
    print(f"    ✗ 连接失败: {e}")

# 检查实际的 AI 服务器
print("\n  测试实际 AI 服务器 (188.166.247.5:8080):")
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex(("188.166.247.5", 8080))
    sock.close()
    if result == 0:
        print(f"    ✓ Socket 连接成功")
    else:
        print(f"    ✗ Socket 连接失败，错误码: {result}")
except Exception as e:
    print(f"    ✗ 异常: {e}")

# 3. 测试 recorder.py 的 check_network_status
print("\n[3] 测试 recorder.py 的 check_network_status...")
from showforai.recorder.recorder import Recorder
from showforai.config import Config

config = Config()
recorder = Recorder(config=config)
network_status = recorder.check_network_status()
print(f"  recorder.check_network_status(): {network_status}")

# 4. 模拟录制启动
print("\n[4] 模拟录制启动流程...")
print("  在 recorder.py 的 start_recording() 中:")
print(f"    - is_online() = {online}")
print(f"    - check_ai_service() = {ai_service}")
print(f"    - check_network_status() = {network_status}")

if not network_status:
    print("\n❌ 问题诊断结果：")
    print("  recorder.check_network_status() 返回 False")
    print("  这会触发 RuntimeError('录制需要网络连接以进行AI识别')")
    print("  导致弹窗显示")
    
    print("\n根本原因：")
    if not online:
        print("  - is_online() 返回 False")
    if not ai_service:
        print("  - check_ai_service() 返回 False")
        print("  - 原因：check_ai_service 检查 api.anthropic.com")
        print("  - 这个地址在中国可能被屏蔽")
        print("  - 应该检查实际的 AI 服务器 188.166.247.5:8080")
else:
    print("\n✅ 网络检查通过，录制应该可以正常启动")

print("\n" + "="*60)
print("诊断完成")
print("="*60)