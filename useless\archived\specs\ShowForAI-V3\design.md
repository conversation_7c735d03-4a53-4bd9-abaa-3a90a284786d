# ShowForAI-V3 Technical Design

## Architecture Overview
- **Modular Design**: Separate modules for recording, AI processing, execution, and GUI
- **Event-Driven**: PyQt6 signals/slots for communication
- **Layered Architecture**: Clear separation between UI, business logic, and data layers

## Key Components

### Recording Module
- Screen capture at 10 FPS
- Mouse and keyboard event listeners
- Action buffer for deduplication
- Screenshot storage with timestamps

### AI Module
- Element detection using computer vision
- Bounding box processing
- Image caching for performance
- Upload management with retry logic

### Execution Module
- Multi-level matching algorithm
- Smart wait system for element appearance
- Force containment rule for click accuracy
- Visual feedback during execution

### Security Module
- API key encryption
- Nonce-based request signing
- Secure configuration management

## Data Flow
1. User actions → Recorder → Action buffer → Screenshots
2. Screenshots → AI Service → Element detection → Script generation
3. Script → Executor → Element matching → Action replay

## Performance Targets
- Recording: < 5% CPU usage
- Execution: < 100ms element matching
- Memory: < 500MB for typical session