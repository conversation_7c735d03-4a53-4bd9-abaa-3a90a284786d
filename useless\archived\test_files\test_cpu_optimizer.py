"""
Test CPU Optimizer functionality
"""

import time
import unittest
import psutil
from unittest.mock import Mock, patch
from src.showforai.optimization.cpu_optimizer import (
    CPUOptimizer, SmartSleepManager, EventDrivenScheduler,
    CPUPriority, HotLoopOptimizer, CPUMonitor,
    optimize_cpu_loop, cpu_throttled, get_cpu_optimizer
)


class TestSmartSleepManager(unittest.TestCase):
    """Test smart sleep manager"""
    
    def setUp(self):
        self.manager = SmartSleepManager()
        
    def test_initial_values(self):
        """Test initial configuration"""
        self.assertEqual(self.manager.min_sleep, 0.01)
        self.assertEqual(self.manager.max_sleep, 0.5)
        self.assertEqual(self.manager.current_sleep, 0.1)
        self.assertEqual(self.manager.target_cpu, 15.0)
        
    @patch('psutil.cpu_percent')
    def test_high_cpu_increases_sleep(self, mock_cpu):
        """Test that high CPU increases sleep time"""
        mock_cpu.return_value = 30.0  # High CPU
        
        initial_sleep = self.manager.current_sleep
        sleep_time = self.manager.get_sleep_time()
        
        self.assertGreater(self.manager.current_sleep, initial_sleep)
        self.assertLessEqual(self.manager.current_sleep, self.manager.max_sleep)
        
    @patch('psutil.cpu_percent')
    def test_low_cpu_decreases_sleep(self, mock_cpu):
        """Test that low CPU decreases sleep time"""
        mock_cpu.return_value = 5.0  # Low CPU
        
        initial_sleep = self.manager.current_sleep
        sleep_time = self.manager.get_sleep_time()
        
        self.assertLess(self.manager.current_sleep, initial_sleep)
        self.assertGreaterEqual(self.manager.current_sleep, self.manager.min_sleep)


class TestEventDrivenScheduler(unittest.TestCase):
    """Test event-driven scheduler"""
    
    def setUp(self):
        self.scheduler = EventDrivenScheduler(max_workers=2)
        
    def tearDown(self):
        if self.scheduler.running:
            self.scheduler.stop()
            
    def test_start_stop(self):
        """Test scheduler start and stop"""
        self.scheduler.start()
        self.assertTrue(self.scheduler.running)
        
        time.sleep(0.1)  # Let workers start
        
        self.scheduler.stop()
        self.assertFalse(self.scheduler.running)
        
    def test_task_execution(self):
        """Test task submission and execution"""
        self.scheduler.start()
        
        result_container = []
        
        def test_func(x):
            return x * 2
            
        def callback(result):
            result_container.append(result)
        
        self.scheduler.submit_task(test_func, 5, callback=callback)
        
        # Wait for execution
        time.sleep(0.5)
        
        self.assertEqual(result_container, [10])
        
        self.scheduler.stop()
        
    def test_priority_execution(self):
        """Test task priority"""
        self.scheduler.start()
        
        results = []
        
        def task(name):
            results.append(name)
            return name
        
        # Submit tasks with different priorities
        self.scheduler.submit_low_priority(task, "low")
        self.scheduler.submit_high_priority(task, "high")
        self.scheduler.submit_task(task, "normal", priority=CPUPriority.NORMAL)
        
        # Wait for execution
        time.sleep(1.0)
        
        # High priority should be executed first
        self.assertEqual(results[0], "high")
        
        self.scheduler.stop()


class TestHotLoopOptimizer(unittest.TestCase):
    """Test hot loop optimizer"""
    
    def setUp(self):
        self.optimizer = HotLoopOptimizer()
        
    def test_profile_loop(self):
        """Test loop profiling"""
        def slow_func(n):
            time.sleep(0.01)  # Simulate slow operation
            return sum(range(n))
        
        # Profile the function multiple times
        for i in range(5):
            result = self.optimizer.profile_loop("test_loop", slow_func, 100)
            
        # Check profile data
        self.assertIn("test_loop", self.optimizer.profile_data)
        data = self.optimizer.profile_data["test_loop"]
        self.assertEqual(data['count'], 5)
        self.assertGreater(data['avg_time'], 0.01)
        
    def test_optimization_suggestion(self):
        """Test that optimization is suggested for slow loops"""
        def very_slow_func():
            time.sleep(0.02)  # Above threshold
            
        with patch('logging.Logger.warning') as mock_warning:
            # Run function to trigger optimization suggestion
            for i in range(3):
                self.optimizer.profile_loop("slow_loop", very_slow_func)
            
            # Check if warning was logged
            mock_warning.assert_called()


class TestCPUMonitor(unittest.TestCase):
    """Test CPU monitor"""
    
    def setUp(self):
        self.monitor = CPUMonitor(target_cpu=15.0)
        
    def tearDown(self):
        if self.monitor.monitoring:
            self.monitor.stop_monitoring()
            
    def test_start_stop_monitoring(self):
        """Test monitoring start and stop"""
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.monitoring)
        
        time.sleep(2.0)  # Let it collect some data
        
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.monitoring)
        
    def test_history_collection(self):
        """Test CPU history collection"""
        self.monitor.start_monitoring()
        
        time.sleep(3.0)  # Collect some history
        
        self.assertGreater(len(self.monitor.history), 0)
        
        # Check history structure
        if self.monitor.history:
            entry = self.monitor.history[0]
            self.assertIn('time', entry)
            self.assertIn('cpu', entry)
            
        self.monitor.stop_monitoring()
        
    def test_average_cpu_calculation(self):
        """Test average CPU calculation"""
        # Add mock history
        current_time = time.time()
        self.monitor.history = [
            {'time': current_time - 5, 'cpu': 10.0},
            {'time': current_time - 3, 'cpu': 15.0},
            {'time': current_time - 1, 'cpu': 20.0},
        ]
        
        avg = self.monitor.get_average_cpu(10)
        self.assertEqual(avg, 15.0)  # Average of 10, 15, 20
        
    def test_callback_notification(self):
        """Test callback notifications"""
        callback_called = []
        
        def test_callback(cpu_percent):
            callback_called.append(cpu_percent)
            
        self.monitor.add_callback(test_callback)
        self.monitor.start_monitoring()
        
        time.sleep(2.0)  # Wait for at least one callback
        
        self.assertGreater(len(callback_called), 0)
        
        self.monitor.stop_monitoring()


class TestCPUOptimizer(unittest.TestCase):
    """Test main CPU optimizer"""
    
    def setUp(self):
        self.optimizer = CPUOptimizer()
        
    def tearDown(self):
        if self.optimizer.scheduler.running:
            self.optimizer.shutdown()
            
    def test_initialization(self):
        """Test optimizer initialization"""
        self.optimizer.initialize()
        
        self.assertTrue(self.optimizer.scheduler.running)
        self.assertTrue(self.optimizer.monitor.monitoring)
        
        self.optimizer.shutdown()
        
    def test_get_cpu_stats(self):
        """Test CPU statistics retrieval"""
        self.optimizer.initialize()
        
        stats = self.optimizer.get_cpu_stats()
        
        self.assertIn('current_cpu', stats)
        self.assertIn('avg_cpu_10s', stats)
        self.assertIn('avg_cpu_60s', stats)
        self.assertIn('target_cpu', stats)
        self.assertIn('sleep_time', stats)
        self.assertIn('worker_count', stats)
        
        self.assertEqual(stats['target_cpu'], 15.0)
        self.assertEqual(stats['worker_count'], 4)
        
        self.optimizer.shutdown()
        
    def test_task_submission(self):
        """Test task submission through optimizer"""
        self.optimizer.initialize()
        
        result_container = []
        
        def test_task(x):
            result_container.append(x * 2)
            
        self.optimizer.submit_task(test_task, 5)
        
        time.sleep(0.5)  # Wait for execution
        
        self.assertEqual(result_container, [10])
        
        self.optimizer.shutdown()


class TestDecorators(unittest.TestCase):
    """Test CPU optimization decorators"""
    
    def test_optimize_cpu_loop_decorator(self):
        """Test loop optimization decorator"""
        @optimize_cpu_loop("test_decorated_loop")
        def decorated_func(n):
            return sum(range(n))
        
        result = decorated_func(100)
        self.assertEqual(result, sum(range(100)))
        
        # Check that it was profiled
        optimizer = get_cpu_optimizer()
        self.assertIn("test_decorated_loop", optimizer.loop_optimizer.profile_data)
        
    def test_cpu_throttled_decorator(self):
        """Test CPU throttled decorator"""
        @cpu_throttled(priority=CPUPriority.HIGH)
        def throttled_func(x):
            return x * 3
        
        result = throttled_func(7)
        self.assertEqual(result, 21)


class TestIntegration(unittest.TestCase):
    """Integration tests for CPU optimizer"""
    
    def test_cpu_usage_reduction(self):
        """Test that CPU optimizer reduces CPU usage"""
        optimizer = CPUOptimizer()
        optimizer.initialize()
        
        # Simulate high CPU task
        def cpu_intensive_task():
            # Do some calculations
            for i in range(1000000):
                _ = i ** 2
                
        # Submit multiple tasks
        for i in range(10):
            optimizer.submit_task(cpu_intensive_task)
            
        # Let it run
        time.sleep(2.0)
        
        # Check that average CPU is controlled
        stats = optimizer.get_cpu_stats()
        
        # CPU should be managed (this is a soft check as actual CPU depends on system)
        self.assertIsNotNone(stats['current_cpu'])
        
        optimizer.shutdown()
        
    def test_adaptive_behavior(self):
        """Test adaptive behavior under different loads"""
        optimizer = CPUOptimizer()
        optimizer.initialize()
        
        # Start with low load
        initial_sleep = optimizer.sleep_manager.current_sleep
        
        # Simulate different CPU loads
        with patch('psutil.cpu_percent') as mock_cpu:
            # High CPU
            mock_cpu.return_value = 30.0
            optimizer.sleep_manager.get_sleep_time()
            high_load_sleep = optimizer.sleep_manager.current_sleep
            
            # Low CPU  
            mock_cpu.return_value = 5.0
            optimizer.sleep_manager.get_sleep_time()
            low_load_sleep = optimizer.sleep_manager.current_sleep
            
        # Sleep should increase under high load
        self.assertGreater(high_load_sleep, initial_sleep)
        
        # Sleep should decrease under low load
        self.assertLess(low_load_sleep, high_load_sleep)
        
        optimizer.shutdown()


def run_performance_test():
    """Run a performance test to verify CPU usage"""
    print("\n=== CPU Optimizer Performance Test ===")
    
    optimizer = CPUOptimizer()
    optimizer.initialize()
    
    print(f"Initial CPU stats: {optimizer.get_cpu_stats()}")
    
    # Simulate workload
    def workload():
        # Image processing simulation
        import numpy as np
        for i in range(100):
            arr = np.random.rand(100, 100)
            result = np.fft.fft2(arr)
            time.sleep(0.01)
    
    print("Starting workload...")
    
    # Submit workload tasks
    for i in range(5):
        optimizer.submit_task(workload)
    
    # Monitor for 10 seconds
    for i in range(10):
        time.sleep(1)
        stats = optimizer.get_cpu_stats()
        print(f"Second {i+1}: CPU={stats['current_cpu']:.1f}%, "
              f"Sleep={stats['sleep_time']:.3f}s")
    
    final_stats = optimizer.get_cpu_stats()
    print(f"\nFinal CPU stats: {final_stats}")
    
    # Check if target was met
    if final_stats['avg_cpu_10s'] <= 15.0:
        print("✓ SUCCESS: CPU usage is within target (≤15%)")
    else:
        print(f"✗ FAILED: CPU usage {final_stats['avg_cpu_10s']:.1f}% exceeds target")
    
    optimizer.shutdown()


if __name__ == '__main__':
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()