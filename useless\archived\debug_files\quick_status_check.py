"""
快速状态检查
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.sync.offline_manager import get_offline_manager

# 检查网络状态
manager = get_offline_manager()
is_online = manager.is_online()
can_record = manager.is_recording_allowed()

print("=" * 50)
print("ShowForAI V3 状态")
print("=" * 50)
print(f"网络状态: {'✅ 在线' if is_online else '❌ 离线'}")
print(f"录制功能: {'✅ 可用' if can_record else '❌ 不可用'}")

if can_record:
    print("\n🎉 恭喜！录制功能现在可以正常使用了！")
    print("\n使用说明：")
    print("1. 运行: python -m showforai.recorder.gui")
    print("2. 点击 'Start' 按钮开始录制")
    print("3. 执行你的操作")
    print("4. 按 F9 键停止录制")
    print("5. 点击 'Upload & Create Script' 生成脚本")
else:
    print("\n❌ 录制功能不可用")
    print(manager.get_recording_disabled_message())