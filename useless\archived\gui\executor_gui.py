"""
GUI module for the executor using PyQt6.

This module provides the executor interface with script list and execution controls.
"""

import sys
from pathlib import Path
from typing import Optional, List, Dict
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QPushButton, 
    QVBoxLayout, QHBoxLayout, QLabel, QMessageBox,
    QSystemTrayIcon, QMenu, QScrollArea, QFrame,
    QSplitter, QGroupBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, QSettings
from PyQt6.QtGui import QIcon, QAction, QPixmap

from loguru import logger

from showforai.config import Config
from showforai.executor.executor import Executor
from showforai.script.script_manager import ScriptManager


class ExecutorThread(QThread):
    """Thread for running executor operations."""
    
    execution_started = pyqtSignal(str)
    execution_completed = pyqtSignal(str)
    execution_error = pyqtSignal(str)
    progress_update = pyqtSignal(int, str)
    
    def __init__(self, executor: Executor):
        super().__init__()
        self.executor = executor
        self.script_id = None
        self.mode = None
    
    def execute_script(self, script_id: str, mode: str = 'active'):
        """Start script execution."""
        self.script_id = script_id
        self.mode = mode
        self.start()
    
    def run(self):
        """Run the script execution."""
        try:
            self.execution_started.emit(self.script_id)
            
            # Execute the script
            result = self.executor.execute_script(
                self.script_id,
                mode=self.mode,
                progress_callback=self.handle_progress
            )
            
            if result:
                self.execution_completed.emit(self.script_id)
            else:
                self.execution_error.emit(f"Failed to execute script {self.script_id}")
                
        except Exception as e:
            self.execution_error.emit(str(e))
    
    def handle_progress(self, current: int, total: int, message: str):
        """Handle progress updates."""
        progress = int((current / total) * 100) if total > 0 else 0
        self.progress_update.emit(progress, message)


class ExecutorWindow(QMainWindow):
    """Main executor window with script management interface."""
    
    def __init__(self, config: Optional[Config] = None):
        super().__init__()
        self.config = config or Config()
        self.executor = Executor(config=self.config)
        self.script_manager = ScriptManager(config=self.config)
        self.executor_thread = None
        
        # Settings
        self.settings = QSettings('ShowForAI', 'Executor')
        
        # Setup UI
        self.init_ui()
        
        # Setup system tray
        self.init_system_tray()
        
        # Load scripts
        self.load_scripts()
        
        logger.info("ExecutorWindow initialized")
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("ShowForAI Executor")
        self.setGeometry(100, 100, 1000, 700)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # Header
        header_layout = QHBoxLayout()
        main_layout.addLayout(header_layout)
        
        # Title
        title_label = QLabel("ShowForAI Executor")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #333;
                padding: 10px;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Switch to recorder button
        self.recorder_button = QPushButton("Switch to Recorder")
        self.recorder_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.recorder_button.clicked.connect(self.switch_to_recorder)
        header_layout.addWidget(self.recorder_button)
        
        # Sync button
        self.sync_button = QPushButton("Sync")
        self.sync_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.sync_button.clicked.connect(self.sync_scripts)
        header_layout.addWidget(self.sync_button)
        
        # Main splitter for two-panel layout
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Active mode
        self.active_panel = self.create_active_panel()
        splitter.addWidget(self.active_panel)
        
        # Right panel - Auxiliary mode
        self.auxiliary_panel = self.create_auxiliary_panel()
        splitter.addWidget(self.auxiliary_panel)
        
        # Set splitter sizes (60% for active, 40% for auxiliary)
        splitter.setSizes([600, 400])
        
        # Status bar
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
            }
        """)
        main_layout.addWidget(self.status_label)
        
        # Apply window style
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
        """)
    
    def create_active_panel(self) -> QWidget:
        """Create the active mode panel."""
        panel = QGroupBox("Active Mode")
        panel.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding-top: 10px;
                margin-top: 10px;
            }
        """)
        
        layout = QVBoxLayout()
        panel.setLayout(layout)
        
        # Control buttons
        control_layout = QHBoxLayout()
        layout.addLayout(control_layout)
        
        self.play_button = QPushButton("Execute")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.play_button.clicked.connect(self.execute_active_scripts)
        control_layout.addWidget(self.play_button)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.stop_button.clicked.connect(self.stop_execution)
        control_layout.addWidget(self.stop_button)
        
        control_layout.addStretch()
        
        # Script list scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
            }
        """)
        layout.addWidget(scroll_area)
        
        # Script list container
        self.active_script_container = QWidget()
        self.active_script_layout = QVBoxLayout()
        self.active_script_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.active_script_container.setLayout(self.active_script_layout)
        scroll_area.setWidget(self.active_script_container)
        
        # Add placeholder
        placeholder = QLabel("No scripts available\nRecord scripts to see them here")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                color: #999;
                font-size: 14px;
                padding: 50px;
            }
        """)
        self.active_script_layout.addWidget(placeholder)
        
        return panel
    
    def create_auxiliary_panel(self) -> QWidget:
        """Create the auxiliary mode panel."""
        panel = QGroupBox("Auxiliary Mode")
        panel.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                padding-top: 10px;
                margin-top: 10px;
            }
        """)
        
        layout = QVBoxLayout()
        panel.setLayout(layout)
        
        # Control buttons
        control_layout = QHBoxLayout()
        layout.addLayout(control_layout)
        
        self.aux_enable_button = QPushButton("Enable Monitoring")
        self.aux_enable_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.aux_enable_button.clicked.connect(self.toggle_auxiliary_mode)
        control_layout.addWidget(self.aux_enable_button)
        
        control_layout.addStretch()
        
        # Detection frequency selector
        freq_label = QLabel("Detection Frequency:")
        freq_label.setStyleSheet("font-weight: normal; font-size: 12px;")
        control_layout.addWidget(freq_label)
        
        # Frequency buttons
        self.freq_high = QPushButton("High (4/s)")
        self.freq_medium = QPushButton("Medium (2/s)")
        self.freq_low = QPushButton("Low (1/s)")
        
        for btn in [self.freq_high, self.freq_medium, self.freq_low]:
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 5px 10px;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                    color: white;
                }
            """)
            control_layout.addWidget(btn)
        
        # Default to medium frequency
        self.freq_medium.setChecked(True)
        
        # Script list scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
            }
        """)
        layout.addWidget(scroll_area)
        
        # Script list container
        self.aux_script_container = QWidget()
        self.aux_script_layout = QVBoxLayout()
        self.aux_script_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.aux_script_container.setLayout(self.aux_script_layout)
        scroll_area.setWidget(self.aux_script_container)
        
        # Add placeholder
        placeholder = QLabel("No scripts enabled for auxiliary mode\nSelect scripts to monitor in background")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                color: #999;
                font-size: 14px;
                padding: 50px;
            }
        """)
        self.aux_script_layout.addWidget(placeholder)
        
        return panel
    
    def init_system_tray(self):
        """Initialize system tray icon."""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            logger.warning("System tray not available")
            return
        
        # Create tray icon
        self.tray_icon = QSystemTrayIcon(self)
        
        # Create default icon
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.GlobalColor.blue)
        self.tray_icon.setIcon(QIcon(pixmap))
        
        # Create tray menu
        tray_menu = QMenu()
        
        show_action = QAction("Show", self)
        show_action.triggered.connect(self.show)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.setToolTip("ShowForAI Executor")
        
        # Show tray icon
        self.tray_icon.show()
    
    def load_scripts(self):
        """Load available scripts."""
        try:
            scripts = self.script_manager.get_local_scripts()
            logger.info(f"Loaded {len(scripts)} scripts")
            
            # Clear existing script widgets
            # TODO: Implement script card widgets
            
            if not scripts:
                self.update_status("No scripts available")
            else:
                self.update_status(f"Loaded {len(scripts)} scripts")
                
        except Exception as e:
            logger.error(f"Failed to load scripts: {e}")
            self.update_status("Failed to load scripts")
    
    def execute_active_scripts(self):
        """Execute scripts in active mode."""
        # TODO: Get selected scripts and execute
        self.update_status("Executing scripts...")
        self.play_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        QMessageBox.information(self, "Execute", "Script execution will be implemented with the script cards")
    
    def stop_execution(self):
        """Stop current execution."""
        if self.executor_thread and self.executor_thread.isRunning():
            self.executor_thread.quit()
            self.executor_thread.wait()
        
        self.play_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.update_status("Execution stopped")
    
    def toggle_auxiliary_mode(self):
        """Toggle auxiliary monitoring mode."""
        # TODO: Implement auxiliary mode toggle
        if self.aux_enable_button.text() == "Enable Monitoring":
            self.aux_enable_button.setText("Disable Monitoring")
            self.aux_enable_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                }
            """)
            self.update_status("Auxiliary monitoring enabled")
            
            # Minimize to tray
            QTimer.singleShot(1000, self.hide)
            
            if hasattr(self, 'tray_icon'):
                self.tray_icon.showMessage(
                    "Auxiliary Mode",
                    "Monitoring in background",
                    QSystemTrayIcon.MessageIcon.Information,
                    2000
                )
        else:
            self.aux_enable_button.setText("Enable Monitoring")
            self.aux_enable_button.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                }
            """)
            self.update_status("Auxiliary monitoring disabled")
    
    def sync_scripts(self):
        """Sync scripts with cloud."""
        # TODO: Implement sync functionality
        QMessageBox.information(self, "Sync", "Script synchronization will be implemented in the sync module")
    
    def switch_to_recorder(self):
        """Switch to recorder mode."""
        reply = QMessageBox.question(
            self, "Switch to Recorder",
            "Do you want to switch to the Recorder?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Launch recorder
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "-m", "showforai.launcher", "--recorder"])
            # Close this window
            self.close()
    
    def update_status(self, message: str):
        """Update status bar message."""
        self.status_label.setText(message)
        logger.info(f"Status: {message}")
    
    def closeEvent(self, event):
        """Handle close event."""
        # Clean up executor
        if self.executor_thread and self.executor_thread.isRunning():
            self.executor_thread.quit()
            self.executor_thread.wait()
        
        event.accept()
    
    def quit_application(self):
        """Quit the application."""
        self.close()
        QApplication.quit()


def main():
    """Main entry point for executor GUI."""
    app = QApplication(sys.argv)
    app.setApplicationName("ShowForAI Executor")
    app.setOrganizationName("ShowForAI")
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show window
    window = ExecutorWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == '__main__':
    main()