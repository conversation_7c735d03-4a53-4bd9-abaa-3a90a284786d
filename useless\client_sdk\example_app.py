"""
ShowForAI 客户端示例应用
演示如何在本地应用中调用云端API
"""

import json
import sys
import os
from pathlib import Path
from showforai_client import ShowForAIClient

def load_config(config_path="config.json"):
    """加载配置文件"""
    with open(config_path, 'r') as f:
        return json.load(f)

def main():
    """主函数"""
    print("=" * 60)
    print("ShowForAI Detection Client - 示例应用")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    
    # 选择环境
    env = input("\n选择环境 (1=生产环境, 2=开发环境): ")
    if env == "1":
        server_config = config["server"]["production"]
        print("✓ 使用生产环境")
    else:
        server_config = config["server"]["development"]
        print("✓ 使用开发环境")
    
    # 创建客户端
    client = ShowForAIClient(
        api_key=server_config["api_key"],
        server_url=server_config["url"],
        timeout=server_config["timeout"],
        retry_count=server_config["retry_count"]
    )
    
    # 测试连接
    print("\n正在连接服务器...")
    try:
        health = client.health_check()
        print(f"✓ 连接成功！服务器状态: {health['status']}")
        print(f"  版本: {health['version']}")
        print(f"  模型: {health['services']['model']}")
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return
    
    # 主菜单循环
    while True:
        print("\n" + "=" * 60)
        print("功能菜单:")
        print("1. 检测单张图片")
        print("2. 批量检测")
        print("3. 查看统计信息")
        print("4. 测试性能")
        print("0. 退出")
        print("=" * 60)
        
        choice = input("\n请选择功能 (0-4): ")
        
        if choice == "0":
            print("再见！")
            break
        
        elif choice == "1":
            # 单张图片检测
            image_path = input("请输入图片路径: ").strip()
            if not os.path.exists(image_path):
                print(f"✗ 文件不存在: {image_path}")
                continue
            
            try:
                click_x = int(input("输入点击X坐标: "))
                click_y = int(input("输入点击Y坐标: "))
                mode = input("选择模式 (basic/enhanced) [basic]: ").strip() or "basic"
                
                print("\n正在检测...")
                result = client.detect_element(
                    image_path=image_path,
                    click_x=click_x,
                    click_y=click_y,
                    mode=mode,
                    save_result=config["detection"]["save_results"],
                    output_dir=config["detection"]["results_dir"]
                )
                
                if result['success']:
                    print("\n✓ 检测成功！")
                    print(f"  边界框: {result['data']['bounding_box']}")
                    print(f"  置信度: {result['data']['confidence']:.2%}")
                    print(f"  检测轮次: {result['data']['detection_round']}")
                    print(f"  耗时: {result['data'].get('processing_time', 'N/A')}ms")
                else:
                    print(f"\n✗ 检测失败: {result.get('error')}")
            
            except ValueError as e:
                print(f"✗ 输入错误: {e}")
            except Exception as e:
                print(f"✗ 检测错误: {e}")
        
        elif choice == "2":
            # 批量检测
            folder_path = input("请输入图片文件夹路径: ").strip()
            if not os.path.exists(folder_path):
                print(f"✗ 文件夹不存在: {folder_path}")
                continue
            
            # 收集所有图片
            image_files = []
            for ext in config["detection"]["supported_formats"]:
                image_files.extend(Path(folder_path).glob(f"*.{ext}"))
            
            if not image_files:
                print("✗ 文件夹中没有找到支持的图片文件")
                continue
            
            print(f"\n找到 {len(image_files)} 张图片")
            
            # 设置默认点击位置（图片中心）
            default_x = input("默认点击X坐标 [400]: ").strip() or "400"
            default_y = input("默认点击Y坐标 [300]: ").strip() or "300"
            mode = input("选择模式 (basic/enhanced) [basic]: ").strip() or "basic"
            
            # 准备批量任务
            tasks = [
                (str(img), int(default_x), int(default_y))
                for img in image_files
            ]
            
            print(f"\n开始批量检测 {len(tasks)} 张图片...")
            results = client.detect_batch(tasks, mode=mode)
            
            # 统计结果
            success_count = sum(1 for r in results if r.get('success'))
            print(f"\n批量检测完成！")
            print(f"  成功: {success_count}/{len(results)}")
            print(f"  失败: {len(results) - success_count}/{len(results)}")
            
            # 显示失败的图片
            failed = [tasks[i][0] for i, r in enumerate(results) if not r.get('success')]
            if failed:
                print("\n失败的图片:")
                for img in failed[:5]:
                    print(f"  - {Path(img).name}")
                if len(failed) > 5:
                    print(f"  ... 还有 {len(failed) - 5} 个")
        
        elif choice == "3":
            # 查看统计信息
            try:
                stats = client.get_stats()
                print("\n服务器统计信息:")
                print(json.dumps(stats, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"✗ 获取统计信息失败: {e}")
        
        elif choice == "4":
            # 性能测试
            test_image = input("测试图片路径 [使用示例图片]: ").strip()
            
            if not test_image:
                # 创建示例图片
                from PIL import Image
                import numpy as np
                
                test_image = "test_image.png"
                img = Image.fromarray(np.random.randint(0, 255, (768, 768, 3), dtype=np.uint8))
                img.save(test_image)
                print(f"✓ 创建测试图片: {test_image}")
            
            if not os.path.exists(test_image):
                print(f"✗ 文件不存在: {test_image}")
                continue
            
            import time
            
            print("\n开始性能测试...")
            test_count = 10
            mode = "basic"
            
            times = []
            for i in range(test_count):
                start = time.time()
                try:
                    result = client.detect_element(
                        image_path=test_image,
                        click_x=400,
                        click_y=300,
                        mode=mode
                    )
                    elapsed = time.time() - start
                    times.append(elapsed)
                    print(f"  测试 {i+1}/{test_count}: {elapsed:.2f}秒")
                except Exception as e:
                    print(f"  测试 {i+1}/{test_count}: 失败 - {e}")
            
            if times:
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                
                print(f"\n性能测试结果:")
                print(f"  平均耗时: {avg_time:.2f}秒")
                print(f"  最快: {min_time:.2f}秒")
                print(f"  最慢: {max_time:.2f}秒")
                print(f"  成功率: {len(times)}/{test_count}")
                print(f"  QPS: {1/avg_time:.2f}")
        
        else:
            print("✗ 无效的选项")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序错误: {e}")
        sys.exit(1)