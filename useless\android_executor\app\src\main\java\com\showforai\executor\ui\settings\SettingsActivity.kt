package com.showforai.executor.ui.settings

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.showforai.executor.ui.theme.DSLExecutorTheme
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 设置界面
 * 
 * 管理应用配置参数：
 * - 执行参数（超时、阈值等）
 * - 视觉匹配参数
 * - OCR参数
 * - 缓存管理
 * - 关于信息
 */
@AndroidEntryPoint
class SettingsActivity : ComponentActivity() {
    
    private val viewModel: SettingsViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            DSLExecutorTheme {
                SettingsScreen(
                    viewModel = viewModel,
                    onNavigateBack = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel,
    onNavigateBack: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设置") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 执行设置
            item {
                SettingsCategoryCard(
                    title = "执行设置",
                    icon = Icons.Default.PlayArrow
                ) {
                    // 超时设置
                    SliderSetting(
                        title = "执行超时时间",
                        value = uiState.executionTimeout,
                        valueRange = 10f..120f,
                        steps = 11,
                        onValueChange = { viewModel.updateExecutionTimeout(it) },
                        valueText = "${uiState.executionTimeout.toInt()} 秒",
                        description = "每个步骤的最大等待时间"
                    )
                    
                    // 重试设置
                    SliderSetting(
                        title = "重试次数",
                        value = uiState.retryCount.toFloat(),
                        valueRange = 0f..5f,
                        steps = 5,
                        onValueChange = { viewModel.updateRetryCount(it.toInt()) },
                        valueText = "${uiState.retryCount} 次",
                        description = "失败时的自动重试次数"
                    )
                    
                    // 执行延迟
                    SliderSetting(
                        title = "操作间隔",
                        value = uiState.operationDelay.toFloat(),
                        valueRange = 0f..2000f,
                        steps = 20,
                        onValueChange = { viewModel.updateOperationDelay(it.toLong()) },
                        valueText = "${uiState.operationDelay} 毫秒",
                        description = "每个操作之间的等待时间"
                    )
                }
            }
            
            // 视觉匹配设置
            item {
                SettingsCategoryCard(
                    title = "视觉匹配设置",
                    icon = Icons.Default.Image
                ) {
                    // 匹配阈值
                    SliderSetting(
                        title = "匹配阈值",
                        value = uiState.visualMatchThreshold,
                        valueRange = 0.5f..0.95f,
                        steps = 9,
                        onValueChange = { viewModel.updateVisualMatchThreshold(it) },
                        valueText = String.format("%.2f", uiState.visualMatchThreshold),
                        description = "图像匹配的最低相似度（越高越严格）"
                    )
                    
                    // 多尺度匹配
                    SwitchSetting(
                        title = "多尺度匹配",
                        checked = uiState.useMultiScaleMatching,
                        onCheckedChange = { viewModel.updateUseMultiScaleMatching(it) },
                        description = "在不同缩放比例下进行匹配（更准确但更慢）"
                    )
                }
            }
            
            // OCR设置
            item {
                SettingsCategoryCard(
                    title = "OCR设置",
                    icon = Icons.Default.TextFields
                ) {
                    // OCR置信度
                    SliderSetting(
                        title = "OCR置信度阈值",
                        value = uiState.ocrConfidenceThreshold,
                        valueRange = 0.3f..0.9f,
                        steps = 6,
                        onValueChange = { viewModel.updateOcrConfidenceThreshold(it) },
                        valueText = String.format("%.2f", uiState.ocrConfidenceThreshold),
                        description = "文本识别的最低置信度（越高越严格）"
                    )
                    
                    // 精确匹配
                    SwitchSetting(
                        title = "精确文本匹配",
                        checked = uiState.useExactTextMatching,
                        onCheckedChange = { viewModel.updateUseExactTextMatching(it) },
                        description = "要求文本完全匹配（不启用则进行模糊匹配）"
                    )
                }
            }
            
            // 缓存设置
            item {
                SettingsCategoryCard(
                    title = "缓存管理",
                    icon = Icons.Default.Storage
                ) {
                    // 缓存大小
                    TextSetting(
                        title = "当前缓存大小",
                        value = uiState.cacheSize,
                        description = "图像和脚本缓存"
                    )
                    
                    // 清除缓存按钮
                    ButtonSetting(
                        title = "清除缓存",
                        buttonText = "清除",
                        onClick = { viewModel.clearCache() },
                        description = "删除所有临时文件和图像缓存"
                    )
                }
            }
            
            // 关于信息
            item {
                SettingsCategoryCard(
                    title = "关于",
                    icon = Icons.Default.Info
                ) {
                    // 版本信息
                    TextSetting(
                        title = "版本",
                        value = uiState.appVersion,
                        description = null
                    )
                    
                    // DSL版本
                    TextSetting(
                        title = "DSL规范版本",
                        value = "v3.1",
                        description = "与桌面版兼容"
                    )
                    
                    // 开发者信息
                    TextSetting(
                        title = "开发者",
                        value = "ShowForAI Team",
                        description = null
                    )
                }
            }
        }
    }
}

@Composable
fun SettingsCategoryCard(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            content()
        }
    }
}

@Composable
fun SliderSetting(
    title: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    steps: Int,
    onValueChange: (Float) -> Unit,
    valueText: String,
    description: String?
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )
            
            Text(
                text = valueText,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            steps = steps,
            modifier = Modifier.fillMaxWidth()
        )
        
        if (description != null) {
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun SwitchSetting(
    title: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    description: String?
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
fun TextSetting(
    title: String,
    value: String,
    description: String?
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )
            
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        if (description != null) {
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun ButtonSetting(
    title: String,
    buttonText: String,
    onClick: () -> Unit,
    description: String?
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Button(
            onClick = onClick,
            modifier = Modifier.padding(start = 8.dp)
        ) {
            Text(buttonText)
        }
    }
}
