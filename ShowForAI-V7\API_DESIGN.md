# API 设计文档

> "The best API is no API, but when you need one, make it obvious." - Unknown

## 设计原则

1. **RESTful** - 遵循REST架构风格
2. **一致性** - 统一的错误处理和响应格式
3. **安全性** - 所有请求签名验证
4. **版本化** - API版本管理

## API 基础

### 基础URL
```
生产环境: https://api.showforai.com/v1
开发环境: http://localhost:8080/v1
```

### 认证方式
```http
Authorization: Bearer {token}
X-Request-ID: {uuid}
X-Timestamp: {unix_timestamp}
X-Signature: {hmac_sha256}
```

### 请求签名算法
```typescript
function generateSignature(
  method: string,
  path: string,
  timestamp: number,
  body: any,
  secret: string
): string {
  const message = `${method}:${path}:${timestamp}:${JSON.stringify(body)}`;
  return crypto.createHmac('sha256', secret).update(message).digest('hex');
}
```

## 核心 API

### 1. 录制相关

#### 上传录制数据
```http
POST /recordings/upload
Content-Type: multipart/form-data

Request:
{
  "metadata": {
    "duration_ms": 30000,
    "frame_count": 15,
    "resolution": [1920, 1080],
    "client_version": "1.0.0"
  },
  "frames": [
    {
      "index": 0,
      "image": "<base64_image_768x768>",
      "timestamp": 1234567890
    }
  ],
  "actions": [
    {
      "type": "click",
      "x": 500,
      "y": 300,
      "timestamp": 1234567891,
      "interval_ms": 1000
    }
  ]
}

Response:
{
  "success": true,
  "data": {
    "recording_id": "rec_abc123",
    "processing_status": "pending",
    "estimated_time": 5000
  }
}
```

#### 获取AI处理结果
```http
GET /recordings/{recording_id}/result

Response:
{
  "success": true,
  "data": {
    "status": "completed",
    "script_id": "scr_xyz789",
    "elements": [
      {
        "frame_index": 0,
        "bbox": {
          "x": 100,
          "y": 200,
          "width": 150,
          "height": 50,
          "confidence": 0.95
        },
        "element_type": "button"
      }
    ],
    "processing_time": 3500
  }
}
```

### 2. 脚本相关

#### 获取脚本详情
```http
GET /scripts/{script_id}

Response:
{
  "success": true,
  "data": {
    "id": "scr_xyz789",
    "name": "脚本1",
    "description": "登录流程自动化",
    "created_at": 1234567890,
    "original_resolution": [1920, 1080],
    "frames": [
      {
        "index": 0,
        "image_768": "<base64>",
        "bbox": {
          "x": 100,
          "y": 200,
          "width": 150,
          "height": 50
        }
      }
    ],
    "actions": [
      {
        "type": "click",
        "x": 500,
        "y": 300,
        "interval_ms": 1000
      }
    ]
  }
}
```

#### 分享脚本
```http
POST /scripts/{script_id}/share

Request:
{
  "expire_days": 7,
  "max_uses": 10
}

Response:
{
  "success": true,
  "data": {
    "share_link": "https://showforai.com/s/abc123",
    "share_code": "ABC123",
    "expires_at": 1235172690
  }
}
```

#### 获取分享的脚本
```http
GET /scripts/shared/{share_code}

Response:
{
  "success": true,
  "data": {
    // 同获取脚本详情
  }
}
```

### 3. 用户相关

#### 用户登录
```http
POST /auth/login

Request:
{
  "email": "<EMAIL>",
  "password": "hashed_password"
}

Response:
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": "usr_123",
      "email": "<EMAIL>",
      "name": "张三"
    }
  }
}
```

#### 刷新Token
```http
POST /auth/refresh

Request:
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}

Response:
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
  }
}
```

### 4. 统计相关

#### 获取使用统计
```http
GET /stats/usage

Response:
{
  "success": true,
  "data": {
    "scripts_created": 42,
    "scripts_executed": 156,
    "total_execution_time": 234567,
    "success_rate": 0.95,
    "daily_stats": [
      {
        "date": "2024-01-15",
        "executions": 23,
        "success": 22,
        "failed": 1
      }
    ]
  }
}
```

## WebSocket API

### 连接建立
```javascript
const ws = new WebSocket('wss://api.showforai.com/v1/ws');

ws.onopen = () => {
  // 发送认证消息
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'your_token'
  }));
};
```

### 消息类型

#### 执行进度更新
```json
{
  "type": "execution_progress",
  "data": {
    "script_id": "scr_123",
    "current_step": 5,
    "total_steps": 10,
    "status": "running",
    "message": "正在查找登录按钮..."
  }
}
```

#### 录制状态更新
```json
{
  "type": "recording_status",
  "data": {
    "status": "recording",
    "frames_captured": 15,
    "actions_recorded": 3,
    "duration": 5000
  }
}
```

#### 错误通知
```json
{
  "type": "error",
  "data": {
    "code": "ELEMENT_NOT_FOUND",
    "message": "无法找到目标元素",
    "details": {
      "step": 3,
      "action": "click"
    }
  }
}
```

#### 执行模式状态更新
```json
{
  "type": "execution_mode_status",
  "data": {
    "active_running": true,
    "active_script_id": "scr_123",
    "active_progress": 45,
    "auxiliary_waiting": true,
    "auxiliary_script_id": "scr_456",
    "input_owner": 1,
    "message": "Active模式执行中，Auxiliary模式等待"
  }
}
```

## 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "用户友好的错误消息",
    "details": {
      // 额外的错误信息
    }
  }
}
```

### 错误码定义

| 错误码 | HTTP状态码 | 说明 |
|-------|-----------|-----|
| INVALID_REQUEST | 400 | 请求参数错误 |
| UNAUTHORIZED | 401 | 未授权 |
| FORBIDDEN | 403 | 禁止访问 |
| NOT_FOUND | 404 | 资源不存在 |
| RATE_LIMITED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务暂时不可用 |

### 具体错误码

```typescript
enum ErrorCode {
  // 认证相关
  AUTH_INVALID_TOKEN = 'AUTH_INVALID_TOKEN',
  AUTH_TOKEN_EXPIRED = 'AUTH_TOKEN_EXPIRED',
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  
  // 录制相关
  RECORDING_TOO_LARGE = 'RECORDING_TOO_LARGE',
  RECORDING_INVALID_FORMAT = 'RECORDING_INVALID_FORMAT',
  RECORDING_PROCESSING_FAILED = 'RECORDING_PROCESSING_FAILED',
  
  // 脚本相关
  SCRIPT_NOT_FOUND = 'SCRIPT_NOT_FOUND',
  SCRIPT_CORRUPTED = 'SCRIPT_CORRUPTED',
  SCRIPT_EXECUTION_FAILED = 'SCRIPT_EXECUTION_FAILED',
  
  // 系统相关
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
  SYSTEM_OVERLOAD = 'SYSTEM_OVERLOAD',
}
```

## 限流策略

### 请求限制
- 普通用户：100 请求/分钟
- 高级用户：500 请求/分钟
- 上传接口：10 请求/分钟

### 响应头
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1234567890
```

## 数据验证

### 请求验证规则
```typescript
// 上传录制数据验证
const uploadSchema = {
  metadata: {
    duration_ms: { type: 'number', min: 1000, max: 600000 }, // 1秒-10分钟
    frame_count: { type: 'number', min: 1, max: 1000 },
    resolution: { type: 'array', items: 'number', length: 2 },
    client_version: { type: 'string', pattern: /^\d+\.\d+\.\d+$/ }
  },
  frames: {
    type: 'array',
    maxItems: 100,
    items: {
      index: { type: 'number', min: 0 },
      image: { type: 'string', maxLength: 5000000 }, // 5MB
      timestamp: { type: 'number' }
    }
  },
  actions: {
    type: 'array',
    maxItems: 1000,
    items: {
      type: { type: 'string', enum: ['click', 'double_click', 'right_click', 'drag', 'input'] },
      x: { type: 'number', min: 0 },
      y: { type: 'number', min: 0 },
      timestamp: { type: 'number' },
      interval_ms: { type: 'number', min: 0, max: 60000 }
    }
  }
};
```


## 分页规范

### 请求参数
```http
GET /scripts?page=1&limit=20&sort=created_at&order=desc
```

### 响应格式
```json
{
  "success": true,
  "data": {
    "items": [ /* ... */ ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## SDK 示例

### JavaScript/TypeScript
```typescript
class ShowForAIClient {
  private apiKey: string;
  private baseUrl: string;
  
  constructor(apiKey: string, baseUrl = 'https://api.showforai.com/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }
  
  async uploadRecording(data: RecordingData): Promise<ScriptId> {
    const formData = new FormData();
    formData.append('metadata', JSON.stringify(data.metadata));
    formData.append('frames', JSON.stringify(data.frames));
    formData.append('actions', JSON.stringify(data.actions));
    
    const response = await fetch(`${this.baseUrl}/recordings/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Request-ID': generateUUID(),
        'X-Timestamp': Date.now().toString(),
        'X-Signature': this.generateSignature(...)
      },
      body: formData
    });
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error.message);
    }
    
    return result.data.script_id;
  }
}
```

### Rust
```rust
pub struct ShowForAIClient {
    api_key: String,
    base_url: String,
    client: reqwest::Client,
}

impl ShowForAIClient {
    pub async fn upload_recording(&self, data: RecordingData) -> Result<String> {
        let url = format!("{}/recordings/upload", self.base_url);
        
        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("X-Request-ID", Uuid::new_v4().to_string())
            .header("X-Timestamp", SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs())
            .header("X-Signature", self.generate_signature(&data)?)
            .json(&data)
            .send()
            .await?;
            
        let result: ApiResponse<UploadResult> = response.json().await?;
        
        if !result.success {
            return Err(Error::Api(result.error.message));
        }
        
        Ok(result.data.script_id)
    }
}
```

## API 版本管理

### 版本策略
- 主版本号变更：不兼容的API修改
- 次版本号变更：向后兼容的功能新增
- 修订号变更：向后兼容的问题修正

### 弃用策略
1. 新版本发布时标记弃用API
2. 弃用期至少6个月
3. 响应头包含弃用警告
```http
X-API-Deprecation: true
X-API-Deprecation-Date: 2024-12-31
X-API-Deprecation-Info: https://docs.showforai.com/deprecations
```

---

*"A good API is like a good joke - if you have to explain it, it's not that good."*