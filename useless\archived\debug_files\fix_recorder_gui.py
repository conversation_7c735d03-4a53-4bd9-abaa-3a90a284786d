"""
修复录制器GUI的问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 确保使用正确的日志级别
import logging
logging.basicConfig(level=logging.INFO)

def main():
    print("="*60)
    print("修复录制器GUI")
    print("="*60)
    
    # 先测试offline_manager
    from showforai.sync.offline_manager import get_offline_manager, _offline_manager
    
    print(f"\n1. 测试全局单例:")
    print(f"   _offline_manager 初始值: {_offline_manager}")
    
    manager = get_offline_manager()
    print(f"   get_offline_manager() 返回: {manager}")
    print(f"   manager.is_online(): {manager.is_online()}")
    print(f"   manager.is_recording_allowed(): {manager.is_recording_allowed()}")
    
    # 测试GUI
    print(f"\n2. 创建GUI窗口:")
    from PyQt6.QtWidgets import QApplication
    from showforai.recorder.gui import RecorderWindow
    from showforai.config import Config
    
    app = QApplication([])
    config = Config()
    
    # 在创建窗口前，确保offline_manager已经初始化
    print(f"   创建窗口前 _offline_manager: {_offline_manager}")
    
    window = RecorderWindow(config)
    
    print(f"   创建窗口后 window.offline_manager: {window.offline_manager}")
    print(f"   是否同一实例: {manager is window.offline_manager}")
    print(f"   window.offline_manager.is_online(): {window.offline_manager.is_online()}")
    print(f"   window.offline_manager.is_recording_allowed(): {window.offline_manager.is_recording_allowed()}")
    
    # 检查UI状态
    print(f"\n3. 检查UI状态:")
    print(f"   offline_warning.isHidden(): {window.offline_warning.isHidden()}")
    print(f"   record_button.isEnabled(): {window.record_button.isEnabled()}")
    print(f"   record_button.text(): {window.record_button.text()}")
    
    # 手动调用一次check_network_status
    print(f"\n4. 手动调用check_network_status:")
    window.check_network_status()
    print(f"   调用后 offline_warning.isHidden(): {window.offline_warning.isHidden()}")
    print(f"   调用后 record_button.isEnabled(): {window.record_button.isEnabled()}")
    
    # 模拟点击
    print(f"\n5. 模拟点击录制按钮:")
    print("   调用 window.toggle_recording()...")
    window.toggle_recording()
    
    print("\n6. 显示窗口")
    window.show()
    
    print("\n窗口已显示。请尝试点击录制按钮。")
    print("如果显示离线消息，说明问题仍然存在。")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()