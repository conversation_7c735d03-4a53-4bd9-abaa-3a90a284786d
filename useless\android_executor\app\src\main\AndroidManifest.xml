<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 基础权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 屏幕捕捉相关权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 文件访问权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="28" />
    
    <!-- Android 11+ 文件访问 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <!-- 设备特性要求 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="true" />

    <application
        android:name=".DSLExecutorApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DSLExecutor"
        android:hardwareAccelerated="true"
        tools:targetApi="31">

        <!-- 主活动 -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.DSLExecutor"
            android:screenOrientation="portrait"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 执行活动 -->
        <activity
            android:name=".ui.execution.ExecutionActivity"
            android:exported="false"
            android:theme="@style/Theme.DSLExecutor"
            android:screenOrientation="portrait"
            android:launchMode="singleTop" />

        <!-- 设置活动 -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.DSLExecutor"
            android:screenOrientation="portrait" />

        <!-- 权限引导活动 -->
        <activity
            android:name=".ui.permissions.PermissionGuideActivity"
            android:exported="false"
            android:theme="@style/Theme.DSLExecutor"
            android:screenOrientation="portrait" />

        <!-- 屏幕捕捉服务 -->
        <service
            android:name=".services.ScreenCaptureService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <!-- 无障碍服务 -->
        <service
            android:name=".services.AutomationAccessibilityService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- DSL执行服务 -->
        <service
            android:name=".services.DSLExecutionService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 文件提供者 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 广播接收器 -->
        <receiver
            android:name=".receivers.ExecutionControlReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.showforai.executor.ACTION_PAUSE_EXECUTION" />
                <action android:name="com.showforai.executor.ACTION_RESUME_EXECUTION" />
                <action android:name="com.showforai.executor.ACTION_STOP_EXECUTION" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
