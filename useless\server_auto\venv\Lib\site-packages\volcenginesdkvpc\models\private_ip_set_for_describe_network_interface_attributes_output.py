# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PrivateIpSetForDescribeNetworkInterfaceAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'associated_elastic_ip': 'AssociatedElasticIpForDescribeNetworkInterfaceAttributesOutput',
        'primary': 'bool',
        'private_ip_address': 'str'
    }

    attribute_map = {
        'associated_elastic_ip': 'AssociatedElasticIp',
        'primary': 'Primary',
        'private_ip_address': 'PrivateIpAddress'
    }

    def __init__(self, associated_elastic_ip=None, primary=None, private_ip_address=None, _configuration=None):  # noqa: E501
        """PrivateIpSetForDescribeNetworkInterfaceAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._associated_elastic_ip = None
        self._primary = None
        self._private_ip_address = None
        self.discriminator = None

        if associated_elastic_ip is not None:
            self.associated_elastic_ip = associated_elastic_ip
        if primary is not None:
            self.primary = primary
        if private_ip_address is not None:
            self.private_ip_address = private_ip_address

    @property
    def associated_elastic_ip(self):
        """Gets the associated_elastic_ip of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501


        :return: The associated_elastic_ip of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501
        :rtype: AssociatedElasticIpForDescribeNetworkInterfaceAttributesOutput
        """
        return self._associated_elastic_ip

    @associated_elastic_ip.setter
    def associated_elastic_ip(self, associated_elastic_ip):
        """Sets the associated_elastic_ip of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.


        :param associated_elastic_ip: The associated_elastic_ip of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501
        :type: AssociatedElasticIpForDescribeNetworkInterfaceAttributesOutput
        """

        self._associated_elastic_ip = associated_elastic_ip

    @property
    def primary(self):
        """Gets the primary of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501


        :return: The primary of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._primary

    @primary.setter
    def primary(self, primary):
        """Sets the primary of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.


        :param primary: The primary of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501
        :type: bool
        """

        self._primary = primary

    @property
    def private_ip_address(self):
        """Gets the private_ip_address of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501


        :return: The private_ip_address of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip_address

    @private_ip_address.setter
    def private_ip_address(self, private_ip_address):
        """Sets the private_ip_address of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.


        :param private_ip_address: The private_ip_address of this PrivateIpSetForDescribeNetworkInterfaceAttributesOutput.  # noqa: E501
        :type: str
        """

        self._private_ip_address = private_ip_address

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrivateIpSetForDescribeNetworkInterfaceAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrivateIpSetForDescribeNetworkInterfaceAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PrivateIpSetForDescribeNetworkInterfaceAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
