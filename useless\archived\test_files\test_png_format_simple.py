"""
Simple test to verify PNG format usage without circular imports.
"""

import os
import sys
import tempfile
from pathlib import Path
from PIL import Image
import io

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_screen_capture_png():
    """Test ScreenCapture module for PNG format."""
    from showforai.recorder.screen_capture import ScreenCapture
    
    temp_dir = tempfile.mkdtemp()
    print(f"Testing ScreenCapture in {temp_dir}")
    
    try:
        # Initialize and test
        capture = ScreenCapture(output_dir=temp_dir)
        capture.start()
        
        # Capture a screenshot
        filename = capture.capture_screenshot_sync()
        capture.stop()
        
        if filename:
            # Check extension
            assert filename.endswith('.png'), f"Expected .png extension, got: {filename}"
            print(f"✓ Screenshot has .png extension: {filename}")
            
            # Check actual format
            file_path = Path(temp_dir) / filename
            if file_path.exists():
                with Image.open(file_path) as img:
                    assert img.format == 'PNG', f"Expected PNG format, got: {img.format}"
                    print(f"✓ File is in PNG format")
        
        # Check no JPEG files created
        jpeg_files = list(Path(temp_dir).glob("*.jpg")) + list(Path(temp_dir).glob("*.jpeg"))
        assert len(jpeg_files) == 0, f"Found JPEG files: {jpeg_files}"
        print("✓ No JPEG files created")
        
    finally:
        # Cleanup
        for file in Path(temp_dir).glob("*"):
            file.unlink()
        Path(temp_dir).rmdir()
    
    print("ScreenCapture test passed!")


def test_image_processor_png():
    """Test ImageProcessor module for PNG format."""
    from showforai.ai.image_processor import ImageProcessor
    
    print("\nTesting ImageProcessor...")
    
    # Create test image
    img = Image.new('RGB', (100, 100), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    test_data = buffer.getvalue()
    
    processor = ImageProcessor()
    
    # Test process_screenshot defaults to PNG
    processed = processor.process_screenshot(test_data)
    img = Image.open(io.BytesIO(processed))
    assert img.format == 'PNG', f"process_screenshot should output PNG, got: {img.format}"
    print("✓ process_screenshot outputs PNG")
    
    # Test compress_image defaults to PNG
    compressed = processor.compress_image(test_data)
    img = Image.open(io.BytesIO(compressed))
    assert img.format == 'PNG', f"compress_image should output PNG, got: {img.format}"
    print("✓ compress_image outputs PNG")
    
    # Test convert_format defaults to PNG
    converted = processor.convert_format(test_data)
    img = Image.open(io.BytesIO(converted))
    assert img.format == 'PNG', f"convert_format should output PNG, got: {img.format}"
    print("✓ convert_format outputs PNG")
    
    print("ImageProcessor test passed!")


def test_image_optimizer_png():
    """Test ImageOptimizer module for PNG format."""
    import numpy as np
    
    print("\nTesting ImageOptimizer...")
    
    try:
        from showforai.optimizer.image_optimizer import ImageOptimizer
        
        optimizer = ImageOptimizer()
        
        # Create test numpy array
        test_array = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        # Test compress_image defaults to PNG
        compressed = optimizer.compress_image(test_array)
        if compressed:
            img = Image.open(io.BytesIO(compressed))
            assert img.format == 'PNG', f"compress_image should output PNG, got: {img.format}"
            print("✓ compress_image outputs PNG")
        
        # Test optimize_for_storage with screenshot flag
        optimized = optimizer.optimize_for_storage(test_array, is_screenshot=True)
        if optimized:
            img = Image.open(io.BytesIO(optimized))
            assert img.format == 'PNG', f"optimize_for_storage should output PNG for screenshots, got: {img.format}"
            print("✓ optimize_for_storage outputs PNG for screenshots")
        
        print("ImageOptimizer test passed!")
        
    except ImportError as e:
        print(f"Could not import ImageOptimizer (may need opencv-python): {e}")


def main():
    """Run all tests."""
    print("=" * 50)
    print("Testing PNG Format Unification")
    print("=" * 50)
    
    try:
        test_screen_capture_png()
        test_image_processor_png()
        test_image_optimizer_png()
        
        print("\n" + "=" * 50)
        print("ALL TESTS PASSED! ✓")
        print("PNG format is now used exclusively.")
        print("=" * 50)
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()