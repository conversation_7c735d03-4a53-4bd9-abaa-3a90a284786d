"""
Simple test script for network exception handling enhancement.
Tests Task 4: Perfect Network Exception Handling
"""

import time
import json
import sys
from pathlib import Path

# Add project to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import only what we need to avoid circular imports
from showforai.utils.api_handler import <PERSON>hancedAP<PERSON>Hand<PERSON>, NetworkStatus, RetryConfig, RetryStrategy
from showforai.sync.offline_manager import OfflineManager


def test_exponential_backoff():
    """Test exponential backoff retry mechanism."""
    print("\n=== Testing Exponential Backoff ===")
    
    retry_config = RetryConfig(
        max_retries=3,
        initial_delay=1.0,
        max_delay=10.0,
        exponential_base=2.0,
        jitter=False  # Disable jitter for predictable testing
    )
    
    api_handler = EnhancedAPIHandler(
        base_url="http://test.example.com",
        retry_config=retry_config
    )
    
    # Test delay calculation
    delays = []
    for i in range(4):
        delay = api_handler._calculate_retry_delay(i)
        delays.append(delay)
        print(f"Attempt {i+1}: Delay = {delay:.2f}s")
    
    # Verify exponential growth
    assert delays[0] == 1.0  # Initial delay
    assert delays[1] == 2.0  # 1 * 2^1
    assert delays[2] == 4.0  # 1 * 2^2
    assert delays[3] == 8.0  # 1 * 2^3
    print("✓ Exponential backoff working correctly")
    
    api_handler.stop_monitoring()


def test_linear_backoff():
    """Test linear backoff strategy."""
    print("\n=== Testing Linear Backoff ===")
    
    retry_config = RetryConfig(
        max_retries=3,
        initial_delay=2.0,
        strategy=RetryStrategy.LINEAR,
        jitter=False
    )
    
    api_handler = EnhancedAPIHandler(
        base_url="http://test.example.com",
        retry_config=retry_config
    )
    
    # Test delay calculation
    delays = []
    for i in range(3):
        delay = api_handler._calculate_retry_delay(i)
        delays.append(delay)
        print(f"Attempt {i+1}: Delay = {delay:.2f}s")
    
    # Verify linear growth
    assert delays[0] == 2.0  # 2 * 1
    assert delays[1] == 4.0  # 2 * 2
    assert delays[2] == 6.0  # 2 * 3
    print("✓ Linear backoff working correctly")
    
    api_handler.stop_monitoring()


def test_network_status_tracking():
    """Test network status tracking and metrics."""
    print("\n=== Testing Network Status Tracking ===")
    
    status_changes = []
    
    def status_callback(status: NetworkStatus):
        status_changes.append(status)
        print(f"  Status changed to: {status.value}")
    
    api_handler = EnhancedAPIHandler(
        base_url="http://localhost:8000",
        status_callback=status_callback
    )
    
    # Simulate various operations
    api_handler.metrics.total_requests = 10
    api_handler.metrics.successful_requests = 7
    api_handler.metrics.failed_requests = 3
    api_handler.metrics.total_retries = 5
    api_handler.metrics.consecutive_failures = 2
    
    # Get metrics
    metrics = api_handler.get_metrics()
    print(f"\nNetwork Metrics:")
    print(f"  Status: {metrics['status']}")
    print(f"  Success rate: {metrics['success_rate']}")
    print(f"  Total retries: {metrics['total_retries']}")
    print(f"  Consecutive failures: {metrics['consecutive_failures']}")
    
    assert metrics['total_requests'] == 10
    assert metrics['successful_requests'] == 7
    assert metrics['failed_requests'] == 3
    print("✓ Network status tracking working correctly")
    
    api_handler.stop_monitoring()


def test_offline_queue():
    """Test offline request queue."""
    print("\n=== Testing Offline Queue ===")
    
    offline_manager = OfflineManager()
    
    # Clear any existing queue
    offline_manager.clear_queue()
    
    # Queue some requests
    offline_manager.queue_request({
        "method": "POST",
        "endpoint": "/api/v2/detect",
        "data": {"test": "data1"},
        "timestamp": "2024-01-01T12:00:00"
    })
    
    offline_manager.queue_request({
        "method": "GET",
        "endpoint": "/api/v2/status",
        "data": {"test": "data2"},
        "timestamp": "2024-01-01T12:01:00"
    })
    
    # Get queued requests
    queued = offline_manager.get_queued_requests()
    print(f"  Queued {len(queued)} requests")
    
    assert len(queued) == 2
    assert queued[0]["endpoint"] == "/api/v2/detect"
    assert queued[1]["endpoint"] == "/api/v2/status"
    print("✓ Offline queue working correctly")
    
    # Clear queue
    offline_manager.clear_queue()
    assert len(offline_manager.get_queued_requests()) == 0
    print("✓ Queue clearing working correctly")


def test_error_analysis():
    """Test error logging and analysis."""
    print("\n=== Testing Error Analysis ===")
    
    api_handler = EnhancedAPIHandler(
        base_url="http://test.example.com"
    )
    
    # Record some errors
    api_handler._record_error("network_error", {
        "error": "Connection timeout",
        "url": "/api/test1"
    })
    
    api_handler._record_error("http_error", {
        "error": "500 Server Error",
        "url": "/api/test2"
    })
    
    api_handler._record_error("network_error", {
        "error": "Connection refused",
        "url": "/api/test3"
    })
    
    # Get error analysis
    analysis = api_handler.get_error_analysis()
    print(f"  Total errors: {analysis['total_errors']}")
    print(f"  Error types: {analysis['error_types']}")
    
    assert analysis['total_errors'] == 3
    assert analysis['error_types']['network_error'] == 2
    assert analysis['error_types']['http_error'] == 1
    print("✓ Error analysis working correctly")
    
    api_handler.stop_monitoring()


def test_network_status_transitions():
    """Test network status state transitions."""
    print("\n=== Testing Network Status Transitions ===")
    
    api_handler = EnhancedAPIHandler(
        base_url="http://test.example.com"
    )
    
    # Test state transitions
    initial_status = api_handler.network_status
    print(f"  Initial status: {initial_status.value}")
    
    # Simulate successful request
    api_handler._handle_success(1.0)
    assert api_handler.network_status == NetworkStatus.ONLINE
    print(f"  After success: {api_handler.network_status.value}")
    
    # Simulate multiple failures
    for i in range(3):
        api_handler._handle_failure(Exception("Test error"), "http://test.url")
    
    assert api_handler.metrics.consecutive_failures == 3
    assert api_handler.network_status == NetworkStatus.DEGRADED
    print(f"  After 3 failures: {api_handler.network_status.value}")
    
    # More failures trigger offline mode
    for i in range(2):
        api_handler._handle_failure(Exception("Test error"), "http://test.url")
    
    assert api_handler.metrics.consecutive_failures == 5
    assert api_handler.network_status == NetworkStatus.OFFLINE
    print(f"  After 5 failures: {api_handler.network_status.value}")
    
    # Success resets consecutive failures
    api_handler._handle_success(1.0)
    assert api_handler.metrics.consecutive_failures == 0
    assert api_handler.network_status == NetworkStatus.ONLINE
    print(f"  After recovery: {api_handler.network_status.value}")
    
    print("✓ Network status transitions working correctly")
    
    api_handler.stop_monitoring()


def main():
    """Run all network handling tests."""
    print("=" * 60)
    print("NETWORK EXCEPTION HANDLING TEST SUITE")
    print("Testing Task 4: Perfect Network Exception Handling")
    print("=" * 60)
    
    try:
        test_exponential_backoff()
        test_linear_backoff()
        test_network_status_tracking()
        test_offline_queue()
        test_error_analysis()
        test_network_status_transitions()
        
        print("\n" + "=" * 60)
        print("ALL TESTS PASSED ✓")
        print("\nTask 4 Implementation Summary:")
        print("  ✓ 4.1 Enhanced API handler created")
        print("  ✓ 4.2 Exponential backoff retry mechanism")
        print("  ✓ 4.3 Graceful degradation to offline mode")
        print("  ✓ 4.4 Real-time network status monitoring")
        print("\nAdditional features implemented:")
        print("  ✓ Multiple retry strategies (exponential, linear, fibonacci)")
        print("  ✓ Offline request queue with persistence")
        print("  ✓ Detailed error logging and analysis")
        print("  ✓ Network metrics tracking")
        print("  ✓ Status change callbacks")
        print("  ✓ Automatic sync when connection restored")
        print("=" * 60)
        
        return True
        
    except AssertionError as e:
        print(f"\n❌ Test assertion failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)