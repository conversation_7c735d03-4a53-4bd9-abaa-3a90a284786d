# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MetaForUpdateRuleInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comparator': 'str',
        'key': 'str',
        'values': 'list[str]'
    }

    attribute_map = {
        'comparator': 'Comparator',
        'key': 'Key',
        'values': 'Values'
    }

    def __init__(self, comparator=None, key=None, values=None, _configuration=None):  # noqa: E501
        """MetaForUpdateRuleInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comparator = None
        self._key = None
        self._values = None
        self.discriminator = None

        if comparator is not None:
            self.comparator = comparator
        if key is not None:
            self.key = key
        if values is not None:
            self.values = values

    @property
    def comparator(self):
        """Gets the comparator of this MetaForUpdateRuleInput.  # noqa: E501


        :return: The comparator of this MetaForUpdateRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._comparator

    @comparator.setter
    def comparator(self, comparator):
        """Sets the comparator of this MetaForUpdateRuleInput.


        :param comparator: The comparator of this MetaForUpdateRuleInput.  # noqa: E501
        :type: str
        """

        self._comparator = comparator

    @property
    def key(self):
        """Gets the key of this MetaForUpdateRuleInput.  # noqa: E501


        :return: The key of this MetaForUpdateRuleInput.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this MetaForUpdateRuleInput.


        :param key: The key of this MetaForUpdateRuleInput.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def values(self):
        """Gets the values of this MetaForUpdateRuleInput.  # noqa: E501


        :return: The values of this MetaForUpdateRuleInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._values

    @values.setter
    def values(self, values):
        """Sets the values of this MetaForUpdateRuleInput.


        :param values: The values of this MetaForUpdateRuleInput.  # noqa: E501
        :type: list[str]
        """

        self._values = values

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MetaForUpdateRuleInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MetaForUpdateRuleInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MetaForUpdateRuleInput):
            return True

        return self.to_dict() != other.to_dict()
