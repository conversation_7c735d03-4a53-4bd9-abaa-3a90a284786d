#!/usr/bin/env python3
"""
检查Replicate账户余额和购买指导

这个脚本会检查你的账户余额，并提供购买积分的指导
"""

import os
import requests
import json

# 设置API Token
API_TOKEN = "****************************************"

def check_account_info():
    """检查账户信息和余额"""
    print("🔍 检查账户信息...")
    
    headers = {
        "Authorization": f"Token {API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # 获取账户信息
        response = requests.get("https://api.replicate.com/v1/account", headers=headers)
        
        if response.status_code == 200:
            account_info = response.json()
            
            print("✅ 账户信息:")
            print(f"   用户名: {account_info.get('username', 'N/A')}")
            print(f"   账户类型: {account_info.get('type', 'N/A')}")
            print(f"   GitHub用户名: {account_info.get('github_login', 'N/A')}")
            
            return True
        else:
            print(f"❌ 获取账户信息失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 检查账户信息失败: {str(e)}")
        return False

def get_billing_info():
    """获取计费信息"""
    print("\n💰 检查计费信息...")
    
    headers = {
        "Authorization": f"Token {API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # 尝试获取计费信息
        response = requests.get("https://api.replicate.com/v1/account/billing", headers=headers)
        
        if response.status_code == 200:
            billing_info = response.json()
            
            print("✅ 计费信息:")
            print(f"   当前余额: ${billing_info.get('balance', 'N/A')}")
            print(f"   本月使用: ${billing_info.get('current_month_usage', 'N/A')}")
            
            return billing_info
        else:
            print(f"⚠️  无法获取详细计费信息 (状态码: {response.status_code})")
            print("   这可能是因为账户还没有设置计费信息")
            return None
            
    except Exception as e:
        print(f"⚠️  获取计费信息失败: {str(e)}")
        return None

def show_pricing_info():
    """显示定价信息"""
    print("\n💵 OmniParserV2 定价信息:")
    print("   模型: microsoft/omniparser-v2")
    print("   硬件: Nvidia T4 GPU")
    print("   成本: 约 $0.0018 每次运行")
    print("   换算: 每 $1 可运行约 555 次")
    print("   平均处理时间: 8秒内")
    
    print("\n📊 使用量估算:")
    amounts = [1, 5, 10, 20]
    for amount in amounts:
        runs = int(amount / 0.0018)
        print(f"   ${amount:2d} = 约 {runs:4d} 次运行")

def show_purchase_guide():
    """显示购买指导"""
    print("\n🛒 如何购买积分:")
    print("   1. 访问: https://replicate.com/account/billing")
    print("   2. 点击 'Add credit' 或 'Purchase credit'")
    print("   3. 选择充值金额 (建议从 $5 开始)")
    print("   4. 完成支付")
    print("   5. 等待几分钟让积分生效")
    
    print("\n💡 建议:")
    print("   - 新用户建议先充值 $5-10 进行测试")
    print("   - 如果是生产环境，可以考虑 $20-50")
    print("   - 积分不会过期，可以随时使用")
    
    print("\n⚠️  注意事项:")
    print("   - 支付后需要等待几分钟积分才会生效")
    print("   - 确保使用的是有效的信用卡或PayPal账户")
    print("   - 可以设置自动充值避免余额不足")

def test_minimal_request():
    """测试最小请求以确认API工作"""
    print("\n🧪 测试API连接 (不消耗积分)...")
    
    headers = {
        "Authorization": f"Token {API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # 获取模型信息 (不消耗积分)
        response = requests.get(
            "https://api.replicate.com/v1/models/microsoft/omniparser-v2", 
            headers=headers
        )
        
        if response.status_code == 200:
            model_info = response.json()
            print("✅ 模型信息获取成功:")
            print(f"   模型名称: {model_info.get('name', 'N/A')}")
            print(f"   描述: {model_info.get('description', 'N/A')[:100]}...")
            print(f"   可见性: {model_info.get('visibility', 'N/A')}")
            
            # 获取最新版本信息
            if 'latest_version' in model_info:
                version = model_info['latest_version']
                print(f"   最新版本: {version.get('id', 'N/A')[:20]}...")
                print(f"   创建时间: {version.get('created_at', 'N/A')}")
            
            return True
        else:
            print(f"❌ 获取模型信息失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Replicate 账户检查和购买指导")
    print("=" * 50)
    print(f"🔑 API Token: {API_TOKEN[:15]}...")
    print()
    
    # 1. 检查账户信息
    account_ok = check_account_info()
    
    if not account_ok:
        print("\n❌ 无法获取账户信息，请检查API Token")
        return
    
    # 2. 检查计费信息
    billing_info = get_billing_info()
    
    # 3. 测试API连接
    api_ok = test_minimal_request()
    
    # 4. 显示定价信息
    show_pricing_info()
    
    # 5. 显示购买指导
    show_purchase_guide()
    
    print("\n" + "=" * 50)
    print("📋 总结:")
    print(f"   🔑 API Token: ✅ 有效")
    print(f"   👤 账户状态: ✅ 正常")
    print(f"   🔌 API连接: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"   💰 账户余额: ❌ 不足 (需要充值)")
    
    print("\n🎯 下一步:")
    print("   1. 访问 https://replicate.com/account/billing 充值")
    print("   2. 建议充值 $5-10 开始测试")
    print("   3. 充值后等待几分钟")
    print("   4. 重新运行 python test_api_key.py 测试")
    
    print("\n💡 充值完成后，你就可以开始使用OmniParserV2了！")

if __name__ == "__main__":
    main()
