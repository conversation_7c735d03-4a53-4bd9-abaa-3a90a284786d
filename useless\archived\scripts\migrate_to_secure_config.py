#!/usr/bin/env python3
"""
Migration script to help users transition to secure configuration management.

This script will:
1. Check for existing hardcoded configuration
2. Create a .env.example template
3. Help users set up their .env file
4. Validate the configuration
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import showforai modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from showforai.security.config_manager import SecureConfigManager
import click


@click.command()
@click.option('--check-only', is_flag=True, help='Only check configuration without creating files')
@click.option('--create-template', is_flag=True, help='Create .env.example template')
@click.option('--validate', is_flag=True, help='Validate current configuration')
def migrate(check_only, create_template, validate):
    """Migrate to secure configuration management."""
    
    click.echo("🔐 ShowForAI V3 - Secure Configuration Migration Tool")
    click.echo("=" * 50)
    
    # Initialize SecureConfigManager
    config_manager = SecureConfigManager()
    
    # Step 1: Check current configuration
    if check_only or not (create_template or validate):
        click.echo("\n📋 Checking current configuration...")
        validation_results = config_manager.validate_environment()
        
        missing_required = []
        configured_required = []
        
        for var, is_present in validation_results.items():
            if is_present:
                configured_required.append(var)
            else:
                missing_required.append(var)
        
        if configured_required:
            click.echo(f"\n✅ Configured ({len(configured_required)}):")
            for var in configured_required:
                value = config_manager.get_secret(var)
                masked = config_manager.get_all_config().get(var, "***")
                click.echo(f"  • {var}: {masked}")
        
        if missing_required:
            click.echo(f"\n❌ Missing Required ({len(missing_required)}):")
            for var in missing_required:
                click.echo(f"  • {var}")
            
            click.echo("\n⚠️  Please configure the missing environment variables.")
            click.echo("Run with --create-template to generate a .env.example file.")
            return
        
        click.echo("\n✅ All required environment variables are configured!")
    
    # Step 2: Create template if requested
    if create_template:
        click.echo("\n📝 Creating .env.example template...")
        
        template_path = Path.cwd() / ".env.example"
        config_manager.create_env_template(template_path)
        
        click.echo(f"✅ Template created at: {template_path}")
        click.echo("\nNext steps:")
        click.echo("1. Copy .env.example to .env")
        click.echo("2. Fill in your actual configuration values")
        click.echo("3. Run this script with --validate to verify")
    
    # Step 3: Validate configuration if requested
    if validate:
        click.echo("\n🔍 Validating configuration...")
        
        validation_results = config_manager.validate_environment()
        all_valid = all(validation_results.values())
        
        if all_valid:
            click.echo("✅ Configuration is valid and complete!")
            
            # Test connections if possible
            click.echo("\n🌐 Testing connections...")
            
            # Test Supabase connection
            try:
                from showforai.storage.supabase_client import SupabaseStorageClient
                storage_client = SupabaseStorageClient(config_manager=config_manager)
                if storage_client.test_connection():
                    click.echo("  ✅ Supabase connection successful")
                else:
                    click.echo("  ❌ Supabase connection failed")
            except Exception as e:
                click.echo(f"  ❌ Supabase connection error: {e}")
            
            # Check proxy configuration
            proxy_config = config_manager.get_proxy_config()
            if proxy_config.get("enable_proxy_mode"):
                click.echo(f"  ✅ Proxy mode enabled: {proxy_config['url']}")
            else:
                click.echo("  ℹ️  Proxy mode disabled")
            
            # Display performance settings
            perf_config = config_manager.get_performance_config()
            click.echo("\n⚙️  Performance Settings:")
            click.echo(f"  • Max CPU: {perf_config['max_cpu_percent']}%")
            click.echo(f"  • Cache TTL: {perf_config['cache_ttl']}s")
            click.echo(f"  • Max Batch Size: {perf_config['max_batch_size']}")
            
        else:
            click.echo("❌ Configuration validation failed!")
            click.echo("Please check the missing variables above.")
            sys.exit(1)
    
    # Migration tips
    if not any([check_only, create_template, validate]):
        click.echo("\n📚 Migration Guide:")
        click.echo("-" * 40)
        click.echo("1. Run with --check-only to see current status")
        click.echo("2. Run with --create-template to generate .env.example")
        click.echo("3. Copy .env.example to .env and fill in values")
        click.echo("4. Run with --validate to verify configuration")
        click.echo("\n💡 All API keys are now managed securely through:")
        click.echo("  • Environment variables (.env file)")
        click.echo("  • Proxy server (recommended for production)")
        click.echo("  • Encrypted local storage (optional)")


if __name__ == "__main__":
    migrate()