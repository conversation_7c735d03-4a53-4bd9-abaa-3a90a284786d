"""
Test script for validating the force containment rule implementation.

This script tests that the AI detection always returns a BBOX that contains
the click position after conversion to 768x768 coordinates.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.ai.element_detector import ElementDetector
from showforai.ai.bbox_processor import BboxProcessor
from loguru import logger
import json

def test_containment_validation():
    """Test the containment validation functions."""
    
    logger.info("Testing BBOX containment validation...")
    
    processor = BboxProcessor()
    
    # Test case 1: Point inside bbox
    bbox1 = {'x': 100, 'y': 100, 'width': 200, 'height': 150}
    click1 = (150, 120)  # Inside
    result1 = processor.contains_point(bbox1, click1[0], click1[1])
    logger.info(f"Test 1 - Point {click1} in bbox {bbox1}: {result1} (expected: True)")
    assert result1 == True, "Should contain point inside bbox"
    
    # Test case 2: Point outside bbox
    bbox2 = {'x': 100, 'y': 100, 'width': 200, 'height': 150}
    click2 = (50, 120)  # Outside left
    result2 = processor.contains_point(bbox2, click2[0], click2[1])
    logger.info(f"Test 2 - Point {click2} in bbox {bbox2}: {result2} (expected: False)")
    assert result2 == False, "Should not contain point outside bbox"
    
    # Test case 3: Point on edge (should be contained)
    bbox3 = {'x': 100, 'y': 100, 'width': 200, 'height': 150}
    click3 = (300, 250)  # On right-bottom edge
    result3 = processor.contains_point(bbox3, click3[0], click3[1])
    logger.info(f"Test 3 - Point {click3} on edge of bbox {bbox3}: {result3} (expected: True)")
    assert result3 == True, "Should contain point on edge"
    
    logger.success("Basic containment tests passed!")

def test_bbox_adjustment():
    """Test BBOX adjustment to contain click points."""
    
    logger.info("Testing BBOX adjustment for containment...")
    
    processor = BboxProcessor()
    
    # Test case 1: Adjust bbox to contain point to the left
    bbox1 = {'x': 200, 'y': 200, 'width': 100, 'height': 100}
    click1_x, click1_y = 150, 250
    adjusted1 = processor.ensure_bbox_contains_point(bbox1, click1_x, click1_y)
    
    assert processor.contains_point(adjusted1, click1_x, click1_y), \
        f"Adjusted bbox should contain point ({click1_x}, {click1_y})"
    logger.success(f"Test 1 passed - Adjusted left: {bbox1} -> {adjusted1}")
    
    # Test case 2: Adjust bbox to contain point to the right
    bbox2 = {'x': 200, 'y': 200, 'width': 100, 'height': 100}
    click2_x, click2_y = 350, 250
    adjusted2 = processor.ensure_bbox_contains_point(bbox2, click2_x, click2_y)
    
    assert processor.contains_point(adjusted2, click2_x, click2_y), \
        f"Adjusted bbox should contain point ({click2_x}, {click2_y})"
    logger.success(f"Test 2 passed - Adjusted right: {bbox2} -> {adjusted2}")
    
    # Test case 3: Adjust bbox to contain point above
    bbox3 = {'x': 200, 'y': 200, 'width': 100, 'height': 100}
    click3_x, click3_y = 250, 150
    adjusted3 = processor.ensure_bbox_contains_point(bbox3, click3_x, click3_y)
    
    assert processor.contains_point(adjusted3, click3_x, click3_y), \
        f"Adjusted bbox should contain point ({click3_x}, {click3_y})"
    logger.success(f"Test 3 passed - Adjusted up: {bbox3} -> {adjusted3}")
    
    # Test case 4: Adjust bbox to contain point below
    bbox4 = {'x': 200, 'y': 200, 'width': 100, 'height': 100}
    click4_x, click4_y = 250, 350
    adjusted4 = processor.ensure_bbox_contains_point(bbox4, click4_x, click4_y)
    
    assert processor.contains_point(adjusted4, click4_x, click4_y), \
        f"Adjusted bbox should contain point ({click4_x}, {click4_y})"
    logger.success(f"Test 4 passed - Adjusted down: {bbox4} -> {adjusted4}")
    
    # Test case 5: Point already inside - no adjustment needed
    bbox5 = {'x': 200, 'y': 200, 'width': 100, 'height': 100}
    click5_x, click5_y = 250, 250
    adjusted5 = processor.ensure_bbox_contains_point(bbox5, click5_x, click5_y)
    
    assert adjusted5 == bbox5, "Bbox should not change when point is already inside"
    logger.success(f"Test 5 passed - No adjustment needed: {bbox5}")
    
    logger.success("All BBOX adjustment tests passed!")

def test_coordinate_conversion():
    """Test coordinate conversion from original to 768x768."""
    
    logger.info("Testing coordinate conversion to 768x768...")
    
    processor = BboxProcessor()
    
    # Test case 1: Convert from 1920x1080 to 768x768
    original_width, original_height = 1920, 1080
    click_x, click_y = 960, 540  # Center of 1920x1080
    
    converted = processor.convert_click_to_standard(
        click_x, click_y, original_width, original_height
    )
    
    expected_x = int(960 * 768 / 1920)  # Should be 384
    expected_y = int(540 * 768 / 1080)  # Should be 384
    
    assert converted['x'] == expected_x, f"X conversion failed: {converted['x']} != {expected_x}"
    assert converted['y'] == expected_y, f"Y conversion failed: {converted['y']} != {expected_y}"
    logger.success(f"Test 1 passed - Converted ({click_x}, {click_y}) from {original_width}x{original_height} "
                  f"to ({converted['x']}, {converted['y']}) in 768x768")
    
    # Test case 2: Convert from 2560x1440 to 768x768
    original_width2, original_height2 = 2560, 1440
    click_x2, click_y2 = 1280, 720  # Center
    
    converted2 = processor.convert_click_to_standard(
        click_x2, click_y2, original_width2, original_height2
    )
    
    expected_x2 = int(1280 * 768 / 2560)  # Should be 384
    expected_y2 = int(720 * 768 / 1440)  # Should be 384
    
    assert converted2['x'] == expected_x2, f"X conversion failed: {converted2['x']} != {expected_x2}"
    assert converted2['y'] == expected_y2, f"Y conversion failed: {converted2['y']} != {expected_y2}"
    logger.success(f"Test 2 passed - Converted ({click_x2}, {click_y2}) from {original_width2}x{original_height2} "
                  f"to ({converted2['x']}, {converted2['y']}) in 768x768")
    
    # Test case 3: Edge case - maximum coordinates
    original_width3, original_height3 = 1920, 1080
    click_x3, click_y3 = 1919, 1079  # Max coordinates
    
    converted3 = processor.convert_click_to_standard(
        click_x3, click_y3, original_width3, original_height3
    )
    
    assert converted3['x'] <= 767, f"X should be within bounds: {converted3['x']}"
    assert converted3['y'] <= 767, f"Y should be within bounds: {converted3['y']}"
    logger.success(f"Test 3 passed - Edge case handled: ({click_x3}, {click_y3}) -> "
                  f"({converted3['x']}, {converted3['y']})")
    
    logger.success("All coordinate conversion tests passed!")

def test_validation_logging():
    """Test validation logging functionality."""
    
    logger.info("Testing validation logging...")
    
    processor = BboxProcessor()
    
    # Test validation with contained click
    bbox = {'x': 100, 'y': 100, 'width': 200, 'height': 200}
    click_x, click_y = 200, 200
    
    is_valid = processor.validate_click_in_bbox(bbox, click_x, click_y, "768x768")
    assert is_valid == True, "Should validate as contained"
    logger.success("Validation logging test 1 passed - Click contained")
    
    # Test validation with non-contained click
    click_x2, click_y2 = 50, 50
    is_valid2 = processor.validate_click_in_bbox(bbox, click_x2, click_y2, "768x768")
    assert is_valid2 == False, "Should validate as not contained"
    logger.success("Validation logging test 2 passed - Click not contained")
    
    logger.success("All validation logging tests passed!")

def main():
    """Run all tests."""
    
    logger.info("="*60)
    logger.info("Starting Force Containment Rule Tests")
    logger.info("="*60)
    
    try:
        # Run test suites
        test_containment_validation()
        logger.info("")
        
        test_bbox_adjustment()
        logger.info("")
        
        test_coordinate_conversion()
        logger.info("")
        
        test_validation_logging()
        logger.info("")
        
        logger.success("="*60)
        logger.success("ALL TESTS PASSED SUCCESSFULLY!")
        logger.success("Force containment rule implementation is working correctly.")
        logger.success("="*60)
        
    except AssertionError as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()