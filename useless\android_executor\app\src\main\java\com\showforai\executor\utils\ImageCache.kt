package com.showforai.executor.utils

import android.graphics.Bitmap
import android.util.LruCache
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 图像缓存管理器
 * 
 * 使用LRU缓存管理图像，优化内存使用：
 * - 模板图像缓存
 * - 屏幕截图缓存
 * - 自动内存管理
 * - 缓存统计
 */
@Singleton
class ImageCache @Inject constructor() {
    
    companion object {
        // 缓存大小配置
        private const val TEMPLATE_CACHE_SIZE_MB = 20 // 模板图像缓存大小
        private const val SCREENSHOT_CACHE_SIZE_MB = 10 // 屏幕截图缓存大小
        private const val BYTES_PER_MB = 1024 * 1024
        
        // 缓存键前缀
        private const val TEMPLATE_KEY_PREFIX = "template_"
        private const val SCREENSHOT_KEY_PREFIX = "screenshot_"
    }
    
    // 模板图像缓存
    private val templateCache: LruCache<String, Bitmap> = object : LruCache<String, Bitmap>(
        TEMPLATE_CACHE_SIZE_MB * BYTES_PER_MB
    ) {
        override fun sizeOf(key: String, bitmap: Bitmap): Int {
            return bitmap.byteCount
        }
        
        override fun entryRemoved(evicted: Boolean, key: String, oldValue: Bitmap, newValue: Bitmap?) {
            if (evicted) {
                Timber.d("Template image evicted from cache: $key")
                // 不要在这里回收Bitmap，可能还在使用中
            }
        }
    }
    
    // 屏幕截图缓存
    private val screenshotCache: LruCache<String, Bitmap> = object : LruCache<String, Bitmap>(
        SCREENSHOT_CACHE_SIZE_MB * BYTES_PER_MB
    ) {
        override fun sizeOf(key: String, bitmap: Bitmap): Int {
            return bitmap.byteCount
        }
        
        override fun entryRemoved(evicted: Boolean, key: String, oldValue: Bitmap, newValue: Bitmap?) {
            if (evicted) {
                Timber.d("Screenshot evicted from cache: $key")
            }
        }
    }
    
    // 缓存统计
    private var templateHits = 0
    private var templateMisses = 0
    private var screenshotHits = 0
    private var screenshotMisses = 0
    
    /**
     * 缓存模板图像
     */
    fun putTemplate(key: String, bitmap: Bitmap) {
        val cacheKey = TEMPLATE_KEY_PREFIX + key
        templateCache.put(cacheKey, bitmap)
        Timber.d("Template image cached: $key, size: ${bitmap.byteCount} bytes")
    }
    
    /**
     * 获取模板图像
     */
    fun getTemplate(key: String): Bitmap? {
        val cacheKey = TEMPLATE_KEY_PREFIX + key
        val bitmap = templateCache.get(cacheKey)
        
        if (bitmap != null) {
            templateHits++
            Timber.d("Template cache hit: $key")
        } else {
            templateMisses++
            Timber.d("Template cache miss: $key")
        }
        
        return bitmap
    }
    
    /**
     * 缓存屏幕截图
     */
    fun putScreenshot(key: String, bitmap: Bitmap) {
        val cacheKey = SCREENSHOT_KEY_PREFIX + key
        screenshotCache.put(cacheKey, bitmap)
        Timber.d("Screenshot cached: $key, size: ${bitmap.byteCount} bytes")
    }
    
    /**
     * 获取屏幕截图
     */
    fun getScreenshot(key: String): Bitmap? {
        val cacheKey = SCREENSHOT_KEY_PREFIX + key
        val bitmap = screenshotCache.get(cacheKey)
        
        if (bitmap != null) {
            screenshotHits++
            Timber.d("Screenshot cache hit: $key")
        } else {
            screenshotMisses++
            Timber.d("Screenshot cache miss: $key")
        }
        
        return bitmap
    }
    
    /**
     * 移除模板图像
     */
    fun removeTemplate(key: String) {
        val cacheKey = TEMPLATE_KEY_PREFIX + key
        templateCache.remove(cacheKey)
        Timber.d("Template image removed from cache: $key")
    }
    
    /**
     * 移除屏幕截图
     */
    fun removeScreenshot(key: String) {
        val cacheKey = SCREENSHOT_KEY_PREFIX + key
        screenshotCache.remove(cacheKey)
        Timber.d("Screenshot removed from cache: $key")
    }
    
    /**
     * 清除所有模板缓存
     */
    fun clearTemplateCache() {
        templateCache.evictAll()
        Timber.i("Template cache cleared")
    }
    
    /**
     * 清除所有屏幕截图缓存
     */
    fun clearScreenshotCache() {
        screenshotCache.evictAll()
        Timber.i("Screenshot cache cleared")
    }
    
    /**
     * 清除所有缓存
     */
    fun clearAllCache() {
        clearTemplateCache()
        clearScreenshotCache()
        resetStatistics()
        Timber.i("All image cache cleared")
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStatistics(): CacheStatistics {
        return CacheStatistics(
            templateCacheSize = templateCache.size(),
            templateCacheMaxSize = templateCache.maxSize(),
            templateCacheHitCount = templateCache.hitCount(),
            templateCacheMissCount = templateCache.missCount(),
            templateCacheHitRate = if (templateHits + templateMisses > 0) {
                templateHits.toFloat() / (templateHits + templateMisses)
            } else 0f,
            
            screenshotCacheSize = screenshotCache.size(),
            screenshotCacheMaxSize = screenshotCache.maxSize(),
            screenshotCacheHitCount = screenshotCache.hitCount(),
            screenshotCacheMissCount = screenshotCache.missCount(),
            screenshotCacheHitRate = if (screenshotHits + screenshotMisses > 0) {
                screenshotHits.toFloat() / (screenshotHits + screenshotMisses)
            } else 0f
        )
    }
    
    /**
     * 获取缓存使用情况
     */
    fun getCacheUsage(): CacheUsage {
        val templateUsage = templateCache.size().toFloat() / templateCache.maxSize()
        val screenshotUsage = screenshotCache.size().toFloat() / screenshotCache.maxSize()
        
        return CacheUsage(
            templateCacheUsagePercent = (templateUsage * 100).toInt(),
            screenshotCacheUsagePercent = (screenshotUsage * 100).toInt(),
            totalMemoryUsageMB = (templateCache.size() + screenshotCache.size()) / BYTES_PER_MB.toFloat()
        )
    }
    
    /**
     * 检查是否需要清理缓存
     */
    fun shouldCleanupCache(): Boolean {
        val usage = getCacheUsage()
        return usage.templateCacheUsagePercent > 90 || usage.screenshotCacheUsagePercent > 90
    }
    
    /**
     * 执行缓存清理
     */
    fun performCacheCleanup() {
        if (shouldCleanupCache()) {
            // 清理旧的屏幕截图（保留最近的）
            val screenshotUsage = screenshotCache.size().toFloat() / screenshotCache.maxSize()
            if (screenshotUsage > 0.8f) {
                // 清理一半的屏幕截图缓存
                val targetSize = screenshotCache.maxSize() / 2
                screenshotCache.trimToSize(targetSize)
                Timber.i("Screenshot cache trimmed to ${targetSize / BYTES_PER_MB}MB")
            }
            
            // 模板图像通常需要保留，只在内存压力很大时清理
            val templateUsage = templateCache.size().toFloat() / templateCache.maxSize()
            if (templateUsage > 0.95f) {
                val targetSize = (templateCache.maxSize() * 0.8f).toInt()
                templateCache.trimToSize(targetSize)
                Timber.i("Template cache trimmed to ${targetSize / BYTES_PER_MB}MB")
            }
        }
    }
    
    /**
     * 重置统计信息
     */
    private fun resetStatistics() {
        templateHits = 0
        templateMisses = 0
        screenshotHits = 0
        screenshotMisses = 0
    }
    
    /**
     * 生成屏幕截图缓存键
     */
    fun generateScreenshotKey(timestamp: Long): String {
        return "screenshot_$timestamp"
    }
    
    /**
     * 生成模板图像缓存键
     */
    fun generateTemplateKey(visualHash: String): String {
        return "template_$visualHash"
    }
}

/**
 * 缓存统计信息
 */
data class CacheStatistics(
    val templateCacheSize: Int,
    val templateCacheMaxSize: Int,
    val templateCacheHitCount: Long,
    val templateCacheMissCount: Long,
    val templateCacheHitRate: Float,
    
    val screenshotCacheSize: Int,
    val screenshotCacheMaxSize: Int,
    val screenshotCacheHitCount: Long,
    val screenshotCacheMissCount: Long,
    val screenshotCacheHitRate: Float
)

/**
 * 缓存使用情况
 */
data class CacheUsage(
    val templateCacheUsagePercent: Int,
    val screenshotCacheUsagePercent: Int,
    val totalMemoryUsageMB: Float
)
