package com.showforai.executor.ui.execution

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.showforai.executor.data.models.ExecutionLog
import com.showforai.executor.data.models.ExecutionStatus
import com.showforai.executor.data.models.LogLevel
import com.showforai.executor.ui.theme.DSLExecutorTheme
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 执行监控界面
 * 
 * 显示脚本执行状态和进度：
 * - 实时执行状态
 * - 执行进度条
 * - 执行日志
 * - 控制按钮（暂停/恢复/停止）
 */
@AndroidEntryPoint
class ExecutionActivity : ComponentActivity() {
    
    private val viewModel: ExecutionViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            DSLExecutorTheme {
                ExecutionScreen(
                    viewModel = viewModel,
                    onNavigateBack = { finish() }
                )
            }
        }
        
        // 从Intent获取脚本ID
        intent.getStringExtra("script_id")?.let { scriptId ->
            viewModel.loadScript(scriptId)
        }
        
        // 如果没有传入脚本ID，尝试使用当前加载的脚本
        if (!intent.hasExtra("script_id")) {
            viewModel.useCurrentScript()
        }
    }
    
    override fun onBackPressed() {
        // 如果正在执行，显示确认对话框
        if (viewModel.isExecuting()) {
            showExitConfirmationDialog()
        } else {
            super.onBackPressed()
        }
    }
    
    private fun showExitConfirmationDialog() {
        // 使用AlertDialog确认是否退出
        val builder = android.app.AlertDialog.Builder(this)
        builder.setTitle("确认退出")
            .setMessage("脚本正在执行中，退出将停止执行。确定要退出吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.stopExecution()
                finish()
            }
            .setNegativeButton("取消", null)
            .show()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExecutionScreen(
    viewModel: ExecutionViewModel,
    onNavigateBack: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("脚本执行") },
                navigationIcon = {
                    IconButton(onClick = {
                        if (uiState.executionStatus == ExecutionStatus.RUNNING || 
                            uiState.executionStatus == ExecutionStatus.PAUSED) {
                            // 显示确认对话框
                        } else {
                            onNavigateBack()
                        }
                    }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.toggleLogExpanded() }) {
                        Icon(
                            if (uiState.isLogExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                            contentDescription = "展开/收起日志"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 脚本信息卡片
            ScriptInfoCard(
                scriptName = uiState.scriptName,
                scriptDescription = uiState.scriptDescription
            )
            
            // 执行状态卡片
            ExecutionStatusCard(
                status = uiState.executionStatus,
                currentStep = uiState.currentStep,
                totalSteps = uiState.totalSteps,
                currentStepDescription = uiState.currentStepDescription,
                executionTime = uiState.executionTime,
                onPauseResume = { viewModel.togglePauseResume() },
                onStop = { viewModel.stopExecution() }
            )
            
            // 执行日志
            ExecutionLogsCard(
                logs = uiState.logs,
                isExpanded = uiState.isLogExpanded
            )
        }
    }
}

@Composable
fun ScriptInfoCard(
    scriptName: String,
    scriptDescription: String?
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "脚本信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "名称: $scriptName",
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (!scriptDescription.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "描述: $scriptDescription",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun ExecutionStatusCard(
    status: ExecutionStatus,
    currentStep: Int,
    totalSteps: Int,
    currentStepDescription: String,
    executionTime: Long,
    onPauseResume: () -> Unit,
    onStop: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 状态和时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    StatusIcon(status)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = getStatusText(status),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Text(
                    text = formatTime(executionTime),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 进度条
            LinearProgressIndicator(
                progress = if (totalSteps > 0) currentStep.toFloat() / totalSteps else 0f,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 步骤信息
            Text(
                text = "步骤 $currentStep / $totalSteps",
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 当前步骤描述
            if (currentStepDescription.isNotEmpty()) {
                Text(
                    text = "当前操作: $currentStepDescription",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // 控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                if (status == ExecutionStatus.RUNNING || status == ExecutionStatus.PAUSED) {
                    Button(
                        onClick = onPauseResume,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Icon(
                            if (status == ExecutionStatus.RUNNING) Icons.Default.Pause else Icons.Default.PlayArrow,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(if (status == ExecutionStatus.RUNNING) "暂停" else "继续")
                    }
                    
                    Button(
                        onClick = onStop,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Icon(Icons.Default.Stop, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("停止")
                    }
                } else if (status == ExecutionStatus.COMPLETED || status == ExecutionStatus.FAILED || status == ExecutionStatus.CANCELLED) {
                    Button(
                        onClick = { /* 重新执行 */ },
                        modifier = Modifier.fillMaxWidth(0.8f)
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重新执行")
                    }
                }
            }
        }
    }
}

@Composable
fun ExecutionLogsCard(
    logs: List<ExecutionLog>,
    isExpanded: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "执行日志",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = "${logs.size} 条记录",
                    style = MaterialTheme.typography.bodySmall
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (logs.isEmpty()) {
                Text(
                    text = "暂无日志记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    textAlign = TextAlign.Center
                )
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(if (isExpanded) 300.dp else 150.dp)
                ) {
                    items(logs) { log ->
                        LogItem(log)
                    }
                }
            }
        }
    }
}

@Composable
fun LogItem(log: ExecutionLog) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.Top
    ) {
        // 日志级别图标
        Box(
            modifier = Modifier
                .size(24.dp)
                .background(
                    color = getLogLevelColor(log.level),
                    shape = RoundedCornerShape(4.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = getLogLevelShortText(log.level),
                color = Color.White,
                style = MaterialTheme.typography.bodySmall
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 日志内容
        Column {
            Text(
                text = log.message,
                style = MaterialTheme.typography.bodySmall
            )
            
            if (log.details != null) {
                Text(
                    text = log.details,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun StatusIcon(status: ExecutionStatus) {
    val icon = when (status) {
        ExecutionStatus.IDLE -> Icons.Default.HourglassEmpty
        ExecutionStatus.PREPARING -> Icons.Default.Pending
        ExecutionStatus.RUNNING -> Icons.Default.PlayArrow
        ExecutionStatus.PAUSED -> Icons.Default.Pause
        ExecutionStatus.COMPLETED -> Icons.Default.CheckCircle
        ExecutionStatus.FAILED -> Icons.Default.Error
        ExecutionStatus.CANCELLED -> Icons.Default.Cancel
    }
    
    val tint = when (status) {
        ExecutionStatus.COMPLETED -> MaterialTheme.colorScheme.primary
        ExecutionStatus.FAILED -> MaterialTheme.colorScheme.error
        ExecutionStatus.CANCELLED -> MaterialTheme.colorScheme.error
        else -> MaterialTheme.colorScheme.onSurface
    }
    
    Icon(
        icon,
        contentDescription = getStatusText(status),
        tint = tint
    )
}

// 辅助函数
private fun getStatusText(status: ExecutionStatus): String {
    return when (status) {
        ExecutionStatus.IDLE -> "准备就绪"
        ExecutionStatus.PREPARING -> "准备中"
        ExecutionStatus.RUNNING -> "执行中"
        ExecutionStatus.PAUSED -> "已暂停"
        ExecutionStatus.COMPLETED -> "执行完成"
        ExecutionStatus.FAILED -> "执行失败"
        ExecutionStatus.CANCELLED -> "已取消"
    }
}

private fun formatTime(milliseconds: Long): String {
    val seconds = milliseconds / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    
    return when {
        hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
        minutes > 0 -> String.format("%d:%02d", minutes, seconds % 60)
        else -> String.format("%ds", seconds)
    }
}

private fun getLogLevelColor(level: LogLevel): Color {
    return when (level) {
        LogLevel.DEBUG -> Color(0xFF607D8B)  // Blue Gray
        LogLevel.INFO -> Color(0xFF2196F3)   // Blue
        LogLevel.WARNING -> Color(0xFFFFC107) // Amber
        LogLevel.ERROR -> Color(0xFFF44336)   // Red
        LogLevel.SUCCESS -> Color(0xFF4CAF50) // Green
    }
}

private fun getLogLevelShortText(level: LogLevel): String {
    return when (level) {
        LogLevel.DEBUG -> "D"
        LogLevel.INFO -> "I"
        LogLevel.WARNING -> "W"
        LogLevel.ERROR -> "E"
        LogLevel.SUCCESS -> "S"
    }
}
