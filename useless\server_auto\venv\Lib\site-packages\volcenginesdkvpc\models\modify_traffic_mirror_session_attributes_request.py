# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyTrafficMirrorSessionAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'packet_length': 'int',
        'priority': 'int',
        'traffic_mirror_filter_id': 'str',
        'traffic_mirror_session_id': 'str',
        'traffic_mirror_session_name': 'str',
        'traffic_mirror_target_id': 'str',
        'virtual_network_id': 'int'
    }

    attribute_map = {
        'description': 'Description',
        'packet_length': 'PacketLength',
        'priority': 'Priority',
        'traffic_mirror_filter_id': 'TrafficMirrorFilterId',
        'traffic_mirror_session_id': 'TrafficMirrorSessionId',
        'traffic_mirror_session_name': 'TrafficMirrorSessionName',
        'traffic_mirror_target_id': 'TrafficMirrorTargetId',
        'virtual_network_id': 'VirtualNetworkId'
    }

    def __init__(self, description=None, packet_length=None, priority=None, traffic_mirror_filter_id=None, traffic_mirror_session_id=None, traffic_mirror_session_name=None, traffic_mirror_target_id=None, virtual_network_id=None, _configuration=None):  # noqa: E501
        """ModifyTrafficMirrorSessionAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._packet_length = None
        self._priority = None
        self._traffic_mirror_filter_id = None
        self._traffic_mirror_session_id = None
        self._traffic_mirror_session_name = None
        self._traffic_mirror_target_id = None
        self._virtual_network_id = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if packet_length is not None:
            self.packet_length = packet_length
        if priority is not None:
            self.priority = priority
        if traffic_mirror_filter_id is not None:
            self.traffic_mirror_filter_id = traffic_mirror_filter_id
        self.traffic_mirror_session_id = traffic_mirror_session_id
        if traffic_mirror_session_name is not None:
            self.traffic_mirror_session_name = traffic_mirror_session_name
        if traffic_mirror_target_id is not None:
            self.traffic_mirror_target_id = traffic_mirror_target_id
        if virtual_network_id is not None:
            self.virtual_network_id = virtual_network_id

    @property
    def description(self):
        """Gets the description of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The description of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyTrafficMirrorSessionAttributesRequest.


        :param description: The description of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def packet_length(self):
        """Gets the packet_length of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The packet_length of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._packet_length

    @packet_length.setter
    def packet_length(self, packet_length):
        """Sets the packet_length of this ModifyTrafficMirrorSessionAttributesRequest.


        :param packet_length: The packet_length of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: int
        """

        self._packet_length = packet_length

    @property
    def priority(self):
        """Gets the priority of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The priority of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this ModifyTrafficMirrorSessionAttributesRequest.


        :param priority: The priority of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def traffic_mirror_filter_id(self):
        """Gets the traffic_mirror_filter_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The traffic_mirror_filter_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_filter_id

    @traffic_mirror_filter_id.setter
    def traffic_mirror_filter_id(self, traffic_mirror_filter_id):
        """Sets the traffic_mirror_filter_id of this ModifyTrafficMirrorSessionAttributesRequest.


        :param traffic_mirror_filter_id: The traffic_mirror_filter_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_filter_id = traffic_mirror_filter_id

    @property
    def traffic_mirror_session_id(self):
        """Gets the traffic_mirror_session_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The traffic_mirror_session_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_session_id

    @traffic_mirror_session_id.setter
    def traffic_mirror_session_id(self, traffic_mirror_session_id):
        """Sets the traffic_mirror_session_id of this ModifyTrafficMirrorSessionAttributesRequest.


        :param traffic_mirror_session_id: The traffic_mirror_session_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and traffic_mirror_session_id is None:
            raise ValueError("Invalid value for `traffic_mirror_session_id`, must not be `None`")  # noqa: E501

        self._traffic_mirror_session_id = traffic_mirror_session_id

    @property
    def traffic_mirror_session_name(self):
        """Gets the traffic_mirror_session_name of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The traffic_mirror_session_name of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_session_name

    @traffic_mirror_session_name.setter
    def traffic_mirror_session_name(self, traffic_mirror_session_name):
        """Sets the traffic_mirror_session_name of this ModifyTrafficMirrorSessionAttributesRequest.


        :param traffic_mirror_session_name: The traffic_mirror_session_name of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_session_name = traffic_mirror_session_name

    @property
    def traffic_mirror_target_id(self):
        """Gets the traffic_mirror_target_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The traffic_mirror_target_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_target_id

    @traffic_mirror_target_id.setter
    def traffic_mirror_target_id(self, traffic_mirror_target_id):
        """Sets the traffic_mirror_target_id of this ModifyTrafficMirrorSessionAttributesRequest.


        :param traffic_mirror_target_id: The traffic_mirror_target_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_target_id = traffic_mirror_target_id

    @property
    def virtual_network_id(self):
        """Gets the virtual_network_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501


        :return: The virtual_network_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._virtual_network_id

    @virtual_network_id.setter
    def virtual_network_id(self, virtual_network_id):
        """Sets the virtual_network_id of this ModifyTrafficMirrorSessionAttributesRequest.


        :param virtual_network_id: The virtual_network_id of this ModifyTrafficMirrorSessionAttributesRequest.  # noqa: E501
        :type: int
        """

        self._virtual_network_id = virtual_network_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyTrafficMirrorSessionAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyTrafficMirrorSessionAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyTrafficMirrorSessionAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
