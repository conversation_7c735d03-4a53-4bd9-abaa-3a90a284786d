"""Test script for CircularFrameBuffer implementation."""

import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from showforai.buffer_manager import CircularFrameBuffer
from loguru import logger

def test_circular_buffer():
    """Test the CircularFrameBuffer implementation."""
    
    logger.info("=== Testing CircularFrameBuffer ===")
    
    # Create buffer with 15 frames at 10 FPS
    buffer = CircularFrameBuffer(max_frames=15, fps=10)
    
    # Start continuous capture
    logger.info("Starting continuous capture...")
    buffer.start_capture()
    
    # Let it capture for 3 seconds (should capture multiple frames)
    logger.info("Capturing frames for 3 seconds...")
    time.sleep(3)
    
    # Get buffer statistics
    stats = buffer.get_buffer_stats()
    logger.info(f"Buffer stats after 3 seconds: {stats}")
    
    # Simulate a click and get frame from 0.3 seconds before
    click_time = time.perf_counter()
    logger.info(f"Simulating click at time: {click_time:.3f}")
    
    # Get single frame before click
    frame_before = buffer.get_frame_before_click(click_time, seconds_before=0.3)
    if frame_before:
        logger.info(f"Got frame from before click: Frame #{frame_before['frame_number']}")
        logger.info(f"  Time difference: {click_time - frame_before['timestamp']:.3f} seconds")
    else:
        logger.warning("No frame available before click")
    
    # Get multiple frames before click
    frames_before = buffer.get_frames_before_click(click_time, num_frames=3)
    logger.info(f"Got {len(frames_before)} frames before click")
    for i, frame in enumerate(frames_before):
        logger.info(f"  Frame {i}: #{frame['frame_number']}, "
                   f"time diff: {click_time - frame['timestamp']:.3f}s")
    
    # Continue capturing for another second
    time.sleep(1)
    
    # Get final statistics
    final_stats = buffer.get_buffer_stats()
    logger.info(f"Final buffer stats: {final_stats}")
    
    # Verify buffer size is correctly limited to 15
    assert final_stats['buffer_size'] <= 15, f"Buffer size exceeds limit: {final_stats['buffer_size']}"
    logger.success("✓ Buffer size correctly limited to 15 frames")
    
    # Verify continuous capture (adjusted for actual capture rate)
    assert final_stats['total_captured'] >= 5, f"Not enough frames captured: {final_stats['total_captured']}"
    logger.success(f"✓ Continuous capture working: {final_stats['total_captured']} frames captured")
    
    # Stop capture
    logger.info("Stopping capture...")
    buffer.stop_capture()
    
    # Clear buffer
    buffer.clear_buffer()
    
    logger.success("=== CircularFrameBuffer test completed successfully ===")
    return True

if __name__ == "__main__":
    try:
        success = test_circular_buffer()
        if success:
            logger.success("All tests passed!")
            sys.exit(0)
        else:
            logger.error("Some tests failed")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)