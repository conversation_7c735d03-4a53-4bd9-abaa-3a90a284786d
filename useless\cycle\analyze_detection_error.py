"""
分析Gemini检测错误的工具
"""

from PIL import Image, ImageDraw, ImageFont
import os

def analyze_detection(image_path, box_2d):
    """分析检测结果"""
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    img_width, img_height = img.size
    center_x = img_width // 2
    center_y = img_height // 2
    
    # 解析坐标（标准格式：ymin, xmin, ymax, xmax）
    ymin, xmin, ymax, xmax = box_2d
    x1 = int(xmin * img_width / 1000)
    y1 = int(ymin * img_height / 1000)
    x2 = int(xmax * img_width / 1000)
    y2 = int(ymax * img_height / 1000)
    
    # 计算边界框中心
    bbox_center_x = (x1 + x2) // 2
    bbox_center_y = (y1 + y2) // 2
    
    # 计算偏移
    offset_x = bbox_center_x - center_x
    offset_y = bbox_center_y - center_y
    distance = (offset_x**2 + offset_y**2)**0.5
    
    print("检测结果分析")
    print("="*60)
    print(f"图片尺寸: {img_width}x{img_height}")
    print(f"图片中心: ({center_x}, {center_y})")
    print(f"\nGemini返回的box_2d: {box_2d}")
    print(f"转换后像素坐标: ({x1}, {y1}) - ({x2}, {y2})")
    print(f"边界框尺寸: {x2-x1}x{y2-y1}")
    print(f"\n边界框中心: ({bbox_center_x}, {bbox_center_y})")
    print(f"偏移量: X={offset_x}, Y={offset_y}")
    print(f"距离中心: {distance:.1f} 像素")
    
    # 判断检测是否正确
    contains_center = (x1 <= center_x <= x2 and y1 <= center_y <= y2)
    print(f"\n检测结果: {'[ERROR] 未检测到中心元素' if not contains_center else '[OK] 正确'}")
    
    # 绘制分析图
    # 1. 图片中心（大黄圈）
    draw.ellipse([center_x-10, center_y-10, center_x+10, center_y+10], 
                 fill='yellow', outline='black', width=3)
    
    # 2. 期望的检测区域（绿色虚线）
    expected_size = 80  # 假设期望的元素大小
    expected_x1 = center_x - expected_size // 2
    expected_y1 = center_y - expected_size // 2
    expected_x2 = center_x + expected_size // 2
    expected_y2 = center_y + expected_size // 2
    
    # 绘制虚线框
    for i in range(0, expected_size, 10):
        draw.line([expected_x1 + i, expected_y1, expected_x1 + i + 5, expected_y1], fill='green', width=2)
        draw.line([expected_x1 + i, expected_y2, expected_x1 + i + 5, expected_y2], fill='green', width=2)
        draw.line([expected_x1, expected_y1 + i, expected_x1, expected_y1 + i + 5], fill='green', width=2)
        draw.line([expected_x2, expected_y1 + i, expected_x2, expected_y1 + i + 5], fill='green', width=2)
    
    # 3. 实际检测的边界框（红色）
    draw.rectangle([x1, y1, x2, y2], outline='red', width=3)
    
    # 4. 边界框中心（红色X）
    cross_size = 8
    draw.line([bbox_center_x - cross_size, bbox_center_y - cross_size, 
               bbox_center_x + cross_size, bbox_center_y + cross_size], fill='red', width=2)
    draw.line([bbox_center_x - cross_size, bbox_center_y + cross_size, 
               bbox_center_x + cross_size, bbox_center_y - cross_size], fill='red', width=2)
    
    # 5. 连接线显示偏移
    draw.line([center_x, center_y, bbox_center_x, bbox_center_y], 
              fill='orange', width=2)
    
    # 添加文字说明
    try:
        font = ImageFont.truetype("arial.ttf", 14)
    except:
        font = ImageFont.load_default()
    
    # 标注
    draw.text((10, 10), "Image Center (Expected)", fill='yellow', font=font)
    draw.text((10, 30), "Detected Box (Actual)", fill='red', font=font)
    draw.text((10, 50), f"Offset: {distance:.1f}px", fill='orange', font=font)
    
    # 保存分析图
    output_path = "detection_analysis.png"
    img.save(output_path)
    print(f"\n分析图已保存: {output_path}")
    
    # 诊断建议
    print("\n诊断建议:")
    if not contains_center:
        print("1. Gemini未能正确识别中心位置的元素")
        print("2. 可能原因:")
        print("   - 提示词未能有效传达'中心'概念")
        print("   - 模型倾向于检测更明显/突出的元素")
        print("   - 中心元素可能不够清晰")
        print("\n3. 建议解决方案:")
        print("   - 在图片中心添加明显的标记")
        print("   - 使用更强调中心位置的提示词")
        print("   - 尝试不同的模型参数设置")

def main():
    test_image = r"C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png"
    box_2d = [444, 601, 562, 748]
    
    analyze_detection(test_image, box_2d)

if __name__ == "__main__":
    main()