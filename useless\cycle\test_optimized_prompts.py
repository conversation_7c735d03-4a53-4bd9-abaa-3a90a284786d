"""
测试优化后的Gemini提示词
用于验证不同提示词版本的效果
"""

import json
from datetime import datetime
import os

# 优化后的提示词集合
OPTIMIZED_PROMPTS = {
    "A_precise_format": """Detect the UI element at the exact center of this image.

Return the bounding box in this exact format:
{
  "element": {
    "description": "brief description of the element",
    "bounding_box": {
      "ymin": top edge coordinate (0-1000),
      "xmin": left edge coordinate (0-1000),
      "ymax": bottom edge coordinate (0-1000),
      "xmax": right edge coordinate (0-1000)
    },
    "text_content": "visible text or empty string"
  }
}

Requirements:
- Coordinates must be normalized to 0-1000
- The element at the center is the clicked target
- Make the bounding box as tight as possible
- Use [ymin, xmin, ymax, xmax] order""",

    "B_simple_array": """Find the UI element at the center of this screenshot.

Output format: [ymin, xmin, ymax, xmax, element_type, text_content]
- Coordinates normalized to 0-1000
- Tight bounding box around the center element
- Example: [245, 180, 290, 380, "button", "Submit"]""",

    "C_center_anchored": """User clicked at the center of this image (500, 500 in 0-1000 coordinates).
Identify the clicked element and return its bounding box.

Format:
{
  "box_2d": [ymin, xmin, ymax, xmax],  // normalized 0-1000
  "label": "element description",
  "text": "text content if any"
}

The box should tightly fit the visual boundaries of the element.""",

    "D_visual_boundary": """Detect the UI element at coordinates (500, 500) - the center of this 1000x1000 normalized image.

Requirements:
1. Find the element's visual boundaries including:
   - Background area
   - Border/shadow
   - Padding that's part of the clickable area
2. Return tight bounding box: [ymin, xmin, ymax, xmax]
3. All coordinates in 0-1000 range
4. Include element description and text

Output as JSON with box_2d array.""",

    # 对比测试：错误的坐标顺序
    "E_wrong_order": """Find the UI element at the center of this screenshot.

Output format: [xmin, ymin, xmax, ymax, element_type, text_content]
- Coordinates normalized to 0-1000
- Note: Using different coordinate order for testing""",

    # 对比测试：启用思考模式的提示词
    "F_with_thinking": """Think step by step about the UI element at the center of this image.

First, analyze what type of element it might be.
Then, carefully determine its boundaries.
Finally, return the bounding box.

Format: [ymin, xmin, ymax, xmax] normalized to 0-1000"""
}

def convert_coordinates(result, img_width, img_height):
    """转换各种格式的坐标到统一格式"""
    # 处理不同的响应格式
    if isinstance(result, list):
        # 数组格式: [ymin, xmin, ymax, xmax, ...]
        if len(result) >= 4:
            ymin, xmin, ymax, xmax = result[:4]
            return {
                'x': int(xmin * img_width / 1000),
                'y': int(ymin * img_height / 1000),
                'width': int((xmax - xmin) * img_width / 1000),
                'height': int((ymax - ymin) * img_height / 1000),
                'label': result[4] if len(result) > 4 else "",
                'text': result[5] if len(result) > 5 else ""
            }
    
    elif isinstance(result, dict):
        # JSON格式
        if 'element' in result:
            bbox = result['element'].get('bounding_box', {})
            if 'ymin' in bbox:
                # 新格式：ymin, xmin, ymax, xmax
                return {
                    'x': int(bbox['xmin'] * img_width / 1000),
                    'y': int(bbox['ymin'] * img_height / 1000),
                    'width': int((bbox['xmax'] - bbox['xmin']) * img_width / 1000),
                    'height': int((bbox['ymax'] - bbox['ymin']) * img_height / 1000),
                    'description': result['element'].get('description', ''),
                    'text': result['element'].get('text_content', '')
                }
            elif 'x' in bbox:
                # 旧格式：x, y, width, height
                return {
                    'x': int(bbox['x'] * img_width / 1000),
                    'y': int(bbox['y'] * img_height / 1000),
                    'width': int(bbox['width'] * img_width / 1000),
                    'height': int(bbox['height'] * img_height / 1000),
                    'description': result['element'].get('description', ''),
                    'text': result['element'].get('text_content', '')
                }
        
        elif 'box_2d' in result:
            # box_2d格式
            ymin, xmin, ymax, xmax = result['box_2d']
            return {
                'x': int(xmin * img_width / 1000),
                'y': int(ymin * img_height / 1000),
                'width': int((xmax - xmin) * img_width / 1000),
                'height': int((ymax - ymin) * img_height / 1000),
                'label': result.get('label', ''),
                'text': result.get('text', '')
            }
    
    return None

def generate_test_report():
    """生成测试报告模板"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    report = f"""# Gemini优化提示词测试报告
生成时间: {timestamp}

## 测试配置
- Gemini模型: Gemini 2.0 Flash Thinking
- Thinking设置: 对比测试（启用/禁用）
- 温度设置: 0.2
- 输出格式: JSON

## 测试图片信息
- 路径: [填写测试图片路径]
- 尺寸: [填写图片尺寸]
- 描述: [填写图片内容描述]

## 测试结果

### 提示词A：精确格式版
```
思考模式: 禁用
响应时间: [填写]
返回结果:
[粘贴JSON结果]

转换后坐标:
- X: [计算值]
- Y: [计算值]
- Width: [计算值]
- Height: [计算值]

准确性评价: [优秀/良好/一般/差]
边界框紧密度: [紧密/适中/过大]
```

### 提示词B：简化数组版
```
思考模式: 禁用
响应时间: [填写]
返回结果:
[粘贴结果]

转换后坐标:
[填写转换结果]

准确性评价: [评价]
```

### 提示词C：中心锚定版
```
[同上格式]
```

### 提示词D：视觉边界强调版
```
[同上格式]
```

### 对比测试E：错误坐标顺序
```
注意：使用了[xmin, ymin, xmax, ymax]顺序
结果: [填写是否能正确识别]
```

### 对比测试F：启用思考模式
```
思考模式: 启用
结果对比: [与禁用思考模式的差异]
```

## 总结

### 最佳提示词
- 提示词版本: [A/B/C/D]
- 原因: [说明选择理由]

### 关键发现
1. 坐标顺序影响: [描述影响程度]
2. 思考模式影响: [描述影响]
3. 提示词长度影响: [简洁vs详细]

### 建议配置
- 推荐提示词: [选择最佳版本]
- Gemini设置:
  - Thinking: 禁用
  - Temperature: 0.2
  - JSON模式: 启用
"""
    
    # 保存报告模板
    report_path = f"gemini_prompt_test_{timestamp}.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"测试报告模板已生成: {report_path}")
    print("\n请使用各个提示词在Gemini AI Studio中测试，并填写结果。")
    
    # 同时生成提示词文件方便复制
    prompts_path = f"prompts_for_testing_{timestamp}.txt"
    with open(prompts_path, 'w', encoding='utf-8') as f:
        for name, prompt in OPTIMIZED_PROMPTS.items():
            f.write(f"\n{'='*50}\n")
            f.write(f"提示词 {name}\n")
            f.write(f"{'='*50}\n")
            f.write(prompt)
            f.write("\n\n")
    
    print(f"提示词集合已保存: {prompts_path}")

def create_coordinate_converter():
    """创建一个简单的坐标转换工具"""
    converter_code = '''
# 快速坐标转换工具
def convert(ymin, xmin, ymax, xmax, img_width=512, img_height=512):
    """将Gemini坐标转换为像素坐标"""
    x = int(xmin * img_width / 1000)
    y = int(ymin * img_height / 1000)
    width = int((xmax - xmin) * img_width / 1000)
    height = int((ymax - ymin) * img_height / 1000)
    
    print(f"Gemini坐标: [{ymin}, {xmin}, {ymax}, {xmax}]")
    print(f"图片尺寸: {img_width}x{img_height}")
    print(f"转换后:")
    print(f"  X: {x}")
    print(f"  Y: {y}")
    print(f"  Width: {width}")
    print(f"  Height: {height}")
    print(f"  右下角: ({x + width}, {y + height})")
    
    return {'x': x, 'y': y, 'width': width, 'height': height}

# 使用示例:
# convert(388, 330, 470, 438, 512, 512)
'''
    
    with open('quick_converter.py', 'w', encoding='utf-8') as f:
        f.write(converter_code)
    
    print("\n坐标转换工具已创建: quick_converter.py")

if __name__ == "__main__":
    print("Gemini优化提示词测试工具")
    print("="*50)
    
    # 生成测试报告模板
    generate_test_report()
    
    # 创建坐标转换工具
    create_coordinate_converter()
    
    print("\n下一步:")
    print("1. 打开 prompts_for_testing_*.txt 复制提示词")
    print("2. 在 Gemini AI Studio 中测试每个提示词")
    print("3. 将结果填写到生成的测试报告中")
    print("4. 使用 quick_converter.py 转换坐标")