# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IpAddressPoolForDescribeIpAddressPoolsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'business_status': 'str',
        'cidr_blocks': 'list[str]',
        'creation_time': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'isp': 'str',
        'ip_address_pool_id': 'str',
        'name': 'str',
        'overdue_time': 'str',
        'project_name': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeIpAddressPoolsOutput]',
        'total_ip_count': 'int',
        'update_time': 'str',
        'used_ip_count': 'int'
    }

    attribute_map = {
        'business_status': 'BusinessStatus',
        'cidr_blocks': 'CidrBlocks',
        'creation_time': 'CreationTime',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'isp': 'ISP',
        'ip_address_pool_id': 'IpAddressPoolId',
        'name': 'Name',
        'overdue_time': 'OverdueTime',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tags': 'Tags',
        'total_ip_count': 'TotalIpCount',
        'update_time': 'UpdateTime',
        'used_ip_count': 'UsedIpCount'
    }

    def __init__(self, business_status=None, cidr_blocks=None, creation_time=None, deleted_time=None, description=None, isp=None, ip_address_pool_id=None, name=None, overdue_time=None, project_name=None, status=None, tags=None, total_ip_count=None, update_time=None, used_ip_count=None, _configuration=None):  # noqa: E501
        """IpAddressPoolForDescribeIpAddressPoolsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._business_status = None
        self._cidr_blocks = None
        self._creation_time = None
        self._deleted_time = None
        self._description = None
        self._isp = None
        self._ip_address_pool_id = None
        self._name = None
        self._overdue_time = None
        self._project_name = None
        self._status = None
        self._tags = None
        self._total_ip_count = None
        self._update_time = None
        self._used_ip_count = None
        self.discriminator = None

        if business_status is not None:
            self.business_status = business_status
        if cidr_blocks is not None:
            self.cidr_blocks = cidr_blocks
        if creation_time is not None:
            self.creation_time = creation_time
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if isp is not None:
            self.isp = isp
        if ip_address_pool_id is not None:
            self.ip_address_pool_id = ip_address_pool_id
        if name is not None:
            self.name = name
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if total_ip_count is not None:
            self.total_ip_count = total_ip_count
        if update_time is not None:
            self.update_time = update_time
        if used_ip_count is not None:
            self.used_ip_count = used_ip_count

    @property
    def business_status(self):
        """Gets the business_status of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The business_status of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param business_status: The business_status of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def cidr_blocks(self):
        """Gets the cidr_blocks of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The cidr_blocks of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cidr_blocks

    @cidr_blocks.setter
    def cidr_blocks(self, cidr_blocks):
        """Sets the cidr_blocks of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param cidr_blocks: The cidr_blocks of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: list[str]
        """

        self._cidr_blocks = cidr_blocks

    @property
    def creation_time(self):
        """Gets the creation_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The creation_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param creation_time: The creation_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def deleted_time(self):
        """Gets the deleted_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The deleted_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param deleted_time: The deleted_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The description of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param description: The description of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def isp(self):
        """Gets the isp of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The isp of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param isp: The isp of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def ip_address_pool_id(self):
        """Gets the ip_address_pool_id of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The ip_address_pool_id of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_pool_id

    @ip_address_pool_id.setter
    def ip_address_pool_id(self, ip_address_pool_id):
        """Sets the ip_address_pool_id of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param ip_address_pool_id: The ip_address_pool_id of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._ip_address_pool_id = ip_address_pool_id

    @property
    def name(self):
        """Gets the name of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The name of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param name: The name of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def overdue_time(self):
        """Gets the overdue_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The overdue_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param overdue_time: The overdue_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def project_name(self):
        """Gets the project_name of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The project_name of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param project_name: The project_name of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The status of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param status: The status of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The tags of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: list[TagForDescribeIpAddressPoolsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param tags: The tags of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: list[TagForDescribeIpAddressPoolsOutput]
        """

        self._tags = tags

    @property
    def total_ip_count(self):
        """Gets the total_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The total_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_ip_count

    @total_ip_count.setter
    def total_ip_count(self, total_ip_count):
        """Sets the total_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param total_ip_count: The total_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: int
        """

        self._total_ip_count = total_ip_count

    @property
    def update_time(self):
        """Gets the update_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The update_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param update_time: The update_time of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def used_ip_count(self):
        """Gets the used_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501


        :return: The used_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._used_ip_count

    @used_ip_count.setter
    def used_ip_count(self, used_ip_count):
        """Sets the used_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.


        :param used_ip_count: The used_ip_count of this IpAddressPoolForDescribeIpAddressPoolsOutput.  # noqa: E501
        :type: int
        """

        self._used_ip_count = used_ip_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IpAddressPoolForDescribeIpAddressPoolsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IpAddressPoolForDescribeIpAddressPoolsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IpAddressPoolForDescribeIpAddressPoolsOutput):
            return True

        return self.to_dict() != other.to_dict()
