# Plan: Critical Fixes for ShowForAI-V3

## 现状分析

### 产品原则遵循情况
- ❌ **图像识别为核心**: 多级降级策略未完全实现
- ❌ **智能等待机制**: 主动模式和辅助模式未正确集成
- ❌ **脚本处理流程**: 10FPS缓冲和点击前第3帧获取未正确实现
- ✅ **分辨率标准化**: 768×768基础架构已建立，但使用不一致
- ❌ **离线模式设计**: 录制按钮禁用逻辑未实现

### 主要问题识别
1. **循环导入** - 程序无法启动（最严重）
2. **类名不一致** - 测试无法运行
3. **网络状态集成缺失** - 离线模式不工作
4. **核心功能未完成** - 违反产品原则

## 开发计划

### P0级任务（阻塞产品运行）

#### 任务1：修复循环导入问题
- **任务描述**: 解决ai、gui、api模块之间的循环依赖，让程序能启动
- **产品原则依据**: 基础架构必须稳定才能实现其他功能
- **具体修复步骤**:
  1. 修改 `ai/element_detector.py` - 移除对gui.status_manager的直接导入
  2. 修改 `ai/upload_manager.py` - 移除对gui.status_manager的直接导入
  3. 在 `api/adapter.py` 中使用延迟导入ai_service
  4. 添加状态回调机制替代直接依赖
- **验证方法**: 执行 `python -m showforai --help` 应显示帮助信息

#### 任务2：修复类名不一致问题
- **任务描述**: 统一Standardizer和CircularFrameBuffer的引用
- **产品原则依据**: 代码一致性是系统稳定的基础
- **具体修复步骤**:
  1. 更新 `tests/test_standardizer.py` - 改为导入 `Standardizer`
  2. 更新所有测试文件中的 `CircularBuffer` 引用为 `CircularFrameBuffer`
  3. 检查并更新所有相关导入
- **验证方法**: 执行 `python -m pytest tests/` 应能成功收集测试

#### 任务3：实现网络状态检测与录制按钮控制
- **任务描述**: 离线时禁用录制按钮，符合产品原则
- **产品原则依据**: "录制必须禁用（离线状态下）"
- **具体修复步骤**:
  1. 增强 `utils/network_checker.py` - 添加实时监控和回调
  2. 在 `gui/main_window.py` 中集成网络状态检测
  3. 实现录制按钮的启用/禁用逻辑
  4. 添加中文提示信息
- **验证方法**: 断网后录制按钮应变灰，鼠标悬停显示提示

### P1级任务（影响核心功能）

#### 任务4：完善10FPS循环缓冲机制
- **任务描述**: 实现持续截图和点击前第3帧获取
- **产品原则依据**: "持续缓存机制：以10FPS持续截取屏幕"
- **具体修复步骤**:
  1. 确保 `buffer_manager.py` 的FrameBufferManager正确启动
  2. 在 `recorder/recorder.py` 中集成缓冲管理器
  3. 实现点击事件时获取前第3帧的逻辑
  4. 验证帧的时间戳准确性
- **验证方法**: 录制时应能获取点击前0.3秒的截图

#### 任务5：实现智能等待机制
- **任务描述**: 完善主动模式和辅助模式的等待逻辑
- **产品原则依据**: "初始等待：录制时的操作间隔"，"持续识别：每隔1秒重新识别"
- **具体修复步骤**:
  1. 完善 `executor/smart_wait_manager.py` 的等待逻辑
  2. 实现基于录制间隔的初始等待
  3. 添加1秒重试的持续识别机制
  4. 区分主动模式和辅助模式的行为
- **验证方法**: 执行脚本时应按录制间隔等待，失败后每秒重试

#### 任务6：统一BBOX裁切流程
- **任务描述**: 确保所有裁切都基于768×768坐标系
- **产品原则依据**: "BBOX坐标系：完全基于768×768的标准化图像"
- **具体修复步骤**:
  1. 完善 `preprocessing/bbox_processor.py` 的裁切逻辑
  2. 替换所有分散的裁切代码
  3. 确保坐标系统一致性
  4. 添加边界检查和验证
- **验证方法**: 裁切的元素应准确对应点击位置

### P2级任务（功能完善）

#### 任务7：完成分享模块API集成
- **任务描述**: 实现脚本分享的完整流程
- **产品原则依据**: "脚本分享策略"
- **具体修复步骤**:
  1. 实现 `api/adapter.py` 中的share_script方法
  2. 完成分享链接生成逻辑
  3. 实现导入分享脚本功能
  4. 添加错误处理
- **验证方法**: 能生成分享链接并成功导入

#### 任务8：实现缺失的对话框
- **任务描述**: 替换TODO占位符，实现实际功能
- **产品原则依据**: 用户体验完整性
- **具体修复步骤**:
  1. 实现批量重命名对话框
  2. 实现永久删除确认对话框
  3. 实现设置对话框
  4. 添加输入验证
- **验证方法**: 所有对话框功能正常，无TODO提示

#### 任务9：添加可视化进度反馈
- **任务描述**: 为长时间操作添加进度提示
- **产品原则依据**: "需要可视化反馈的环节"
- **具体修复步骤**:
  1. 创建进度管理器
  2. 在上传、AI处理、执行等环节添加进度显示
  3. 实现3秒延迟显示逻辑
  4. 添加中文提示信息
- **验证方法**: 长操作应显示进度，短操作不显示

## 执行优先级

1. **立即执行（今天）**: 任务1-3（P0级）- 必须先让程序能运行
2. **明天执行**: 任务4-6（P1级）- 确保核心功能符合产品原则
3. **后续执行**: 任务7-9（P2级）- 完善用户体验

## 成功标准

### 阶段一（P0完成）
- ✅ 程序能正常启动
- ✅ GUI界面能打开
- ✅ 离线状态正确处理
- ✅ 测试框架能运行

### 阶段二（P1完成）
- ✅ 录制功能完全符合产品原则
- ✅ 执行时智能等待正确工作
- ✅ BBOX裁切准确无误

### 阶段三（P2完成）
- ✅ 分享功能端到端可用
- ✅ 所有UI交互流畅
- ✅ 用户体验完整

## 注意事项

1. **不要过度优化** - 项目已经进行了大量优化，重点是修复bug
2. **保持产品原则** - 每个修改都要符合产品原则文档
3. **测试驱动** - 每个修复都要有对应的验证方法
4. **中文提示** - 所有用户可见信息使用中文