# 开发指南

> "The best way to get a project done faster is to start sooner." - <PERSON>

## 快速开始

### 环境要求

- **操作系统**: Windows 10/11 (主要)，macOS，Linux
- **Node.js**: >= 18.0.0
- **Rust**: >= 1.75.0
- **Python**: >= 3.8 (用于OpenCV)
- **Git**: 最新版本

### 环境配置

#### 1. 安装 Rust
```bash
# Windows (使用 rustup)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 设置为 stable 版本
rustup default stable

# 添加 Windows 目标（如果需要）
rustup target add x86_64-pc-windows-msvc

# 验证安装
rustc --version
cargo --version
```

#### 2. 安装 Node.js 和 pnpm
```bash
# 安装 Node.js (推荐使用 nvm)
nvm install 18
nvm use 18

# 安装 pnpm
npm install -g pnpm

# 验证安装
node --version
pnpm --version
```

#### 3. 安装 OpenCV
```bash
# Windows (使用 vcpkg)
vcpkg install opencv4

# macOS
brew install opencv

# Linux
sudo apt-get install libopencv-dev

# 设置环境变量
# Windows
set OPENCV_DIR=C:\tools\vcpkg\installed\x64-windows

# macOS/Linux
export OPENCV_DIR=/usr/local/opt/opencv
```

#### 4. 克隆项目
```bash
git clone https://github.com/your-org/showforai-v6.git
cd showforai-v6

# 安装依赖
pnpm install
cargo build
```

## 项目结构

```
ShowForAI-V6/
├── src/                    # Rust 后端代码
│   ├── main.rs            # 主入口
│   ├── modules/           # 功能模块
│   │   ├── recording/     # 录制模块
│   │   ├── execution/     # 执行模块
│   │   ├── matching/      # 图像匹配
│   │   └── storage/       # 存储模块
│   ├── types/             # 数据类型定义
│   ├── utils/             # 工具函数
│   └── api/               # API 接口
│
├── src-ui/                # React 前端代码
│   ├── main.tsx          # 前端入口
│   ├── components/        # React 组件
│   │   ├── recording/     # 录制界面组件
│   │   ├── scripts/       # 脚本管理组件
│   │   └── shared/        # 共享组件
│   ├── store/            # Zustand 状态管理
│   ├── hooks/            # 自定义 Hooks
│   ├── utils/            # 前端工具函数
│   └── styles/           # 样式文件
│
├── assets/               # 静态资源
├── technical-docs/       # 技术文档
├── tests/               # 测试文件
├── scripts/             # 构建脚本
│
├── Cargo.toml          # Rust 依赖配置
├── package.json        # Node 依赖配置
├── tauri.conf.json     # Tauri 配置
└── tsconfig.json       # TypeScript 配置
```

## 开发流程

### 1. 创建新功能分支
```bash
# 从 main 创建功能分支
git checkout main
git pull origin main
git checkout -b feature/your-feature-name

# 分支命名规范
# feature/xxx - 新功能
# fix/xxx - Bug修复
# refactor/xxx - 重构
# docs/xxx - 文档更新
```

### 2. 开发环境启动

#### 启动后端开发服务器
```bash
# 开发模式（热重载）
cargo watch -x run

# 或者手动运行
cargo run
```

#### 启动前端开发服务器
```bash
# 开发模式
pnpm dev

# 构建预览
pnpm build
pnpm preview
```

#### 启动 Tauri 开发模式
```bash
# 同时启动前后端
pnpm tauri dev

# 仅构建
pnpm tauri build
```

### 3. 代码编写

#### Rust 开发示例
```rust
// src/modules/recording/capture.rs

use crate::types::{Frame, Result};

/// 屏幕捕获模块
pub struct ScreenCapture {
    // 实现细节
}

impl ScreenCapture {
    pub fn new() -> Result<Self> {
        // 初始化
        Ok(Self {})
    }
    
    pub fn capture(&mut self) -> Result<Frame> {
        // 捕获逻辑
        todo!()
    }
}

// 编写测试
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_capture() {
        let mut capture = ScreenCapture::new().unwrap();
        let frame = capture.capture().unwrap();
        assert!(!frame.data.is_empty());
    }
}
```

#### React 开发示例
```tsx
// src-ui/components/recording/RecordButton.tsx

import React, { useState, useCallback } from 'react';
import { Button } from 'antd';
import { useRecordingStore } from '@/store/recording';

export const RecordButton: React.FC = () => {
  const { isRecording, startRecording, stopRecording } = useRecordingStore();
  
  const handleClick = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, startRecording, stopRecording]);
  
  return (
    <Button
      type="primary"
      danger={isRecording}
      size="large"
      onClick={handleClick}
    >
      {isRecording ? '停止录制' : '开始录制'}
    </Button>
  );
};
```

### 4. 测试

#### 运行 Rust 测试
```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test recording

# 运行并显示输出
cargo test -- --nocapture

# 运行基准测试
cargo bench
```

#### 运行前端测试
```bash
# 运行测试
pnpm test

# 运行测试覆盖率
pnpm test:coverage

# 运行 E2E 测试
pnpm test:e2e
```

### 5. 代码检查

#### Rust 代码检查
```bash
# 格式化
cargo fmt

# Lint 检查
cargo clippy -- -D warnings

# 安全审计
cargo audit
```

#### TypeScript 代码检查
```bash
# ESLint 检查
pnpm lint

# TypeScript 类型检查
pnpm type-check

# 格式化
pnpm format
```

### 6. 提交代码
```bash
# 添加改动
git add .

# 提交（遵循提交规范）
git commit -m "feat(recording): 添加屏幕录制功能"

# 推送到远程
git push origin feature/your-feature-name
```

### 7. 创建 Pull Request
1. 在 GitHub 上创建 PR
2. 填写 PR 模板
3. 等待代码审查
4. 根据反馈修改
5. 合并到 main

## 调试技巧

### Rust 调试

#### 使用 VS Code
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug Rust",
      "cargo": {
        "args": ["build"],
        "filter": {
          "name": "showforai",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
```

#### 使用日志
```rust
use log::{debug, info, warn, error};

// 在代码中添加日志
info!("Starting recording");
debug!("Frame captured: {:?}", frame);
warn!("Retry attempt: {}", retry_count);
error!("Failed to capture: {}", e);

// 设置日志级别
RUST_LOG=debug cargo run
```

### React 调试

#### 使用 React DevTools
1. 安装 React DevTools 浏览器扩展
2. 打开开发者工具
3. 查看组件树和状态

#### 使用 console
```typescript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', data);
  console.table(scripts);
  console.time('operation');
  // 操作
  console.timeEnd('operation');
}
```

### Tauri 调试

#### 开启开发者工具
```json
// tauri.conf.json
{
  "tauri": {
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.showforai.app",
      "icon": ["icons/icon.ico"]
    },
    "windows": [
      {
        "fullscreen": false,
        "resizable": true,
        "title": "ShowForAI",
        "width": 1200,
        "height": 800,
        "webviewDebugging": true  // 开启调试
      }
    ]
  }
}
```

#### IPC 调试
```rust
// Rust 端
#[tauri::command]
fn my_command(value: String) -> Result<String, String> {
    println!("Received: {}", value);  // 调试输出
    Ok(format!("Processed: {}", value))
}

// TypeScript 端
import { invoke } from '@tauri-apps/api';

const result = await invoke('my_command', { value: 'test' });
console.log('Result:', result);
```

## 性能优化

### Rust 性能分析

#### 使用 Flamegraph
```bash
# 安装 flamegraph
cargo install flamegraph

# 生成火焰图
cargo flamegraph --bin showforai

# 查看结果
open flamegraph.svg
```

#### 使用 cargo-profiling
```bash
# 安装
cargo install cargo-profiling

# CPU 分析
cargo profiling callgrind --bin showforai

# 内存分析
cargo profiling massif --bin showforai
```

### React 性能分析

#### 使用 React Profiler
```tsx
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log(`${id} (${phase}) took ${actualDuration}ms`);
}

<Profiler id="RecordButton" onRender={onRenderCallback}>
  <RecordButton />
</Profiler>
```

#### 使用 Chrome DevTools
1. Performance 标签页记录
2. 分析火焰图
3. 查找性能瓶颈

## 常见问题

### Q: Rust 编译失败
```bash
# 清理缓存
cargo clean

# 更新依赖
cargo update

# 重新构建
cargo build --verbose
```

### Q: OpenCV 链接错误
```bash
# Windows
set OPENCV_LINK_LIBS=opencv_world455
set OPENCV_LINK_PATHS=C:\opencv\build\x64\vc15\lib
set OPENCV_INCLUDE_PATHS=C:\opencv\build\include

# macOS/Linux
export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig
export LD_LIBRARY_PATH=/usr/local/lib
```

### Q: Tauri 构建失败
```bash
# 重置 Tauri
pnpm tauri clean

# 重新安装依赖
rm -rf node_modules package-lock.json
pnpm install

# 重新构建
pnpm tauri build --verbose
```

### Q: 内存泄漏调试
```rust
// 使用 valgrind (Linux/macOS)
valgrind --leak-check=full --show-leak-kinds=all ./target/debug/showforai

// 使用 heaptrack
heaptrack ./target/debug/showforai
heaptrack --analyze heaptrack.showforai.12345.gz
```

## 发布流程

### 1. 版本更新
```bash
# 更新版本号
# Cargo.toml
version = "1.1.0"

# package.json
"version": "1.1.0"

# tauri.conf.json
"version": "1.1.0"
```

### 2. 构建发布版本
```bash
# 构建 Windows
pnpm tauri build --target x86_64-pc-windows-msvc

# 构建 macOS
pnpm tauri build --target universal-apple-darwin

# 构建 Linux
pnpm tauri build --target x86_64-unknown-linux-gnu
```

### 3. 生成更新日志
```bash
# 使用 conventional-changelog
npx conventional-changelog -p angular -i CHANGELOG.md -s
```

### 4. 创建 Release
```bash
# 打标签
git tag -a v1.1.0 -m "Release version 1.1.0"
git push origin v1.1.0

# 在 GitHub 创建 Release
# 上传构建产物
```

## 团队协作

### 代码审查要点
- [ ] 代码符合编码规范
- [ ] 有适当的测试覆盖
- [ ] 文档已更新
- [ ] 性能影响已评估
- [ ] 安全性已考虑
- [ ] 向后兼容性

### 分支保护规则
- main 分支禁止直接推送
- 需要至少一个审查批准
- 必须通过所有 CI 检查
- 必须是最新的分支

### CI/CD 配置
```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
      - uses: actions/setup-node@v2
      
      - name: Test Rust
        run: cargo test
        
      - name: Test TypeScript
        run: |
          pnpm install
          pnpm test
```

---

*"The only way to go fast is to go well."* - Robert C. Martin