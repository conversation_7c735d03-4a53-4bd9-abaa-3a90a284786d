# ShowForAI 技术文档体系

> "Simplicity is the ultimate sophistication." - <PERSON> da Vinci

## 文档结构

本目录包含ShowForAI项目的核心技术文档，所有文档严格遵循产品原则。

### 核心文档

1. **[DATA_STRUCTURES.md](./DATA_STRUCTURES.md)** - 数据结构设计
   - 录制数据流
   - 图像处理结构
   - 脚本存储格式

2. **[SYSTEM_ARCHITECTURE.md](./SYSTEM_ARCHITECTURE.md)** - 系统架构
   - 三层架构设计
   - 组件职责划分
   - 数据流向

3. **[API_DESIGN.md](./API_DESIGN.md)** - API设计
   - 前后端通信协议
   - 服务端API规范
   - 错误处理

4. **[DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)** - 开发指南
   - 环境配置
   - 开发流程
   - 调试技巧

5. **[CODING_STANDARDS.md](./CODING_STANDARDS.md)** - 编码规范
   - Rust代码规范
   - TypeScript规范
   - 命名约定

6. **[IMPLEMENTATION_GUIDE.md](./IMPLEMENTATION_GUIDE.md)** - 实现指南
   - 图像识别实现
   - 录制流程实现
   - 执行引擎实现

## 设计原则

### 1. 简单优先
- 不过度设计
- 不过早优化
- 不增加不必要的抽象

### 2. 数据驱动
- 数据结构决定算法
- 清晰的数据流
- 明确的数据所有权

### 3. 实用主义
- 解决真实问题
- 快速迭代
- 持续改进

## 技术栈

- **前端**: Electron + React + TypeScript + Ant Design
- **后端**: Rust (Tauri)
- **图像处理**: OpenCV + 自定义算法
- **存储**: 本地SQLite + 文件系统

## 产品核心流程

```
录制 → 标准化(768×768) → AI识别 → BBOX裁切 → 本地存储 → 执行
```

## 关键约束

1. **图像标准化**: 所有处理基于768×768
2. **离线执行**: 执行不依赖网络
3. **质量优先**: 宁可失败不误点击

---

*"Show me your data structures, and I won't need to see your code."* - Linus Torvalds