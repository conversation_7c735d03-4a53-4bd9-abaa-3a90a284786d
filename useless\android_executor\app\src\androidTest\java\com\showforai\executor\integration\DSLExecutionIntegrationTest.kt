package com.showforai.executor.integration

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.showforai.executor.data.models.*
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.runBlocking
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * DSL执行集成测试
 * 
 * 测试完整的DSL脚本执行流程
 */
@RunWith(AndroidJUnit4::class)
class DSLExecutionIntegrationTest {
    
    private lateinit var context: Context
    private lateinit var moshi: Moshi
    
    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        moshi = Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
    }
    
    @Test
    fun testParseAndValidateSampleScript() = runBlocking {
        // 读取示例脚本
        val sampleScriptJson = """
            {
              "version": "3.1",
              "metadata": {
                "session_id": "integration_test_001",
                "description": "集成测试脚本",
                "created_at": "2024-01-15T10:30:00Z",
                "device_info": "Android测试设备"
              },
              "steps": [
                {
                  "type": "action",
                  "command": "CLICK",
                  "timeout_seconds": 10,
                  "target": {
                    "description": "点击屏幕中心",
                    "bounding_box": [0.4, 0.4, 0.6, 0.6]
                  }
                },
                {
                  "type": "action", 
                  "command": "WAIT",
                  "timeout_seconds": 5,
                  "parameters": {
                    "duration": 2000
                  }
                },
                {
                  "type": "action",
                  "command": "SWIPE", 
                  "timeout_seconds": 10,
                  "target": {
                    "description": "从屏幕中心向上滑动",
                    "bounding_box": [0.4, 0.5, 0.6, 0.5]
                  },
                  "parameters": {
                    "direction": "UP",
                    "distance": 300,
                    "duration": 500
                  }
                }
              ]
            }
        """.trimIndent()
        
        // 解析脚本
        val adapter = moshi.adapter(DSLScript::class.java)
        val script = adapter.fromJson(sampleScriptJson)
        
        // 验证脚本解析
        assertNotNull("Script should be parsed successfully", script)
        assertEquals("3.1", script?.version)
        assertEquals("integration_test_001", script?.metadata?.sessionId)
        assertEquals(3, script?.steps?.size)
        
        // 验证步骤类型
        assertEquals(CommandType.CLICK, script?.steps?.get(0)?.command)
        assertEquals(CommandType.WAIT, script?.steps?.get(1)?.command)
        assertEquals(CommandType.SWIPE, script?.steps?.get(2)?.command)
        
        // 验证脚本有效性
        script?.let { validateScript(it) }
    }
    
    @Test
    fun testScriptValidation() {
        // 测试脚本验证逻辑
        val validScript = createValidTestScript()
        
        // 验证有效脚本
        try {
            validateScript(validScript)
            // 如果没有抛出异常，验证通过
        } catch (e: Exception) {
            fail("Valid script should pass validation: ${e.message}")
        }
        
        // 测试无效脚本
        val invalidScript = createInvalidTestScript()
        
        try {
            validateScript(invalidScript)
            fail("Invalid script should fail validation")
        } catch (e: IllegalArgumentException) {
            // 预期的异常
            assertTrue("Error message should mention version", e.message?.contains("version") == true)
        }
    }
    
    @Test
    fun testCoordinateConversion() {
        // 测试坐标转换功能
        val screenWidth = 1080
        val screenHeight = 1920
        
        // 测试归一化坐标转换
        val normalizedX = 0.5f
        val normalizedY = 0.5f
        
        val pixelX = (normalizedX * screenWidth).toInt()
        val pixelY = (normalizedY * screenHeight).toInt()
        
        assertEquals(540, pixelX)
        assertEquals(960, pixelY)
        
        // 测试边界框转换
        val normalizedBox = listOf(0.1f, 0.2f, 0.9f, 0.8f)
        val pixelBox = convertNormalizedBoxToPixel(normalizedBox, screenWidth, screenHeight)
        
        assertEquals(108f, pixelBox.left, 0.001f)
        assertEquals(384f, pixelBox.top, 0.001f)
        assertEquals(972f, pixelBox.right, 0.001f)
        assertEquals(1536f, pixelBox.bottom, 0.001f)
    }
    
    @Test
    fun testExecutionResultCreation() {
        // 测试执行结果创建
        val logs = listOf(
            ExecutionLog(
                timestamp = System.currentTimeMillis(),
                level = LogLevel.INFO,
                stepIndex = 0,
                message = "Starting execution",
                details = null
            ),
            ExecutionLog(
                timestamp = System.currentTimeMillis(),
                level = LogLevel.SUCCESS,
                stepIndex = 1,
                message = "Step completed",
                details = "Click executed successfully"
            )
        )
        
        val result = ExecutionResult(
            success = true,
            completedSteps = 2,
            totalSteps = 3,
            errorMessage = null,
            executionTime = 5000L,
            logs = logs
        )
        
        assertTrue("Execution should be successful", result.success)
        assertEquals(2, result.completedSteps)
        assertEquals(3, result.totalSteps)
        assertEquals(5000L, result.executionTime)
        assertEquals(2, result.logs.size)
    }
    
    @Test
    fun testPermissionStatusCheck() {
        // 测试权限状态检查
        val allGranted = PermissionStatus(
            screenCapture = true,
            accessibility = true,
            storage = true,
            overlay = true
        )
        
        assertTrue("All permissions should be granted", allGranted.allGranted)
        
        val partialGranted = PermissionStatus(
            screenCapture = true,
            accessibility = false,
            storage = true,
            overlay = true
        )
        
        assertFalse("Not all permissions are granted", partialGranted.allGranted)
    }
    
    @Test
    fun testLocationResultCreation() {
        // 测试定位结果创建
        val locationResult = LocationResult(
            found = true,
            x = 540f,
            y = 960f,
            confidence = 0.95f,
            strategy = LocationStrategy.VISUAL_MATCHING,
            boundingBox = android.graphics.RectF(500f, 920f, 580f, 1000f)
        )
        
        assertTrue("Target should be found", locationResult.found)
        assertEquals(540f, locationResult.x, 0.001f)
        assertEquals(960f, locationResult.y, 0.001f)
        assertEquals(0.95f, locationResult.confidence, 0.001f)
        assertEquals(LocationStrategy.VISUAL_MATCHING, locationResult.strategy)
        assertNotNull("Bounding box should not be null", locationResult.boundingBox)
    }
    
    // 辅助方法
    private fun createValidTestScript(): DSLScript {
        return DSLScript(
            version = "3.1",
            metadata = DSLMetadata(
                sessionId = "test_valid",
                description = "Valid test script",
                createdAt = "2024-01-01T12:00:00Z",
                deviceInfo = "Test device"
            ),
            steps = listOf(
                DSLStep(
                    type = "action",
                    command = CommandType.CLICK,
                    timeoutSeconds = 10,
                    target = DSLTarget(
                        description = "Test button",
                        visualHash = null,
                        textContent = "Click me",
                        boundingBox = listOf(0.1f, 0.2f, 0.3f, 0.4f)
                    ),
                    parameters = null
                )
            )
        )
    }
    
    private fun createInvalidTestScript(): DSLScript {
        return DSLScript(
            version = "2.0", // 无效版本
            metadata = null,
            steps = emptyList() // 无步骤
        )
    }
    
    private fun validateScript(script: DSLScript) {
        // 检查版本
        if (script.version != "3.1") {
            throw IllegalArgumentException("Unsupported DSL version: ${script.version}")
        }
        
        // 检查步骤
        if (script.steps.isEmpty()) {
            throw IllegalArgumentException("Script has no steps")
        }
        
        // 验证每个步骤
        script.steps.forEachIndexed { index, step ->
            validateStep(index, step)
        }
    }
    
    private fun validateStep(index: Int, step: DSLStep) {
        // 检查超时时间
        if (step.timeoutSeconds <= 0) {
            throw IllegalArgumentException("Step $index: Invalid timeout: ${step.timeoutSeconds}")
        }
        
        // 检查命令类型特定的参数
        when (step.command) {
            CommandType.CLICK, CommandType.LONG_PRESS, CommandType.DOUBLE_CLICK -> {
                if (step.target == null) {
                    throw IllegalArgumentException("Step $index: ${step.command} requires target")
                }
            }
            CommandType.INPUT_TEXT -> {
                if (step.target == null || step.parameters?.text.isNullOrEmpty()) {
                    throw IllegalArgumentException("Step $index: INPUT_TEXT requires target and text parameter")
                }
            }
            else -> {
                // 其他命令的验证
            }
        }
    }
    
    private fun convertNormalizedBoxToPixel(
        normalizedBox: List<Float>,
        screenWidth: Int,
        screenHeight: Int
    ): android.graphics.RectF {
        val left = normalizedBox[0] * screenWidth
        val top = normalizedBox[1] * screenHeight
        val right = normalizedBox[2] * screenWidth
        val bottom = normalizedBox[3] * screenHeight
        return android.graphics.RectF(left, top, right, bottom)
    }
}
