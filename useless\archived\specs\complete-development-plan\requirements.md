# ShowForAI V3 完整开发计划 - 需求定义

## 1. 项目背景与现状分析

### 1.1 产品原则核心要点
基于产品原则文档（C:\Users\<USER>\Desktop\aijioaben\产品原则.md），核心原则包括：

1. **图像识别为核心**
   - 坚持纯图像识别，不使用其他定位方式
   - 质量优先：检测阈值不低于0.80，宁可失败也不误点击
   - 接受失败：允许一定失败率，保证执行准确性
   - 步进执行：每步必须成功才能继续

2. **智能等待机制**
   - 辅助模式：无限等待，后台持续识别
   - 主动模式：基于录制间隔的步进式执行

3. **768×768标准化**
   - 所有录制截图统一缩放为768×768
   - AI识别基于768×768坐标系
   - BBOX裁切在768×768图像上进行

4. **离线模式设计**
   - 脚本可离线执行（已下载的）
   - 录制必须在线（需要AI识别）

5. **多级识别降级策略**
   - 模板匹配 → ORB → SIFT → 多尺度匹配
   - 每级都保持高质量阈值

### 1.2 现有代码问题分析

#### 违反产品原则的问题
1. **检测阈值管理不统一**
   - 部分代码使用0.8阈值，部分使用0.85
   - 缺乏统一的阈值管理机制

2. **768标准化实现不完整**
   - ImageProcessor已实现标准化，但使用不一致
   - BBOX裁切流程需要验证

3. **离线模式功能划分不清晰**
   - 录制器的网络检查已实现，但提示不够明确
   - 需要更好的用户引导

4. **智能等待机制不完善**
   - SmartWaitManager已实现，但与产品原则的描述有差异
   - 操作间隔记录需要优化

#### 用户体验问题
1. **GUI界面问题**
   - 主窗口布局需要优化
   - 状态反馈不够实时
   - 错误提示不够友好

2. **操作流程问题**
   - 录制到执行的流程不够顺畅
   - 缺少进度显示
   - 帮助系统缺失

#### 已发现的Bug
1. **TODO标记的未完成功能**（50+处）
2. **录制器的截图缓存机制未完全实现**
3. **执行器的多级识别策略需要完善**
4. **GUI的部分功能未实现**

## 2. 功能需求（EARS格式）

### 2.1 产品原则合规改造

#### REQ-001: 统一检测阈值管理
**WHEN** 系统进行任何图像匹配操作
**THEN** 系统SHALL 使用统一的阈值配置
**WHERE** 阈值配置定义为：
- 模板匹配：0.85
- ORB匹配：最少15个特征点，ratio < 0.7
- SIFT匹配：最少20个特征点，ratio < 0.65
- 多尺度匹配：0.80
**AND** 系统SHALL NOT 在任何情况下降低这些阈值

#### REQ-002: 完整的768标准化流程
**WHEN** 用户录制脚本
**THEN** 系统SHALL：
1. 以10FPS持续截取屏幕到循环缓冲区（15帧）
2. 点击时获取点击前第3帧图像
3. 将图像缩放为768×768
4. 记录原始分辨率信息
**AND** 系统SHALL 确保所有后续处理基于768×768坐标系

#### REQ-003: 离线模式清晰提示
**WHEN** 系统处于离线状态
**AND** 用户尝试开始录制
**THEN** 系统SHALL：
1. 禁用录制按钮（灰色显示）
2. 显示提示："录制需要网络连接以进行AI识别"
3. 提供"为什么需要网络？"的帮助链接

#### REQ-004: 智能等待完整实现
**WHEN** 执行脚本时
**IF** 选择主动模式
**THEN** 系统SHALL：
1. 首次等待 = 录制时的操作间隔
2. 如果识别失败，每秒重试一次
3. 必须成功才能继续下一步

### 2.2 用户体验优化需求

#### REQ-005: GUI主窗口重构
**WHEN** 用户打开应用
**THEN** 系统SHALL 显示：
1. 顶部工具栏：录制、执行、停止等快捷操作
2. 左侧导航栏：脚本列表（卡片式）
3. 中央工作区：当前操作界面
4. 底部状态栏：实时状态信息
5. 右侧面板：辅助功能和设置

#### REQ-006: 实时进度反馈
**WHEN** 系统执行耗时操作
**THEN** 系统SHALL 显示：
- 进度条（百分比）
- 当前操作描述
- 预估剩余时间
- 取消按钮（如适用）

#### REQ-007: 友好错误提示
**WHEN** 系统遇到错误
**THEN** 系统SHALL：
1. 显示错误类型图标（警告/错误/信息）
2. 用简单语言描述问题
3. 提供解决建议
4. 记录详细日志供技术支持

#### REQ-008: 操作流程优化
**WHEN** 用户完成录制
**THEN** 系统SHALL：
1. 自动显示"正在上传..."进度
2. 完成后显示"正在AI识别..."
3. 成功后自动打开脚本预览
4. 提供"立即执行"快捷按钮

### 2.3 GUI界面详细需求

#### REQ-009: 现代化视觉设计
**WHEN** 渲染任何UI元素
**THEN** 系统SHALL 使用：
- 暗色主题（可选亮色）
- 圆角设计（8px）
- 阴影效果（卡片式）
- 平滑动画（200ms过渡）
- 一致的颜色方案

#### REQ-010: 响应式布局
**WHEN** 窗口大小改变
**THEN** 系统SHALL：
- 自动调整布局
- 保持最小可用尺寸（1024×768）
- 记住用户的布局偏好

#### REQ-011: 脚本卡片设计
**WHEN** 显示脚本列表
**THEN** 每个脚本SHALL 显示：
- 缩略图（第一步截图）
- 脚本名称
- 创建时间
- 步骤数量
- 执行/编辑/删除按钮
- 鼠标悬停显示详情

### 2.4 Bug修复需求

#### REQ-012: 崩溃类Bug修复
**WHEN** 系统运行时
**THEN** 系统SHALL NOT 因以下原因崩溃：
- 网络断开
- 文件权限问题
- 内存不足
- 异常输入

#### REQ-013: 功能类Bug修复
**WHEN** 使用核心功能
**THEN** 系统SHALL 确保：
- 录制缓冲区正确维护
- 操作间隔精确记录
- BBOX裁切准确
- 多级识别正确降级

#### REQ-014: 界面类Bug修复
**WHEN** 操作GUI
**THEN** 系统SHALL：
- 正确响应所有按钮点击
- 更新所有状态显示
- 不出现界面卡死
- 正确处理并发操作

## 3. 非功能需求

### 3.1 性能需求

#### NFR-001: 录制性能
- 帧率：稳定10FPS
- CPU占用：< 15%
- 内存占用：< 500MB
- 磁盘写入：< 10MB/s

#### NFR-002: 执行性能
- 单次识别：< 500ms（SIFT < 1000ms）
- 界面响应：< 100ms
- 脚本加载：< 2s

### 3.2 可靠性需求

#### NFR-003: 稳定性
- 连续运行：> 24小时无崩溃
- 错误恢复：自动恢复率 > 80%
- 数据完整性：100%无损

### 3.3 兼容性需求

#### NFR-004: 系统兼容性
- Windows 10/11
- macOS 10.15+
- Ubuntu 20.04+
- Python 3.8+

## 4. 约束条件

### 4.1 技术约束
- 必须使用PyQt6（不能降级到PyQt5）
- 必须使用OpenCV进行图像处理
- 必须保持768×768标准化
- 必须使用Supabase作为后端

### 4.2 产品原则约束
- 绝不使用DOM或Accessibility API
- 绝不降低检测阈值
- 绝不允许离线录制
- 必须保持步进式执行

## 5. 验收标准

### 5.1 产品原则验收
- [ ] 所有识别使用纯图像方法
- [ ] 所有阈值符合要求
- [ ] 768标准化完整实现
- [ ] 离线/在线功能正确区分

### 5.2 用户体验验收
- [ ] GUI界面美观现代
- [ ] 操作流程顺畅
- [ ] 错误提示友好
- [ ] 进度反馈实时

### 5.3 Bug修复验收
- [ ] 无崩溃类Bug
- [ ] 核心功能正常
- [ ] 界面响应正常
- [ ] 性能达标

## 6. 优先级定义

### P0 - 必须立即修复
- 违反产品原则的问题
- 崩溃类Bug
- 数据丢失问题

### P1 - 本版本必须完成
- 核心功能Bug
- 用户体验关键问题
- 性能瓶颈

### P2 - 重要但可延后
- GUI美化
- 非核心功能
- 优化类需求

### P3 - 未来版本
- 新特性
- 高级功能
- 实验性功能

---

**文档状态**：初稿完成
**创建时间**：2025-01-13
**版本**：1.0