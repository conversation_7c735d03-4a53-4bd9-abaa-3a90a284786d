# ShowForAI V3 完整开发计划 - 技术设计

## 1. 系统架构设计

### 1.1 整体架构

```mermaid
graph TB
    subgraph "客户端层"
        GUI[GUI界面层]
        Core[核心业务层]
        Storage[本地存储层]
    end
    
    subgraph "网络层"
        API[API适配层]
        Security[安全层]
    end
    
    subgraph "服务端层"
        AI[AI识别服务]
        DB[Supabase数据库]
        Storage2[云存储]
    end
    
    GUI --> Core
    Core --> Storage
    Core --> API
    API --> Security
    Security --> AI
    Security --> DB
    Security --> Storage2
```

### 1.2 模块划分

```mermaid
graph LR
    subgraph "核心模块"
        Recorder[录制器]
        Executor[执行器]
        ScriptManager[脚本管理]
    end
    
    subgraph "支撑模块"
        ImageProcessor[图像处理]
        Matcher[元素匹配]
        WaitManager[等待管理]
    end
    
    subgraph "界面模块"
        MainWindow[主窗口]
        Dashboard[仪表板]
        Monitor[执行监控]
    end
    
    Recorder --> ImageProcessor
    Executor --> Matcher
    Executor --> WaitManager
    MainWindow --> Dashboard
    MainWindow --> Monitor
```

## 2. 核心功能设计

### 2.1 统一阈值管理系统

```python
# threshold_manager.py
class ThresholdManager:
    """统一的阈值管理器"""
    
    # 严格的阈值定义（不可修改）
    THRESHOLDS = {
        'template_matching': 0.85,
        'orb_min_points': 15,
        'orb_ratio': 0.7,
        'orb_inlier_ratio': 0.6,
        'sift_min_points': 20,
        'sift_ratio': 0.65,
        'sift_inlier_ratio': 0.7,
        'multiscale_matching': 0.80
    }
    
    @classmethod
    def get_threshold(cls, key: str) -> float:
        """获取阈值，确保不被修改"""
        return cls.THRESHOLDS.get(key)
    
    @classmethod
    def validate_match(cls, match_type: str, score: float, **kwargs) -> bool:
        """验证匹配是否满足阈值要求"""
        # 实现验证逻辑
        pass
```

### 2.2 完整的768标准化流程

```python
# standardization_manager.py
class StandardizationManager:
    """768×768标准化管理器"""
    
    STANDARD_SIZE = (768, 768)
    
    def __init__(self):
        self.buffer = CircularBuffer(max_frames=15)
        self.capture_thread = None
        
    def start_continuous_capture(self):
        """开始10FPS连续截图"""
        self.capture_thread = Thread(target=self._capture_loop)
        self.capture_thread.start()
    
    def _capture_loop(self):
        """截图循环"""
        while self.capturing:
            frame = self._capture_screen()
            self.buffer.add(frame)
            time.sleep(0.1)  # 10FPS
    
    def get_pre_click_frame(self, frames_back=3):
        """获取点击前第N帧"""
        return self.buffer.get_frame(-frames_back)
    
    def standardize_image(self, image: np.ndarray) -> np.ndarray:
        """标准化图像为768×768"""
        return cv2.resize(image, self.STANDARD_SIZE, 
                         interpolation=cv2.INTER_AREA)
```

### 2.3 智能等待机制完整实现

```python
# smart_wait_system.py
class SmartWaitSystem:
    """智能等待系统"""
    
    def __init__(self):
        self.mode = WaitMode.ACTIVE  # 或 AUXILIARY
        self.recorded_intervals = []
        
    def wait_for_element(self, template, recorded_interval=None):
        """等待元素出现"""
        if self.mode == WaitMode.ACTIVE:
            return self._active_wait(template, recorded_interval)
        else:
            return self._auxiliary_wait(template)
    
    def _active_wait(self, template, recorded_interval):
        """主动模式等待"""
        # 1. 首先等待录制时的间隔
        if recorded_interval:
            time.sleep(recorded_interval)
        
        # 2. 尝试识别
        while True:
            if self._try_match(template):
                return True
            time.sleep(1)  # 每秒重试
    
    def _auxiliary_wait(self, template):
        """辅助模式等待"""
        # 无限等待直到找到
        while True:
            if self._try_match(template):
                return True
            time.sleep(0.5)  # 更频繁检查
```

## 3. GUI界面设计

### 3.1 主窗口布局设计

```mermaid
graph TB
    subgraph "主窗口布局"
        MenuBar[菜单栏]
        ToolBar[工具栏]
        
        subgraph "主体区域"
            LeftPanel[左侧面板-脚本列表]
            CenterArea[中央工作区]
            RightPanel[右侧面板-辅助功能]
        end
        
        StatusBar[状态栏]
    end
```

### 3.2 GUI组件详细设计

```python
# enhanced_gui_design.py
class ModernMainWindow(QMainWindow):
    """现代化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        # 应用现代主题
        self.apply_modern_theme()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def apply_modern_theme(self):
        """应用现代化主题"""
        self.setStyleSheet("""
            QMainWindow {
                background: #1e1e1e;
            }
            QPushButton {
                background: #2d2d30;
                border: none;
                border-radius: 8px;
                padding: 10px;
                color: white;
            }
            QPushButton:hover {
                background: #3e3e42;
            }
            QListWidget {
                background: #252526;
                border: none;
                border-radius: 8px;
            }
        """)
```

### 3.3 脚本卡片组件设计

```python
# script_card_widget.py
class ScriptCard(QWidget):
    """脚本卡片组件"""
    
    def __init__(self, script_data):
        super().__init__()
        self.script_data = script_data
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 缩略图
        self.thumbnail = QLabel()
        self.thumbnail.setPixmap(self.get_thumbnail())
        
        # 脚本信息
        self.name_label = QLabel(self.script_data['name'])
        self.info_label = QLabel(f"{self.script_data['steps']}步")
        
        # 操作按钮
        button_layout = QHBoxLayout()
        self.play_btn = QPushButton("▶")
        self.edit_btn = QPushButton("✏")
        self.delete_btn = QPushButton("🗑")
        
        # 添加动画效果
        self.setup_animations()
```

## 4. Bug修复方案设计

### 4.1 崩溃类Bug修复策略

```python
# crash_prevention.py
class CrashPrevention:
    """崩溃预防系统"""
    
    @staticmethod
    def safe_execute(func, *args, **kwargs):
        """安全执行函数"""
        try:
            return func(*args, **kwargs)
        except MemoryError:
            # 内存不足处理
            gc.collect()
            return None
        except PermissionError:
            # 权限问题处理
            logger.error("权限不足")
            return None
        except Exception as e:
            # 通用异常处理
            logger.error(f"未预期错误: {e}")
            return None
    
    @staticmethod
    def network_resilient(func):
        """网络弹性装饰器"""
        def wrapper(*args, **kwargs):
            max_retries = 3
            for i in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except NetworkError:
                    if i < max_retries - 1:
                        time.sleep(2 ** i)
                    else:
                        raise
        return wrapper
```

### 4.2 功能类Bug修复清单

| Bug类型 | 问题描述 | 修复方案 | 优先级 |
|---------|----------|----------|--------|
| 录制缓冲区 | 15帧缓冲未正确维护 | 实现CircularBuffer类 | P0 |
| 操作间隔 | 时间戳记录不准确 | 使用高精度计时器 | P0 |
| BBOX裁切 | 坐标转换错误 | 统一使用768坐标系 | P0 |
| 多级识别 | 降级逻辑不完整 | 实现完整降级链 | P1 |
| TODO功能 | 50+处未完成 | 逐个实现或移除 | P1 |

### 4.3 界面类Bug修复方案

```python
# ui_bug_fixes.py
class UIBugFixes:
    """UI Bug修复集合"""
    
    @staticmethod
    def fix_button_response(button: QPushButton):
        """修复按钮响应问题"""
        # 防止重复点击
        button.clicked.connect(lambda: button.setEnabled(False))
        QTimer.singleShot(500, lambda: button.setEnabled(True))
    
    @staticmethod
    def fix_status_update(status_bar: QStatusBar):
        """修复状态栏更新问题"""
        # 使用信号槽机制确保线程安全
        update_signal = pyqtSignal(str)
        update_signal.connect(status_bar.showMessage)
    
    @staticmethod
    def fix_ui_freeze():
        """修复界面卡死问题"""
        # 将耗时操作移到工作线程
        QApplication.processEvents()
```

## 5. 性能优化设计

### 5.1 内存优化策略

```python
# memory_optimization.py
class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.cache_size_limit = 100 * 1024 * 1024  # 100MB
        self.current_cache_size = 0
        
    def optimize_image_cache(self):
        """优化图像缓存"""
        # 使用LRU缓存
        # 压缩存储
        # 及时释放
        pass
    
    def monitor_memory_usage(self):
        """监控内存使用"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        if memory_info.rss > 500 * 1024 * 1024:  # 500MB
            self.trigger_cleanup()
```

### 5.2 CPU优化策略

```python
# cpu_optimization.py
class CPUOptimizer:
    """CPU优化器"""
    
    def __init__(self):
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
    def optimize_image_matching(self):
        """优化图像匹配"""
        # 使用多线程
        # 图像金字塔
        # 感兴趣区域（ROI）
        pass
    
    def optimize_continuous_capture(self):
        """优化连续截图"""
        # 降低非活动时的帧率
        # 使用硬件加速
        pass
```

## 6. 数据模型设计

### 6.1 脚本数据模型

```python
@dataclass
class ScriptData:
    """脚本数据模型"""
    id: str
    name: str
    description: str
    created_at: datetime
    updated_at: datetime
    
    # 录制信息
    original_resolution: Tuple[int, int]
    recorder_version: str
    
    # 操作序列
    actions: List[ActionData]
    
    # 768×768数据
    standardized_images: List[np.ndarray]
    bbox_info: List[BBoxInfo]
    
    # 执行统计
    execution_count: int
    success_rate: float
    average_duration: float
```

### 6.2 配置数据模型

```python
@dataclass
class GlobalConfig:
    """全局配置模型"""
    
    # 界面配置
    theme: str = "dark"
    language: str = "zh_CN"
    window_geometry: Dict = field(default_factory=dict)
    
    # 执行配置
    default_mode: str = "active"
    confidence_threshold: float = 0.85  # 只读
    
    # 网络配置
    api_endpoint: str = ""
    timeout: int = 30
    
    # 性能配置
    capture_fps: int = 10
    max_cache_size: int = 100  # MB
```

## 7. API接口设计

### 7.1 内部API设计

```python
class InternalAPI:
    """内部API接口"""
    
    # 录制相关
    async def start_recording(self, name: str) -> bool:
        """开始录制"""
        pass
    
    async def stop_recording(self) -> RecordingData:
        """停止录制"""
        pass
    
    # 执行相关
    async def execute_script(self, script_id: str, mode: str) -> ExecutionResult:
        """执行脚本"""
        pass
    
    # 脚本管理
    async def get_scripts(self) -> List[ScriptData]:
        """获取脚本列表"""
        pass
    
    async def delete_script(self, script_id: str) -> bool:
        """删除脚本"""
        pass
```

### 7.2 与服务端通信API

```python
class ServerAPI:
    """服务端API接口"""
    
    # AI识别
    async def process_recording(self, recording_data: bytes) -> ProcessResult:
        """处理录制数据"""
        pass
    
    # 云同步
    async def sync_scripts(self) -> SyncResult:
        """同步脚本"""
        pass
    
    # 分享功能
    async def share_script(self, script_id: str) -> ShareLink:
        """分享脚本"""
        pass
```

## 8. 测试策略设计

### 8.1 单元测试

```python
# test_threshold_manager.py
class TestThresholdManager:
    """阈值管理器测试"""
    
    def test_threshold_immutability(self):
        """测试阈值不可修改"""
        pass
    
    def test_validation_logic(self):
        """测试验证逻辑"""
        pass
```

### 8.2 集成测试

```python
# test_integration.py
class TestIntegration:
    """集成测试"""
    
    def test_record_to_execute_flow(self):
        """测试录制到执行完整流程"""
        pass
    
    def test_offline_mode_behavior(self):
        """测试离线模式行为"""
        pass
```

### 8.3 性能测试

```python
# test_performance.py
class TestPerformance:
    """性能测试"""
    
    def test_memory_usage(self):
        """测试内存使用"""
        pass
    
    def test_cpu_usage(self):
        """测试CPU使用"""
        pass
    
    def test_recognition_speed(self):
        """测试识别速度"""
        pass
```

## 9. 部署架构设计

### 9.1 客户端部署

```yaml
# deployment.yaml
client:
  platforms:
    - windows: 
        installer: NSIS
        requirements: Windows 10+
    - macos:
        installer: DMG
        requirements: macOS 10.15+
    - linux:
        installer: AppImage
        requirements: Ubuntu 20.04+
        
  dependencies:
    - python: ">=3.8"
    - pyqt6: ">=6.0"
    - opencv-python: ">=4.5"
```

### 9.2 更新机制

```python
# auto_updater.py
class AutoUpdater:
    """自动更新器"""
    
    def check_updates(self):
        """检查更新"""
        pass
    
    def download_update(self):
        """下载更新"""
        pass
    
    def apply_update(self):
        """应用更新"""
        pass
```

---

**文档状态**：初稿完成
**创建时间**：2025-01-13
**版本**：1.0