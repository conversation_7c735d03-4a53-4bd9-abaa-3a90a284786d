"""
Main window for ShowForAI V3 GUI
Manages the main application window and coordinates all GUI components
"""

import sys
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QMenu, QStatusBar, QMessageBox,
    QSplitter, QToolBar, QProgressBar, QLabel
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, pyqtSlot, QTimer, QSettings
)
from PyQt6.QtGui import QAction, QIcon, QKeySequence

from showforai.api.adapter import create_api_adapter, APIResponse
from showforai.config import Config
from showforai.logger import get_logger
from showforai.utils.network_checker import is_online

# Import other GUI components (to be created)
from .dashboard import DashboardWidget
from .script_editor import ScriptEditorWidget
from .execution_monitor import ExecutionMonitorWidget
from .auxiliary_panel import AuxiliaryPanel
from .async_worker import <PERSON>ync<PERSON>or<PERSON>
from .styles import ModernDarkTheme


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self, config: Optional[Config] = None):
        super().__init__()
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Initialize API adapter
        self.api_adapter = create_api_adapter(self.config)
        
        # Settings
        self.settings = QSettings("ShowForAI", "V3")
        
        # Initialize UI
        self.init_ui()
        
        # Start initialization
        self.initialize_modules()
        
        # Setup status updates
        self.setup_timers()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("ShowForAI V3 - AI-Powered Automation")
        self.setGeometry(100, 100, 1200, 800)
        
        # Apply modern dark theme
        ModernDarkTheme.apply_theme(self)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        
        # Add tabs
        self.dashboard_widget = DashboardWidget(self.api_adapter)
        self.script_editor_widget = ScriptEditorWidget(self.api_adapter)
        
        # Create execution tab with splitter for active and auxiliary modes
        execution_tab = QWidget()
        execution_layout = QVBoxLayout(execution_tab)
        execution_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create splitter for execution modes
        execution_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Active mode (execution monitor)
        self.execution_monitor_widget = ExecutionMonitorWidget(self.api_adapter)
        execution_splitter.addWidget(self.execution_monitor_widget)
        
        # Auxiliary mode panel
        self.auxiliary_panel = AuxiliaryPanel(self.api_adapter)
        execution_splitter.addWidget(self.auxiliary_panel)
        
        # Set splitter sizes (60% for active mode, 40% for auxiliary mode)
        execution_splitter.setSizes([480, 320])
        
        execution_layout.addWidget(execution_splitter)
        
        self.tab_widget.addTab(self.dashboard_widget, "Dashboard")
        self.tab_widget.addTab(self.script_editor_widget, "Script Editor")
        self.tab_widget.addTab(execution_tab, "Execution Monitor")
        
        main_layout.addWidget(self.tab_widget)
        
        # Create status bar
        self.create_status_bar()
        
        # Restore window state
        self.restore_state()
    
    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        new_action = QAction("&New Script", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_script)
        file_menu.addAction(new_action)
        
        open_action = QAction("&Open Script", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_script)
        file_menu.addAction(open_action)
        
        save_action = QAction("&Save Script", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_script)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        settings_action = QAction("&Settings", self)
        settings_action.setShortcut(QKeySequence("Ctrl+,"))
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Recording menu
        recording_menu = menubar.addMenu("&Recording")
        
        self.start_recording_action = QAction("&Start Recording", self)
        self.start_recording_action.setShortcut(QKeySequence("Ctrl+R"))
        self.start_recording_action.triggered.connect(self.start_recording)
        recording_menu.addAction(self.start_recording_action)
        
        self.stop_recording_action = QAction("S&top Recording", self)
        self.stop_recording_action.setShortcut(QKeySequence("Ctrl+Shift+R"))
        self.stop_recording_action.triggered.connect(self.stop_recording)
        self.stop_recording_action.setEnabled(False)
        recording_menu.addAction(self.stop_recording_action)
        
        recording_menu.addSeparator()
        
        clear_actions_action = QAction("&Clear Actions", self)
        clear_actions_action.triggered.connect(self.clear_recording)
        recording_menu.addAction(clear_actions_action)
        
        # Execution menu
        execution_menu = menubar.addMenu("&Execution")
        
        self.run_script_action = QAction("&Run Script", self)
        self.run_script_action.setShortcut(QKeySequence("F5"))
        self.run_script_action.triggered.connect(self.run_script)
        execution_menu.addAction(self.run_script_action)
        
        self.stop_execution_action = QAction("&Stop Execution", self)
        self.stop_execution_action.setShortcut(QKeySequence("Shift+F5"))
        self.stop_execution_action.triggered.connect(self.stop_execution)
        self.stop_execution_action.setEnabled(False)
        execution_menu.addAction(self.stop_execution_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        docs_action = QAction("&Documentation", self)
        docs_action.setShortcut(QKeySequence.StandardKey.HelpContents)
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)
        
        help_menu.addSeparator()
        
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """Create the toolbar"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # Add actions to toolbar
        file_menu = self.menuBar().findChild(QMenu, "&File")
        if file_menu and file_menu.actions():
            if len(file_menu.actions()) > 0:
                toolbar.addAction(file_menu.actions()[0])  # New
            if len(file_menu.actions()) > 1:
                toolbar.addAction(file_menu.actions()[1])  # Open
            if len(file_menu.actions()) > 2:
                toolbar.addAction(file_menu.actions()[2])  # Save
        toolbar.addSeparator()
        toolbar.addAction(self.start_recording_action)
        toolbar.addAction(self.stop_recording_action)
        toolbar.addSeparator()
        toolbar.addAction(self.run_script_action)
        toolbar.addAction(self.stop_execution_action)
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Connection status
        self.connection_label = QLabel("Connecting...")
        self.status_bar.addWidget(self.connection_label)
        
        # Recording status
        self.recording_label = QLabel("Ready")
        self.status_bar.addWidget(self.recording_label)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def initialize_modules(self):
        """Initialize backend modules asynchronously"""
        self.show_progress("Initializing modules...")
        
        worker = AsyncWorker(self.api_adapter.initialize())
        worker.result.connect(self.on_initialization_complete)
        worker.error.connect(self.on_initialization_error)
        worker.start()
    
    @pyqtSlot(object)
    def on_initialization_complete(self, response: APIResponse):
        """Handle initialization completion"""
        self.hide_progress()
        
        if response.success:
            self.connection_label.setText("Connected")
            self.connection_label.setStyleSheet("QLabel { color: #4CAF50; }")
            self.logger.info("All modules initialized successfully")
            
            # Enable features
            self.enable_features()
        else:
            self.connection_label.setText("Connection Error")
            self.connection_label.setStyleSheet("QLabel { color: #F44336; }")
            self.show_error("Initialization Error", 
                          f"Some modules failed to initialize: {response.error}")
    
    @pyqtSlot(str)
    def on_initialization_error(self, error: str):
        """Handle initialization error"""
        self.hide_progress()
        self.connection_label.setText("Initialization Failed")
        self.connection_label.setStyleSheet("QLabel { color: #F44336; }")
        self.show_error("Initialization Error", error)
    
    def setup_timers(self):
        """Setup periodic update timers"""
        # Health check timer
        self.health_timer = QTimer()
        self.health_timer.timeout.connect(self.check_health)
        self.health_timer.start(30000)  # Check every 30 seconds
        
        # Recording status timer
        self.recording_timer = QTimer()
        self.recording_timer.timeout.connect(self.update_recording_status)
        self.recording_timer.start(1000)  # Update every second
    
    def check_health(self):
        """Check health of backend modules"""
        worker = AsyncWorker(self.api_adapter.health_check())
        worker.result.connect(self.on_health_check_complete)
        worker.start()
    
    @pyqtSlot(object)
    def on_health_check_complete(self, response: APIResponse):
        """Handle health check completion"""
        if response.success:
            self.connection_label.setText("Connected")
            self.connection_label.setStyleSheet("QLabel { color: #4CAF50; }")
        else:
            self.connection_label.setText("Connection Issues")
            self.connection_label.setStyleSheet("QLabel { color: #FFA500; }")
    
    def update_recording_status(self):
        """Update recording status"""
        worker = AsyncWorker(self.api_adapter.recorder.get_state())
        worker.result.connect(self.on_recording_status_update)
        worker.start()
    
    @pyqtSlot(object)
    def on_recording_status_update(self, response: APIResponse):
        """Handle recording status update"""
        if response.success:
            data = response.data
            if data.get("is_recording"):
                count = data.get("actions_count", 0)
                self.recording_label.setText(f"Recording ({count} actions)")
                self.recording_label.setStyleSheet("QLabel { color: #F44336; }")
                self.start_recording_action.setEnabled(False)
                self.stop_recording_action.setEnabled(True)
            else:
                count = data.get("actions_count", 0)
                if count > 0:
                    self.recording_label.setText(f"Ready ({count} actions)")
                else:
                    self.recording_label.setText("Ready")
                self.recording_label.setStyleSheet("QLabel { color: #4CAF50; }")
                self.start_recording_action.setEnabled(True)
                self.stop_recording_action.setEnabled(False)
    
    def enable_features(self):
        """Enable features after successful initialization"""
        self.dashboard_widget.refresh_data()
        self.script_editor_widget.load_scripts()
    
    # Action handlers
    def new_script(self):
        """Create new script"""
        self.script_editor_widget.new_script()
        self.tab_widget.setCurrentWidget(self.script_editor_widget)
    
    def open_script(self):
        """Open existing script"""
        self.script_editor_widget.open_script()
        self.tab_widget.setCurrentWidget(self.script_editor_widget)
    
    def save_script(self):
        """Save current script"""
        self.script_editor_widget.save_script()
    
    def start_recording(self):
        """开始录制动作"""
        # 检查网络状态
        if not is_online():
            QMessageBox.warning(
                self,
                "无法录制",
                "录制需要网络连接以进行AI识别。\n请检查您的网络连接。"
            )
            return
            
        self.show_progress("正在启动录制...")
        
        worker = AsyncWorker(self.api_adapter.recorder.start_recording())
        worker.result.connect(self.on_recording_started)
        worker.error.connect(self.on_recording_error)
        worker.start()
    
    @pyqtSlot(object)
    def on_recording_started(self, response: APIResponse):
        """Handle recording start"""
        self.hide_progress()
        
        if response.success:
            self.show_info("Recording Started", "Recording is now active. Perform your actions.")
            self.script_editor_widget.on_recording_started()
        else:
            self.show_error("Recording Error", response.error)
    
    def stop_recording(self):
        """Stop recording actions"""
        self.show_progress("Stopping recording...")
        
        worker = AsyncWorker(self.api_adapter.recorder.stop_recording())
        worker.result.connect(self.on_recording_stopped)
        worker.error.connect(self.on_recording_error)
        worker.start()
    
    @pyqtSlot(object)
    def on_recording_stopped(self, response: APIResponse):
        """Handle recording stop"""
        self.hide_progress()
        
        if response.success:
            data = response.data
            count = data.get("count", 0)
            self.show_info("Recording Stopped", f"Captured {count} actions")
            self.script_editor_widget.on_recording_stopped(data.get("actions", []))
        else:
            self.show_error("Recording Error", response.error)
    
    @pyqtSlot(str)
    def on_recording_error(self, error: str):
        """Handle recording error"""
        self.hide_progress()
        self.show_error("Recording Error", error)
    
    def clear_recording(self):
        """Clear recorded actions"""
        reply = QMessageBox.question(
            self, "Clear Actions",
            "Are you sure you want to clear all recorded actions?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            worker = AsyncWorker(self.api_adapter.recorder.clear_actions())
            worker.result.connect(lambda r: self.show_info("Actions Cleared", 
                                                          "All recorded actions have been cleared"))
            worker.start()
    
    def run_script(self):
        """Run current script"""
        self.tab_widget.setCurrentWidget(self.execution_monitor_widget)
        self.execution_monitor_widget.run_script()
        self.run_script_action.setEnabled(False)
        self.stop_execution_action.setEnabled(True)
    
    def stop_execution(self):
        """Stop script execution"""
        self.execution_monitor_widget.stop_execution()
        self.run_script_action.setEnabled(True)
        self.stop_execution_action.setEnabled(False)
    
    def show_settings(self):
        """Show settings dialog"""
        # TODO: Implement settings dialog
        self.show_info("Settings", "Settings dialog will be implemented soon")
    
    def show_documentation(self):
        """Show documentation"""
        # TODO: Open documentation
        self.show_info("Documentation", "Documentation will be available soon")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About ShowForAI V3",
            "ShowForAI V3\n"
            "AI-Powered Automation Tool\n\n"
            "Version: 3.0.0\n"
            "License: MIT\n\n"
            "Automate your workflow with AI assistance"
        )
    
    # Utility methods
    def show_progress(self, message: str):
        """Show progress indicator"""
        self.status_bar.showMessage(message)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
    
    def hide_progress(self):
        """Hide progress indicator"""
        self.status_bar.clearMessage()
        self.progress_bar.setVisible(False)
    
    def show_error(self, title: str, message: str):
        """Show error message"""
        QMessageBox.critical(self, title, message)
    
    def show_info(self, title: str, message: str):
        """Show info message"""
        QMessageBox.information(self, title, message)
    
    
    def restore_state(self):
        """Restore window state from settings"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        state = self.settings.value("windowState")
        if state:
            self.restoreState(state)
    
    def save_state(self):
        """Save window state to settings"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Save state
        self.save_state()
        
        # Cleanup
        self.api_adapter.cleanup()
        
        # Accept close
        event.accept()


def main():
    """Main entry point for GUI application"""
    # Import security manager for environment validation
    from showforai.security.config_manager import SecureConfigManager
    
    # Validate environment before starting GUI
    try:
        config_manager = SecureConfigManager()
        validation_results = config_manager.validate_environment()
        missing_vars = [var for var, valid in validation_results.items() if not valid]
        
        if missing_vars and not config_manager.is_development_mode():
            # Show error dialog and exit if critical variables are missing
            app = QApplication(sys.argv)
            QMessageBox.critical(
                None,
                "Environment Configuration Error",
                f"Missing required environment variables:\n\n" +
                "\n".join(f"• {var}" for var in missing_vars) +
                "\n\nPlease configure the .env file and restart the application."
            )
            sys.exit(1)
    except Exception as e:
        # Log error but continue in development mode
        import logging
        logging.error(f"Environment validation error: {e}")
    
    app = QApplication(sys.argv)
    app.setApplicationName("ShowForAI V3")
    app.setOrganizationName("ShowForAI")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()