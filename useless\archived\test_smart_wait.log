2025-08-14 02:01:55.620 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:01:55.621 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting auxiliary mode wait with config: WaitConfig(mode=<WaitMode.AUXILIARY: 'auxiliary'>, initial_wait_ms=0, retry_interval_ms=500, max_retries=-1, timeout_seconds=-1)
2025-08-14 02:01:55.621 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:105 - Starting auxiliary mode infinite wait
2025-08-14 02:01:57.630 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:114 - Element detected after 5 attempts, 2008ms
2025-08-14 02:02:53.620 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:02:53.621 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting auxiliary mode wait with config: WaitConfig(mode=<WaitMode.AUXILIARY: 'auxiliary'>, initial_wait_ms=0, retry_interval_ms=500, max_retries=-1, timeout_seconds=-1)
2025-08-14 02:02:53.621 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:105 - Starting auxiliary mode infinite wait
2025-08-14 02:02:55.622 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:114 - Element detected after 5 attempts, 2000ms
2025-08-14 02:03:21.758 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:03:21.758 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_with_interval:336 - Wait with recorded interval: 500ms
2025-08-14 02:03:21.758 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting active mode wait with config: WaitConfig(mode=<WaitMode.ACTIVE: 'active'>, initial_wait_ms=500, retry_interval_ms=1000, max_retries=30, timeout_seconds=30)
2025-08-14 02:03:21.758 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:164 - Initial wait: 500ms (from recorded interval)
2025-08-14 02:03:22.259 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:172 - Element detected on first attempt after 500ms
2025-08-14 02:03:22.259 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_with_interval:336 - Wait with recorded interval: 200ms
2025-08-14 02:03:22.259 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting active mode wait with config: WaitConfig(mode=<WaitMode.ACTIVE: 'active'>, initial_wait_ms=200, retry_interval_ms=1000, max_retries=30, timeout_seconds=30)
2025-08-14 02:03:22.259 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:164 - Initial wait: 200ms (from recorded interval)
2025-08-14 02:03:22.459 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:183 - First detection failed, starting retry loop
2025-08-14 02:03:24.460 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:199 - Element detected after 3 attempts, 2200ms
2025-08-14 02:03:24.460 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting active mode wait with config: WaitConfig(mode=<WaitMode.ACTIVE: 'active'>, initial_wait_ms=100, retry_interval_ms=1000, max_retries=3, timeout_seconds=3)
2025-08-14 02:03:24.460 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:164 - Initial wait: 100ms (from recorded interval)
2025-08-14 02:03:24.560 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:183 - First detection failed, starting retry loop
2025-08-14 02:03:26.564 | WARNING  | src.showforai.executor.smart_wait_manager:wait_for_element:219 - Element not found after 3 attempts, 2104ms. Max retries reached
2025-08-14 02:03:26.565 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:03:26.565 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting auxiliary mode wait with config: WaitConfig(mode=<WaitMode.AUXILIARY: 'auxiliary'>, initial_wait_ms=0, retry_interval_ms=500, max_retries=-1, timeout_seconds=-1)
2025-08-14 02:03:26.565 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:105 - Starting auxiliary mode infinite wait
2025-08-14 02:03:28.566 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:114 - Element detected after 5 attempts, 2001ms
2025-08-14 02:03:28.567 | INFO     | showforai.storage.element_cache:__init__:57 - ElementCacheManager initialized at: C:\Users\<USER>\.showforai\element_cache
2025-08-14 02:03:28.568 | INFO     | showforai.processing.bbox_cutter:__init__:51 - BboxCutter initialized with cache at: C:\Users\<USER>\.showforai\element_cache
2025-08-14 02:03:28.568 | INFO     | src.showforai.executor.script_loader:__init__:110 - Script loader initialized with element cache support
2025-08-14 02:03:28.568 | INFO     | showforai.ai.image_processor:__init__:28 - ImageProcessor initialized with standard resolution: (768, 768)
2025-08-14 02:03:28.568 | INFO     | src.showforai.executor.element_matcher:__init__:84 - Element matcher initialized with threshold=0.8, multi_scale=True, grayscale=True, standardized_matching=True
2025-08-14 02:03:28.568 | INFO     | src.showforai.executor.action_executor:__init__:102 - Action executor initialized. Screen size: 3840x2160
2025-08-14 02:03:28.568 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:03:28.568 | DEBUG    | src.showforai.robustness.timeout_manager:__init__:79 - TimeoutManager initialized with config: TimeoutConfig(default_timeout=10.0, element_detection_timeout=5, action_execution_timeout=5.0, ai_processing_timeout=30.0, network_timeout=20.0, screenshot_timeout=2.0, active_mode_strategy=<TimeoutStrategy.SKIP: 2>, auxiliary_mode_strategy=<TimeoutStrategy.FAIL: 1>, enable_logging=True, enable_callback=True, use_thread_timeout=True, use_signal_timeout=False)
2025-08-14 02:03:28.568 | DEBUG    | src.showforai.robustness.skip_handler:__init__:102 - SkipHandler initialized with config: SkipConfig(enable_skip=False, skip_strategy=<SkipStrategy.CHECK_NEXT: 4>, element_timeout=5, check_subsequent_count=2, subsequent_check_timeout=5.0, confidence_threshold=0.5, max_consecutive_skips=3, skip_on_errors=['timeout', 'not_found'], enable_logging=True, track_skip_history=True, generate_skip_report=True)
2025-08-14 02:04:02.984 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:04:02.984 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_with_interval:336 - Wait with recorded interval: 500ms
2025-08-14 02:04:02.984 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting active mode wait with config: WaitConfig(mode=<WaitMode.ACTIVE: 'active'>, initial_wait_ms=500, retry_interval_ms=1000, max_retries=30, timeout_seconds=30)
2025-08-14 02:04:02.985 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:164 - Initial wait: 500ms (from recorded interval)
2025-08-14 02:04:03.485 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:172 - Element detected on first attempt after 500ms
2025-08-14 02:04:03.485 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_with_interval:336 - Wait with recorded interval: 200ms
2025-08-14 02:04:03.485 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting active mode wait with config: WaitConfig(mode=<WaitMode.ACTIVE: 'active'>, initial_wait_ms=200, retry_interval_ms=1000, max_retries=30, timeout_seconds=30)
2025-08-14 02:04:03.485 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:164 - Initial wait: 200ms (from recorded interval)
2025-08-14 02:04:03.686 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:183 - First detection failed, starting retry loop
2025-08-14 02:04:05.687 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:199 - Element detected after 3 attempts, 2201ms
2025-08-14 02:04:05.687 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting active mode wait with config: WaitConfig(mode=<WaitMode.ACTIVE: 'active'>, initial_wait_ms=100, retry_interval_ms=1000, max_retries=3, timeout_seconds=3)
2025-08-14 02:04:05.687 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:164 - Initial wait: 100ms (from recorded interval)
2025-08-14 02:04:05.788 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:183 - First detection failed, starting retry loop
2025-08-14 02:04:07.788 | WARNING  | src.showforai.executor.smart_wait_manager:wait_for_element:219 - Element not found after 3 attempts, 2100ms. Max retries reached
2025-08-14 02:04:07.788 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:04:07.789 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:303 - Starting auxiliary mode wait with config: WaitConfig(mode=<WaitMode.AUXILIARY: 'auxiliary'>, initial_wait_ms=0, retry_interval_ms=500, max_retries=-1, timeout_seconds=-1)
2025-08-14 02:04:07.789 | DEBUG    | src.showforai.executor.smart_wait_manager:wait_for_element:105 - Starting auxiliary mode infinite wait
2025-08-14 02:04:09.790 | INFO     | src.showforai.executor.smart_wait_manager:wait_for_element:114 - Element detected after 5 attempts, 2001ms
2025-08-14 02:04:09.791 | INFO     | showforai.storage.element_cache:__init__:57 - ElementCacheManager initialized at: C:\Users\<USER>\.showforai\element_cache
2025-08-14 02:04:09.791 | INFO     | showforai.processing.bbox_cutter:__init__:51 - BboxCutter initialized with cache at: C:\Users\<USER>\.showforai\element_cache
2025-08-14 02:04:09.791 | INFO     | src.showforai.executor.script_loader:__init__:110 - Script loader initialized with element cache support
2025-08-14 02:04:09.791 | INFO     | showforai.ai.image_processor:__init__:28 - ImageProcessor initialized with standard resolution: (768, 768)
2025-08-14 02:04:09.791 | INFO     | src.showforai.executor.element_matcher:__init__:84 - Element matcher initialized with threshold=0.8, multi_scale=True, grayscale=True, standardized_matching=True
2025-08-14 02:04:09.791 | INFO     | src.showforai.executor.action_executor:__init__:102 - Action executor initialized. Screen size: 3840x2160
2025-08-14 02:04:09.791 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
2025-08-14 02:04:09.792 | DEBUG    | src.showforai.robustness.timeout_manager:__init__:79 - TimeoutManager initialized with config: TimeoutConfig(default_timeout=10.0, element_detection_timeout=5, action_execution_timeout=5.0, ai_processing_timeout=30.0, network_timeout=20.0, screenshot_timeout=2.0, active_mode_strategy=<TimeoutStrategy.SKIP: 2>, auxiliary_mode_strategy=<TimeoutStrategy.FAIL: 1>, enable_logging=True, enable_callback=True, use_thread_timeout=True, use_signal_timeout=False)
2025-08-14 02:04:09.792 | DEBUG    | src.showforai.robustness.skip_handler:__init__:102 - SkipHandler initialized with config: SkipConfig(enable_skip=False, skip_strategy=<SkipStrategy.CHECK_NEXT: 4>, element_timeout=5, check_subsequent_count=2, subsequent_check_timeout=5.0, confidence_threshold=0.5, max_consecutive_skips=3, skip_on_errors=['timeout', 'not_found'], enable_logging=True, track_skip_history=True, generate_skip_report=True)
2025-08-14 02:04:09.792 | INFO     | src.showforai.executor.smart_wait_manager:__init__:259 - Smart wait manager initialized
