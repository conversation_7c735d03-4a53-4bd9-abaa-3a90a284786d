"""
Integration test for the force containment rule in the complete detection workflow.

This test simulates the complete detection process and validates that the 
BBOX always contains the click position.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.ai.element_detector import ElementDetector
from showforai.ai.bbox_processor import BboxProcessor
from loguru import logger
import json
from typing import Dict, Any, <PERSON><PERSON>

def simulate_detection_response(click_x: int, click_y: int, offset: int = 50) -> Dict[str, Any]:
    """
    Simulate an AI server response with a bbox that might not contain the click.
    
    Args:
        click_x: Click X coordinate in 768x768
        click_y: Click Y coordinate in 768x768
        offset: Offset to create a bbox that doesn't contain the click
        
    Returns:
        Simulated AI response
    """
    # Create a bbox that intentionally doesn't contain the click
    bbox = {
        'x': click_x + offset,  # Offset to the right
        'y': click_y + offset,  # Offset down
        'width': 100,
        'height': 80
    }
    
    return {
        'success': True,
        'data': {
            'bounding_box': bbox,
            'confidence': 0.95,
            'element_type': 'button',
            'model': 'test-model',
            'detection_round': 1,
            'total_rounds': 1,
            'processing_time_ms': 100
        }
    }

def test_detection_with_containment():
    """Test the complete detection workflow with containment enforcement."""
    
    logger.info("Testing detection workflow with containment enforcement...")
    
    # Initialize detector (we'll patch the response)
    detector = ElementDetector()
    processor = BboxProcessor()
    
    # Test case 1: Detection with adjustment needed
    logger.info("\nTest Case 1: BBOX needs adjustment to contain click")
    
    # Simulate a click at position (400, 300) in original resolution (1920x1080)
    original_click = {'x': 1000, 'y': 600}
    original_resolution = (1920, 1080)
    
    # Convert to 768x768
    click_768 = processor.convert_click_to_standard(
        original_click['x'], 
        original_click['y'],
        original_resolution[0],
        original_resolution[1]
    )
    
    logger.info(f"Original click: {original_click} in {original_resolution}")
    logger.info(f"Converted to 768x768: {click_768}")
    
    # Simulate AI response with bbox that doesn't contain the click
    simulated_response = simulate_detection_response(
        click_768['x'], 
        click_768['y'], 
        offset=50  # Bbox will be offset by 50px
    )
    
    # Process the response with containment enforcement
    processed = detector._process_response(simulated_response, click_768)
    
    if processed:
        # Apply containment enforcement
        processed = detector._enforce_bbox_containment(processed, click_768)
        
        # Verify containment
        bbox = processed['bbox']
        is_contained = processor.contains_point(bbox, click_768['x'], click_768['y'])
        
        logger.info(f"Final BBOX: {bbox}")
        logger.info(f"Click contained: {is_contained}")
        logger.info(f"BBOX was adjusted: {processed.get('containment_adjusted', False)}")
        
        assert is_contained, "BBOX must contain the click position after enforcement"
        logger.success("Test Case 1 PASSED: BBOX was adjusted to contain click")
    
    # Test case 2: Detection with bbox already containing click
    logger.info("\nTest Case 2: BBOX already contains click")
    
    click2 = {'x': 200, 'y': 200}
    
    # Create a bbox that already contains the click
    good_response = {
        'success': True,
        'data': {
            'bounding_box': {
                'x': 150,
                'y': 150,
                'width': 200,
                'height': 200
            },
            'confidence': 0.98,
            'element_type': 'input'
        }
    }
    
    processed2 = detector._process_response(good_response, click2)
    if processed2:
        processed2 = detector._enforce_bbox_containment(processed2, click2)
        
        bbox2 = processed2['bbox']
        is_contained2 = processor.contains_point(bbox2, click2['x'], click2['y'])
        
        logger.info(f"Click position: {click2}")
        logger.info(f"BBOX: {bbox2}")
        logger.info(f"Click contained: {is_contained2}")
        logger.info(f"BBOX was adjusted: {processed2.get('containment_adjusted', False)}")
        
        assert is_contained2, "BBOX must contain the click position"
        assert not processed2.get('containment_adjusted', False), "BBOX should not be adjusted when already contains click"
        logger.success("Test Case 2 PASSED: BBOX already contained click, no adjustment needed")
    
    # Test case 3: Edge case - click near image boundary
    logger.info("\nTest Case 3: Click near image boundary")
    
    click3 = {'x': 765, 'y': 765}  # Near the edge of 768x768
    
    edge_response = simulate_detection_response(click3['x'], click3['y'], offset=10)
    
    processed3 = detector._process_response(edge_response, click3)
    if processed3:
        processed3 = detector._enforce_bbox_containment(processed3, click3)
        
        bbox3 = processed3['bbox']
        is_contained3 = processor.contains_point(bbox3, click3['x'], click3['y'])
        
        # Verify bbox is within bounds
        bbox_in_bounds = (
            bbox3['x'] >= 0 and 
            bbox3['y'] >= 0 and
            bbox3['x'] + bbox3['width'] <= 768 and
            bbox3['y'] + bbox3['height'] <= 768
        )
        
        logger.info(f"Click position (near edge): {click3}")
        logger.info(f"BBOX: {bbox3}")
        logger.info(f"Click contained: {is_contained3}")
        logger.info(f"BBOX within bounds: {bbox_in_bounds}")
        
        assert is_contained3, "BBOX must contain the click position"
        assert bbox_in_bounds, "BBOX must stay within 768x768 bounds"
        logger.success("Test Case 3 PASSED: Edge case handled correctly")
    
    logger.success("\nAll integration tests passed!")

def test_coordinate_chain():
    """Test the complete coordinate transformation chain."""
    
    logger.info("Testing complete coordinate transformation chain...")
    
    processor = BboxProcessor()
    
    # Test various resolutions
    test_cases = [
        ((1920, 1080), (960, 540)),    # 16:9 Full HD
        ((2560, 1440), (1280, 720)),   # 16:9 2K
        ((3840, 2160), (1920, 1080)),  # 16:9 4K
        ((1366, 768), (683, 384)),     # Common laptop
        ((1024, 768), (512, 384)),     # 4:3 old monitor
    ]
    
    for resolution, click_pos in test_cases:
        logger.info(f"\nTesting {resolution[0]}x{resolution[1]} -> 768x768")
        
        # Convert to 768x768
        click_768 = processor.convert_click_to_standard(
            click_pos[0], click_pos[1],
            resolution[0], resolution[1]
        )
        
        # Create a bbox around the click in 768x768
        bbox_768 = {
            'x': max(0, click_768['x'] - 50),
            'y': max(0, click_768['y'] - 50),
            'width': min(100, 768 - max(0, click_768['x'] - 50)),
            'height': min(100, 768 - max(0, click_768['y'] - 50))
        }
        
        # Verify containment in 768x768
        contains_768 = processor.contains_point(bbox_768, click_768['x'], click_768['y'])
        
        # Convert bbox back to original resolution
        bbox_original = processor.bbox_to_original(
            bbox_768,
            resolution[0],
            resolution[1]
        )
        
        # Verify containment in original resolution
        contains_original = processor.contains_point(
            bbox_original,
            click_pos[0],
            click_pos[1]
        )
        
        logger.info(f"  Original click: {click_pos}")
        logger.info(f"  768x768 click: {click_768}")
        logger.info(f"  768x768 BBOX: {bbox_768}")
        logger.info(f"  Contains in 768x768: {contains_768}")
        logger.info(f"  Original BBOX: {bbox_original}")
        logger.info(f"  Contains in original: {contains_original}")
        
        assert contains_768, f"BBOX must contain click in 768x768 space for {resolution}"
        assert contains_original, f"BBOX must contain click in original space for {resolution}"
        
        logger.success(f"  Resolution {resolution[0]}x{resolution[1]} passed")
    
    logger.success("\nAll coordinate chain tests passed!")

def test_logging_and_debugging():
    """Test that all logging and debugging features work correctly."""
    
    logger.info("Testing logging and debugging features...")
    
    detector = ElementDetector()
    processor = BboxProcessor()
    
    # Test with various scenarios to ensure proper logging
    scenarios = [
        {
            'name': 'Normal detection',
            'click': {'x': 400, 'y': 300},
            'bbox': {'x': 350, 'y': 250, 'width': 100, 'height': 100},
            'should_adjust': False
        },
        {
            'name': 'Requires left adjustment',
            'click': {'x': 100, 'y': 300},
            'bbox': {'x': 150, 'y': 250, 'width': 100, 'height': 100},
            'should_adjust': True
        },
        {
            'name': 'Requires top adjustment',
            'click': {'x': 400, 'y': 100},
            'bbox': {'x': 350, 'y': 150, 'width': 100, 'height': 100},
            'should_adjust': True
        },
        {
            'name': 'Requires corner adjustment',
            'click': {'x': 50, 'y': 50},
            'bbox': {'x': 100, 'y': 100, 'width': 100, 'height': 100},
            'should_adjust': True
        }
    ]
    
    for scenario in scenarios:
        logger.info(f"\nScenario: {scenario['name']}")
        
        # Create test response
        test_response = {
            'success': True,
            'data': {
                'bounding_box': scenario['bbox'],
                'confidence': 0.95
            }
        }
        
        # Process with logging
        processed = detector._process_response(test_response, scenario['click'])
        if processed:
            before_bbox = processed['bbox'].copy()
            processed = detector._enforce_bbox_containment(processed, scenario['click'])
            after_bbox = processed['bbox']
            
            was_adjusted = processed.get('containment_adjusted', False)
            
            logger.info(f"  Click: {scenario['click']}")
            logger.info(f"  Original BBOX: {before_bbox}")
            logger.info(f"  Final BBOX: {after_bbox}")
            logger.info(f"  Was adjusted: {was_adjusted}")
            logger.info(f"  Expected adjustment: {scenario['should_adjust']}")
            
            # Verify containment
            is_contained = processor.contains_point(after_bbox, scenario['click']['x'], scenario['click']['y'])
            assert is_contained, f"BBOX must contain click for scenario: {scenario['name']}"
            
            # Verify adjustment flag
            assert was_adjusted == scenario['should_adjust'], \
                f"Adjustment flag mismatch for scenario: {scenario['name']}"
            
            logger.success(f"  Scenario '{scenario['name']}' passed")
    
    logger.success("\nAll logging and debugging tests passed!")

def main():
    """Run all integration tests."""
    
    logger.info("="*70)
    logger.info("INTEGRATION TESTS FOR FORCE CONTAINMENT RULE")
    logger.info("="*70)
    
    try:
        test_detection_with_containment()
        logger.info("\n" + "="*70)
        
        test_coordinate_chain()
        logger.info("\n" + "="*70)
        
        test_logging_and_debugging()
        logger.info("\n" + "="*70)
        
        logger.success("\n" + "="*70)
        logger.success("ALL INTEGRATION TESTS PASSED!")
        logger.success("Force containment rule is fully integrated and working correctly.")
        logger.success("="*70)
        
    except AssertionError as e:
        logger.error(f"Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error in integration test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()