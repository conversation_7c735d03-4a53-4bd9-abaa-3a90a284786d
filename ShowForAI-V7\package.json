{"name": "showforai", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:tauri": "cargo test --manifest-path=src-tauri/Cargo.toml", "test:all": "npm run test && npm run test:tauri && npm run test:e2e", "build:release": "npm run lint && npm run test:all && npm run build", "tauri:build": "tauri build", "tauri:dev": "tauri dev"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@tauri-apps/api": "^1.5.1", "@types/react-beautiful-dnd": "^13.1.8", "antd": "^5.12.8", "classnames": "^2.3.2", "dayjs": "^1.11.10", "framer-motion": "^12.23.12", "immer": "^10.1.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@tauri-apps/cli": "^1.5.1", "@types/lodash": "^4.14.202", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "msw": "^2.0.8", "vitest": "^1.0.4"}}