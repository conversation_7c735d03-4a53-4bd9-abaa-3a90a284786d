"""
简单的网络状态测试
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from showforai.sync.offline_manager import get_offline_manager
from showforai.recorder.gui import RecorderWindow
from showforai.config import Config
from PyQt6.QtWidgets import QApplication

def main():
    # 直接检查网络状态
    print("=" * 50)
    print("直接检查网络状态")
    print("=" * 50)
    
    manager = get_offline_manager()
    
    print(f"is_online(): {manager.is_online()}")
    print(f"is_recording_allowed(): {manager.is_recording_allowed()}")
    print(f"get_offline_message(): {manager.get_offline_message()}")
    
    # 现在检查GUI中的状态
    print("\n" + "=" * 50)
    print("检查GUI中的网络状态")
    print("=" * 50)
    
    app = QApplication([])
    config = Config()
    window = RecorderWindow(config)
    
    # 直接访问窗口的offline_manager
    print(f"窗口的offline_manager.is_online(): {window.offline_manager.is_online()}")
    print(f"窗口的offline_warning是否隐藏: {window.offline_warning.isHidden()}")
    print(f"窗口的record_button是否启用: {window.record_button.isEnabled()}")
    
    # 手动调用check_network_status
    print("\n手动调用window.check_network_status()...")
    window.check_network_status()
    
    print(f"调用后offline_warning是否隐藏: {window.offline_warning.isHidden()}")
    print(f"调用后record_button是否启用: {window.record_button.isEnabled()}")
    
    # 如果仍然显示离线，检查问题
    if not window.offline_warning.isHidden():
        print("\n⚠️ 问题：网络已连接但仍显示离线警告!")
        print("需要检查update_network_ui方法")

if __name__ == "__main__":
    main()