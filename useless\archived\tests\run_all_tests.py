#!/usr/bin/env python3
"""
Test Runner for ShowForAI-V3
Runs all tests with coverage reporting and generates detailed test reports
"""

import unittest
import sys
import os
import time
import json
import argparse
from pathlib import Path
from datetime import datetime
import coverage
from io import StringIO
import traceback

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Test categories
TEST_CATEGORIES = {
    'unit': [
        'test_threshold_manager',
        'test_standardizer', 
        'test_buffer_manager',
        'test_smart_wait',
        'test_execution_engine',
        'test_bug_fixes',
    ],
    'ui': [
        'test_modern_ui',
        'test_feedback_system',
    ],
    'security': [
        'test_api_security',
    ],
    'integration': [
        'test_integration',
    ],
    'all': []  # Will include all tests
}


class TestRunner:
    """Custom test runner with reporting capabilities"""
    
    def __init__(self, verbosity=2, coverage_enabled=True, report_format='console'):
        self.verbosity = verbosity
        self.coverage_enabled = coverage_enabled
        self.report_format = report_format
        self.results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': 0,
            'skipped': 0,
            'execution_time': 0,
            'coverage_percentage': 0,
            'test_details': []
        }
        
    def run_tests(self, test_category='all', pattern=None):
        """
        Run tests for specified category
        
        Args:
            test_category: Category of tests to run
            pattern: Specific test pattern to match
        """
        print(f"\n{'='*70}")
        print(f"ShowForAI-V3 Test Runner")
        print(f"{'='*70}")
        print(f"Category: {test_category}")
        print(f"Pattern: {pattern or 'All tests'}")
        print(f"Coverage: {'Enabled' if self.coverage_enabled else 'Disabled'}")
        print(f"{'='*70}\n")
        
        # Start coverage if enabled
        cov = None
        if self.coverage_enabled:
            cov = coverage.Coverage(source=['showforai'])
            cov.start()
            
        # Start timing
        start_time = time.time()
        
        # Create test suite
        suite = self._create_test_suite(test_category, pattern)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=self.verbosity, stream=sys.stdout)
        result = runner.run(suite)
        
        # Stop timing
        execution_time = time.time() - start_time
        
        # Stop coverage and generate report
        if cov:
            cov.stop()
            
            # Save coverage data
            cov.save()
            
            # Generate coverage report
            coverage_stream = StringIO()
            cov.report(file=coverage_stream)
            coverage_output = coverage_stream.getvalue()
            
            # Try to get coverage percentage
            try:
                for line in coverage_output.split('\n'):
                    if 'TOTAL' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            self.results['coverage_percentage'] = float(parts[-1].rstrip('%'))
                            break
            except:
                self.results['coverage_percentage'] = 0
                
        # Update results
        self.results['total_tests'] = result.testsRun
        self.results['passed'] = result.testsRun - len(result.failures) - len(result.errors)
        self.results['failed'] = len(result.failures)
        self.results['errors'] = len(result.errors)
        self.results['skipped'] = len(result.skipped)
        self.results['execution_time'] = execution_time
        
        # Collect detailed test results
        self._collect_test_details(result)
        
        # Generate report
        self._generate_report()
        
        return result.wasSuccessful()
        
    def _create_test_suite(self, category, pattern):
        """Create test suite based on category and pattern"""
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        if category == 'all':
            # Load all tests
            test_dir = Path(__file__).parent
            suite = loader.discover(test_dir, pattern=pattern or 'test_*.py')
        else:
            # Load specific category
            test_modules = TEST_CATEGORIES.get(category, [])
            for module_name in test_modules:
                try:
                    module = __import__(module_name)
                    suite.addTests(loader.loadTestsFromModule(module))
                except ImportError as e:
                    print(f"Warning: Could not import {module_name}: {e}")
                    
        return suite
        
    def _collect_test_details(self, result):
        """Collect detailed information about each test"""
        # Failures
        for test, traceback in result.failures:
            self.results['test_details'].append({
                'test': str(test),
                'status': 'FAILED',
                'message': traceback
            })
            
        # Errors
        for test, traceback in result.errors:
            self.results['test_details'].append({
                'test': str(test),
                'status': 'ERROR',
                'message': traceback
            })
            
        # Skipped
        for test, reason in result.skipped:
            self.results['test_details'].append({
                'test': str(test),
                'status': 'SKIPPED',
                'message': reason
            })
            
    def _generate_report(self):
        """Generate test report"""
        if self.report_format == 'console':
            self._print_console_report()
        elif self.report_format == 'json':
            self._save_json_report()
        elif self.report_format == 'html':
            self._save_html_report()
        else:
            self._print_console_report()
            
    def _print_console_report(self):
        """Print report to console"""
        print(f"\n{'='*70}")
        print("TEST RESULTS SUMMARY")
        print(f"{'='*70}")
        print(f"Total Tests:    {self.results['total_tests']}")
        print(f"Passed:         {self.results['passed']} ({self._get_percentage('passed')}%)")
        print(f"Failed:         {self.results['failed']} ({self._get_percentage('failed')}%)")
        print(f"Errors:         {self.results['errors']} ({self._get_percentage('errors')}%)")
        print(f"Skipped:        {self.results['skipped']} ({self._get_percentage('skipped')}%)")
        print(f"{'='*70}")
        print(f"Execution Time: {self.results['execution_time']:.2f} seconds")
        
        if self.coverage_enabled:
            print(f"Code Coverage:  {self.results['coverage_percentage']:.1f}%")
            
        print(f"{'='*70}")
        
        # Print failures and errors
        if self.results['failed'] > 0 or self.results['errors'] > 0:
            print("\nFAILED/ERROR TESTS:")
            print("-" * 70)
            for detail in self.results['test_details']:
                if detail['status'] in ['FAILED', 'ERROR']:
                    print(f"\n[{detail['status']}] {detail['test']}")
                    print(f"Message: {detail['message'][:200]}...")
                    
        # Overall result
        print(f"\n{'='*70}")
        if self.results['failed'] == 0 and self.results['errors'] == 0:
            print("✅ ALL TESTS PASSED!")
        else:
            print("❌ SOME TESTS FAILED")
        print(f"{'='*70}\n")
        
    def _save_json_report(self):
        """Save report as JSON"""
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'results': self.results
        }
        
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
            
        print(f"\nTest report saved to: {report_file}")
        
    def _save_html_report(self):
        """Save report as HTML"""
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>ShowForAI-V3 Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #333; }}
                .summary {{ background: #f0f0f0; padding: 15px; border-radius: 5px; }}
                .passed {{ color: green; }}
                .failed {{ color: red; }}
                .error {{ color: orange; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #4CAF50; color: white; }}
            </style>
        </head>
        <body>
            <h1>ShowForAI-V3 Test Report</h1>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary">
                <h2>Summary</h2>
                <p>Total Tests: {self.results['total_tests']}</p>
                <p class="passed">Passed: {self.results['passed']} ({self._get_percentage('passed')}%)</p>
                <p class="failed">Failed: {self.results['failed']} ({self._get_percentage('failed')}%)</p>
                <p class="error">Errors: {self.results['errors']} ({self._get_percentage('errors')}%)</p>
                <p>Execution Time: {self.results['execution_time']:.2f} seconds</p>
                <p>Code Coverage: {self.results['coverage_percentage']:.1f}%</p>
            </div>
            
            <h2>Test Details</h2>
            <table>
                <tr>
                    <th>Test</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
        """
        
        for detail in self.results['test_details']:
            status_class = detail['status'].lower()
            html_content += f"""
                <tr>
                    <td>{detail['test']}</td>
                    <td class="{status_class}">{detail['status']}</td>
                    <td>{detail.get('message', '')[:200]}</td>
                </tr>
            """
            
        html_content += """
            </table>
        </body>
        </html>
        """
        
        with open(report_file, 'w') as f:
            f.write(html_content)
            
        print(f"\nHTML report saved to: {report_file}")
        
    def _get_percentage(self, status):
        """Calculate percentage for a status"""
        if self.results['total_tests'] == 0:
            return 0
        return round(self.results[status] * 100 / self.results['total_tests'], 1)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Run ShowForAI-V3 tests')
    parser.add_argument(
        '--category',
        choices=['all', 'unit', 'ui', 'security', 'integration'],
        default='all',
        help='Test category to run'
    )
    parser.add_argument(
        '--pattern',
        help='Specific test pattern to match (e.g., "test_threshold*")'
    )
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='Disable coverage reporting'
    )
    parser.add_argument(
        '--verbosity',
        type=int,
        choices=[0, 1, 2],
        default=2,
        help='Test output verbosity'
    )
    parser.add_argument(
        '--report',
        choices=['console', 'json', 'html'],
        default='console',
        help='Report format'
    )
    parser.add_argument(
        '--failfast',
        action='store_true',
        help='Stop on first failure'
    )
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner(
        verbosity=args.verbosity,
        coverage_enabled=not args.no_coverage,
        report_format=args.report
    )
    
    # Run tests
    success = runner.run_tests(
        test_category=args.category,
        pattern=args.pattern
    )
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()