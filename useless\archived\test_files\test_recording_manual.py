#!/usr/bin/env python3
"""
手动测试录制功能 - 验证网络检查和录制功能
Manual test for recording functionality with network check verification
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication
from loguru import logger

# 配置日志
logger.add("test_recording.log", rotation="10 MB", level="DEBUG")

print("="*60)
print("ShowForAI V3 录制功能测试")
print("="*60)

# 测试网络连接
print("\n检查网络连接...")
from showforai.utils.network_checker import is_online, get_network_checker
from showforai.sync.offline_manager import get_offline_manager

network_checker = get_network_checker()
offline_manager = get_offline_manager()

# 显示网络状态
print(f"✓ NetworkChecker.is_online(): {is_online()}")
print(f"✓ OfflineModeManager.is_online(): {offline_manager.is_online()}")
print(f"✓ OfflineModeManager.is_recording_allowed(): {offline_manager.is_recording_allowed()}")
print(f"✓ 网络状态消息: {network_checker.get_status_message()}")

if not offline_manager.is_recording_allowed():
    print("\n❌ 录制功能不可用 - 请检查网络连接")
    sys.exit(1)

print("\n✅ 网络检查通过，录制功能可用！")
print("\n选择要测试的GUI：")
print("1. recorder/gui.py (推荐 - 主录制界面)")
print("2. main_window.py (主窗口)")
print("3. enhanced_main_window.py (增强主窗口)")
print("4. 仅测试网络，不启动GUI")

choice = input("\n请选择 (1-4) [默认1]: ").strip() or '1'

if choice == '1':
    print("\n启动 recorder/gui.py...")
    print("请点击录制按钮测试是否能正常开始录制")
    from showforai.recorder.gui import main
    main()
elif choice == '2':
    print("\n启动 main_window.py...")
    print("请点击 Recording > Start Recording 测试")
    from showforai.gui.main_window import MainWindow
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
elif choice == '3':
    print("\n启动 enhanced_main_window.py...")
    print("请点击 录制 > 开始录制 测试")
    from showforai.gui.enhanced_main_window import EnhancedMainWindow
    app = QApplication(sys.argv)
    window = EnhancedMainWindow()
    window.show()
    sys.exit(app.exec())
else:
    print("\n网络测试完成，未启动GUI")
    print("所有网络检查已通过，录制功能应该可以正常使用")