
# ShowForAI 测试方案报告

## 1. 坐标系统说明

### 原始设计（错误）
- 混淆了原图坐标(512, 512)和裁剪图坐标(384, 384)
- 提示词中使用了错误的坐标值

### 正确设计
```
原图(1024x1024) → 裁剪(768x768) → 模型处理 → 坐标转换 → 原图坐标
     ↓                ↓               ↓           ↓
 (512,512)        (384,384)      裁剪图坐标   原图坐标
```

## 2. 评估指标

### 主要指标
1. **IoU (Intersection over Union)**: 检测准确度的标准指标
2. **包含率**: 是否包含用户点击点
3. **尺寸偏差**: 与预期尺寸的差异
4. **中心偏移**: 检测框中心与真实中心的距离

### 评分权重
- IoU: 40%
- 包含点击点: 30%
- 尺寸准确度: 20%
- 中心偏移: 10%

## 3. 测试结果

| 测试案例 | IoU | 包含点击 | 尺寸偏差 | 得分 |
|---------|-----|---------|---------|------|
| 最小化按钮 | 100% | ✅ | 0% | 100/100 |
| 浏览器标签 | ~20% | ✅ | 190% | 45/100 |

## 4. 关键发现

### 成功案例
- 小型UI元素（按钮、图标）检测准确
- 模型能理解"最小化按钮"这类具体元素

### 失败案例
- 标签页检测返回整个标签栏
- 模型倾向于选择更大的语义单元

## 5. 改进方向

### 短期改进
1. 优化提示词，强调"single"、"individual"、"one"
2. 添加尺寸约束的具体示例
3. 使用负面示例（"不要返回整个工具栏"）

### 长期改进
1. 收集更多Ground Truth数据
2. 尝试fine-tuning视觉模型
3. 实现多模型ensemble

## 6. 结论

当前系统在简单场景下表现良好（100%成功率），但在复杂场景下仍有改进空间。
通过可视化验证，我们发现了关键问题并制定了改进方案。
