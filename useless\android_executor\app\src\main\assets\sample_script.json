{"version": "3.1", "metadata": {"session_id": "sample_android_test_001", "description": "Android测试脚本示例", "created_at": "2024-01-15T10:30:00Z", "device_info": "Android测试设备"}, "steps": [{"type": "action", "command": "CLICK", "timeout_seconds": 10, "target": {"description": "点击屏幕中心", "bounding_box": [0.4, 0.4, 0.6, 0.6]}}, {"type": "action", "command": "WAIT", "timeout_seconds": 5, "parameters": {"duration": 2000}}, {"type": "action", "command": "SWIPE", "timeout_seconds": 10, "target": {"description": "从屏幕中心向上滑动", "bounding_box": [0.4, 0.5, 0.6, 0.5]}, "parameters": {"direction": "UP", "distance": 300, "duration": 500}}, {"type": "action", "command": "INPUT_TEXT", "timeout_seconds": 15, "target": {"description": "文本输入框", "text_content": "输入框", "bounding_box": [0.1, 0.3, 0.9, 0.4]}, "parameters": {"text": "Hello Android DSL Executor!"}}, {"type": "action", "command": "SCROLL", "timeout_seconds": 10, "target": {"description": "滚动区域", "bounding_box": [0.0, 0.2, 1.0, 0.8]}, "parameters": {"scroll_direction": "DOWN", "scroll_amount": 3}}]}