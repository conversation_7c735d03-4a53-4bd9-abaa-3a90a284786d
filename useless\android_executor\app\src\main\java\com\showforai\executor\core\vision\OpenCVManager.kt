package com.showforai.executor.core.vision

import android.content.Context
import android.graphics.Bitmap
import org.opencv.android.BaseLoaderCallback
import org.opencv.android.LoaderCallbackInterface
import org.opencv.android.OpenCVLoader
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * OpenCV管理器
 * 
 * 负责OpenCV的初始化和图像处理功能：
 * - OpenCV库的异步初始化
 * - 模板匹配算法
 * - 图像预处理
 * - 特征点检测
 */
@Singleton
class OpenCVManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val MATCH_THRESHOLD_HIGH = 0.8
        private const val MATCH_THRESHOLD_MEDIUM = 0.7
        private const val MATCH_THRESHOLD_LOW = 0.6
    }
    
    @Volatile
    private var isInitialized = false
    private var initializationCallbacks = mutableListOf<(Boolean) -> Unit>()
    
    private val loaderCallback = object : BaseLoaderCallback(context) {
        override fun onManagerConnected(status: Int) {
            when (status) {
                LoaderCallbackInterface.SUCCESS -> {
                    Timber.i("OpenCV loaded successfully")
                    isInitialized = true
                    notifyInitializationCallbacks(true)
                }
                else -> {
                    Timber.e("OpenCV initialization failed with status: $status")
                    notifyInitializationCallbacks(false)
                    super.onManagerConnected(status)
                }
            }
        }
    }
    
    /**
     * 初始化OpenCV
     */
    suspend fun initialize(): Boolean {
        if (isInitialized) {
            return true
        }

        return suspendCoroutine { continuation ->
            addInitializationCallback { success ->
                continuation.resume(success)
            }

            try {
                // 尝试加载OpenCV本地库
                System.loadLibrary("opencv_java4")

                if (!OpenCVLoader.initDebug()) {
                    Timber.d("Internal OpenCV library not found. Using OpenCV Manager for initialization")

                    // 尝试使用OpenCV Manager初始化
                    try {
                        OpenCVLoader.initAsync(OpenCVLoader.OPENCV_VERSION, context, loaderCallback)
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to initialize with OpenCV Manager")

                        // 尝试使用备用方法初始化
                        val success = initializeWithFallback()
                        isInitialized = success
                        notifyInitializationCallbacks(success)
                    }
                } else {
                    Timber.i("OpenCV library found inside package. Using it!")
                    isInitialized = true
                    notifyInitializationCallbacks(true)
                }
            } catch (e: UnsatisfiedLinkError) {
                Timber.e(e, "Failed to load OpenCV native library")

                // 尝试使用备用方法初始化
                val success = initializeWithFallback()
                isInitialized = success
                notifyInitializationCallbacks(success)
            } catch (e: Exception) {
                Timber.e(e, "OpenCV initialization failed with exception")
                notifyInitializationCallbacks(false)
            }
        }
    }

    /**
     * 备用初始化方法
     */
    private fun initializeWithFallback(): Boolean {
        return try {
            Timber.d("Trying fallback initialization method")

            // 检查是否可以创建Mat对象
            val testMat = Mat(10, 10, CvType.CV_8UC1)
            testMat.release()

            Timber.i("OpenCV fallback initialization successful")
            true
        } catch (e: Exception) {
            Timber.e(e, "OpenCV fallback initialization failed")
            false
        }
    }
    
    /**
     * 添加初始化回调
     */
    private fun addInitializationCallback(callback: (Boolean) -> Unit) {
        if (isInitialized) {
            callback(true)
        } else {
            initializationCallbacks.add(callback)
        }
    }
    
    /**
     * 通知初始化回调
     */
    private fun notifyInitializationCallbacks(success: Boolean) {
        initializationCallbacks.forEach { it(success) }
        initializationCallbacks.clear()
    }
    
    /**
     * 检查OpenCV是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * 模板匹配
     * @param sourceImage 源图像
     * @param templateImage 模板图像
     * @param threshold 匹配阈值
     * @return 匹配结果
     */
    suspend fun templateMatch(
        sourceImage: Bitmap,
        templateImage: Bitmap,
        threshold: Double = MATCH_THRESHOLD_HIGH
    ): TemplateMatchResult {
        if (!isInitialized) {
            throw IllegalStateException("OpenCV not initialized")
        }

        return try {
            // 转换Bitmap到Mat
            val sourceMat = Mat()
            val templateMat = Mat()
            Utils.bitmapToMat(sourceImage, sourceMat)
            Utils.bitmapToMat(templateImage, templateMat)

            // 转换为灰度图像
            val sourceGray = Mat()
            val templateGray = Mat()
            Imgproc.cvtColor(sourceMat, sourceGray, Imgproc.COLOR_BGR2GRAY)
            Imgproc.cvtColor(templateMat, templateGray, Imgproc.COLOR_BGR2GRAY)

            // 性能优化：降低分辨率
            val downsampleFactor = calculateDownsampleFactor(sourceGray.width(), sourceGray.height())

            val downsampledSource = Mat()
            val downsampledTemplate = Mat()

            if (downsampleFactor > 1) {
                // 降低分辨率
                Imgproc.resize(
                    sourceGray,
                    downsampledSource,
                    Size(sourceGray.width() / downsampleFactor, sourceGray.height() / downsampleFactor)
                )
                Imgproc.resize(
                    templateGray,
                    downsampledTemplate,
                    Size(templateGray.width() / downsampleFactor, templateGray.height() / downsampleFactor)
                )
            } else {
                // 不需要降低分辨率
                sourceGray.copyTo(downsampledSource)
                templateGray.copyTo(downsampledTemplate)
            }

            // 执行模板匹配
            val result = Mat()
            Imgproc.matchTemplate(downsampledSource, downsampledTemplate, result, Imgproc.TM_CCOEFF_NORMED)

            // 查找最佳匹配位置
            val minMaxLocResult = Core.minMaxLoc(result)
            val maxVal = minMaxLocResult.maxVal
            val maxLoc = minMaxLocResult.maxLoc

            // 计算原始分辨率下的位置
            val originalX = maxLoc.x * downsampleFactor
            val originalY = maxLoc.y * downsampleFactor

            // 计算匹配区域
            val matchRect = Rect(
                originalX.toInt(),
                originalY.toInt(),
                templateMat.cols(),
                templateMat.rows()
            )

            // 释放资源
            sourceMat.release()
            templateMat.release()
            sourceGray.release()
            templateGray.release()
            downsampledSource.release()
            downsampledTemplate.release()
            result.release()

            TemplateMatchResult(
                found = maxVal >= threshold,
                confidence = maxVal,
                location = android.graphics.Point(originalX.toInt(), originalY.toInt()),
                boundingBox = android.graphics.Rect(
                    matchRect.x,
                    matchRect.y,
                    matchRect.x + matchRect.width,
                    matchRect.y + matchRect.height
                )
            )

        } catch (e: Exception) {
            Timber.e(e, "Template matching failed")
            TemplateMatchResult(
                found = false,
                confidence = 0.0,
                location = android.graphics.Point(0, 0),
                boundingBox = android.graphics.Rect()
            )
        }
    }

    /**
     * 计算降采样因子
     * 根据图像大小动态计算降采样因子，以优化性能
     */
    private fun calculateDownsampleFactor(width: Int, height: Int): Double {
        // 目标分辨率阈值
        val targetPixels = 640 * 480
        val actualPixels = width * height

        if (actualPixels <= targetPixels) {
            return 1.0 // 不需要降采样
        }

        // 计算降采样因子，保持宽高比
        val factor = Math.sqrt(actualPixels.toDouble() / targetPixels)

        // 限制最大降采样因子
        return Math.min(factor, 4.0)
    }
    
    /**
     * 多尺度模板匹配
     * @param sourceImage 源图像
     * @param templateImage 模板图像
     * @param scaleFactors 缩放因子列表
     * @param threshold 匹配阈值
     * @return 最佳匹配结果
     */
    suspend fun multiScaleTemplateMatch(
        sourceImage: Bitmap,
        templateImage: Bitmap,
        scaleFactors: List<Double> = listOf(0.8, 0.9, 1.0, 1.1, 1.2),
        threshold: Double = MATCH_THRESHOLD_MEDIUM
    ): TemplateMatchResult {
        if (!isInitialized) {
            throw IllegalStateException("OpenCV not initialized")
        }
        
        var bestResult = TemplateMatchResult(
            found = false,
            confidence = 0.0,
            location = android.graphics.Point(0, 0),
            boundingBox = android.graphics.Rect()
        )
        
        for (scale in scaleFactors) {
            try {
                // 缩放模板图像
                val scaledTemplate = scaleImage(templateImage, scale)
                
                // 执行模板匹配
                val result = templateMatch(sourceImage, scaledTemplate, threshold)
                
                // 更新最佳结果
                if (result.found && result.confidence > bestResult.confidence) {
                    bestResult = result.copy(
                        // 调整坐标以补偿缩放
                        location = android.graphics.Point(
                            (result.location.x / scale).toInt(),
                            (result.location.y / scale).toInt()
                        )
                    )
                }
                
                scaledTemplate.recycle()
                
            } catch (e: Exception) {
                Timber.w(e, "Multi-scale matching failed for scale: $scale")
            }
        }
        
        return bestResult
    }
    
    /**
     * 缩放图像
     */
    private fun scaleImage(bitmap: Bitmap, scale: Double): Bitmap {
        val newWidth = (bitmap.width * scale).toInt()
        val newHeight = (bitmap.height * scale).toInt()
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * 图像预处理
     * @param bitmap 输入图像
     * @return 预处理后的图像
     */
    fun preprocessImage(bitmap: Bitmap): Bitmap {
        if (!isInitialized) {
            return bitmap
        }
        
        return try {
            val mat = Mat()
            Utils.bitmapToMat(bitmap, mat)
            
            // 转换为灰度
            val gray = Mat()
            Imgproc.cvtColor(mat, gray, Imgproc.COLOR_BGR2GRAY)
            
            // 高斯模糊去噪
            val blurred = Mat()
            Imgproc.GaussianBlur(gray, blurred, Size(3.0, 3.0), 0.0)
            
            // 直方图均衡化增强对比度
            val equalized = Mat()
            Imgproc.equalizeHist(blurred, equalized)
            
            // 转换回Bitmap
            val resultBitmap = Bitmap.createBitmap(
                equalized.cols(),
                equalized.rows(),
                Bitmap.Config.ARGB_8888
            )
            Utils.matToBitmap(equalized, resultBitmap)
            
            // 释放资源
            mat.release()
            gray.release()
            blurred.release()
            equalized.release()
            
            resultBitmap
            
        } catch (e: Exception) {
            Timber.e(e, "Image preprocessing failed")
            bitmap
        }
    }
    
    /**
     * 获取图像相似度
     * @param image1 图像1
     * @param image2 图像2
     * @return 相似度 (0.0 - 1.0)
     */
    fun getImageSimilarity(image1: Bitmap, image2: Bitmap): Double {
        if (!isInitialized) {
            return 0.0
        }
        
        return try {
            val mat1 = Mat()
            val mat2 = Mat()
            Utils.bitmapToMat(image1, mat1)
            Utils.bitmapToMat(image2, mat2)
            
            // 调整图像大小使其一致
            val size = Size(100.0, 100.0) // 使用固定大小进行比较
            val resized1 = Mat()
            val resized2 = Mat()
            Imgproc.resize(mat1, resized1, size)
            Imgproc.resize(mat2, resized2, size)
            
            // 转换为灰度
            val gray1 = Mat()
            val gray2 = Mat()
            Imgproc.cvtColor(resized1, gray1, Imgproc.COLOR_BGR2GRAY)
            Imgproc.cvtColor(resized2, gray2, Imgproc.COLOR_BGR2GRAY)
            
            // 计算直方图
            val hist1 = Mat()
            val hist2 = Mat()
            val histSize = MatOfInt(256)
            val ranges = MatOfFloat(0f, 256f)
            
            Imgproc.calcHist(listOf(gray1), MatOfInt(0), Mat(), hist1, histSize, ranges)
            Imgproc.calcHist(listOf(gray2), MatOfInt(0), Mat(), hist2, histSize, ranges)
            
            // 比较直方图
            val similarity = Imgproc.compareHist(hist1, hist2, Imgproc.HISTCMP_CORREL)
            
            // 释放资源
            mat1.release()
            mat2.release()
            resized1.release()
            resized2.release()
            gray1.release()
            gray2.release()
            hist1.release()
            hist2.release()
            
            similarity
            
        } catch (e: Exception) {
            Timber.e(e, "Image similarity calculation failed")
            0.0
        }
    }
}

/**
 * 模板匹配结果
 */
data class TemplateMatchResult(
    val found: Boolean,
    val confidence: Double,
    val location: android.graphics.Point,
    val boundingBox: android.graphics.Rect
)
