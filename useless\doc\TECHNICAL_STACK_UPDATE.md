# ShowForAI 技术栈更新文档

**版本**: 1.1  
**日期**: 2025-01-27  
**背景**: 基于AI视觉识别的架构调整  
**更新**: 添加OpenCV 4.x版本说明和辅助模式优化策略

---

## 1. 核心变化概述

### 1.1 识别方式的根本转变
- **之前**: 基于OpenCV的传统图像匹配（模板匹配、特征匹配）
- **现在**: 基于AI大模型的视觉识别 + OpenCV辅助验证

### 1.2 架构影响
```
录制时：用户操作 → 截图 → AI识别元素 → 保存元素图像
执行时：截屏 → 快速筛选 → 精确匹配 → 执行操作
```

---

## 2. 技术栈调整

### 2.1 录制端技术栈

#### 保持不变的部分
- **Python 3.9+**: 主开发语言
- **PySide6**: UI框架
- **MSS**: 高性能截图库
- **PyAutoGUI**: 鼠标键盘控制
- **pynput**: 事件监听

#### 新增的部分
- **火山引擎SDK**: AI视觉识别接口
- **aiohttp**: 异步HTTP调用（AI API）
- **Pillow**: 图像预处理（添加红十字标记）

#### 移除或降级的部分
- **OpenCV**: 从核心依赖降级为可选依赖
  - 录制时不再需要
  - 仅在执行端使用

### 2.2 执行端技术栈

#### OpenCV的新定位
从"主要识别手段"变为"性能优化工具"：

1. **快速筛选层**（可选）
   ```python
   # 使用简单的哈希对比快速排除
   def quick_filter(screenshot, target_hash):
       # 不需要OpenCV，使用imagehash库即可
       return image_hash_similarity > 0.9
   ```

2. **精确匹配层**（必需）
   ```python
   # OpenCV模板匹配作为核心匹配引擎
   def precise_match(screenshot, element_image):
       # 使用matchTemplate进行精确定位
       result = cv2.matchTemplate(screenshot, element_image, cv2.TM_CCOEFF_NORMED)
       return cv2.minMaxLoc(result)
   ```

#### OpenCV版本选择
```yaml
推荐版本:
  - OpenCV 4.x (最新稳定版)
  - 通过 pip install opencv-python 安装
  - 自带 TBB 优化（但辅助模式不依赖）

版本特性:
  - 4.x 系列：算法优化更好，基础函数更快
  - TBB 并行：对批量任务有效，单次小任务无明显提升
  - 重点是算法优化而非并行化
```

#### 执行端依赖调整
```yaml
必需依赖:
  - pyautogui      # 操作执行
  - mss            # 截屏
  - numpy          # 数组处理
  - pillow         # 基础图像处理
  - opencv-python  # 图像匹配（执行端必需）

性能优化依赖:
  - imagehash      # 快速筛选
  - lru-cache      # 匹配结果缓存
  
辅助模式专用:
  - psutil         # 进程监控
  - watchdog       # 文件监控（可选）
```

### 2.3 服务端技术栈

#### 新增组件
```yaml
AI服务:
  - 火山引擎视觉API  # 主要识别引擎
  - FastAPI         # API框架
  - Redis           # 识别结果缓存
  
图像处理:
  - Pillow          # 图像裁切
  - NumPy           # 数组操作
```

---

## 3. OpenCV使用策略

### 3.1 不再使用OpenCV的场景
1. **录制时的元素识别** - 完全由AI处理
2. **复杂的特征提取** - AI更准确
3. **动态UI适配** - AI更鲁棒

### 3.2 继续使用OpenCV的场景

#### 场景1：游戏脚本的像素级匹配
```python
# 游戏图标、道具等需要100%精确匹配
def match_game_icon(screen, icon_template):
    result = cv2.matchTemplate(screen, icon_template, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if max_val > 0.95:  # 高阈值
        return max_loc
```

#### 场景2：高频执行的性能优化
```python
# 辅助模式下的快速检测
def fast_element_detection(screen_region, element_templates):
    # 使用OpenCV的GPU加速（如果可用）
    for template in element_templates:
        # 多尺度模板匹配
        result = cv2.matchTemplate(screen_region, template, cv2.TM_CCOEFF_NORMED)
        if result.max() > threshold:
            return True
```

#### 场景3：离线场景
```python
# 无网络时的降级方案
def offline_matching(screen, saved_element):
    # 使用保存的元素图像进行传统匹配
    # 虽然鲁棒性较差，但可以基本工作
    pass
```

---

## 4. 辅助模式专项优化

### 4.1 性能挑战分析
```yaml
辅助模式特点:
  - 单次小任务，频繁执行
  - 延迟敏感（<100ms）
  - CPU占用要求严格（<3%）

并行化局限:
  - TBB线程池开销: 5-10ms
  - 任务分配开销: 1-2ms  
  - 对于20ms的任务，并行化可能更慢
```

### 4.2 算法优化策略

#### 策略1：多尺度金字塔匹配
```yaml
原理: 先低分辨率粗定位，再高分辨率精确匹配
优势: 减少90%计算量
耗时: 5-10ms（vs 原始20ms）
```

#### 策略2：智能缓存机制
```yaml
缓存设计:
  - 屏幕区域哈希 → 匹配结果映射
  - LRU淘汰策略，缓存大小可配置
  - 静态界面命中率 >95%
  
性能收益:
  - 缓存命中: <1ms
  - 缓存未中: 15-20ms
```

#### 策略3：感知哈希筛选
```yaml
快速排除:
  - 计算图像感知哈希
  - 对光标、时间等小变化不敏感
  - 1-2ms完成初筛
```

#### 策略4：模板预处理
```yaml
启动时预计算:
  - 灰度图转换
  - 边缘检测结果
  - 特征点提取
  - 直方图数据
  
运行时直接使用，节省5-8ms
```

### 4.3 实现架构
```python
class OptimizedMatcher:
    def __init__(self):
        self.cache = LRUCache(maxsize=1000)
        self.preprocessed_templates = {}
        
    def match(self, screen, template_id):
        # 1. 缓存检查（<1ms）
        cache_key = self._compute_cache_key(screen, template_id)
        if cache_key in self.cache:
            return self.cache[cache_key]
            
        # 2. 感知哈希快速筛选（1-2ms）
        if not self._perceptual_hash_check(screen, template_id):
            return None
            
        # 3. 多尺度匹配（5-10ms）
        result = self._pyramid_match(screen, self.preprocessed_templates[template_id])
        
        # 4. 缓存结果
        self.cache[cache_key] = result
        return result
```

### 4.4 性能指标对比
```yaml
传统OpenCV匹配:
  - 单次匹配: 20-30ms
  - CPU占用: 5-8%
  
优化后（算法层面）:
  - 缓存命中: <1ms（95%+场景）
  - 首次匹配: 10-15ms
  - CPU占用: 1-3%
  
关键改进:
  - 不依赖TBB并行化
  - 专注算法和缓存优化
  - 适合辅助模式的高频小任务
```

---

## 5. 依赖管理策略

### 5.1 分级安装
```bash
# 基础安装（无OpenCV）
pip install showforai

# 完整安装（含OpenCV优化）
pip install showforai[full]

# 开发安装（含所有可选依赖）
pip install showforai[dev]
```

### 5.2 运行时检测
```python
# 自动检测并适配
def init_matcher():
    try:
        import cv2
        return OptimizedMatcher()  # 使用OpenCV优化
    except ImportError:
        return BasicMatcher()      # 基础实现
```

---

## 6. 迁移建议

### 6.1 现有代码调整
1. **录制模块**: 移除所有OpenCV相关代码
2. **执行模块**: 保留OpenCV但作为可选优化
3. **图像处理**: 优先使用Pillow，复杂操作才用OpenCV

### 6.2 性能测试计划
```yaml
测试场景:
  1. 标准Windows应用
     - 纯AI方案
     - AI + OpenCV优化
     
  2. 游戏应用
     - 静态UI元素
     - 动态游戏对象
     
  3. 辅助模式
     - CPU占用对比
     - 响应延迟对比
```

---

## 7. 总结

### 7.1 核心原则
1. **AI优先**: 识别依赖AI，不依赖OpenCV
2. **性能优化**: OpenCV作为可选的性能优化工具
3. **渐进增强**: 基础功能不依赖OpenCV，高级功能可选用

### 7.2 技术栈精简效果
- **依赖包大小**: 减少约100MB（OpenCV可选）
- **安装复杂度**: 大幅降低（OpenCV编译问题）
- **跨平台兼容**: 提升（减少C++依赖）

### 7.3 未来展望
随着AI模型的本地化部署（如ONNX Runtime），可能完全不需要OpenCV，实现：
- 更小的包体积
- 更好的跨平台性
- 更高的识别准确率

---

**文档状态**: 技术栈调整方案已确定，待实施验证