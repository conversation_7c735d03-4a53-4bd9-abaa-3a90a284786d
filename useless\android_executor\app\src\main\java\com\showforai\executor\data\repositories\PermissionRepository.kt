package com.showforai.executor.data.repositories

import android.app.Activity
import com.showforai.executor.data.models.PermissionStatus
import com.showforai.executor.utils.PermissionManager
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限仓库接口
 */
interface PermissionRepository {
    suspend fun checkAllPermissions(): PermissionStatus
    suspend fun requestStoragePermission(activity: Activity): Boolean
    suspend fun requestOverlayPermission(activity: Activity): Boolean
    suspend fun requestMediaProjectionPermission(activity: Activity): Boolean
    suspend fun requestAccessibilityPermission(activity: Activity): Boolean
    fun getMissingPermissions(): List<String>
    fun areAllPermissionsGranted(): Boolean
}

/**
 * 权限仓库实现
 * 
 * 负责权限管理：
 * - 检查权限状态
 * - 请求权限
 * - 权限状态监控
 */
@Singleton
class PermissionRepositoryImpl @Inject constructor(
    private val permissionManager: PermissionManager
) : PermissionRepository {
    
    /**
     * 检查所有权限状态
     */
    override suspend fun checkAllPermissions(): PermissionStatus {
        return try {
            val status = permissionManager.checkAllPermissions()
            Timber.d("Permission status checked: $status")
            status
        } catch (e: Exception) {
            Timber.e(e, "Error checking permissions")
            PermissionStatus() // 返回默认状态（所有权限未授予）
        }
    }
    
    /**
     * 请求存储权限
     */
    override suspend fun requestStoragePermission(activity: Activity): Boolean {
        return try {
            Timber.d("Requesting storage permission")
            permissionManager.requestStoragePermission(activity)
            
            // 等待用户操作后重新检查权限
            kotlinx.coroutines.delay(1000)
            val granted = permissionManager.isStoragePermissionGranted()
            
            if (granted) {
                Timber.i("Storage permission granted")
            } else {
                Timber.w("Storage permission denied")
            }
            
            granted
        } catch (e: Exception) {
            Timber.e(e, "Error requesting storage permission")
            false
        }
    }
    
    /**
     * 请求悬浮窗权限
     */
    override suspend fun requestOverlayPermission(activity: Activity): Boolean {
        return try {
            Timber.d("Requesting overlay permission")
            permissionManager.requestOverlayPermission(activity)
            
            // 等待用户操作后重新检查权限
            kotlinx.coroutines.delay(1000)
            val granted = permissionManager.isOverlayPermissionGranted()
            
            if (granted) {
                Timber.i("Overlay permission granted")
            } else {
                Timber.w("Overlay permission denied")
            }
            
            granted
        } catch (e: Exception) {
            Timber.e(e, "Error requesting overlay permission")
            false
        }
    }
    
    /**
     * 请求屏幕捕捉权限
     */
    override suspend fun requestMediaProjectionPermission(activity: Activity): Boolean {
        return try {
            Timber.d("Requesting media projection permission")
            permissionManager.requestMediaProjectionPermission(activity)
            
            // MediaProjection权限需要在Activity的onActivityResult中处理
            // 这里只是发起请求，实际结果需要在Activity中处理
            true
        } catch (e: Exception) {
            Timber.e(e, "Error requesting media projection permission")
            false
        }
    }
    
    /**
     * 请求无障碍服务权限
     */
    override suspend fun requestAccessibilityPermission(activity: Activity): Boolean {
        return try {
            Timber.d("Requesting accessibility permission")
            permissionManager.requestAccessibilityPermission(activity)
            
            // 等待用户操作后重新检查权限
            kotlinx.coroutines.delay(2000)
            val granted = permissionManager.isAccessibilityServiceEnabled()
            
            if (granted) {
                Timber.i("Accessibility permission granted")
            } else {
                Timber.w("Accessibility permission denied")
            }
            
            granted
        } catch (e: Exception) {
            Timber.e(e, "Error requesting accessibility permission")
            false
        }
    }
    
    /**
     * 获取缺失的权限列表
     */
    override fun getMissingPermissions(): List<String> {
        return try {
            val missing = permissionManager.getMissingPermissions()
            Timber.d("Missing permissions: $missing")
            missing
        } catch (e: Exception) {
            Timber.e(e, "Error getting missing permissions")
            emptyList()
        }
    }
    
    /**
     * 检查是否所有权限都已授予
     */
    override fun areAllPermissionsGranted(): Boolean {
        return try {
            val allGranted = permissionManager.areAllPermissionsGranted()
            Timber.d("All permissions granted: $allGranted")
            allGranted
        } catch (e: Exception) {
            Timber.e(e, "Error checking if all permissions granted")
            false
        }
    }
}
