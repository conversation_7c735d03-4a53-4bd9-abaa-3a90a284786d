package com.showforai.executor

import android.app.Application
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber

/**
 * DSL执行器应用程序类
 * 
 * 负责应用程序的全局初始化，包括：
 * - Hilt依赖注入初始化
 * - 日志系统初始化
 * - OpenCV初始化
 * - 全局异常处理
 */
@HiltAndroidApp
class DSLExecutorApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志系统
        initializeLogging()
        
        // 初始化OpenCV
        initializeOpenCV()
        
        // 设置全局异常处理
        setupGlobalExceptionHandler()
        
        Timber.i("DSL Executor Application initialized")
    }

    /**
     * 初始化日志系统
     */
    private fun initializeLogging() {
        if (BuildConfig.DEBUG) {
            // 调试模式下使用详细日志
            Timber.plant(object : Timber.DebugTree() {
                override fun createStackElementTag(element: StackTraceElement): String {
                    return "DSLExecutor:${element.fileName}:${element.lineNumber}#${element.methodName}"
                }
            })
        } else {
            // 发布模式下使用简化日志
            Timber.plant(ReleaseTree())
        }
    }

    /**
     * 初始化OpenCV
     */
    private fun initializeOpenCV() {
        try {
            // 检查OpenCV是否可用
            if (!org.opencv.android.OpenCVLoader.initDebug()) {
                Timber.d("Internal OpenCV library not found. Will use OpenCV Manager for initialization")
            } else {
                Timber.i("OpenCV library found inside package. Using it!")
            }

            // 预加载OpenCV核心模块
            System.loadLibrary("opencv_java4")
            Timber.d("OpenCV native library loaded successfully")
        } catch (e: Exception) {
            Timber.w(e, "OpenCV native library not available, will use fallback initialization")
        }
    }

    /**
     * 设置全局异常处理
     */
    private fun setupGlobalExceptionHandler() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            Timber.e(exception, "Uncaught exception in thread: ${thread.name}")
            
            // 记录崩溃信息
            recordCrashInfo(exception)
            
            // 调用默认处理器
            defaultHandler?.uncaughtException(thread, exception)
        }
    }

    /**
     * 记录崩溃信息
     */
    private fun recordCrashInfo(exception: Throwable) {
        try {
            // 这里可以添加崩溃信息收集逻辑
            // 例如保存到本地文件或上传到服务器
            Timber.e("Crash recorded: ${exception.message}")
        } catch (e: Exception) {
            // 避免在异常处理中再次抛出异常
            Timber.e(e, "Failed to record crash info")
        }
    }

    /**
     * 发布版本的日志树
     * 只记录WARNING及以上级别的日志
     */
    private class ReleaseTree : Timber.Tree() {
        override fun isLoggable(tag: String?, priority: Int): Boolean {
            return priority >= android.util.Log.WARN
        }

        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (isLoggable(tag, priority)) {
                // 在发布版本中，可以将日志发送到崩溃报告服务
                // 例如 Firebase Crashlytics 或其他日志收集服务
                android.util.Log.println(priority, tag, message)
                
                if (t != null) {
                    android.util.Log.println(priority, tag, android.util.Log.getStackTraceString(t))
                }
            }
        }
    }
}
