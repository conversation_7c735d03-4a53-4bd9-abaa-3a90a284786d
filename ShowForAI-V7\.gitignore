# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Tauri
src-tauri/target
src-tauri/Cargo.lock

# Rust
*.exe
*.pdb

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS
Thumbs.db
*.tmp
*.temp

# Build outputs
build/
dist/

# Dependencies
node_modules/
/target/

# Generated files
*.generated.*
src-tauri/gen/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
temp/
tmp/

# IDE
.vscode/
.idea/
*.swp
*.swo

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini