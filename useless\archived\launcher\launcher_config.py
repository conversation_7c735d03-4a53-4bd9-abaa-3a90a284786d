"""
Launcher configuration module for ShowForAI V3.
Handles launcher-specific settings and startup configurations.
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from loguru import logger


@dataclass
class LauncherConfig:
    """Configuration for launcher startup."""
    
    # Authentication settings
    require_auth: bool = True
    remember_login: bool = True
    auto_login: bool = False
    
    # Window settings
    start_minimized: bool = False
    start_in_tray: bool = False
    always_on_top: bool = False
    
    # Mode settings
    default_mode: str = "recorder"  # "recorder" or "executor"
    
    # Performance settings
    enable_gpu: bool = False
    max_memory_mb: int = 500
    
    # Paths
    data_dir: Optional[str] = None
    log_dir: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LauncherConfig':
        """Create from dictionary."""
        return cls(**data)
    
    @classmethod
    def load(cls, config_file: Optional[Path] = None) -> 'LauncherConfig':
        """
        Load configuration from file.
        
        Args:
            config_file: Path to configuration file
            
        Returns:
            LauncherConfig instance
        """
        if config_file is None:
            # Default config location
            config_file = Path.home() / ".showforai" / "launcher_config.json"
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"Loaded launcher config from {config_file}")
                return cls.from_dict(data)
            except Exception as e:
                logger.error(f"Failed to load launcher config: {e}")
                return cls()
        else:
            logger.info("No launcher config found, using defaults")
            return cls()
    
    def save(self, config_file: Optional[Path] = None):
        """
        Save configuration to file.
        
        Args:
            config_file: Path to save configuration
        """
        if config_file is None:
            config_file = Path.home() / ".showforai" / "launcher_config.json"
        
        # Create directory if needed
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2)
            logger.info(f"Saved launcher config to {config_file}")
        except Exception as e:
            logger.error(f"Failed to save launcher config: {e}")


class StartupValidator:
    """Validates startup conditions and environment."""
    
    @staticmethod
    def validate_environment() -> bool:
        """
        Validate that the environment is properly configured.
        
        Returns:
            True if environment is valid
        """
        try:
            # Check Python version
            import sys
            if sys.version_info < (3, 8):
                logger.error(f"Python 3.8+ required, found {sys.version}")
                return False
            
            # Check required modules
            required_modules = [
                'PyQt6',
                'loguru',
                'supabase',
                'pydantic'
            ]
            
            missing = []
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing.append(module)
            
            if missing:
                logger.error(f"Missing required modules: {missing}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Environment validation failed: {e}")
            return False
    
    @staticmethod
    def validate_config(config_path: Optional[Path] = None) -> bool:
        """
        Validate that configuration is properly set up.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            True if configuration is valid
        """
        try:
            from showforai.config import Config
            
            # Load and validate config
            config = Config()
            return config.validate_all()
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    @staticmethod
    def check_permissions() -> bool:
        """
        Check that required permissions are available.
        
        Returns:
            True if permissions are adequate
        """
        try:
            import tempfile
            import os
            
            # Check write permissions in temp directory
            with tempfile.NamedTemporaryFile(delete=True) as tmp:
                tmp.write(b"test")
                tmp.flush()
            
            # Check home directory access
            home_dir = Path.home() / ".showforai"
            home_dir.mkdir(parents=True, exist_ok=True)
            
            # Test write in home directory
            test_file = home_dir / ".test"
            test_file.write_text("test")
            test_file.unlink()
            
            return True
            
        except Exception as e:
            logger.error(f"Permission check failed: {e}")
            return False
    
    @staticmethod
    def validate_all() -> bool:
        """
        Run all validation checks.
        
        Returns:
            True if all validations pass
        """
        checks = [
            ("Environment", StartupValidator.validate_environment),
            ("Configuration", StartupValidator.validate_config),
            ("Permissions", StartupValidator.check_permissions),
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            logger.info(f"Running {check_name} check...")
            if check_func():
                logger.info(f"✓ {check_name} check passed")
            else:
                logger.error(f"✗ {check_name} check failed")
                all_passed = False
        
        return all_passed


def get_launcher_config() -> LauncherConfig:
    """
    Get the launcher configuration.
    
    Returns:
        LauncherConfig instance
    """
    return LauncherConfig.load()


def save_launcher_config(config: LauncherConfig):
    """
    Save launcher configuration.
    
    Args:
        config: LauncherConfig to save
    """
    config.save()