# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateNetworkAclEntriesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'egress_acl_entries': 'list[EgressAclEntryForUpdateNetworkAclEntriesInput]',
        'ingress_acl_entries': 'list[IngressAclEntryForUpdateNetworkAclEntriesInput]',
        'network_acl_id': 'str',
        'update_egress_acl_entries': 'bool',
        'update_ingress_acl_entries': 'bool'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'egress_acl_entries': 'EgressAclEntries',
        'ingress_acl_entries': 'IngressAclEntries',
        'network_acl_id': 'NetworkAclId',
        'update_egress_acl_entries': 'UpdateEgressAclEntries',
        'update_ingress_acl_entries': 'UpdateIngressAclEntries'
    }

    def __init__(self, client_token=None, egress_acl_entries=None, ingress_acl_entries=None, network_acl_id=None, update_egress_acl_entries=None, update_ingress_acl_entries=None, _configuration=None):  # noqa: E501
        """UpdateNetworkAclEntriesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._egress_acl_entries = None
        self._ingress_acl_entries = None
        self._network_acl_id = None
        self._update_egress_acl_entries = None
        self._update_ingress_acl_entries = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if egress_acl_entries is not None:
            self.egress_acl_entries = egress_acl_entries
        if ingress_acl_entries is not None:
            self.ingress_acl_entries = ingress_acl_entries
        self.network_acl_id = network_acl_id
        if update_egress_acl_entries is not None:
            self.update_egress_acl_entries = update_egress_acl_entries
        if update_ingress_acl_entries is not None:
            self.update_ingress_acl_entries = update_ingress_acl_entries

    @property
    def client_token(self):
        """Gets the client_token of this UpdateNetworkAclEntriesRequest.  # noqa: E501


        :return: The client_token of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this UpdateNetworkAclEntriesRequest.


        :param client_token: The client_token of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def egress_acl_entries(self):
        """Gets the egress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501


        :return: The egress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :rtype: list[EgressAclEntryForUpdateNetworkAclEntriesInput]
        """
        return self._egress_acl_entries

    @egress_acl_entries.setter
    def egress_acl_entries(self, egress_acl_entries):
        """Sets the egress_acl_entries of this UpdateNetworkAclEntriesRequest.


        :param egress_acl_entries: The egress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :type: list[EgressAclEntryForUpdateNetworkAclEntriesInput]
        """

        self._egress_acl_entries = egress_acl_entries

    @property
    def ingress_acl_entries(self):
        """Gets the ingress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501


        :return: The ingress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :rtype: list[IngressAclEntryForUpdateNetworkAclEntriesInput]
        """
        return self._ingress_acl_entries

    @ingress_acl_entries.setter
    def ingress_acl_entries(self, ingress_acl_entries):
        """Sets the ingress_acl_entries of this UpdateNetworkAclEntriesRequest.


        :param ingress_acl_entries: The ingress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :type: list[IngressAclEntryForUpdateNetworkAclEntriesInput]
        """

        self._ingress_acl_entries = ingress_acl_entries

    @property
    def network_acl_id(self):
        """Gets the network_acl_id of this UpdateNetworkAclEntriesRequest.  # noqa: E501


        :return: The network_acl_id of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_acl_id

    @network_acl_id.setter
    def network_acl_id(self, network_acl_id):
        """Sets the network_acl_id of this UpdateNetworkAclEntriesRequest.


        :param network_acl_id: The network_acl_id of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and network_acl_id is None:
            raise ValueError("Invalid value for `network_acl_id`, must not be `None`")  # noqa: E501

        self._network_acl_id = network_acl_id

    @property
    def update_egress_acl_entries(self):
        """Gets the update_egress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501


        :return: The update_egress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._update_egress_acl_entries

    @update_egress_acl_entries.setter
    def update_egress_acl_entries(self, update_egress_acl_entries):
        """Sets the update_egress_acl_entries of this UpdateNetworkAclEntriesRequest.


        :param update_egress_acl_entries: The update_egress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :type: bool
        """

        self._update_egress_acl_entries = update_egress_acl_entries

    @property
    def update_ingress_acl_entries(self):
        """Gets the update_ingress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501


        :return: The update_ingress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._update_ingress_acl_entries

    @update_ingress_acl_entries.setter
    def update_ingress_acl_entries(self, update_ingress_acl_entries):
        """Sets the update_ingress_acl_entries of this UpdateNetworkAclEntriesRequest.


        :param update_ingress_acl_entries: The update_ingress_acl_entries of this UpdateNetworkAclEntriesRequest.  # noqa: E501
        :type: bool
        """

        self._update_ingress_acl_entries = update_ingress_acl_entries

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateNetworkAclEntriesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateNetworkAclEntriesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateNetworkAclEntriesRequest):
            return True

        return self.to_dict() != other.to_dict()
