# Technical Design for Bug Fixes and Compliance

## 1. Architecture Overview

```mermaid
graph TB
    subgraph "Recording Flow"
        RC[Recorder] --> NC[Network Checker]
        NC -->|Online| CB[Circular Buffer]
        NC -->|Offline| DIS[Disabled State]
        CB --> UP[Upload Manager]
        UP --> AI[AI Service]
        AI --> BBOX[BBOX Processor]
    end
    
    subgraph "Execution Flow"  
        SL[Script Loader] --> OM[Offline Manager]
        OM -->|Check Cache| EC[Element Cache]
        EC --> MLM[Multi-Level Matcher]
        MLM --> SWM[Smart Wait Manager]
        SWM -->|Active| INT[Interval Based]
        SWM -->|Auxiliary| INF[Infinite Wait]
    end
    
    subgraph "UI Layer"
        MW[Main Window] --> SM[Status Manager]
        SM --> FB[Feedback System]
        MW --> SD[Script Dialogs]
        SD --> TODO[TODO Handlers]
    end
```

## 2. Component Designs

### 2.1 Network Status Integration

```python
class NetworkStatusWidget(QWidget):
    """Visual indicator for network status"""
    
    def __init__(self):
        self.checker = NetworkChecker()
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(5000)  # Check every 5 seconds
        
    def update_status(self):
        is_online = self.checker.is_online()
        self.set_icon(is_online)
        self.set_tooltip(self.checker.get_status_message())
        self.status_changed.emit(is_online)
```

### 2.2 Progress Feedback System

```python
class ProgressManager:
    """Centralized progress feedback manager"""
    
    OPERATIONS = {
        'upload': '正在上传录制数据...',
        'ai_process': '正在识别界面元素...',
        'script_gen': '正在生成脚本...',
        'execution': '正在执行第 {current}/{total} 步...',
        'element_search': '正在查找目标元素...',
        'sync': '正在同步数据...'
    }
    
    def start_operation(self, operation_type, **kwargs):
        message = self.OPERATIONS[operation_type].format(**kwargs)
        self.show_progress(message)
        
    def update_progress(self, current, total):
        self.progress_bar.setValue(current)
        self.progress_bar.setMaximum(total)
```

### 2.3 Share Module Adapter

```python
class ShareModuleAdapter:
    """Bridge between API and share functionality"""
    
    def __init__(self):
        self.share_manager = ShareManager()
        self.uploader = ShareUploader()
        
    async def share_script(self, script_id: str) -> str:
        """Generate shareable link for script"""
        # Upload to server if needed
        if not await self.is_uploaded(script_id):
            await self.uploader.upload(script_id)
        
        # Generate share link
        share_link = await self.share_manager.create_link(script_id)
        return share_link
        
    async def import_shared(self, share_link: str) -> bool:
        """Import script from share link"""
        script_data = await self.share_manager.fetch_from_link(share_link)
        return await self.process_shared_script(script_data)
```

### 2.4 Script Management Dialogs

```python
class BatchRenameDialog(QDialog):
    """Functional batch rename dialog"""
    
    def __init__(self, scripts: List[Script]):
        super().__init__()
        self.scripts = scripts
        self.setup_ui()
        
    def setup_ui(self):
        # Pattern input
        self.pattern_input = QLineEdit()
        self.pattern_input.setPlaceholderText("Enter naming pattern: {name}_{index}")
        
        # Preview list
        self.preview_list = QListWidget()
        self.pattern_input.textChanged.connect(self.update_preview)
        
    def apply_rename(self):
        pattern = self.pattern_input.text()
        for i, script in enumerate(self.scripts):
            new_name = pattern.format(name=script.name, index=i+1)
            script.rename(new_name)

class PermanentDeleteDialog(QDialog):
    """Confirmation dialog for permanent deletion"""
    
    def __init__(self, scripts: List[Script]):
        super().__init__()
        self.scripts = scripts
        self.setup_ui()
        
    def setup_ui(self):
        # Warning message
        warning = QLabel(f"⚠️ This will permanently delete {len(self.scripts)} script(s)")
        
        # Script list
        script_list = QListWidget()
        for script in self.scripts:
            script_list.addItem(f"• {script.name}")
            
        # Confirmation checkbox
        self.confirm_check = QCheckBox("I understand this cannot be undone")
        
        # Delete button (disabled until confirmed)
        self.delete_btn = QPushButton("Delete Permanently")
        self.delete_btn.setEnabled(False)
        self.confirm_check.toggled.connect(self.delete_btn.setEnabled)
```

## 3. Data Models

### 3.1 Recording Status Model

```python
@dataclass
class RecordingStatus:
    is_recording: bool = False
    start_time: Optional[float] = None
    action_count: int = 0
    duration: float = 0.0
    session_id: Optional[str] = None
    
    def to_display_dict(self) -> Dict[str, str]:
        if not self.is_recording:
            return {'status': 'Ready', 'details': ''}
        
        return {
            'status': 'Recording',
            'details': f'{self.action_count} actions, {self.duration:.1f}s'
        }
```

### 3.2 Script Editor Action Model

```python
@dataclass
class ScriptAction:
    action_type: ActionType
    coordinates: Tuple[int, int]
    timestamp: float
    interval_ms: int
    element_image: Optional[bytes] = None
    extra_params: Dict[str, Any] = field(default_factory=dict)
    
class ScriptActionDialog(QDialog):
    """Dialog for adding/editing script actions"""
    
    def __init__(self, action: Optional[ScriptAction] = None):
        self.action = action or ScriptAction(
            action_type=ActionType.CLICK,
            coordinates=(0, 0),
            timestamp=0,
            interval_ms=1000
        )
        self.setup_ui()
```

## 4. API Endpoints

### 4.1 Status Endpoints

```python
GET /api/v1/status/network
Response: {
    "online": true,
    "ai_service": "available",
    "message": "All services operational"
}

GET /api/v1/status/recording
Response: {
    "is_recording": false,
    "session_id": null,
    "action_count": 0
}
```

### 4.2 Script Management Endpoints

```python
POST /api/v1/scripts/batch/rename
Request: {
    "script_ids": ["id1", "id2"],
    "pattern": "{name}_v2"
}

DELETE /api/v1/scripts/batch
Request: {
    "script_ids": ["id1", "id2"],
    "permanent": true
}
```

## 5. UI Component Updates

### 5.1 Main Window Enhancements

```python
class EnhancedMainWindow(QMainWindow):
    def __init__(self):
        # Add network status to status bar
        self.network_status = NetworkStatusWidget()
        self.status_bar.addPermanentWidget(self.network_status)
        
        # Connect recording status updates
        self.recorder.on_status_changed = self.update_recording_status
        
        # Add progress overlay
        self.progress_overlay = ProgressOverlay(self)
        
    def update_recording_status(self, status: RecordingStatus):
        self.record_btn.setText("Stop" if status.is_recording else "Record")
        self.record_btn.setIcon(self.stop_icon if status.is_recording else self.record_icon)
        self.status_label.setText(status.to_display_dict()['details'])
```

### 5.2 Settings Dialog Implementation

```python
class SettingsDialog(QDialog):
    def __init__(self, config: Config):
        self.config = config
        self.setup_ui()
        
    def setup_ui(self):
        tabs = QTabWidget()
        
        # General settings
        general_tab = self.create_general_tab()
        tabs.addTab(general_tab, "General")
        
        # Recording settings
        recording_tab = self.create_recording_tab()
        tabs.addTab(recording_tab, "Recording")
        
        # Execution settings
        execution_tab = self.create_execution_tab()
        tabs.addTab(execution_tab, "Execution")
        
    def create_recording_tab(self):
        widget = QWidget()
        layout = QFormLayout()
        
        # Buffer size
        self.buffer_size = QSpinBox()
        self.buffer_size.setRange(10, 30)
        self.buffer_size.setValue(15)
        layout.addRow("Buffer Size (frames):", self.buffer_size)
        
        # FPS
        self.fps = QSpinBox()
        self.fps.setRange(5, 30)
        self.fps.setValue(10)
        layout.addRow("Capture FPS:", self.fps)
        
        return widget
```

## 6. Error Handling Strategy

### 6.1 Network Failure Handling

```python
class NetworkAwareComponent:
    def handle_network_failure(self, operation: str):
        if operation == 'recording':
            self.show_error("录制需要网络连接以进行AI识别")
            self.disable_recording_ui()
        elif operation == 'sync':
            self.show_warning("网络不可用，使用本地缓存")
            self.use_cached_data()
```

### 6.2 Execution Failure Recovery

```python
class ExecutionErrorHandler:
    def handle_element_not_found(self, element_name: str, mode: WaitMode):
        if mode == WaitMode.AUXILIARY:
            self.show_status(f"等待元素: {element_name}")
            # Continue waiting infinitely
        else:
            self.show_error(f"无法找到元素: {element_name}")
            self.stop_execution()
```

## 7. Migration Path

### 7.1 TODO Removal Plan

1. **Phase 1**: Replace all TODO dialogs with functional implementations
2. **Phase 2**: Complete share module integration
3. **Phase 3**: Implement all progress indicators
4. **Phase 4**: Add comprehensive error handling

### 7.2 Testing Strategy

```python
class ComplianceTests(unittest.TestCase):
    def test_no_todos_in_production(self):
        """Ensure no TODO comments in production code"""
        for file in self.get_source_files():
            content = file.read_text()
            self.assertNotIn('TODO', content, f"TODO found in {file}")
            
    def test_offline_recording_disabled(self):
        """Verify recording disabled when offline"""
        with mock_offline():
            recorder = Recorder()
            with self.assertRaises(RuntimeError):
                recorder.start_recording()
```

## 8. Performance Considerations

- Progress indicators use throttling (update max 10 times/second)
- Network status checks cached for 5 seconds
- Script list virtualization for >100 items
- Lazy loading for script thumbnails

## 9. Security Considerations

- All API calls include timestamp and nonce
- Share links expire after configured duration
- Script execution sandboxed from system operations
- User credentials never stored in plain text