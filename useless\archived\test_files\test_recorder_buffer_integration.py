"""Test script to verify CircularFrameBuffer integration in recorder module."""

import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from showforai.recorder.recorder import Recorder
from showforai.buffer_manager import CircularFrameBuffer
from loguru import logger

def test_recorder_buffer_integration():
    """Test that the recorder properly uses the CircularFrameBuffer."""
    
    print("\n=== Testing Recorder with CircularFrameBuffer Integration ===\n")
    
    # Create recorder instance
    recorder = Recorder(output_dir="test_recordings")
    
    # Check that frame_buffer is initialized
    assert hasattr(recorder, 'frame_buffer'), "Recorder should have frame_buffer attribute"
    assert isinstance(recorder.frame_buffer, CircularFrameBuffer), "frame_buffer should be CircularFrameBuffer instance"
    print("✓ CircularFrameBuffer is properly initialized in Recorder")
    
    # Test network check (recording should require network)
    print("\nChecking network status...")
    has_network = recorder.check_network_status()
    print(f"Network available: {has_network}")
    
    if not has_network:
        print("⚠ Network not available - recording would be disabled in production")
        print("  (This is expected behavior per product requirements)")
        # For testing, we'll check the buffer separately
        
        # Test the frame buffer directly
        print("\n=== Testing CircularFrameBuffer Directly ===")
        buffer = CircularFrameBuffer(max_frames=15, fps=10)
        
        # Start capture
        buffer.start_capture()
        print("Started CircularFrameBuffer capture")
        
        # Let it capture some frames
        print("Capturing frames for 2 seconds...")
        time.sleep(2)
        
        # Get buffer stats
        stats = buffer.get_buffer_stats()
        print(f"\nBuffer statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Test frame retrieval
        click_time = time.perf_counter()
        frame = buffer.get_frame_before_click(click_time, seconds_before=0.3)
        
        if frame:
            print(f"\n✓ Successfully retrieved frame from 0.3s before click")
            print(f"  Frame number: {frame['frame_number']}")
            print(f"  Time difference: {click_time - frame['timestamp']:.3f}s")
        else:
            print("\n✗ No frame available (buffer may still be filling)")
        
        # Stop capture
        buffer.stop_capture()
        print("\nStopped CircularFrameBuffer capture")
        
        # Final stats
        final_stats = buffer.get_buffer_stats()
        print(f"\nFinal buffer statistics:")
        print(f"  Total frames captured: {final_stats['total_captured']}")
        print(f"  Buffer size: {final_stats['buffer_size']}")
        
    else:
        print("\n=== Testing Full Recording with CircularFrameBuffer ===")
        
        try:
            # Start recording
            print("\nStarting recording...")
            success = recorder.start_recording("test_buffer_integration")
            
            if success:
                print("✓ Recording started successfully")
                
                # Check that frame buffer is capturing
                time.sleep(2)  # Let it capture some frames
                
                # Get statistics
                stats = recorder.get_statistics()
                if 'frame_buffer' in stats:
                    buffer_stats = stats['frame_buffer']
                    print(f"\nFrame buffer statistics:")
                    print(f"  Buffer size: {buffer_stats.get('buffer_size', 'N/A')}")
                    print(f"  Total captured: {buffer_stats.get('total_captured', 'N/A')}")
                    print(f"  Is capturing: {buffer_stats.get('is_capturing', False)}")
                    
                    if buffer_stats.get('total_captured', 0) > 0:
                        print("✓ CircularFrameBuffer is actively capturing frames")
                    else:
                        print("⚠ No frames captured yet")
                
                # Simulate a click to test frame retrieval
                print("\nSimulating click event...")
                recorder._on_click(100, 100, 'left', True)
                
                # Check if screenshot was captured
                time.sleep(0.5)
                final_stats = recorder.get_statistics()
                
                print(f"\nRecording statistics after click:")
                print(f"  Total actions: {final_stats.get('total_actions', 0)}")
                print(f"  Clicks: {final_stats.get('clicks', 0)}")
                print(f"  Screenshots: {final_stats.get('screenshots', 0)}")
                
                # Stop recording
                print("\nStopping recording...")
                recorder.stop_recording()
                print("✓ Recording stopped successfully")
                
            else:
                print("✗ Failed to start recording")
                
        except Exception as e:
            print(f"✗ Error during recording test: {e}")
            logger.error(f"Recording test error: {e}", exc_info=True)
        
        finally:
            # Cleanup
            recorder.cleanup()
    
    print("\n=== Integration Test Complete ===")
    
    # Test the _save_frame_as_screenshot method
    print("\n=== Testing Frame Save Functionality ===")
    
    # Create a test frame
    test_frame = {
        'timestamp': time.time(),
        'frame_number': 42,
        'frame': b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x0cIDATx\x9cc\xf8\x0f\x00\x00\x01\x01\x00\x05\x18\xd8+E\x00\x00\x00\x00IEND\xaeB`\x82'  # Minimal valid PNG
    }
    
    # Set session ID for testing
    recorder.session_id = "test_session"
    
    # Test saving frame
    screenshot_path = recorder._save_frame_as_screenshot(test_frame)
    if screenshot_path:
        print(f"✓ Successfully saved frame as screenshot: {Path(screenshot_path).name}")
    else:
        print("✗ Failed to save frame as screenshot")
    
    print("\n✓ All integration tests completed!")

if __name__ == "__main__":
    test_recorder_buffer_integration()