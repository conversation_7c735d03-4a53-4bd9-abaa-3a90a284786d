package com.showforai.executor.data.repositories

import android.net.Uri
import com.showforai.executor.data.models.DSLScript
import com.showforai.executor.utils.FileManager
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 脚本仓库接口
 */
interface ScriptRepository {
    suspend fun loadScriptFromUri(uri: Uri): DSLScript?
    suspend fun loadScriptFromFile(filePath: String): DSLScript?
    suspend fun saveScript(script: DSLScript, fileName: String): Boolean
    suspend fun getLocalScripts(): List<File>
    suspend fun deleteScript(fileName: String): Boolean
}

/**
 * 脚本仓库实现
 * 
 * 负责DSL脚本的存储和管理：
 * - 从URI或文件路径加载脚本
 * - 保存脚本到本地存储
 * - 管理本地脚本文件
 * - 脚本验证和解析
 */
@Singleton
class ScriptRepositoryImpl @Inject constructor(
    private val fileManager: FileManager
) : ScriptRepository {
    
    /**
     * 从URI加载DSL脚本
     */
    override suspend fun loadScriptFromUri(uri: Uri): DSLScript? {
        return try {
            Timber.d("Loading script from URI: $uri")
            val script = fileManager.loadScriptFromUri(uri)
            
            if (script != null) {
                validateScript(script)
                Timber.i("Script loaded successfully from URI")
            } else {
                Timber.w("Failed to load script from URI")
            }
            
            script
        } catch (e: Exception) {
            Timber.e(e, "Error loading script from URI: $uri")
            null
        }
    }
    
    /**
     * 从文件路径加载DSL脚本
     */
    override suspend fun loadScriptFromFile(filePath: String): DSLScript? {
        return try {
            Timber.d("Loading script from file: $filePath")
            val script = fileManager.loadScriptFromFile(filePath)
            
            if (script != null) {
                validateScript(script)
                Timber.i("Script loaded successfully from file")
            } else {
                Timber.w("Failed to load script from file")
            }
            
            script
        } catch (e: Exception) {
            Timber.e(e, "Error loading script from file: $filePath")
            null
        }
    }
    
    /**
     * 保存DSL脚本到本地
     */
    override suspend fun saveScript(script: DSLScript, fileName: String): Boolean {
        return try {
            Timber.d("Saving script: $fileName")
            
            // 验证脚本
            validateScript(script)
            
            // 确保文件名有正确的扩展名
            val finalFileName = if (fileName.endsWith(".json")) {
                fileName
            } else {
                "$fileName.json"
            }
            
            val success = fileManager.saveScript(script, finalFileName)
            
            if (success) {
                Timber.i("Script saved successfully: $finalFileName")
            } else {
                Timber.w("Failed to save script: $finalFileName")
            }
            
            success
        } catch (e: Exception) {
            Timber.e(e, "Error saving script: $fileName")
            false
        }
    }
    
    /**
     * 获取本地脚本列表
     */
    override suspend fun getLocalScripts(): List<File> {
        return try {
            Timber.d("Getting local scripts")
            val scripts = fileManager.getLocalScripts()
            Timber.d("Found ${scripts.size} local scripts")
            scripts
        } catch (e: Exception) {
            Timber.e(e, "Error getting local scripts")
            emptyList()
        }
    }
    
    /**
     * 删除本地脚本
     */
    override suspend fun deleteScript(fileName: String): Boolean {
        return try {
            Timber.d("Deleting script: $fileName")
            val success = fileManager.deleteScript(fileName)
            
            if (success) {
                Timber.i("Script deleted successfully: $fileName")
            } else {
                Timber.w("Failed to delete script: $fileName")
            }
            
            success
        } catch (e: Exception) {
            Timber.e(e, "Error deleting script: $fileName")
            false
        }
    }
    
    /**
     * 验证DSL脚本
     */
    private fun validateScript(script: DSLScript) {
        // 检查版本
        if (script.version != "3.1") {
            throw IllegalArgumentException("Unsupported DSL version: ${script.version}")
        }
        
        // 检查步骤
        if (script.steps.isEmpty()) {
            throw IllegalArgumentException("Script has no steps")
        }
        
        // 验证每个步骤
        script.steps.forEachIndexed { index, step ->
            validateStep(index, step)
        }
        
        Timber.d("Script validation passed: ${script.steps.size} steps")
    }
    
    /**
     * 验证单个步骤
     */
    private fun validateStep(index: Int, step: com.showforai.executor.data.models.DSLStep) {
        // 检查超时时间
        if (step.timeoutSeconds <= 0) {
            throw IllegalArgumentException("Step $index: Invalid timeout: ${step.timeoutSeconds}")
        }
        
        // 检查命令类型特定的参数
        when (step.command) {
            com.showforai.executor.data.models.CommandType.CLICK,
            com.showforai.executor.data.models.CommandType.LONG_PRESS,
            com.showforai.executor.data.models.CommandType.DOUBLE_CLICK -> {
                if (step.target == null) {
                    throw IllegalArgumentException("Step $index: ${step.command} requires target")
                }
                validateTarget(index, step.target)
            }
            
            com.showforai.executor.data.models.CommandType.INPUT_TEXT -> {
                if (step.target == null) {
                    throw IllegalArgumentException("Step $index: INPUT_TEXT requires target")
                }
                if (step.parameters?.text.isNullOrEmpty()) {
                    throw IllegalArgumentException("Step $index: INPUT_TEXT requires text parameter")
                }
                validateTarget(index, step.target)
            }
            
            com.showforai.executor.data.models.CommandType.SWIPE -> {
                if (step.target == null && step.parameters?.startPoint == null) {
                    throw IllegalArgumentException("Step $index: SWIPE requires target or start/end points")
                }
                if (step.target != null) {
                    validateTarget(index, step.target)
                }
                if (step.parameters?.startPoint != null && step.parameters.endPoint != null) {
                    validateCoordinates(index, step.parameters.startPoint, "startPoint")
                    validateCoordinates(index, step.parameters.endPoint, "endPoint")
                }
            }
            
            com.showforai.executor.data.models.CommandType.SCROLL -> {
                if (step.target != null) {
                    validateTarget(index, step.target)
                }
                if (step.parameters?.scrollDirection == null) {
                    throw IllegalArgumentException("Step $index: SCROLL requires scrollDirection parameter")
                }
            }
            
            com.showforai.executor.data.models.CommandType.WAIT -> {
                if (step.parameters?.duration == null || step.parameters.duration <= 0) {
                    throw IllegalArgumentException("Step $index: WAIT requires positive duration parameter")
                }
            }
            
            else -> {
                // 其他命令类型的验证
            }
        }
    }
    
    /**
     * 验证目标参数
     */
    private fun validateTarget(stepIndex: Int, target: com.showforai.executor.data.models.DSLTarget) {
        // 至少需要一种定位方式
        if (target.visualHash.isNullOrEmpty() && 
            target.textContent.isNullOrEmpty() && 
            target.boundingBox.isNullOrEmpty()) {
            throw IllegalArgumentException("Step $stepIndex: Target requires at least one location method")
        }
        
        // 验证边界框格式
        if (target.boundingBox != null) {
            if (target.boundingBox.size != 4) {
                throw IllegalArgumentException("Step $stepIndex: Bounding box must have 4 elements [x1, y1, x2, y2]")
            }
            validateCoordinates(stepIndex, target.boundingBox, "boundingBox")
        }
    }
    
    /**
     * 验证坐标格式
     */
    private fun validateCoordinates(stepIndex: Int, coordinates: List<Float>, paramName: String) {
        coordinates.forEach { coord ->
            if (coord < 0f || coord > 1f) {
                throw IllegalArgumentException("Step $stepIndex: $paramName coordinates must be normalized (0.0-1.0)")
            }
        }
    }
}
