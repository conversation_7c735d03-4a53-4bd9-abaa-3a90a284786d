package com.showforai.executor.utils

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Point
import android.util.DisplayMetrics
import android.view.WindowManager
import com.showforai.executor.data.models.ScreenInfo
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner

/**
 * ScreenUtils单元测试
 * 
 * 测试屏幕工具类的坐标转换和计算功能
 */
@RunWith(MockitoJUnitRunner::class)
class ScreenUtilsTest {
    
    @Mock
    private lateinit var mockContext: Context
    
    @Mock
    private lateinit var mockWindowManager: WindowManager
    
    @Mock
    private lateinit var mockResources: Resources
    
    @Mock
    private lateinit var mockConfiguration: Configuration
    
    private lateinit var screenUtils: ScreenUtils
    
    @Before
    fun setup() {
        // 设置模拟对象
        `when`(mockContext.getSystemService(Context.WINDOW_SERVICE)).thenReturn(mockWindowManager)
        `when`(mockContext.resources).thenReturn(mockResources)
        `when`(mockResources.configuration).thenReturn(mockConfiguration)
        
        // 设置屏幕参数
        mockConfiguration.orientation = Configuration.ORIENTATION_PORTRAIT
        
        screenUtils = ScreenUtils(mockContext)
    }
    
    @Test
    fun testNormalizedToPixel() {
        // 模拟1080x1920屏幕
        val screenInfo = ScreenInfo(
            width = 1080,
            height = 1920,
            density = 3.0f,
            orientation = Configuration.ORIENTATION_PORTRAIT
        )
        
        // 测试归一化坐标转换为像素坐标
        val result = convertNormalizedToPixel(0.5f, 0.5f, screenInfo)
        
        assertEquals(540, result.x) // 1080 * 0.5
        assertEquals(960, result.y) // 1920 * 0.5
    }
    
    @Test
    fun testPixelToNormalized() {
        // 模拟1080x1920屏幕
        val screenInfo = ScreenInfo(
            width = 1080,
            height = 1920,
            density = 3.0f,
            orientation = Configuration.ORIENTATION_PORTRAIT
        )
        
        // 测试像素坐标转换为归一化坐标
        val result = convertPixelToNormalized(540, 960, screenInfo)
        
        assertEquals(0.5f, result.x, 0.001f) // 540 / 1080
        assertEquals(0.5f, result.y, 0.001f) // 960 / 1920
    }
    
    @Test
    fun testNormalizedBoxToPixel() {
        // 模拟1080x1920屏幕
        val screenInfo = ScreenInfo(
            width = 1080,
            height = 1920,
            density = 3.0f,
            orientation = Configuration.ORIENTATION_PORTRAIT
        )
        
        // 测试归一化边界框转换
        val normalizedBox = listOf(0.1f, 0.2f, 0.9f, 0.8f) // [x1, y1, x2, y2]
        val result = convertNormalizedBoxToPixel(normalizedBox, screenInfo)
        
        assertEquals(108f, result.left, 0.001f)   // 1080 * 0.1
        assertEquals(384f, result.top, 0.001f)    // 1920 * 0.2
        assertEquals(972f, result.right, 0.001f)  // 1080 * 0.9
        assertEquals(1536f, result.bottom, 0.001f) // 1920 * 0.8
    }
    
    @Test
    fun testPixelBoxToNormalized() {
        // 模拟1080x1920屏幕
        val screenInfo = ScreenInfo(
            width = 1080,
            height = 1920,
            density = 3.0f,
            orientation = Configuration.ORIENTATION_PORTRAIT
        )
        
        // 测试像素边界框转换
        val pixelBox = android.graphics.RectF(108f, 384f, 972f, 1536f)
        val result = convertPixelBoxToNormalized(pixelBox, screenInfo)
        
        assertEquals(4, result.size)
        assertEquals(0.1f, result[0], 0.001f) // 108 / 1080
        assertEquals(0.2f, result[1], 0.001f) // 384 / 1920
        assertEquals(0.9f, result[2], 0.001f) // 972 / 1080
        assertEquals(0.8f, result[3], 0.001f) // 1536 / 1920
    }
    
    @Test
    fun testIsPointInScreen() {
        // 模拟1080x1920屏幕
        val screenInfo = ScreenInfo(
            width = 1080,
            height = 1920,
            density = 3.0f,
            orientation = Configuration.ORIENTATION_PORTRAIT
        )
        
        // 测试点是否在屏幕范围内
        assertTrue(isPointInScreen(500, 800, screenInfo))  // 在范围内
        assertTrue(isPointInScreen(0, 0, screenInfo))      // 边界点
        assertTrue(isPointInScreen(1079, 1919, screenInfo)) // 边界点
        
        assertFalse(isPointInScreen(-1, 500, screenInfo))   // 超出左边界
        assertFalse(isPointInScreen(1080, 500, screenInfo)) // 超出右边界
        assertFalse(isPointInScreen(500, -1, screenInfo))   // 超出上边界
        assertFalse(isPointInScreen(500, 1920, screenInfo)) // 超出下边界
    }
    
    @Test
    fun testIsNormalizedPointValid() {
        // 测试归一化坐标是否有效
        assertTrue(isNormalizedPointValid(0.5f, 0.5f))   // 有效点
        assertTrue(isNormalizedPointValid(0.0f, 0.0f))   // 边界点
        assertTrue(isNormalizedPointValid(1.0f, 1.0f))   // 边界点
        
        assertFalse(isNormalizedPointValid(-0.1f, 0.5f)) // 无效X坐标
        assertFalse(isNormalizedPointValid(1.1f, 0.5f))  // 无效X坐标
        assertFalse(isNormalizedPointValid(0.5f, -0.1f)) // 无效Y坐标
        assertFalse(isNormalizedPointValid(0.5f, 1.1f))  // 无效Y坐标
    }
    
    @Test
    fun testClampToScreen() {
        // 模拟1080x1920屏幕
        val screenInfo = ScreenInfo(
            width = 1080,
            height = 1920,
            density = 3.0f,
            orientation = Configuration.ORIENTATION_PORTRAIT
        )
        
        // 测试坐标限制
        val result1 = clampToScreen(-10, 500, screenInfo)
        assertEquals(0, result1.x) // 限制到左边界
        assertEquals(500, result1.y)
        
        val result2 = clampToScreen(1090, 500, screenInfo)
        assertEquals(1079, result2.x) // 限制到右边界
        assertEquals(500, result2.y)
        
        val result3 = clampToScreen(500, -10, screenInfo)
        assertEquals(500, result3.x)
        assertEquals(0, result3.y) // 限制到上边界
        
        val result4 = clampToScreen(500, 1930, screenInfo)
        assertEquals(500, result4.x)
        assertEquals(1919, result4.y) // 限制到下边界
    }
    
    @Test
    fun testClampNormalizedPoint() {
        // 测试归一化坐标限制
        val result1 = clampNormalizedPoint(-0.1f, 0.5f)
        assertEquals(0.0f, result1.x, 0.001f) // 限制到0
        assertEquals(0.5f, result1.y, 0.001f)
        
        val result2 = clampNormalizedPoint(1.1f, 0.5f)
        assertEquals(1.0f, result2.x, 0.001f) // 限制到1
        assertEquals(0.5f, result2.y, 0.001f)
        
        val result3 = clampNormalizedPoint(0.5f, -0.1f)
        assertEquals(0.5f, result3.x, 0.001f)
        assertEquals(0.0f, result3.y, 0.001f) // 限制到0
        
        val result4 = clampNormalizedPoint(0.5f, 1.1f)
        assertEquals(0.5f, result4.x, 0.001f)
        assertEquals(1.0f, result4.y, 0.001f) // 限制到1
    }
    
    @Test
    fun testDpToPx() {
        // 测试DP到像素的转换
        val density = 3.0f // xxhdpi
        val result = dpToPx(16f, density)
        assertEquals(48, result) // 16 * 3 + 0.5 = 48.5 -> 48
    }
    
    @Test
    fun testPxToDp() {
        // 测试像素到DP的转换
        val density = 3.0f // xxhdpi
        val result = pxToDp(48, density)
        assertEquals(16.0f, result, 0.001f) // 48 / 3 = 16
    }
    
    // 辅助方法，模拟ScreenUtils的私有方法
    private fun convertNormalizedToPixel(normalizedX: Float, normalizedY: Float, screenInfo: ScreenInfo): Point {
        val pixelX = (normalizedX * screenInfo.width).toInt()
        val pixelY = (normalizedY * screenInfo.height).toInt()
        return Point(pixelX, pixelY)
    }
    
    private fun convertPixelToNormalized(pixelX: Int, pixelY: Int, screenInfo: ScreenInfo): com.showforai.executor.data.models.Point {
        val normalizedX = pixelX.toFloat() / screenInfo.width
        val normalizedY = pixelY.toFloat() / screenInfo.height
        return com.showforai.executor.data.models.Point(normalizedX, normalizedY)
    }
    
    private fun convertNormalizedBoxToPixel(normalizedBox: List<Float>, screenInfo: ScreenInfo): android.graphics.RectF {
        val left = normalizedBox[0] * screenInfo.width
        val top = normalizedBox[1] * screenInfo.height
        val right = normalizedBox[2] * screenInfo.width
        val bottom = normalizedBox[3] * screenInfo.height
        return android.graphics.RectF(left, top, right, bottom)
    }
    
    private fun convertPixelBoxToNormalized(pixelBox: android.graphics.RectF, screenInfo: ScreenInfo): List<Float> {
        return listOf(
            pixelBox.left / screenInfo.width,
            pixelBox.top / screenInfo.height,
            pixelBox.right / screenInfo.width,
            pixelBox.bottom / screenInfo.height
        )
    }
    
    private fun isPointInScreen(x: Int, y: Int, screenInfo: ScreenInfo): Boolean {
        return x >= 0 && x < screenInfo.width && y >= 0 && y < screenInfo.height
    }
    
    private fun isNormalizedPointValid(normalizedX: Float, normalizedY: Float): Boolean {
        return normalizedX >= 0f && normalizedX <= 1f && normalizedY >= 0f && normalizedY <= 1f
    }
    
    private fun clampToScreen(x: Int, y: Int, screenInfo: ScreenInfo): Point {
        val clampedX = x.coerceIn(0, screenInfo.width - 1)
        val clampedY = y.coerceIn(0, screenInfo.height - 1)
        return Point(clampedX, clampedY)
    }
    
    private fun clampNormalizedPoint(normalizedX: Float, normalizedY: Float): com.showforai.executor.data.models.Point {
        val clampedX = normalizedX.coerceIn(0f, 1f)
        val clampedY = normalizedY.coerceIn(0f, 1f)
        return com.showforai.executor.data.models.Point(clampedX, clampedY)
    }
    
    private fun dpToPx(dp: Float, density: Float): Int {
        return (dp * density + 0.5f).toInt()
    }
    
    private fun pxToDp(px: Int, density: Float): Float {
        return px / density
    }
}
