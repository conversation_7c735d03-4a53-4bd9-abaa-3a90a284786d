"""
Unit test to verify that only PNG files are created for screenshots.

This test ensures that all screenshot operations use PNG format exclusively.
"""

import os
import tempfile
import unittest
from pathlib import Path
from PIL import Image
import io

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from showforai.recorder.screen_capture import ScreenCapture
from showforai.ai.image_processor import ImageProcessor
from showforai.optimizer.image_optimizer import ImageOptimizer


class TestPNGFormatOnly(unittest.TestCase):
    """Test that all components use PNG format exclusively."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_image_data = self._create_test_image()
    
    def tearDown(self):
        """Clean up test environment."""
        # Clean up temp directory
        for file in Path(self.temp_dir).glob("*"):
            file.unlink()
        Path(self.temp_dir).rmdir()
    
    def _create_test_image(self) -> bytes:
        """Create a test image in memory."""
        # Create a simple test image
        img = Image.new('RGB', (100, 100), color='red')
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        return buffer.getvalue()
    
    def test_screen_capture_saves_png(self):
        """Test that ScreenCapture saves files as PNG."""
        # Initialize ScreenCapture with temp directory
        capture = ScreenCapture(output_dir=self.temp_dir)
        
        # Start the capture service
        capture.start()
        
        # Capture a screenshot synchronously
        filename = capture.capture_screenshot_sync()
        
        # Stop the capture service
        capture.stop()
        
        if filename:
            # Check that the file has .png extension
            self.assertTrue(filename.endswith('.png'), 
                          f"Screenshot should have .png extension, got: {filename}")
            
            # Check that the file is actually a PNG
            file_path = Path(self.temp_dir) / filename
            if file_path.exists():
                with Image.open(file_path) as img:
                    self.assertEqual(img.format, 'PNG', 
                                   f"File should be PNG format, got: {img.format}")
    
    def test_image_processor_default_format(self):
        """Test that ImageProcessor defaults to PNG format."""
        processor = ImageProcessor()
        
        # Process screenshot without specifying format (should default to PNG)
        processed = processor.process_screenshot(self.test_image_data)
        
        # Check that the output is PNG
        img = Image.open(io.BytesIO(processed))
        self.assertEqual(img.format, 'PNG', 
                       f"Processed image should be PNG, got: {img.format}")
    
    def test_image_processor_compress_format(self):
        """Test that ImageProcessor compress method defaults to PNG."""
        processor = ImageProcessor()
        
        # Compress without specifying format (should default to PNG)
        compressed = processor.compress_image(self.test_image_data)
        
        # Check that the output is PNG
        img = Image.open(io.BytesIO(compressed))
        self.assertEqual(img.format, 'PNG', 
                       f"Compressed image should be PNG, got: {img.format}")
    
    def test_image_processor_convert_format(self):
        """Test that ImageProcessor convert_format defaults to PNG."""
        processor = ImageProcessor()
        
        # Convert without specifying format (should default to PNG)
        converted = processor.convert_format(self.test_image_data)
        
        # Check that the output is PNG
        img = Image.open(io.BytesIO(converted))
        self.assertEqual(img.format, 'PNG', 
                       f"Converted image should be PNG, got: {img.format}")
    
    def test_image_optimizer_default_format(self):
        """Test that ImageOptimizer defaults to PNG format."""
        import numpy as np
        import cv2
        
        optimizer = ImageOptimizer()
        
        # Create a test numpy array (OpenCV format)
        test_array = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        # Compress without specifying format (should default to PNG)
        compressed = optimizer.compress_image(test_array)
        
        if compressed:
            # Check that the output is PNG
            img = Image.open(io.BytesIO(compressed))
            self.assertEqual(img.format, 'PNG', 
                           f"Optimized image should be PNG, got: {img.format}")
    
    def test_image_optimizer_storage_format(self):
        """Test that ImageOptimizer uses PNG for screenshots."""
        import numpy as np
        import cv2
        
        optimizer = ImageOptimizer()
        
        # Create a test numpy array (OpenCV format)
        test_array = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        # Optimize for storage as screenshot
        optimized = optimizer.optimize_for_storage(test_array, is_screenshot=True)
        
        if optimized:
            # Check that the output is PNG
            img = Image.open(io.BytesIO(optimized))
            self.assertEqual(img.format, 'PNG', 
                           f"Storage optimized screenshot should be PNG, got: {img.format}")
    
    def test_no_jpeg_files_created(self):
        """Test that no JPEG files are created in the output directory."""
        capture = ScreenCapture(output_dir=self.temp_dir)
        
        # Start capture
        capture.start()
        
        # Capture multiple screenshots
        for _ in range(3):
            capture.capture_screenshot_sync()
        
        # Stop capture
        capture.stop()
        
        # Check that no JPEG files exist
        jpeg_files = list(Path(self.temp_dir).glob("*.jpg")) + \
                    list(Path(self.temp_dir).glob("*.jpeg"))
        
        self.assertEqual(len(jpeg_files), 0, 
                       f"No JPEG files should be created, found: {jpeg_files}")
        
        # Check that PNG files were created
        png_files = list(Path(self.temp_dir).glob("*.png"))
        self.assertGreater(len(png_files), 0, 
                         "PNG files should be created")
    
    def test_optimize_transmission_uses_png(self):
        """Test that optimize_for_transmission maintains PNG format."""
        processor = ImageProcessor()
        
        # Create a large test image to trigger optimization
        large_img = Image.new('RGB', (2000, 2000), color='blue')
        buffer = io.BytesIO()
        large_img.save(buffer, format='PNG')
        large_data = buffer.getvalue()
        
        # Optimize for transmission with small target size
        optimized = processor.optimize_for_transmission(large_data, target_size_kb=50)
        
        # Check that the output is still PNG
        img = Image.open(io.BytesIO(optimized))
        self.assertEqual(img.format, 'PNG', 
                       f"Transmission optimized image should be PNG, got: {img.format}")


if __name__ == '__main__':
    unittest.main()