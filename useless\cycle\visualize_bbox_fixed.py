import json
import sys
from PIL import Image, ImageDraw, ImageFont
import os

def convert_gemini_coordinates(bbox, image_width, image_height):
    """
    将Gemini的1000x1000归一化坐标转换为实际像素坐标
    
    Gemini返回的坐标格式：
    - 坐标范围是0-1000
    - 格式可能是 [ymin, xmin, ymax, xmax] 或 {x, y, width, height}
    
    Args:
        bbox: 边界框数据
        image_width: 图片实际宽度
        image_height: 图片实际高度
    
    Returns:
        转换后的像素坐标 {x, y, width, height}
    """
    # 如果是标准的 {x, y, width, height} 格式
    if 'x' in bbox and 'y' in bbox and 'width' in bbox and 'height' in bbox:
        # 检查是否需要归一化（如果坐标值大于1，说明是0-1000范围）
        if bbox['x'] > 1 or bbox['y'] > 1 or bbox['width'] > 1 or bbox['height'] > 1:
            # 从1000x1000坐标系转换到实际像素坐标
            x = int(bbox['x'] * image_width / 1000)
            y = int(bbox['y'] * image_height / 1000)
            width = int(bbox['width'] * image_width / 1000)
            height = int(bbox['height'] * image_height / 1000)
            
            return {
                'x': x,
                'y': y,
                'width': width,
                'height': height
            }
    
    # 如果坐标已经是像素坐标（值小于等于1认为是归一化的0-1坐标）
    if bbox['x'] <= 1 and bbox['y'] <= 1:
        x = int(bbox['x'] * image_width)
        y = int(bbox['y'] * image_height)
        width = int(bbox['width'] * image_width)
        height = int(bbox['height'] * image_height)
        
        return {
            'x': x,
            'y': y,
            'width': width,
            'height': height
        }
    
    # 否则直接返回原始坐标
    return bbox

def draw_bounding_box(image_path, bbox_data, output_path=None, use_gemini_format=True):
    """
    在图片上绘制边界框
    
    Args:
        image_path: 输入图片路径
        bbox_data: 边界框数据（可以是JSON字符串或字典）
        output_path: 输出图片路径（如果为None，则自动生成）
        use_gemini_format: 是否使用Gemini的1000x1000坐标格式
    """
    # 加载图片
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)
    
    # 获取图片尺寸
    img_width, img_height = img.size
    
    # 解析边界框数据
    if isinstance(bbox_data, str):
        data = json.loads(bbox_data)
    else:
        data = bbox_data
    
    # 提取边界框坐标
    bbox = data['element']['bounding_box']
    
    # 如果启用Gemini格式转换
    if use_gemini_format:
        bbox = convert_gemini_coordinates(bbox, img_width, img_height)
        print(f"原始坐标: {data['element']['bounding_box']}")
        print(f"转换后坐标: {bbox}")
        print(f"图片尺寸: {img_width}x{img_height}")
    
    x = bbox['x']
    y = bbox['y']
    width = bbox['width']
    height = bbox['height']
    
    # 计算右下角坐标
    x2 = x + width
    y2 = y + height
    
    # 绘制边界框（红色，线宽3）
    draw.rectangle([x, y, x2, y2], outline='red', width=3)
    
    # 绘制中心点（绿色十字）
    center_x = x + width // 2
    center_y = y + height // 2
    cross_size = 10
    draw.line([center_x - cross_size, center_y, center_x + cross_size, center_y], fill='green', width=2)
    draw.line([center_x, center_y - cross_size, center_x, center_y + cross_size], fill='green', width=2)
    
    # 添加描述文本
    description = data['element'].get('description', '')
    text_content = data['element'].get('text_content', '')
    
    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # 在边界框上方显示描述
    if description:
        text_y = max(y - 25, 5)
        draw.text((x, text_y), description, fill='red', font=font)
    
    # 如果有文本内容，在边界框内显示
    if text_content:
        draw.text((x + 5, y + 5), f"Text: {text_content}", fill='blue', font=font)
    
    # 在图片左上角显示坐标信息
    info_text = f"Box: ({x}, {y}) - ({x2}, {y2})\nSize: {width}x{height}"
    draw.text((10, 10), info_text, fill='black', font=font)
    
    # 显示图片中心点（黄色小圆圈）
    img_center_x = img_width // 2
    img_center_y = img_height // 2
    draw.ellipse([img_center_x-5, img_center_y-5, img_center_x+5, img_center_y+5], 
                 fill='yellow', outline='orange', width=2)
    
    # 保存结果
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = f"output_{base_name}_with_bbox_fixed.png"
    
    img.save(output_path)
    print(f"结果已保存到: {output_path}")
    return output_path

def main():
    # 测试数据
    test_image = r"C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png"
    test_bbox = {
        "element": {
            "bounding_box": {
                "height": 82,
                "width": 108,
                "x": 330,
                "y": 388
            },
            "description": "Application icon for ACE software",
            "text_content": ""
        }
    }
    
    # 如果提供了命令行参数
    if len(sys.argv) > 1:
        test_image = sys.argv[1]
        if len(sys.argv) > 2:
            test_bbox = json.loads(sys.argv[2])
    
    # 绘制边界框（使用Gemini格式转换）
    draw_bounding_box(test_image, test_bbox, use_gemini_format=True)

if __name__ == "__main__":
    main()