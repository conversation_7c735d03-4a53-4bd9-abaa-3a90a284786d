# ShowForAI V3 技术设计与实现方案

## 一、安全架构重构设计

### 1.1 密钥管理系统

#### 架构设计
```
┌─────────────────────────────────────────┐
│          密钥管理系统架构                 │
├─────────────────────────────────────────┤
│  ┌─────────────────────────────────┐    │
│  │     环境变量层 (.env)             │    │
│  │  - 开发环境使用                   │    │
│  │  - 从.env.template生成            │    │
│  └─────────────────────────────────┘    │
│              ↓                           │
│  ┌─────────────────────────────────┐    │
│  │     系统密钥库 (Keyring)          │    │
│  │  - 生产环境使用                   │    │
│  │  - 操作系统级加密                 │    │
│  └─────────────────────────────────┘    │
│              ↓                           │
│  ┌─────────────────────────────────┐    │
│  │     加密存储层                    │    │
│  │  - AES-256加密                    │    │
│  │  - PBKDF2密钥派生                 │    │
│  └─────────────────────────────────┘    │
└─────────────────────────────────────────┘
```

#### 实现细节

```python
# secure_key_manager.py
class SecureKeyManager:
    def __init__(self):
        self.env_mode = os.getenv('ENVIRONMENT', 'production')
        self.key_store = self._init_key_store()
        
    def _init_key_store(self):
        if self.env_mode == 'development':
            return EnvKeyStore()  # 从.env读取
        else:
            return SystemKeyStore()  # 使用系统密钥库
            
    def get_api_key(self, key_name: str) -> str:
        """获取API密钥，自动处理加密/解密"""
        # 1. 检查缓存（加密的内存缓存）
        # 2. 从密钥库读取
        # 3. 解密并返回
        # 4. 审计日志记录
        pass
        
    def rotate_key(self, key_name: str) -> None:
        """密钥轮换"""
        # 1. 生成新密钥
        # 2. 更新所有引用
        # 3. 标记旧密钥为过期
        # 4. 审计日志
        pass
```

### 1.2 API安全增强

#### 请求签名流程
```
┌────────────────────────────────────────────┐
│            API请求签名流程                   │
├────────────────────────────────────────────┤
│  1. 构建请求体                               │
│     └─> JSON序列化                          │
│                                             │
│  2. 添加安全头                               │
│     ├─> X-Timestamp: 时间戳                 │
│     ├─> X-Nonce: 随机数                     │
│     └─> X-Device-ID: 设备标识               │
│                                             │
│  3. 计算签名                                 │
│     ├─> 合并: Method + URL + Headers + Body │
│     ├─> HMAC-SHA256签名                     │
│     └─> Base64编码                          │
│                                             │
│  4. 附加签名头                               │
│     └─> X-Signature: 签名值                 │
└────────────────────────────────────────────┘
```

## 二、图像识别质量保证系统

### 2.1 多级阈值管理

#### 阈值配置架构
```yaml
# threshold_config.yaml
recognition_thresholds:
  # 第一级：模板匹配
  template_matching:
    confidence: 0.90  # 提高到0.90
    min_scale: 0.8
    max_scale: 1.2
    
  # 第二级：ORB特征
  orb_matching:
    min_features: 20  # 提高到20
    lowe_ratio: 0.65  # 更严格
    min_inlier_ratio: 0.70  # 提高到70%
    
  # 第三级：SIFT特征
  sift_matching:
    min_features: 25  # 提高到25
    lowe_ratio: 0.60  # 更严格
    min_inlier_ratio: 0.75  # 提高到75%
    
  # 第四级：多尺度
  multiscale_matching:
    confidence: 0.85  # 提高到0.85
    scale_range: 0.15  # 缩小范围到±15%
    scale_step: 0.03  # 更精细的步长
```

#### 质量验证器
```python
class QualityValidator:
    """确保识别质量符合产品原则"""
    
    def validate_match(self, result: MatchResult) -> bool:
        """验证匹配结果是否符合质量标准"""
        # 1. 检查置信度
        if result.confidence < self.get_threshold(result.level):
            return False
            
        # 2. 检查几何一致性
        if not self._check_geometry(result):
            return False
            
        # 3. 检查边界合理性
        if not self._check_boundaries(result):
            return False
            
        # 4. 检查历史一致性（防止跳变）
        if not self._check_consistency(result):
            return False
            
        return True
```

### 2.2 BBOX强制包含规则实现

```python
class BBoxContainmentValidator:
    """BBOX包含验证器"""
    
    def validate_containment(self, 
                            bbox: Dict[str, int],
                            click_x: int, 
                            click_y: int,
                            image_size: int = 768) -> bool:
        """
        验证BBOX是否包含点击坐标
        
        Args:
            bbox: BBOX字典 {"x", "y", "width", "height"}
            click_x, click_y: 点击坐标（已转换到768坐标系）
            image_size: 标准化图像大小
        """
        # 添加容差（考虑点击精度）
        tolerance = 5  # 5像素容差
        
        # 扩展BBOX边界
        expanded_bbox = {
            'x': bbox['x'] - tolerance,
            'y': bbox['y'] - tolerance,
            'width': bbox['width'] + 2 * tolerance,
            'height': bbox['height'] + 2 * tolerance
        }
        
        # 检查包含关系
        contains_x = (expanded_bbox['x'] <= click_x <= 
                     expanded_bbox['x'] + expanded_bbox['width'])
        contains_y = (expanded_bbox['y'] <= click_y <= 
                     expanded_bbox['y'] + expanded_bbox['height'])
        
        if not (contains_x and contains_y):
            logger.warning(
                f"BBOX验证失败: 点击坐标({click_x}, {click_y}) "
                f"不在BBOX {bbox} 内"
            )
            return False
            
        return True
```

## 三、智能等待机制优化

### 3.1 等待策略管理器

```python
class SmartWaitManager:
    """智能等待管理器 - 基于录制间隔"""
    
    def __init__(self):
        self.mode = WaitMode.ACTIVE  # 默认主动模式
        self.initial_wait_times = {}  # 存储每步的初始等待时间
        
    def calculate_wait_time(self, action_index: int) -> float:
        """
        计算等待时间
        
        主动模式: 使用录制时的操作间隔
        辅助模式: 无限等待
        """
        if self.mode == WaitMode.AUXILIARY:
            return float('inf')  # 无限等待
            
        # 主动模式：使用录制间隔
        if action_index in self.initial_wait_times:
            return self.initial_wait_times[action_index]
        else:
            return 0.0  # 第一个动作无需等待
            
    def wait_for_element(self, 
                        template: np.ndarray,
                        initial_wait: float,
                        max_retries: int = None) -> bool:
        """
        等待元素出现
        
        Args:
            template: 要查找的元素模板
            initial_wait: 初始等待时间（录制间隔）
            max_retries: 最大重试次数（None表示无限）
        """
        # 1. 初始等待（模拟用户操作节奏）
        time.sleep(initial_wait)
        
        # 2. 尝试识别
        retry_count = 0
        while True:
            result = self.matcher.match(template)
            
            if result.found:
                logger.info(f"元素找到，耗时: {retry_count}秒")
                return True
                
            # 3. 重试逻辑
            if max_retries and retry_count >= max_retries:
                logger.warning(f"超过最大重试次数: {max_retries}")
                return False
                
            # 4. 等待1秒后重试
            time.sleep(1.0)
            retry_count += 1
            
            # 5. 显示等待提示（超过3秒）
            if retry_count == 3:
                self.show_wait_indicator("正在查找目标元素...")
```

### 3.2 执行节奏控制

```python
class ExecutionRhythmController:
    """执行节奏控制器 - 还原用户操作节奏"""
    
    def __init__(self, recording_data: Dict):
        self.actions = recording_data['actions']
        self.base_timestamp = None
        
    def get_action_interval(self, index: int) -> float:
        """获取动作间隔时间"""
        if index == 0:
            return 0.0
            
        current = self.actions[index]['timestamp']
        previous = self.actions[index - 1]['timestamp']
        
        # 计算实际间隔
        interval = current - previous
        
        # 应用智能调整
        return self._adjust_interval(interval, index)
        
    def _adjust_interval(self, interval: float, index: int) -> float:
        """
        智能调整间隔时间
        
        规则：
        1. 保留用户的思考时间（>2秒的间隔）
        2. 压缩过长的等待（>10秒压缩到5秒）
        3. 保持最小间隔（至少0.1秒）
        """
        if interval < 0.1:
            return 0.1  # 最小间隔
        elif interval > 10.0:
            return 5.0  # 压缩长等待
        elif interval > 2.0:
            return interval * 0.8  # 轻微压缩思考时间
        else:
            return interval  # 保持原始节奏
```

## 四、资源管理与性能优化

### 4.1 内存管理器

```python
class MemoryManager:
    """内存管理器 - 防止内存泄漏"""
    
    def __init__(self):
        self.cache_size_limit = 100 * 1024 * 1024  # 100MB
        self.current_usage = 0
        self.cache_items = OrderedDict()
        
    def add_to_cache(self, key: str, data: bytes) -> None:
        """添加到缓存，自动清理旧数据"""
        size = len(data)
        
        # 检查是否需要清理
        while self.current_usage + size > self.cache_size_limit:
            self._evict_oldest()
            
        # 添加新数据
        self.cache_items[key] = {
            'data': data,
            'size': size,
            'timestamp': time.time()
        }
        self.current_usage += size
        
    def _evict_oldest(self) -> None:
        """移除最旧的缓存项"""
        if not self.cache_items:
            return
            
        key, item = self.cache_items.popitem(last=False)
        self.current_usage -= item['size']
        logger.debug(f"清理缓存: {key}, 释放 {item['size']} bytes")
```

### 4.2 截图缓冲区优化

```python
class OptimizedScreenBuffer:
    """优化的截图缓冲区"""
    
    def __init__(self, max_frames: int = 15):
        self.max_frames = max_frames
        self.buffer = deque(maxlen=max_frames)
        self.lock = threading.Lock()
        
        # 性能监控
        self.stats = {
            'captured_frames': 0,
            'dropped_frames': 0,
            'avg_capture_time': 0
        }
        
    def capture_frame(self) -> None:
        """捕获一帧"""
        start_time = time.perf_counter()
        
        try:
            # 使用更高效的截图方法
            screenshot = self._fast_capture()
            
            with self.lock:
                # 压缩存储（JPEG格式，质量85）
                compressed = self._compress_frame(screenshot)
                
                self.buffer.append({
                    'data': compressed,
                    'timestamp': time.perf_counter(),
                    'size': len(compressed)
                })
                
            # 更新统计
            capture_time = time.perf_counter() - start_time
            self._update_stats(capture_time)
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            self.stats['dropped_frames'] += 1
            
    def _compress_frame(self, frame: np.ndarray) -> bytes:
        """压缩帧数据"""
        # 使用JPEG压缩，平衡质量和大小
        encode_param = [cv2.IMWRITE_JPEG_QUALITY, 85]
        _, encoded = cv2.imencode('.jpg', frame, encode_param)
        return encoded.tobytes()
```

## 五、用户体验增强

### 5.1 进度反馈系统

```python
class ProgressFeedbackSystem:
    """进度反馈系统"""
    
    def __init__(self, ui_callback=None):
        self.ui_callback = ui_callback
        self.current_phase = None
        self.progress = 0
        self.eta = None
        
    def update_progress(self, 
                       phase: str,
                       current: int,
                       total: int,
                       message: str = None) -> None:
        """
        更新进度
        
        Args:
            phase: 当前阶段
            current: 当前步骤
            total: 总步骤数
            message: 可选的状态消息
        """
        self.current_phase = phase
        self.progress = (current / total) * 100 if total > 0 else 0
        
        # 计算预估时间
        self.eta = self._calculate_eta(current, total)
        
        # 构建反馈信息
        feedback = {
            'phase': phase,
            'progress': self.progress,
            'eta': self.eta,
            'message': message or self._get_default_message(phase),
            'current_step': current,
            'total_steps': total
        }
        
        # 更新UI
        if self.ui_callback:
            self.ui_callback(feedback)
            
    def _get_default_message(self, phase: str) -> str:
        """获取默认消息"""
        messages = {
            'recording': '正在录制操作...',
            'uploading': '正在上传录制数据...',
            'ai_processing': '正在识别界面元素...',
            'generating': '正在生成脚本...',
            'executing': '正在执行脚本...',
            'searching': '正在查找目标元素...'
        }
        return messages.get(phase, '处理中...')
```

### 5.2 错误处理优化

```python
class UserFriendlyErrorHandler:
    """用户友好的错误处理器"""
    
    ERROR_MESSAGES = {
        'network_offline': {
            'title': '网络连接失败',
            'message': '无法连接到网络，请检查您的网络设置。',
            'suggestion': '1. 检查网络连接\n2. 检查防火墙设置\n3. 尝试重启应用',
            'recovery': 'retry'  # 可重试
        },
        'ai_service_unavailable': {
            'title': 'AI服务暂时不可用',
            'message': 'AI识别服务暂时无法访问。',
            'suggestion': '1. 稍后重试\n2. 检查服务状态\n3. 联系技术支持',
            'recovery': 'wait'  # 等待恢复
        },
        'element_not_found': {
            'title': '无法找到目标元素',
            'message': '在当前界面未找到预期的元素。',
            'suggestion': '1. 确保界面已完全加载\n2. 检查元素是否可见\n3. 尝试手动操作',
            'recovery': 'manual'  # 手动介入
        }
    }
    
    def handle_error(self, error_code: str, details: Dict = None) -> ErrorResponse:
        """处理错误并返回用户友好的响应"""
        error_info = self.ERROR_MESSAGES.get(
            error_code, 
            self._get_generic_error()
        )
        
        # 记录详细错误（用于调试）
        logger.error(f"Error: {error_code}, Details: {details}")
        
        # 构建用户响应
        response = ErrorResponse(
            title=error_info['title'],
            message=error_info['message'],
            suggestion=error_info['suggestion'],
            recovery_action=error_info['recovery'],
            can_retry=error_info['recovery'] == 'retry',
            details=details if self._is_debug_mode() else None
        )
        
        # 显示错误对话框
        self._show_error_dialog(response)
        
        return response
```

## 六、离线模式完善

### 6.1 离线功能边界管理

```python
class OfflineModeManager:
    """离线模式管理器"""
    
    # 离线可用功能
    OFFLINE_FEATURES = [
        'execute_script',      # 执行已下载脚本
        'view_history',        # 查看历史记录
        'manage_local_scripts', # 管理本地脚本
        'settings',            # 设置调整
        'local_login'          # 本地登录
    ]
    
    # 需要网络的功能
    ONLINE_FEATURES = [
        'record',              # 录制（需要AI）
        'upload',              # 上传
        'sync',                # 同步
        'share',               # 分享
        'download_new'         # 下载新脚本
    ]
    
    def check_feature_availability(self, feature: str) -> FeatureStatus:
        """检查功能可用性"""
        is_online = self.network_checker.is_online()
        
        if feature in self.OFFLINE_FEATURES:
            return FeatureStatus.AVAILABLE
            
        if feature in self.ONLINE_FEATURES:
            if is_online:
                # 进一步检查具体服务
                if feature == 'record':
                    # 录制需要AI服务
                    if self.check_ai_service():
                        return FeatureStatus.AVAILABLE
                    else:
                        return FeatureStatus.UNAVAILABLE(
                            reason="AI服务不可用",
                            message="录制需要AI服务进行元素识别"
                        )
                else:
                    return FeatureStatus.AVAILABLE
            else:
                return FeatureStatus.UNAVAILABLE(
                    reason="离线状态",
                    message=self._get_offline_message(feature)
                )
                
        return FeatureStatus.UNKNOWN
```

### 6.2 离线UI提示系统

```python
class OfflineUIIndicator:
    """离线UI指示器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.status_widget = self._create_status_widget()
        self.is_online = True
        
    def update_status(self, is_online: bool) -> None:
        """更新在线状态"""
        if is_online != self.is_online:
            self.is_online = is_online
            self._update_ui()
            
    def _update_ui(self) -> None:
        """更新UI显示"""
        if self.is_online:
            # 在线状态
            self.status_widget.setStyleSheet("""
                QWidget {
                    background-color: #4CAF50;
                    color: white;
                }
            """)
            self.status_widget.setText("在线")
            
            # 启用在线功能
            self._enable_online_features()
        else:
            # 离线状态
            self.status_widget.setStyleSheet("""
                QWidget {
                    background-color: #FF9800;
                    color: white;
                }
            """)
            self.status_widget.setText("离线模式")
            
            # 禁用在线功能
            self._disable_online_features()
            
    def _disable_online_features(self) -> None:
        """禁用需要网络的功能"""
        # 禁用录制按钮
        self.main_window.record_button.setEnabled(False)
        self.main_window.record_button.setToolTip(
            "录制需要网络连接以进行AI识别"
        )
        
        # 添加离线模式提示
        self._show_offline_banner()
```

## 七、监控与诊断系统

### 7.1 性能监控

```python
class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'response_times': [],
            'frame_rates': [],
            'recognition_accuracy': []
        }
        self.monitoring = False
        
    def start_monitoring(self) -> None:
        """开始监控"""
        self.monitoring = True
        threading.Thread(target=self._monitor_loop, daemon=True).start()
        
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics['cpu_usage'].append({
                'timestamp': time.time(),
                'value': cpu_percent
            })
            
            # 内存使用
            memory = psutil.Process().memory_info()
            self.metrics['memory_usage'].append({
                'timestamp': time.time(),
                'value': memory.rss / 1024 / 1024  # MB
            })
            
            # 检查异常
            self._check_anomalies()
            
            time.sleep(5)  # 5秒采样一次
            
    def _check_anomalies(self) -> None:
        """检查异常"""
        # CPU过高
        recent_cpu = [m['value'] for m in self.metrics['cpu_usage'][-10:]]
        if recent_cpu and sum(recent_cpu) / len(recent_cpu) > 80:
            logger.warning("CPU使用率过高")
            
        # 内存泄漏检测
        recent_memory = [m['value'] for m in self.metrics['memory_usage'][-20:]]
        if len(recent_memory) >= 20:
            # 检查内存是否持续增长
            if all(recent_memory[i] <= recent_memory[i+1] 
                   for i in range(len(recent_memory)-1)):
                logger.warning("可能存在内存泄漏")
```

## 八、测试策略

### 8.1 单元测试框架

```python
# test_quality_validator.py
class TestQualityValidator(unittest.TestCase):
    """质量验证器测试"""
    
    def test_threshold_enforcement(self):
        """测试阈值强制执行"""
        validator = QualityValidator()
        
        # 测试低于阈值的情况
        low_confidence_result = MatchResult(confidence=0.79)
        self.assertFalse(validator.validate_match(low_confidence_result))
        
        # 测试高于阈值的情况
        high_confidence_result = MatchResult(confidence=0.91)
        self.assertTrue(validator.validate_match(high_confidence_result))
        
    def test_bbox_containment(self):
        """测试BBOX包含规则"""
        validator = BBoxContainmentValidator()
        
        bbox = {'x': 100, 'y': 100, 'width': 50, 'height': 50}
        
        # 测试包含的情况
        self.assertTrue(validator.validate_containment(bbox, 125, 125))
        
        # 测试不包含的情况
        self.assertFalse(validator.validate_containment(bbox, 200, 200))
        
        # 测试边界情况
        self.assertTrue(validator.validate_containment(bbox, 100, 100))
        self.assertTrue(validator.validate_containment(bbox, 150, 150))
```

### 8.2 集成测试

```python
# test_end_to_end.py
class TestEndToEndFlow(unittest.TestCase):
    """端到端流程测试"""
    
    def test_record_and_execute(self):
        """测试录制和执行完整流程"""
        # 1. 模拟录制
        recorder = Recorder()
        recorder.start_recording()
        
        # 模拟用户操作
        self._simulate_user_actions()
        
        recorder.stop_recording()
        
        # 2. 验证录制数据
        recording_data = self._load_recording_data()
        self.assertIsNotNone(recording_data)
        self.assertGreater(len(recording_data['actions']), 0)
        
        # 3. 执行脚本
        executor = ScriptExecutor()
        result = executor.execute(recording_data)
        
        # 4. 验证执行结果
        self.assertTrue(result.success)
        self.assertEqual(result.executed_actions, len(recording_data['actions']))
```

## 九、部署与发布策略

### 9.1 版本管理

```yaml
# version_config.yaml
version:
  major: 3
  minor: 1
  patch: 0
  build: "${BUILD_NUMBER}"
  
features:
  - name: "enhanced_security"
    enabled: true
    rollout_percentage: 100
    
  - name: "improved_recognition"
    enabled: true
    rollout_percentage: 50  # A/B测试
    
  - name: "offline_mode"
    enabled: true
    rollout_percentage: 100
```

### 9.2 发布检查清单

```python
class ReleaseValidator:
    """发布验证器"""
    
    CHECKLIST = [
        ('security_scan', '安全扫描通过'),
        ('unit_tests', '单元测试通过'),
        ('integration_tests', '集成测试通过'),
        ('performance_tests', '性能测试达标'),
        ('documentation', '文档更新完成'),
        ('changelog', '更新日志准备'),
        ('backwards_compatibility', '向后兼容性验证')
    ]
    
    def validate_release(self) -> ReleaseReport:
        """验证发布准备情况"""
        report = ReleaseReport()
        
        for check_id, description in self.CHECKLIST:
            result = self._run_check(check_id)
            report.add_check(check_id, description, result)
            
        return report
```

## 十、维护与支持

### 10.1 日志收集系统

```python
class LogCollector:
    """日志收集器"""
    
    def collect_diagnostic_info(self) -> Dict:
        """收集诊断信息"""
        return {
            'system_info': self._get_system_info(),
            'app_version': self._get_app_version(),
            'recent_logs': self._get_recent_logs(),
            'performance_metrics': self._get_performance_metrics(),
            'error_summary': self._get_error_summary(),
            'configuration': self._get_sanitized_config()
        }
        
    def _get_sanitized_config(self) -> Dict:
        """获取清理过的配置（移除敏感信息）"""
        config = Config().get_all()
        
        # 移除敏感字段
        sensitive_keys = ['api_key', 'secret', 'password', 'token']
        for key in list(config.keys()):
            if any(s in key.lower() for s in sensitive_keys):
                config[key] = '***REDACTED***'
                
        return config
```

这个设计文档提供了详细的技术实现方案，涵盖了所有发现的问题和改进点。每个模块都有具体的代码示例和架构设计。