# Contributing to ShowForAI

Thank you for considering contributing to ShowForAI! This document provides guidelines and information for contributors.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Setup](#development-setup)
4. [How to Contribute](#how-to-contribute)
5. [Coding Standards](#coding-standards)
6. [Testing Guidelines](#testing-guidelines)
7. [Documentation](#documentation)
8. [Pull Request Process](#pull-request-process)
9. [Issue Reporting](#issue-reporting)
10. [Release Process](#release-process)
11. [Community](#community)

## Code of Conduct

This project adheres to a code of conduct that all contributors are expected to follow. Please read [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) before contributing.

### Our Pledge

We are committed to making participation in this project a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

## Getting Started

### Prerequisites

Before contributing, ensure you have:

1. **Development Environment**
   - Node.js 18 or later
   - Rust (latest stable)
   - Git
   - Platform-specific dependencies (see [Developer Setup Guide](docs/developer/setup.md))

2. **Accounts**
   - GitHub account
   - (Optional) Discord for community discussions

### First Contribution

Looking for your first contribution? Here are some good starting points:

- **Good First Issues**: Look for issues labeled `good first issue`
- **Documentation**: Improve or expand documentation
- **Tests**: Add test coverage for existing features
- **Bug Fixes**: Fix small, well-defined bugs
- **Performance**: Optimize existing code

## Development Setup

### Quick Setup

```bash
# Clone the repository
git clone https://github.com/showforai/showforai.git
cd showforai

# Install dependencies
npm install

# Start development
npm run dev
npm run tauri dev  # In another terminal
```

For detailed setup instructions, see the [Developer Setup Guide](docs/developer/setup.md).

### Development Workflow

1. **Fork and Clone**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/showforai.git
   cd showforai
   git remote add upstream https://github.com/showforai/showforai.git
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b fix/issue-description
   ```

3. **Make Changes**
   - Write code following our standards
   - Add tests for new functionality
   - Update documentation as needed

4. **Test Changes**
   ```bash
   npm run test:all
   npm run lint
   npm run build
   ```

5. **Commit and Push**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   git push origin feature/your-feature-name
   ```

6. **Create Pull Request**
   - Go to GitHub and create a pull request
   - Fill out the PR template completely
   - Link related issues

## How to Contribute

### Types of Contributions

1. **Bug Reports**
   - Use the bug report template
   - Include steps to reproduce
   - Provide system information
   - Attach logs and screenshots

2. **Feature Requests**
   - Use the feature request template
   - Explain the use case
   - Provide mockups or examples
   - Discuss implementation approach

3. **Code Contributions**
   - Bug fixes
   - New features
   - Performance improvements
   - Code refactoring

4. **Documentation**
   - API documentation
   - User guides
   - Developer documentation
   - Code comments

5. **Testing**
   - Unit tests
   - Integration tests
   - E2E tests
   - Performance tests

### Contribution Areas

#### Frontend (React/TypeScript)
- UI components and interactions
- State management with Zustand
- API integration
- Responsive design
- Accessibility improvements

#### Backend (Rust)
- Core automation engine
- Image processing algorithms
- File system operations
- Performance optimizations
- Security enhancements

#### Cross-Platform Support
- Windows-specific features
- macOS-specific features
- Linux distribution support
- Permission handling

#### Testing and Quality
- Test coverage improvements
- CI/CD enhancements
- Performance benchmarking
- Security auditing

## Coding Standards

### General Principles

1. **Clarity over Cleverness**: Write clear, readable code
2. **Consistency**: Follow established patterns
3. **Performance**: Consider performance implications
4. **Security**: Always consider security implications
5. **Documentation**: Document complex logic

### TypeScript/JavaScript Standards

```typescript
// Use meaningful names
const isRecordingActive = true;
const recordingDuration = 5000;

// Prefer const for immutable values
const API_ENDPOINTS = {
  RECORDING: '/api/recording',
  EXECUTION: '/api/execution'
} as const;

// Use TypeScript types
interface RecordingSettings {
  quality: 'low' | 'medium' | 'high';
  captureInterval: number;
  includeClicks: boolean;
}

// Document complex functions
/**
 * Processes recording data and generates workflow steps
 * @param recordingData Raw recording data from the capture engine
 * @param settings Processing settings and filters
 * @returns Processed workflow steps ready for execution
 */
function processRecordingData(
  recordingData: RawRecordingData,
  settings: ProcessingSettings
): WorkflowStep[] {
  // Implementation
}
```

### Rust Standards

```rust
// Use descriptive names
struct RecordingSettings {
    quality: ImageQuality,
    capture_interval: Duration,
    include_clicks: bool,
}

// Document public APIs
/// Captures a screenshot of the specified region
/// 
/// # Arguments
/// * `x` - X coordinate of the top-left corner
/// * `y` - Y coordinate of the top-left corner
/// * `width` - Width of the capture region
/// * `height` - Height of the capture region
/// 
/// # Returns
/// * `Result<Image, CaptureError>` - The captured image or an error
pub fn capture_region(x: i32, y: i32, width: u32, height: u32) -> Result<Image, CaptureError> {
    // Implementation
}

// Use proper error handling
fn process_action(action: &Action) -> Result<ProcessedAction, ProcessingError> {
    let processed = action.validate()
        .map_err(ProcessingError::ValidationFailed)?
        .transform()
        .map_err(ProcessingError::TransformFailed)?;
    
    Ok(processed)
}

// Prefer iterators over loops
let processed_actions: Vec<ProcessedAction> = actions
    .iter()
    .filter(|action| action.is_valid())
    .map(|action| process_action(action))
    .collect::<Result<Vec<_>, _>>()?;
```

### File Organization

```
src/
├── components/
│   ├── recording/          # Recording-related components
│   ├── execution/          # Execution-related components
│   ├── shared/            # Shared/common components
│   └── layout/            # Layout components
├── pages/                 # Page components
├── stores/                # Zustand stores
├── services/              # API and external services
├── utils/                 # Utility functions
└── types/                 # TypeScript type definitions

src-tauri/src/
├── api/                   # Tauri command handlers
├── modules/               # Core functionality modules
│   ├── recording/         # Recording engine
│   ├── execution/         # Execution engine
│   ├── image_processing/  # Image processing
│   └── management/        # Workflow management
└── utils/                 # Utility functions
```

## Testing Guidelines

### Testing Strategy

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test module interactions
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Benchmark critical operations

### Frontend Testing

```typescript
// Component testing with React Testing Library
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import RecordButton from '../RecordButton';

describe('RecordButton', () => {
  it('should start recording when clicked', async () => {
    const mockStartRecording = vi.fn();
    
    render(<RecordButton onStartRecording={mockStartRecording} />);
    
    const button = screen.getByRole('button', { name: /start recording/i });
    fireEvent.click(button);
    
    expect(mockStartRecording).toHaveBeenCalledOnce();
  });
});

// Store testing
describe('RecordingStore', () => {
  it('should update recording state correctly', () => {
    const store = useRecordingStore.getState();
    
    store.startRecording();
    expect(store.isRecording).toBe(true);
    
    store.stopRecording();
    expect(store.isRecording).toBe(false);
  });
});
```

### Backend Testing

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_recording_workflow() {
        let temp_dir = TempDir::new().unwrap();
        let mut recorder = ActionRecorder::new(temp_dir.path().to_path_buf());
        
        recorder.start_recording().unwrap();
        recorder.record_click_action(100, 200, "left").unwrap();
        let actions = recorder.stop_recording().unwrap();
        
        assert_eq!(actions.len(), 1);
        assert_eq!(actions[0].action_type, "click");
    }
    
    #[test]
    fn test_image_matching() {
        let matcher = ImageMatcher::new();
        let template = create_test_image(50, 50);
        let screenshot = create_test_screenshot(200, 200, &template, 75, 75);
        
        let result = matcher.find_match(&template, &screenshot, 0.8);
        assert!(result.is_some());
        
        let match_result = result.unwrap();
        assert_eq!(match_result.x, 75);
        assert_eq!(match_result.y, 75);
        assert!(match_result.confidence > 0.8);
    }
}
```

### Test Coverage Requirements

- **Minimum Coverage**: 80% for new code
- **Critical Paths**: 95% coverage required
- **UI Components**: Focus on user interactions
- **API Endpoints**: Test all success and error cases

### Running Tests

```bash
# Run all tests
npm run test:all

# Run specific test suites
npm run test                # Frontend unit tests
npm run test:tauri         # Backend tests
npm run test:e2e           # E2E tests

# Coverage reports
npm run test:coverage      # Generate coverage report

# Watch mode for development
npm run test -- --watch
```

## Documentation

### Documentation Standards

1. **Code Comments**
   - Document complex algorithms
   - Explain business logic
   - Include examples for APIs

2. **API Documentation**
   - Complete parameter descriptions
   - Example requests and responses
   - Error code explanations

3. **User Documentation**
   - Step-by-step instructions
   - Screenshots and videos
   - Troubleshooting guides

### Writing Guidelines

- Use clear, concise language
- Provide examples and code snippets
- Include screenshots for UI features
- Keep documentation up-to-date with code changes

## Pull Request Process

### Before Submitting

1. **Check Requirements**
   - [ ] Code follows style guidelines
   - [ ] Tests pass locally
   - [ ] Documentation updated
   - [ ] No merge conflicts

2. **Test Checklist**
   - [ ] Unit tests added/updated
   - [ ] Integration tests pass
   - [ ] E2E tests pass (if applicable)
   - [ ] Performance impact assessed

### PR Template

Use the provided PR template and include:

- **Description**: Clear explanation of changes
- **Type of Change**: Bug fix, feature, documentation, etc.
- **Testing**: How the changes were tested
- **Screenshots**: For UI changes
- **Breaking Changes**: Any backward compatibility issues

### Review Process

1. **Automated Checks**
   - CI/CD pipeline must pass
   - Code coverage requirements met
   - Security scans pass

2. **Code Review**
   - At least one reviewer approval required
   - Core team review for significant changes
   - Address all review comments

3. **Testing**
   - Manual testing by reviewers
   - Cross-platform testing when relevant

### Merge Requirements

- [ ] All CI checks pass
- [ ] Code review approved
- [ ] Documentation updated
- [ ] No unresolved conflicts
- [ ] Branch up-to-date with target

## Issue Reporting

### Bug Reports

Include the following information:

```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
 - OS: [e.g. Windows 11, macOS 12.0, Ubuntu 20.04]
 - ShowForAI Version: [e.g. 1.0.0]
 - Node.js Version: [e.g. 18.17.0]
 - Additional context: [e.g. using virtual machine, external monitors]

**Logs**
Paste relevant log files or error messages.
```

### Feature Requests

```markdown
**Is your feature request related to a problem?**
A clear description of what the problem is.

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
A clear description of alternative solutions or features you've considered.

**Additional context**
Add any other context, mockups, or examples about the feature request.

**Implementation ideas**
If you have ideas about how this could be implemented, please share them.
```

### Issue Labels

- `bug`: Something isn't working
- `enhancement`: New feature or request
- `documentation`: Improvements or additions to documentation
- `good first issue`: Good for newcomers
- `help wanted`: Extra attention is needed
- `priority:high`: High priority issue
- `platform:windows`: Windows-specific issue
- `platform:macos`: macOS-specific issue
- `platform:linux`: Linux-specific issue

## Release Process

### Version Numbering

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR.MINOR.PATCH** (e.g., 1.2.3)
- **Pre-release**: 1.2.3-alpha.1, 1.2.3-beta.2, 1.2.3-rc.1

### Release Schedule

- **Major releases**: Every 6-12 months
- **Minor releases**: Every 2-3 months
- **Patch releases**: As needed for critical fixes

### Release Checklist

1. **Pre-release**
   - [ ] Update version numbers
   - [ ] Update CHANGELOG.md
   - [ ] Run full test suite
   - [ ] Update documentation
   - [ ] Security audit

2. **Release**
   - [ ] Create release branch
   - [ ] Build all platform packages
   - [ ] Test installation packages
   - [ ] Create GitHub release
   - [ ] Update auto-updater

3. **Post-release**
   - [ ] Monitor for issues
   - [ ] Update website
   - [ ] Announce release
   - [ ] Merge back to develop

## Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Discord**: Real-time chat and community support
- **Email**: <EMAIL> for security issues

### Community Guidelines

1. **Be Respectful**: Treat all community members with respect
2. **Be Helpful**: Share knowledge and help others
3. **Be Patient**: Remember that everyone is learning
4. **Be Constructive**: Provide constructive feedback
5. **Follow Guidelines**: Adhere to platform-specific rules

### Recognition

We recognize contributions through:
- **Contributors list** in the repository
- **Release notes** acknowledgments
- **Special badges** for significant contributions
- **Maintainer status** for long-term contributors

## Getting Help

If you need help with contributing:

1. **Read the Documentation**: Check docs/ directory
2. **Search Issues**: Look for similar questions
3. **Ask in Discussions**: Use GitHub Discussions
4. **Join Discord**: Get real-time help
5. **Contact Maintainers**: For complex issues

### Maintainers

Current project maintainers:
- [@maintainer1](https://github.com/maintainer1) - Project Lead
- [@maintainer2](https://github.com/maintainer2) - Frontend Lead
- [@maintainer3](https://github.com/maintainer3) - Backend Lead

## Thank You

Thank you for contributing to ShowForAI! Your contributions help make this project better for everyone. We appreciate your time, effort, and expertise.

---

*This contributing guide is a living document. Please suggest improvements or ask questions if anything is unclear.*