{"version": "2.0", "metadata": {"created_at": "2025-08-14T01:34:26.486701", "ai_processed": true, "confidence_threshold": 0.8, "original_recording": {"session_id": "test_session", "screen_resolution": "768x768", "device_id": "test_device", "recorded_at": "2025-08-14T01:34:26.486635", "duration_ms": 5000}, "coordinate_system": "768x768_standardized", "bbox_resolution": "768x768", "script_id": "56bcdd9058284ac6"}, "actions": [{"type": "click", "sequence": 1, "timestamp": 0, "position": {"x": 50, "y": 50}, "screenshot": "test_output\\test_screenshot_768.png", "element_bbox": {"x": 50, "y": 50, "width": 100, "height": 40}, "detection_confidence": 0.95, "bbox_coordinate_system": "768x768", "element_image_cached": "57339f41d0f32754", "element_image_path": "C:\\Users\\<USER>\\.showforai\\element_cache\\scripts\\56bcdd9058284ac6\\57339f41d0f32754.png", "fallback_strategy": "element_match", "coordinates_standardized": true, "execution_hints": {"retry_count": 1, "wait_before": 0.5, "wait_after": 0.5, "verify_clickable": true, "scroll_into_view": true}}, {"type": "click", "sequence": 2, "timestamp": 1000, "position": {"x": 200, "y": 50}, "screenshot": "test_output\\test_screenshot_768.png", "element_bbox": {"x": 200, "y": 50, "width": 120, "height": 40}, "detection_confidence": 0.95, "bbox_coordinate_system": "768x768", "element_image_cached": "31882ee5044b2b8c", "element_image_path": "C:\\Users\\<USER>\\.showforai\\element_cache\\scripts\\56bcdd9058284ac6\\31882ee5044b2b8c.png", "fallback_strategy": "element_match", "coordinates_standardized": true, "execution_hints": {"retry_count": 1, "wait_before": 0.5, "wait_after": 0.5, "verify_clickable": true, "scroll_into_view": true}}, {"type": "click", "sequence": 3, "timestamp": 2000, "position": {"x": 50, "y": 150}, "screenshot": "test_output\\test_screenshot_768.png", "element_bbox": {"x": 50, "y": 150, "width": 200, "height": 30}, "detection_confidence": 0.95, "bbox_coordinate_system": "768x768", "element_image_cached": "f82ef3a544778c6a", "element_image_path": "C:\\Users\\<USER>\\.showforai\\element_cache\\scripts\\56bcdd9058284ac6\\f82ef3a544778c6a.png", "fallback_strategy": "element_match", "coordinates_standardized": true, "execution_hints": {"retry_count": 1, "wait_before": 0.5, "wait_after": 0.5, "verify_clickable": true, "scroll_into_view": true}}, {"type": "click", "sequence": 4, "timestamp": 3000, "position": {"x": 300, "y": 300}, "screenshot": "test_output\\test_screenshot_768.png", "element_bbox": {"x": 300, "y": 300, "width": 150, "height": 150}, "detection_confidence": 0.95, "bbox_coordinate_system": "768x768", "element_image_cached": "20750490b4d3ad33", "element_image_path": "C:\\Users\\<USER>\\.showforai\\element_cache\\scripts\\56bcdd9058284ac6\\20750490b4d3ad33.png", "fallback_strategy": "element_match", "coordinates_standardized": true, "execution_hints": {"retry_count": 1, "wait_before": 0.5, "wait_after": 0.5, "verify_clickable": true, "scroll_into_view": true}}], "settings": {"use_ai_detection": true, "fallback_to_position": true, "element_match_threshold": 0.7, "wait_timeout": 10}, "element_cache": {"script_id": "56bcdd9058284ac6", "cache_dir": "C:\\Users\\<USER>\\.showforai\\element_cache\\scripts\\56bcdd9058284ac6"}, "statistics": {"total_actions": 4, "ai_enhanced_actions": 4, "average_confidence": 0.95, "action_types": {"click": 4}, "fallback_strategies": {"element_match": 4}}}