# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetMetricDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'int',
        'group_by': 'list[str]',
        'instances': 'list[InstanceForGetMetricDataInput]',
        'metric_name': 'str',
        'namespace': 'str',
        'period': 'str',
        'start_time': 'int',
        'sub_namespace': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'group_by': 'GroupBy',
        'instances': 'Instances',
        'metric_name': 'MetricName',
        'namespace': 'Namespace',
        'period': 'Period',
        'start_time': 'StartTime',
        'sub_namespace': 'SubNamespace'
    }

    def __init__(self, end_time=None, group_by=None, instances=None, metric_name=None, namespace=None, period=None, start_time=None, sub_namespace=None, _configuration=None):  # noqa: E501
        """GetMetricDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._group_by = None
        self._instances = None
        self._metric_name = None
        self._namespace = None
        self._period = None
        self._start_time = None
        self._sub_namespace = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        if group_by is not None:
            self.group_by = group_by
        if instances is not None:
            self.instances = instances
        self.metric_name = metric_name
        self.namespace = namespace
        if period is not None:
            self.period = period
        if start_time is not None:
            self.start_time = start_time
        self.sub_namespace = sub_namespace

    @property
    def end_time(self):
        """Gets the end_time of this GetMetricDataRequest.  # noqa: E501


        :return: The end_time of this GetMetricDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GetMetricDataRequest.


        :param end_time: The end_time of this GetMetricDataRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def group_by(self):
        """Gets the group_by of this GetMetricDataRequest.  # noqa: E501


        :return: The group_by of this GetMetricDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._group_by

    @group_by.setter
    def group_by(self, group_by):
        """Sets the group_by of this GetMetricDataRequest.


        :param group_by: The group_by of this GetMetricDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._group_by = group_by

    @property
    def instances(self):
        """Gets the instances of this GetMetricDataRequest.  # noqa: E501


        :return: The instances of this GetMetricDataRequest.  # noqa: E501
        :rtype: list[InstanceForGetMetricDataInput]
        """
        return self._instances

    @instances.setter
    def instances(self, instances):
        """Sets the instances of this GetMetricDataRequest.


        :param instances: The instances of this GetMetricDataRequest.  # noqa: E501
        :type: list[InstanceForGetMetricDataInput]
        """

        self._instances = instances

    @property
    def metric_name(self):
        """Gets the metric_name of this GetMetricDataRequest.  # noqa: E501


        :return: The metric_name of this GetMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._metric_name

    @metric_name.setter
    def metric_name(self, metric_name):
        """Sets the metric_name of this GetMetricDataRequest.


        :param metric_name: The metric_name of this GetMetricDataRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and metric_name is None:
            raise ValueError("Invalid value for `metric_name`, must not be `None`")  # noqa: E501

        self._metric_name = metric_name

    @property
    def namespace(self):
        """Gets the namespace of this GetMetricDataRequest.  # noqa: E501


        :return: The namespace of this GetMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this GetMetricDataRequest.


        :param namespace: The namespace of this GetMetricDataRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and namespace is None:
            raise ValueError("Invalid value for `namespace`, must not be `None`")  # noqa: E501

        self._namespace = namespace

    @property
    def period(self):
        """Gets the period of this GetMetricDataRequest.  # noqa: E501


        :return: The period of this GetMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this GetMetricDataRequest.


        :param period: The period of this GetMetricDataRequest.  # noqa: E501
        :type: str
        """

        self._period = period

    @property
    def start_time(self):
        """Gets the start_time of this GetMetricDataRequest.  # noqa: E501


        :return: The start_time of this GetMetricDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GetMetricDataRequest.


        :param start_time: The start_time of this GetMetricDataRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def sub_namespace(self):
        """Gets the sub_namespace of this GetMetricDataRequest.  # noqa: E501


        :return: The sub_namespace of this GetMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sub_namespace

    @sub_namespace.setter
    def sub_namespace(self, sub_namespace):
        """Sets the sub_namespace of this GetMetricDataRequest.


        :param sub_namespace: The sub_namespace of this GetMetricDataRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and sub_namespace is None:
            raise ValueError("Invalid value for `sub_namespace`, must not be `None`")  # noqa: E501

        self._sub_namespace = sub_namespace

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetMetricDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetMetricDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetMetricDataRequest):
            return True

        return self.to_dict() != other.to_dict()
