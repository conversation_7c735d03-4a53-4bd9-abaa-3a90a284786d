"""
Test suite for 768 Standardizer Module
Tests image standardization, coordinate conversion, BBOX scaling, and different resolution inputs
"""

import unittest
import numpy as np
from PIL import Image
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.standardizer import Standardizer


class TestStandardizer(unittest.TestCase):
    """Test 768 Standardizer functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.standardizer = Standardizer()
        
        # Create test images of various resolutions
        self.test_resolutions = [
            (1920, 1080),  # Full HD
            (1366, 768),   # Common laptop
            (2560, 1440),  # 2K
            (3840, 2160),  # 4K
            (800, 600),    # Small resolution
            (768, 768),    # Square (target)
        ]
        
        self.test_images = {}
        for width, height in self.test_resolutions:
            img = Image.new('RGB', (width, height), color='white')
            self.test_images[f"{width}x{height}"] = img
            
    def test_image_standardization(self):
        """Test image standardization to 768x768"""
        for resolution, img in self.test_images.items():
            standardized = self.standardizer.standardize_image(img)
            
            # Check output dimensions
            self.assertEqual(standardized.size, (768, 768))
            
            # Check image mode is preserved
            self.assertEqual(standardized.mode, img.mode)
            
    def test_coordinate_conversion(self):
        """Test coordinate conversion between resolutions"""
        # Test conversion from 1920x1080 to 768x768
        original_coords = (960, 540)  # Center of 1920x1080
        converted = self.standardizer.convert_coordinates(
            original_coords, 
            from_resolution=(1920, 1080),
            to_resolution=(768, 768)
        )
        
        # Should be proportionally scaled
        expected_x = 960 * 768 / 1920
        expected_y = 540 * 768 / 1080
        self.assertAlmostEqual(converted[0], expected_x, places=1)
        self.assertAlmostEqual(converted[1], expected_y, places=1)
        
    def test_reverse_coordinate_conversion(self):
        """Test reverse coordinate conversion"""
        # Convert from 768x768 back to original resolution
        standardized_coords = (384, 384)  # Center of 768x768
        
        for width, height in self.test_resolutions:
            original = self.standardizer.convert_coordinates(
                standardized_coords,
                from_resolution=(768, 768),
                to_resolution=(width, height)
            )
            
            # Verify center maps to center
            if standardized_coords == (384, 384):
                self.assertAlmostEqual(original[0], width / 2, places=1)
                self.assertAlmostEqual(original[1], height / 2, places=1)
                
    def test_bbox_scaling(self):
        """Test bounding box scaling"""
        # Original bbox in 1920x1080
        original_bbox = {
            'x': 100,
            'y': 100,
            'width': 200,
            'height': 150
        }
        
        # Scale to 768x768
        scaled_bbox = self.standardizer.scale_bbox(
            original_bbox,
            from_resolution=(1920, 1080),
            to_resolution=(768, 768)
        )
        
        # Check scaling ratios
        x_ratio = 768 / 1920
        y_ratio = 768 / 1080
        
        self.assertAlmostEqual(scaled_bbox['x'], 100 * x_ratio, places=1)
        self.assertAlmostEqual(scaled_bbox['y'], 100 * y_ratio, places=1)
        self.assertAlmostEqual(scaled_bbox['width'], 200 * x_ratio, places=1)
        self.assertAlmostEqual(scaled_bbox['height'], 150 * y_ratio, places=1)
        
    def test_aspect_ratio_preservation(self):
        """Test that aspect ratio is handled correctly"""
        # Test image with different aspect ratio
        img = Image.new('RGB', (1920, 1080))
        standardized = self.standardizer.standardize_image(img, preserve_aspect=True)
        
        # Should fit within 768x768 while preserving aspect
        self.assertLessEqual(standardized.width, 768)
        self.assertLessEqual(standardized.height, 768)
        
        # At least one dimension should be 768
        self.assertTrue(
            standardized.width == 768 or standardized.height == 768
        )
        
    def test_batch_standardization(self):
        """Test batch image standardization"""
        images = list(self.test_images.values())
        
        standardized_batch = self.standardizer.standardize_batch(images)
        
        # Check all images are standardized
        self.assertEqual(len(standardized_batch), len(images))
        
        for img in standardized_batch:
            self.assertEqual(img.size, (768, 768))
            
    def test_numpy_array_input(self):
        """Test standardization with numpy array input"""
        # Create numpy array representation
        np_image = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
        
        standardized = self.standardizer.standardize_numpy(np_image)
        
        # Check output shape
        self.assertEqual(standardized.shape, (768, 768, 3))
        
    def test_coordinate_boundary_conditions(self):
        """Test coordinate conversion at boundaries"""
        test_cases = [
            ((0, 0), (1920, 1080)),      # Top-left corner
            ((1919, 1079), (1920, 1080)), # Bottom-right corner
            ((1920, 1080), (1920, 1080)), # Just outside boundary
        ]
        
        for coords, resolution in test_cases:
            converted = self.standardizer.convert_coordinates(
                coords,
                from_resolution=resolution,
                to_resolution=(768, 768)
            )
            
            # Ensure coordinates are within bounds
            self.assertGreaterEqual(converted[0], 0)
            self.assertLessEqual(converted[0], 768)
            self.assertGreaterEqual(converted[1], 0)
            self.assertLessEqual(converted[1], 768)
            
    def test_bbox_validation(self):
        """Test bounding box validation after scaling"""
        # Test with bbox that extends beyond image boundaries
        oversized_bbox = {
            'x': 1800,
            'y': 1000,
            'width': 200,
            'height': 100
        }
        
        scaled = self.standardizer.scale_bbox(
            oversized_bbox,
            from_resolution=(1920, 1080),
            to_resolution=(768, 768),
            validate=True
        )
        
        # Should be clipped to image boundaries
        self.assertLessEqual(scaled['x'] + scaled['width'], 768)
        self.assertLessEqual(scaled['y'] + scaled['height'], 768)
        
    def test_resolution_detection(self):
        """Test automatic resolution detection from image"""
        for resolution, img in self.test_images.items():
            detected = self.standardizer.detect_resolution(img)
            width, height = resolution.split('x')
            self.assertEqual(detected, (int(width), int(height)))
            
    def test_standardization_quality(self):
        """Test different quality settings for standardization"""
        img = self.test_images["1920x1080"]
        
        # Test different resampling methods
        quality_settings = [
            Image.NEAREST,
            Image.BILINEAR,
            Image.BICUBIC,
            Image.LANCZOS
        ]
        
        for quality in quality_settings:
            standardized = self.standardizer.standardize_image(
                img, 
                resample=quality
            )
            self.assertEqual(standardized.size, (768, 768))
            
    def test_grayscale_standardization(self):
        """Test standardization of grayscale images"""
        gray_img = Image.new('L', (1920, 1080), color=128)
        standardized = self.standardizer.standardize_image(gray_img)
        
        self.assertEqual(standardized.size, (768, 768))
        self.assertEqual(standardized.mode, 'L')
        
    def test_rgba_standardization(self):
        """Test standardization of RGBA images with transparency"""
        rgba_img = Image.new('RGBA', (1920, 1080), color=(255, 255, 255, 128))
        standardized = self.standardizer.standardize_image(rgba_img)
        
        self.assertEqual(standardized.size, (768, 768))
        self.assertEqual(standardized.mode, 'RGBA')
        
    def test_performance_benchmarks(self):
        """Test performance of standardization operations"""
        import time
        
        # Large image for performance testing
        large_img = Image.new('RGB', (3840, 2160))
        
        start_time = time.time()
        for _ in range(10):
            self.standardizer.standardize_image(large_img)
        elapsed = time.time() - start_time
        
        # Should process 10 4K images in reasonable time
        self.assertLess(elapsed, 5.0)  # Less than 5 seconds
        
    def test_memory_efficiency(self):
        """Test memory efficiency of batch processing"""
        import tracemalloc
        
        tracemalloc.start()
        
        # Process batch of large images
        large_images = [
            Image.new('RGB', (3840, 2160)) for _ in range(5)
        ]
        
        snapshot1 = tracemalloc.take_snapshot()
        
        standardized = self.standardizer.standardize_batch(large_images)
        
        snapshot2 = tracemalloc.take_snapshot()
        
        # Memory usage should be reasonable
        top_stats = snapshot2.compare_to(snapshot1, 'lineno')
        total_memory = sum(stat.size_diff for stat in top_stats)
        
        # Memory increase should be proportional to output size
        # 5 images * 768 * 768 * 3 bytes ≈ 8.8 MB
        self.assertLess(total_memory / (1024 * 1024), 20)  # Less than 20 MB
        
        tracemalloc.stop()
        
    def test_edge_detection_preservation(self):
        """Test that important edges are preserved during standardization"""
        # Create image with sharp edges
        img = Image.new('RGB', (1920, 1080), 'white')
        pixels = img.load()
        
        # Draw vertical line
        for y in range(1080):
            pixels[960, y] = (0, 0, 0)
            
        standardized = self.standardizer.standardize_image(img)
        
        # Check that line is still visible
        std_pixels = standardized.load()
        center_x = 384  # 960 * 768 / 1920
        
        # Check for dark pixels around expected position
        dark_pixels = 0
        for x in range(center_x - 2, center_x + 3):
            for y in range(768):
                r, g, b = std_pixels[x, y]
                if r < 128:  # Dark pixel
                    dark_pixels += 1
                    
        # Should have preserved the line
        self.assertGreater(dark_pixels, 700)  # Most of the line visible
        
    def test_metadata_preservation(self):
        """Test that image metadata is preserved or handled correctly"""
        # Create image with metadata
        img = Image.new('RGB', (1920, 1080))
        img.info['custom_data'] = 'test_value'
        img.info['dpi'] = (96, 96)
        
        standardized = self.standardizer.standardize_image(
            img, 
            preserve_metadata=True
        )
        
        # Check metadata preservation
        self.assertIn('custom_data', standardized.info)
        self.assertEqual(standardized.info['custom_data'], 'test_value')


class TestStandardizerIntegration(unittest.TestCase):
    """Integration tests for Standardizer with other components"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.standardizer = Standardizer()
        
    def test_standardizer_with_detector(self):
        """Test standardizer integration with element detector"""
        # Create test image
        img = Image.new('RGB', (1920, 1080))
        
        # Standardize for detection
        standardized = self.standardizer.standardize_for_detection(img)
        
        # Should return standardized image and scaling info
        self.assertIn('image', standardized)
        self.assertIn('scale_x', standardized)
        self.assertIn('scale_y', standardized)
        self.assertIn('original_size', standardized)
        
        self.assertEqual(standardized['image'].size, (768, 768))
        
    def test_coordinate_round_trip(self):
        """Test coordinate conversion round trip"""
        original_coords = [(100, 200), (500, 600), (1900, 1000)]
        original_res = (1920, 1080)
        
        for coords in original_coords:
            # Convert to 768
            standardized = self.standardizer.convert_coordinates(
                coords, original_res, (768, 768)
            )
            
            # Convert back
            restored = self.standardizer.convert_coordinates(
                standardized, (768, 768), original_res
            )
            
            # Should be close to original
            self.assertAlmostEqual(restored[0], coords[0], places=0)
            self.assertAlmostEqual(restored[1], coords[1], places=0)


if __name__ == '__main__':
    # Run tests with coverage
    unittest.main(verbosity=2)