# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cidr_block': 'str',
        'creation_time': 'str',
        'status': 'str',
        'total_ip_count': 'int',
        'used_ip_count': 'int'
    }

    attribute_map = {
        'cidr_block': 'CidrBlock',
        'creation_time': 'CreationTime',
        'status': 'Status',
        'total_ip_count': 'TotalIpCount',
        'used_ip_count': 'UsedIpCount'
    }

    def __init__(self, cidr_block=None, creation_time=None, status=None, total_ip_count=None, used_ip_count=None, _configuration=None):  # noqa: E501
        """IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cidr_block = None
        self._creation_time = None
        self._status = None
        self._total_ip_count = None
        self._used_ip_count = None
        self.discriminator = None

        if cidr_block is not None:
            self.cidr_block = cidr_block
        if creation_time is not None:
            self.creation_time = creation_time
        if status is not None:
            self.status = status
        if total_ip_count is not None:
            self.total_ip_count = total_ip_count
        if used_ip_count is not None:
            self.used_ip_count = used_ip_count

    @property
    def cidr_block(self):
        """Gets the cidr_block of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501


        :return: The cidr_block of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :rtype: str
        """
        return self._cidr_block

    @cidr_block.setter
    def cidr_block(self, cidr_block):
        """Sets the cidr_block of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.


        :param cidr_block: The cidr_block of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :type: str
        """

        self._cidr_block = cidr_block

    @property
    def creation_time(self):
        """Gets the creation_time of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501


        :return: The creation_time of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.


        :param creation_time: The creation_time of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def status(self):
        """Gets the status of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501


        :return: The status of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.


        :param status: The status of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def total_ip_count(self):
        """Gets the total_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501


        :return: The total_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_ip_count

    @total_ip_count.setter
    def total_ip_count(self, total_ip_count):
        """Sets the total_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.


        :param total_ip_count: The total_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :type: int
        """

        self._total_ip_count = total_ip_count

    @property
    def used_ip_count(self):
        """Gets the used_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501


        :return: The used_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :rtype: int
        """
        return self._used_ip_count

    @used_ip_count.setter
    def used_ip_count(self, used_ip_count):
        """Sets the used_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.


        :param used_ip_count: The used_ip_count of this IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput.  # noqa: E501
        :type: int
        """

        self._used_ip_count = used_ip_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IpAddressPoolCidrBlockForDescribeIpAddressPoolCidrBlocksOutput):
            return True

        return self.to_dict() != other.to_dict()
