import json
import os
from visualize_bbox_fixed import draw_bounding_box
from datetime import datetime
from PIL import Image

# 测试数据集
test_cases = [
    {
        "name": "ace_icon",
        "image": r"C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png",
        "result": {
            "element": {
                "bounding_box": {
                    "height": 82,
                    "width": 108,
                    "x": 330,
                    "y": 388
                },
                "description": "Application icon for ACE software",
                "text_content": ""
            }
        },
        "use_gemini_format": True  # 标记是否需要转换Gemini坐标
    }
    # 在这里添加更多测试案例
]

def batch_process():
    """批量处理所有测试案例"""
    # 创建输出文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"output_fixed_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建对比结果文件
    comparison_results = []
    
    # 处理每个测试案例
    for i, case in enumerate(test_cases):
        print(f"\n处理测试案例 {i+1}: {case['name']}")
        
        # 检查图片是否存在
        if not os.path.exists(case['image']):
            print(f"警告: 图片不存在 - {case['image']}")
            continue
        
        # 获取图片尺寸
        img = Image.open(case['image'])
        img_width, img_height = img.size
        
        # 生成输出文件名
        output_path = os.path.join(output_dir, f"{case['name']}_bbox.png")
        
        try:
            # 绘制边界框
            use_gemini = case.get('use_gemini_format', True)
            draw_bounding_box(case['image'], case['result'], output_path, use_gemini_format=use_gemini)
            
            # 记录转换信息
            bbox = case['result']['element']['bounding_box']
            if use_gemini:
                converted_bbox = {
                    'x': int(bbox['x'] * img_width / 1000),
                    'y': int(bbox['y'] * img_height / 1000),
                    'width': int(bbox['width'] * img_width / 1000),
                    'height': int(bbox['height'] * img_height / 1000)
                }
            else:
                converted_bbox = bbox
            
            comparison_results.append({
                'name': case['name'],
                'image_size': f"{img_width}x{img_height}",
                'original_bbox': bbox,
                'converted_bbox': converted_bbox,
                'gemini_format': use_gemini
            })
            
            # 保存JSON结果
            json_path = os.path.join(output_dir, f"{case['name']}_result.json")
            result_with_conversion = case['result'].copy()
            result_with_conversion['conversion_info'] = {
                'original_coordinates': bbox,
                'converted_coordinates': converted_bbox,
                'image_dimensions': {'width': img_width, 'height': img_height},
                'used_gemini_format': use_gemini
            }
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result_with_conversion, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"错误: 处理 {case['name']} 时出错 - {e}")
    
    # 保存对比结果
    comparison_path = os.path.join(output_dir, "coordinate_comparison.json")
    with open(comparison_path, 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n所有结果已保存到: {output_dir}")
    
    # 生成HTML预览页面
    generate_html_preview(output_dir, test_cases, comparison_results)

def generate_html_preview(output_dir, test_cases, comparison_results):
    """生成HTML预览页面"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>边界框测试结果（含坐标转换）</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #fff; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-case { background: #fff; margin-bottom: 30px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px; }
        .images { display: flex; gap: 20px; margin-top: 10px; }
        .image-container { text-align: center; flex: 1; }
        img { max-width: 100%; border: 1px solid #ddd; }
        .bbox-info { background: #f9f9f9; padding: 15px; margin-top: 15px; border-radius: 5px; }
        .coordinate-table { width: 100%; margin-top: 10px; border-collapse: collapse; }
        .coordinate-table th, .coordinate-table td { 
            padding: 8px; 
            text-align: left; 
            border: 1px solid #ddd; 
        }
        .coordinate-table th { background: #f0f0f0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 3px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .formula { background: #e9ecef; padding: 10px; margin: 10px 0; font-family: monospace; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>边界框可视化测试结果（Gemini坐标转换版）</h1>
        <div class="warning">
            <strong>⚠️ 重要提示：</strong>Gemini API返回的坐标是基于1000x1000归一化坐标系，需要转换为实际像素坐标！
        </div>
        <div class="formula">
            转换公式：actual_coordinate = (gemini_coordinate / 1000) × image_dimension
        </div>
    </div>
"""
    
    for i, (case, comparison) in enumerate(zip(test_cases, comparison_results)):
        if not os.path.exists(case['image']):
            continue
            
        html_content += f"""
    <div class="test-case">
        <h2>测试案例 {i+1}: {case['name']}</h2>
        <div class="images">
            <div class="image-container">
                <h3>原始图片</h3>
                <img src="{os.path.abspath(case['image'])}" alt="原始图片">
            </div>
            <div class="image-container">
                <h3>带边界框的图片</h3>
                <img src="{case['name']}_bbox.png" alt="带边界框的图片">
            </div>
        </div>
        <div class="bbox-info">
            <h3>边界框信息</h3>
            <p><strong>图片尺寸:</strong> {comparison['image_size']}</p>
            <p><strong>描述:</strong> {case['result']['element']['description']}</p>
            <p><strong>文本内容:</strong> {case['result']['element']['text_content'] or '(无)'}</p>
            
            <h4>坐标转换对比</h4>
            <table class="coordinate-table">
                <tr>
                    <th>坐标类型</th>
                    <th>X</th>
                    <th>Y</th>
                    <th>Width</th>
                    <th>Height</th>
                </tr>
                <tr>
                    <td>Gemini坐标 (0-1000)</td>
                    <td>{comparison['original_bbox']['x']}</td>
                    <td>{comparison['original_bbox']['y']}</td>
                    <td>{comparison['original_bbox']['width']}</td>
                    <td>{comparison['original_bbox']['height']}</td>
                </tr>
                <tr>
                    <td>实际像素坐标</td>
                    <td>{comparison['converted_bbox']['x']}</td>
                    <td>{comparison['converted_bbox']['y']}</td>
                    <td>{comparison['converted_bbox']['width']}</td>
                    <td>{comparison['converted_bbox']['height']}</td>
                </tr>
            </table>
            
            <details>
                <summary>完整JSON结果</summary>
                <pre>{json.dumps(case['result'], indent=2, ensure_ascii=False)}</pre>
            </details>
        </div>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    html_path = os.path.join(output_dir, "preview.html")
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML预览页面已生成: {html_path}")

if __name__ == "__main__":
    batch_process()