pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // JitPack for additional libraries
        maven { url 'https://jitpack.io' }
        // Maven Central mirror
        maven { url 'https://repo1.maven.org/maven2/' }
    }
}

rootProject.name = "ShowForAI Android Executor"
include ':app'
