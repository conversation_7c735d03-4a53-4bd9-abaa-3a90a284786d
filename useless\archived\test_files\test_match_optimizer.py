"""
Test Image Matching Performance Optimizer
"""

import cv2
import numpy as np
import time
import unittest
from unittest.mock import Mock, patch
from src.showforai.optimization.match_optimizer import (
    MatchOptimizer, LRUCache, Optimized<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>er, FeatureDescriptor, get_match_optimizer,
    optimized_sift_match
)


class TestLRUCache(unittest.TestCase):
    """Test LRU cache functionality"""
    
    def setUp(self):
        self.cache = LRUCache(max_size=3)
        
    def test_basic_operations(self):
        """Test basic cache operations"""
        # Put and get
        self.cache.put("key1", "value1")
        self.assertEqual(self.cache.get("key1"), "value1")
        
        # Miss
        self.assertIsNone(self.cache.get("key2"))
        
    def test_lru_eviction(self):
        """Test LRU eviction"""
        # Fill cache
        self.cache.put("key1", "value1")
        self.cache.put("key2", "value2")
        self.cache.put("key3", "value3")
        
        # Access key1 to make it recently used
        self.cache.get("key1")
        
        # Add new item, should evict key2
        self.cache.put("key4", "value4")
        
        # key2 should be evicted
        self.assertIsNone(self.cache.get("key2"))
        # key1 should still be there
        self.assertEqual(self.cache.get("key1"), "value1")
        
    def test_cache_stats(self):
        """Test cache statistics"""
        self.cache.put("key1", "value1")
        
        # Hit
        self.cache.get("key1")
        # Miss
        self.cache.get("key2")
        
        stats = self.cache.get_stats()
        self.assertEqual(stats['hits'], 1)
        self.assertEqual(stats['misses'], 1)
        self.assertEqual(stats['size'], 1)
        self.assertEqual(stats['max_size'], 3)
        self.assertEqual(stats['hit_rate'], 50.0)


class TestOptimizedSIFT(unittest.TestCase):
    """Test optimized SIFT extractor"""
    
    def setUp(self):
        self.sift = OptimizedSIFT(nfeatures=100)
        # Create test image
        self.test_image = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        
    def test_feature_extraction(self):
        """Test feature extraction"""
        keypoints, descriptors = self.sift.extract_features(self.test_image)
        
        self.assertIsNotNone(keypoints)
        self.assertIsNotNone(descriptors)
        
        # Should respect nfeatures limit
        self.assertLessEqual(len(keypoints), 100)
        
        if len(keypoints) > 0:
            # Check descriptor dimensions
            self.assertEqual(descriptors.shape[0], len(keypoints))
            self.assertEqual(descriptors.shape[1], 128)  # SIFT descriptor size
            
    def test_grayscale_handling(self):
        """Test grayscale image handling"""
        gray_image = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2GRAY)
        
        keypoints, descriptors = self.sift.extract_features(gray_image)
        
        self.assertIsNotNone(keypoints)
        # May have no features in random image
        if descriptors is not None:
            self.assertEqual(descriptors.shape[0], len(keypoints))


class TestHistogramFilter(unittest.TestCase):
    """Test histogram-based filtering"""
    
    def setUp(self):
        self.filter = HistogramFilter()
        # Create test images
        self.image1 = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        self.image2 = self.image1.copy()  # Identical image
        self.image3 = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)  # Different
        
    def test_histogram_computation(self):
        """Test histogram computation"""
        hist = self.filter.compute_histogram(self.image1)
        
        self.assertIsNotNone(hist)
        # Should be normalized
        self.assertAlmostEqual(hist.sum(), 1.0, places=5)
        
    def test_histogram_comparison(self):
        """Test histogram comparison"""
        hist1 = self.filter.compute_histogram(self.image1)
        hist2 = self.filter.compute_histogram(self.image2)
        hist3 = self.filter.compute_histogram(self.image3)
        
        # Identical images should have high similarity
        similarity_same = self.filter.compare_histograms(hist1, hist2)
        self.assertGreater(similarity_same, 0.99)
        
        # Different images should have lower similarity
        similarity_diff = self.filter.compare_histograms(hist1, hist3)
        self.assertLess(similarity_diff, similarity_same)
        
    def test_should_match_decision(self):
        """Test matching decision based on histograms"""
        hist1 = self.filter.compute_histogram(self.image1)
        hist2 = self.filter.compute_histogram(self.image2)
        hist3 = self.filter.compute_histogram(self.image3)
        
        # Same images should match
        self.assertTrue(self.filter.should_match(hist1, hist2))
        
        # Very different images might not match
        # (random images might still be similar by chance)
        result = self.filter.should_match(hist1, hist3, threshold=0.95)
        # Just check it returns a boolean
        self.assertIsInstance(result, bool)


class TestParallelMatcher(unittest.TestCase):
    """Test parallel feature matching"""
    
    def setUp(self):
        self.matcher = ParallelMatcher(n_workers=2)
        
    def tearDown(self):
        self.matcher.cleanup()
        
    def test_match_features(self):
        """Test feature matching"""
        # Create fake descriptors
        desc1 = np.random.rand(50, 128).astype(np.float32)
        desc2 = np.random.rand(50, 128).astype(np.float32)
        
        # Add some similar descriptors
        desc2[:10] = desc1[:10] + np.random.randn(10, 128) * 0.01
        
        matches = self.matcher.match_features(desc1, desc2)
        
        self.assertIsInstance(matches, list)
        # Should find some matches in similar descriptors
        self.assertGreater(len(matches), 0)
        
    def test_none_descriptors(self):
        """Test handling of None descriptors"""
        matches = self.matcher.match_features(None, None)
        self.assertEqual(matches, [])
        
        desc1 = np.random.rand(50, 128).astype(np.float32)
        matches = self.matcher.match_features(desc1, None)
        self.assertEqual(matches, [])


class TestMatchOptimizer(unittest.TestCase):
    """Test main match optimizer"""
    
    def setUp(self):
        self.optimizer = MatchOptimizer(cache_size=10, nfeatures=100)
        # Create test images with patterns
        self.template = self._create_test_image_with_pattern()
        self.screenshot = self._create_screenshot_with_template()
        
    def tearDown(self):
        self.optimizer.cleanup()
        
    def _create_test_image_with_pattern(self):
        """Create a test image with recognizable pattern"""
        img = np.ones((100, 100, 3), dtype=np.uint8) * 255
        # Add some features
        cv2.rectangle(img, (20, 20), (80, 80), (0, 0, 255), 2)
        cv2.circle(img, (50, 50), 20, (0, 255, 0), -1)
        return img
    
    def _create_screenshot_with_template(self):
        """Create screenshot containing the template"""
        screenshot = np.ones((300, 300, 3), dtype=np.uint8) * 200
        # Place template in screenshot
        screenshot[100:200, 100:200] = self.template
        return screenshot
        
    def test_feature_extraction_with_caching(self):
        """Test feature extraction with caching"""
        # First extraction
        features1 = self.optimizer.extract_and_cache_features(self.template)
        
        self.assertIsNotNone(features1)
        self.assertIsInstance(features1, FeatureDescriptor)
        
        # Second extraction should hit cache
        features2 = self.optimizer.extract_and_cache_features(self.template)
        
        self.assertEqual(features1.image_hash, features2.image_hash)
        
        # Check cache stats
        stats = self.optimizer.feature_cache.get_stats()
        self.assertGreater(stats['hits'], 0)
        
    def test_match_images(self):
        """Test image matching"""
        result = self.optimizer.match_images(self.template, self.screenshot)
        
        self.assertIsInstance(result, dict)
        self.assertIn('matched', result)
        self.assertIn('confidence', result)
        self.assertIn('time_ms', result)
        
        # Should complete within target time
        self.assertLess(result['time_ms'], 1000)  # Generous limit for test
        
    def test_match_with_prescreening(self):
        """Test matching with histogram pre-screening"""
        # Create very different image
        different = np.zeros((100, 100, 3), dtype=np.uint8)
        
        result = self.optimizer.match_images(
            self.template, 
            different,
            use_prescreening=True
        )
        
        # Should fail quickly on histogram check
        if 'reason' in result:
            self.assertIn('histogram', result.get('reason', ''))
        self.assertLess(result['time_ms'], 100)  # Should be fast
        
    def test_multiscale_matching(self):
        """Test multi-scale matching"""
        # Create scaled version
        scaled_screenshot = cv2.resize(self.screenshot, None, fx=0.8, fy=0.8)
        
        result = self.optimizer.match_multiscale(self.template, scaled_screenshot)
        
        self.assertIsInstance(result, dict)
        if result['matched']:
            self.assertIn('scale', result)
            
    def test_performance_stats(self):
        """Test performance statistics"""
        # Do some operations
        self.optimizer.match_images(self.template, self.screenshot)
        
        stats = self.optimizer.get_performance_stats()
        
        self.assertIn('cache_stats', stats)
        self.assertIn('profiler_data', stats)
        self.assertIn('use_gpu', stats)
        self.assertIn('worker_count', stats)
        
    def test_parameter_optimization(self):
        """Test automatic parameter optimization"""
        # Set initial features to a higher value
        self.optimizer.sift = OptimizedSIFT(nfeatures=500)
        initial_features = self.optimizer.sift.nfeatures
        
        # Simulate slow matching
        self.optimizer.profiler_data['sift_match'] = {
            'count': 10,
            'total_ms': 6000,
            'avg_ms': 600
        }
        
        # Should reduce features
        self.optimizer.optimize_parameters(target_time_ms=500)
        
        # Features should be reduced
        self.assertLess(self.optimizer.sift.nfeatures, initial_features)


class TestGlobalInstance(unittest.TestCase):
    """Test global instance management"""
    
    def test_singleton_pattern(self):
        """Test that get_match_optimizer returns same instance"""
        optimizer1 = get_match_optimizer()
        optimizer2 = get_match_optimizer()
        
        self.assertIs(optimizer1, optimizer2)
        
    def test_convenience_function(self):
        """Test convenience matching function"""
        template = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        screenshot = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        result = optimized_sift_match(template, screenshot)
        
        self.assertIsInstance(result, dict)
        self.assertIn('time_ms', result)


def run_performance_benchmark():
    """Run performance benchmark"""
    print("\n=== Match Optimizer Performance Benchmark ===")
    
    optimizer = MatchOptimizer(cache_size=50, nfeatures=500)
    
    # Create test images
    template = cv2.imread('test_output/test_screenshot_768.png')
    if template is None:
        # Create synthetic test image
        template = np.random.randint(0, 255, (768, 768, 3), dtype=np.uint8)
        # Add features
        for i in range(10):
            cv2.circle(template, 
                      (np.random.randint(100, 668), np.random.randint(100, 668)),
                      np.random.randint(10, 50),
                      (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)),
                      -1)
    
    screenshot = template.copy()
    # Add noise to make it harder
    noise = np.random.randint(-20, 20, screenshot.shape, dtype=np.int16)
    screenshot = np.clip(screenshot.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    print(f"Template shape: {template.shape}")
    print(f"Screenshot shape: {screenshot.shape}")
    
    # Warm up cache
    print("\n1. Warming up cache...")
    optimizer.extract_and_cache_features(template)
    optimizer.extract_and_cache_features(screenshot)
    
    # Benchmark matching
    print("\n2. Benchmarking SIFT matching...")
    times = []
    results = []
    
    for i in range(10):
        start = time.perf_counter()
        result = optimizer.match_images(template, screenshot)
        elapsed = (time.perf_counter() - start) * 1000
        
        times.append(elapsed)
        results.append(result)
        
        print(f"   Run {i+1}: {elapsed:.1f}ms, "
              f"Matched: {result['matched']}, "
              f"Confidence: {result.get('confidence', 0):.2f}")
    
    # Statistics
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"\n3. Results:")
    print(f"   Average time: {avg_time:.1f}ms")
    print(f"   Min time: {min_time:.1f}ms")
    print(f"   Max time: {max_time:.1f}ms")
    
    # Check performance target
    if avg_time < 500:
        print(f"   ✓ SUCCESS: Average matching time {avg_time:.1f}ms < 500ms target")
    else:
        print(f"   ✗ FAILED: Average matching time {avg_time:.1f}ms exceeds 500ms target")
    
    # Cache statistics
    print(f"\n4. Cache Statistics:")
    cache_stats = optimizer.feature_cache.get_stats()
    print(f"   Cache size: {cache_stats['size']}/{cache_stats['max_size']}")
    print(f"   Hit rate: {cache_stats['hit_rate']:.1f}%")
    
    # Performance stats
    print(f"\n5. Performance Statistics:")
    perf_stats = optimizer.get_performance_stats()
    if 'sift_match' in perf_stats['profiler_data']:
        sift_stats = perf_stats['profiler_data']['sift_match']
        print(f"   SIFT matches: {sift_stats['count']}")
        print(f"   Average time: {sift_stats['avg_ms']:.1f}ms")
    
    # Test multi-scale
    print(f"\n6. Testing multi-scale matching...")
    scaled = cv2.resize(screenshot, None, fx=1.2, fy=1.2)
    start = time.perf_counter()
    result = optimizer.match_multiscale(template, scaled)
    elapsed = (time.perf_counter() - start) * 1000
    
    print(f"   Multi-scale time: {elapsed:.1f}ms")
    if result['matched']:
        print(f"   Best scale: {result.get('scale', 1.0):.1f}")
    
    optimizer.cleanup()


if __name__ == '__main__':
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance benchmark
    run_performance_benchmark()