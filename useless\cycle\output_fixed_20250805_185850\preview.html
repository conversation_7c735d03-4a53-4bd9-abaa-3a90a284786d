
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>边界框测试结果（含坐标转换）</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #fff; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-case { background: #fff; margin-bottom: 30px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px; }
        .images { display: flex; gap: 20px; margin-top: 10px; }
        .image-container { text-align: center; flex: 1; }
        img { max-width: 100%; border: 1px solid #ddd; }
        .bbox-info { background: #f9f9f9; padding: 15px; margin-top: 15px; border-radius: 5px; }
        .coordinate-table { width: 100%; margin-top: 10px; border-collapse: collapse; }
        .coordinate-table th, .coordinate-table td { 
            padding: 8px; 
            text-align: left; 
            border: 1px solid #ddd; 
        }
        .coordinate-table th { background: #f0f0f0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 3px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .formula { background: #e9ecef; padding: 10px; margin: 10px 0; font-family: monospace; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>边界框可视化测试结果（Gemini坐标转换版）</h1>
        <div class="warning">
            <strong>⚠️ 重要提示：</strong>Gemini API返回的坐标是基于1000x1000归一化坐标系，需要转换为实际像素坐标！
        </div>
        <div class="formula">
            转换公式：actual_coordinate = (gemini_coordinate / 1000) × image_dimension
        </div>
    </div>

    <div class="test-case">
        <h2>测试案例 1: ace_icon</h2>
        <div class="images">
            <div class="image-container">
                <h3>原始图片</h3>
                <img src="C:\Users\<USER>\Desktop\aijioaben\ShowForAI-V2\recordings\recording_20250801_165027\screenshots\focus\84085a93-6acf-4035-b25b-242cb5dd6efb.png" alt="原始图片">
            </div>
            <div class="image-container">
                <h3>带边界框的图片</h3>
                <img src="ace_icon_bbox.png" alt="带边界框的图片">
            </div>
        </div>
        <div class="bbox-info">
            <h3>边界框信息</h3>
            <p><strong>图片尺寸:</strong> 512x512</p>
            <p><strong>描述:</strong> Application icon for ACE software</p>
            <p><strong>文本内容:</strong> (无)</p>
            
            <h4>坐标转换对比</h4>
            <table class="coordinate-table">
                <tr>
                    <th>坐标类型</th>
                    <th>X</th>
                    <th>Y</th>
                    <th>Width</th>
                    <th>Height</th>
                </tr>
                <tr>
                    <td>Gemini坐标 (0-1000)</td>
                    <td>330</td>
                    <td>388</td>
                    <td>108</td>
                    <td>82</td>
                </tr>
                <tr>
                    <td>实际像素坐标</td>
                    <td>168</td>
                    <td>198</td>
                    <td>55</td>
                    <td>41</td>
                </tr>
            </table>
            
            <details>
                <summary>完整JSON结果</summary>
                <pre>{
  "element": {
    "bounding_box": {
      "height": 82,
      "width": 108,
      "x": 330,
      "y": 388
    },
    "description": "Application icon for ACE software",
    "text_content": ""
  }
}</pre>
            </details>
        </div>
    </div>

</body>
</html>
