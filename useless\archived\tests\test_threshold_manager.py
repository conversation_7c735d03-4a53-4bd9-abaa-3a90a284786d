"""
Test suite for Threshold Manager
Tests threshold management, validation functions, boundary conditions, and singleton pattern
"""

import unittest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.threshold_manager import ThresholdManager


class TestThresholdManager(unittest.TestCase):
    """Test Threshold Manager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Reset singleton instance for each test
        ThresholdManager._instance = None
        self.threshold_manager = ThresholdManager()
        
    def tearDown(self):
        """Clean up after tests"""
        ThresholdManager._instance = None
        
    def test_singleton_pattern(self):
        """Test that ThresholdManager follows singleton pattern"""
        manager1 = ThresholdManager()
        manager2 = ThresholdManager()
        
        # Both instances should be the same
        self.assertIs(manager1, manager2)
        self.assertEqual(id(manager1), id(manager2))
        
    def test_default_thresholds(self):
        """Test default threshold values"""
        # Check main thresholds
        self.assertEqual(self.threshold_manager.get_threshold('primary'), 0.85)
        self.assertEqual(self.threshold_manager.get_threshold('secondary'), 0.75)
        self.assertEqual(self.threshold_manager.get_threshold('fallback'), 0.65)
        
        # Check auxiliary thresholds
        self.assertEqual(self.threshold_manager.get_threshold('auxiliary_high'), 0.80)
        self.assertEqual(self.threshold_manager.get_threshold('auxiliary_medium'), 0.70)
        self.assertEqual(self.threshold_manager.get_threshold('auxiliary_low'), 0.60)
        
    def test_get_threshold(self):
        """Test getting threshold values"""
        # Valid threshold
        threshold = self.threshold_manager.get_threshold('primary')
        self.assertEqual(threshold, 0.85)
        
        # Invalid threshold with default
        threshold = self.threshold_manager.get_threshold('non_existent', default=0.5)
        self.assertEqual(threshold, 0.5)
        
        # Invalid threshold without default
        threshold = self.threshold_manager.get_threshold('non_existent')
        self.assertIsNone(threshold)
        
    def test_set_threshold(self):
        """Test setting threshold values"""
        # Set valid threshold
        result = self.threshold_manager.set_threshold('custom', 0.9)
        self.assertTrue(result)
        self.assertEqual(self.threshold_manager.get_threshold('custom'), 0.9)
        
        # Set invalid threshold (out of range)
        result = self.threshold_manager.set_threshold('invalid', 1.5)
        self.assertFalse(result)
        self.assertIsNone(self.threshold_manager.get_threshold('invalid'))
        
        # Set invalid threshold (negative)
        result = self.threshold_manager.set_threshold('negative', -0.1)
        self.assertFalse(result)
        
    def test_update_threshold(self):
        """Test updating existing thresholds"""
        # Update existing threshold
        original = self.threshold_manager.get_threshold('primary')
        result = self.threshold_manager.update_threshold('primary', 0.9)
        self.assertTrue(result)
        self.assertEqual(self.threshold_manager.get_threshold('primary'), 0.9)
        
        # Update non-existent threshold (should create it)
        result = self.threshold_manager.update_threshold('new_threshold', 0.7)
        self.assertTrue(result)
        self.assertEqual(self.threshold_manager.get_threshold('new_threshold'), 0.7)
        
    def test_validate_confidence(self):
        """Test confidence validation"""
        # Valid confidence
        valid, reason = self.threshold_manager.validate_confidence(0.8, 'primary')
        self.assertFalse(valid)  # 0.8 < 0.85 (primary threshold)
        self.assertIn('below', reason.lower())
        
        # Confidence above threshold
        valid, reason = self.threshold_manager.validate_confidence(0.9, 'primary')
        self.assertTrue(valid)
        self.assertIsNone(reason)
        
        # Edge case - exactly at threshold
        valid, reason = self.threshold_manager.validate_confidence(0.85, 'primary')
        self.assertTrue(valid)
        
    def test_get_matching_level(self):
        """Test getting matching level based on confidence"""
        # High confidence - primary level
        level = self.threshold_manager.get_matching_level(0.9)
        self.assertEqual(level, 'primary')
        
        # Medium confidence - secondary level
        level = self.threshold_manager.get_matching_level(0.78)
        self.assertEqual(level, 'secondary')
        
        # Low confidence - fallback level
        level = self.threshold_manager.get_matching_level(0.68)
        self.assertEqual(level, 'fallback')
        
        # Very low confidence - no match
        level = self.threshold_manager.get_matching_level(0.5)
        self.assertIsNone(level)
        
    def test_adjust_threshold_dynamically(self):
        """Test dynamic threshold adjustment"""
        # Increase threshold on success
        original = self.threshold_manager.get_threshold('primary')
        self.threshold_manager.adjust_threshold_dynamically('primary', success=True)
        new_value = self.threshold_manager.get_threshold('primary')
        self.assertGreater(new_value, original)
        
        # Decrease threshold on failure
        self.threshold_manager.adjust_threshold_dynamically('primary', success=False)
        decreased = self.threshold_manager.get_threshold('primary')
        self.assertLess(decreased, new_value)
        
    def test_boundary_conditions(self):
        """Test boundary conditions for thresholds"""
        # Test minimum boundary (0.0)
        result = self.threshold_manager.set_threshold('min_test', 0.0)
        self.assertTrue(result)
        self.assertEqual(self.threshold_manager.get_threshold('min_test'), 0.0)
        
        # Test maximum boundary (1.0)
        result = self.threshold_manager.set_threshold('max_test', 1.0)
        self.assertTrue(result)
        self.assertEqual(self.threshold_manager.get_threshold('max_test'), 1.0)
        
        # Test just below minimum
        result = self.threshold_manager.set_threshold('below_min', -0.001)
        self.assertFalse(result)
        
        # Test just above maximum
        result = self.threshold_manager.set_threshold('above_max', 1.001)
        self.assertFalse(result)
        
    def test_get_all_thresholds(self):
        """Test getting all thresholds"""
        all_thresholds = self.threshold_manager.get_all_thresholds()
        
        # Should return a dictionary
        self.assertIsInstance(all_thresholds, dict)
        
        # Should contain default thresholds
        self.assertIn('primary', all_thresholds)
        self.assertIn('secondary', all_thresholds)
        self.assertIn('fallback', all_thresholds)
        
    def test_reset_thresholds(self):
        """Test resetting thresholds to defaults"""
        # Modify a threshold
        self.threshold_manager.set_threshold('primary', 0.95)
        self.assertEqual(self.threshold_manager.get_threshold('primary'), 0.95)
        
        # Reset to defaults
        self.threshold_manager.reset_to_defaults()
        self.assertEqual(self.threshold_manager.get_threshold('primary'), 0.85)
        
    def test_confidence_range_validation(self):
        """Test validation of confidence ranges"""
        # Test with numpy array
        confidences = np.array([0.6, 0.7, 0.8, 0.9])
        valid_mask = self.threshold_manager.validate_confidence_batch(confidences, 'secondary')
        expected = np.array([False, False, True, True])
        np.testing.assert_array_equal(valid_mask, expected)
        
    def test_adaptive_threshold(self):
        """Test adaptive threshold based on history"""
        # Simulate match history
        history = [0.9, 0.88, 0.92, 0.85, 0.87]
        
        # Calculate adaptive threshold
        adaptive = self.threshold_manager.calculate_adaptive_threshold(history)
        
        # Should be around the mean of history
        expected = np.mean(history) - np.std(history) * 0.5
        self.assertAlmostEqual(adaptive, expected, places=2)
        
    def test_threshold_statistics(self):
        """Test threshold usage statistics"""
        # Track some validations
        self.threshold_manager.validate_confidence(0.9, 'primary')
        self.threshold_manager.validate_confidence(0.6, 'primary')
        self.threshold_manager.validate_confidence(0.8, 'secondary')
        
        # Get statistics
        stats = self.threshold_manager.get_statistics()
        
        # Check structure
        self.assertIn('total_validations', stats)
        self.assertIn('success_rate', stats)
        self.assertIn('threshold_usage', stats)
        
    def test_concurrent_access(self):
        """Test thread-safe concurrent access"""
        import threading
        import time
        
        results = []
        
        def access_manager():
            manager = ThresholdManager()
            results.append(id(manager))
            time.sleep(0.01)
            
        # Create multiple threads
        threads = [threading.Thread(target=access_manager) for _ in range(10)]
        
        # Start all threads
        for t in threads:
            t.start()
            
        # Wait for completion
        for t in threads:
            t.join()
            
        # All should have same instance ID
        self.assertEqual(len(set(results)), 1)
        
    def test_threshold_persistence(self):
        """Test threshold persistence across operations"""
        # Set custom thresholds
        self.threshold_manager.set_threshold('custom1', 0.77)
        self.threshold_manager.set_threshold('custom2', 0.88)
        
        # Get new reference (should be same instance)
        manager2 = ThresholdManager()
        
        # Custom thresholds should persist
        self.assertEqual(manager2.get_threshold('custom1'), 0.77)
        self.assertEqual(manager2.get_threshold('custom2'), 0.88)


class TestThresholdValidation(unittest.TestCase):
    """Test threshold validation functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        ThresholdManager._instance = None
        self.manager = ThresholdManager()
        
    def test_multi_level_validation(self):
        """Test multi-level threshold validation"""
        # Create mock match results
        matches = [
            {'confidence': 0.9, 'level': 'primary'},
            {'confidence': 0.78, 'level': 'secondary'},
            {'confidence': 0.68, 'level': 'fallback'}
        ]
        
        # Validate all matches
        for match in matches:
            valid = self.manager.validate_match(match)
            self.assertTrue(valid)
            
    def test_invalid_match_validation(self):
        """Test validation of invalid matches"""
        # Below threshold match
        match = {'confidence': 0.5, 'level': 'primary'}
        valid = self.manager.validate_match(match)
        self.assertFalse(valid)
        
    def test_edge_case_validation(self):
        """Test edge cases in validation"""
        # Missing confidence
        match = {'level': 'primary'}
        valid = self.manager.validate_match(match)
        self.assertFalse(valid)
        
        # Missing level
        match = {'confidence': 0.9}
        valid = self.manager.validate_match(match)
        self.assertFalse(valid)
        
        # Invalid level
        match = {'confidence': 0.9, 'level': 'invalid'}
        valid = self.manager.validate_match(match)
        self.assertFalse(valid)


if __name__ == '__main__':
    # Run tests with coverage
    unittest.main(verbosity=2)