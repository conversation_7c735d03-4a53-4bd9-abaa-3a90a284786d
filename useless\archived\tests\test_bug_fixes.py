"""
Test suite for Bug Fix Functions
Tests crash prevention, memory management, offline detection, and BBOX validation
"""

import unittest
import gc
import psutil
import numpy as np
from PIL import Image
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path
import tracemalloc
import time

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.bug_fixes import (
    CrashPrevention,
    MemoryManager,
    OfflineDetector,
    BBoxValidator,
    ErrorHandler,
    ResourceMonitor
)


class TestCrashPrevention(unittest.TestCase):
    """Test crash prevention mechanisms"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.crash_prevention = CrashPrevention()
        
    def test_exception_catching(self):
        """Test catching and handling exceptions"""
        @self.crash_prevention.safe_execute
        def risky_function():
            raise ValueError("Test error")
            
        result = risky_function()
        
        # Should not crash, returns None or default
        self.assertIsNone(result)
        
    def test_exception_with_fallback(self):
        """Test exception handling with fallback"""
        @self.crash_prevention.safe_execute(fallback="default_value")
        def risky_function():
            raise Exception("Error")
            
        result = risky_function()
        self.assertEqual(result, "default_value")
        
    def test_segfault_prevention(self):
        """Test prevention of segmentation faults"""
        # Simulate risky pointer operation
        def risky_pointer_op():
            # This would normally cause issues
            data = None
            return data.some_attribute
            
        safe_op = self.crash_prevention.wrap_unsafe(risky_pointer_op)
        result = safe_op()
        
        # Should handle gracefully
        self.assertIsNone(result)
        
    def test_recursion_limit(self):
        """Test recursion limit protection"""
        @self.crash_prevention.limit_recursion(max_depth=10)
        def recursive_function(n):
            if n <= 0:
                return 0
            return 1 + recursive_function(n - 1)
            
        # Should work within limit
        result = recursive_function(5)
        self.assertEqual(result, 5)
        
        # Should prevent deep recursion
        result = recursive_function(100)
        self.assertIsNone(result)  # Or raises controlled exception
        
    def test_timeout_protection(self):
        """Test timeout protection for long operations"""
        @self.crash_prevention.timeout(seconds=1)
        def long_operation():
            time.sleep(2)
            return "completed"
            
        result = long_operation()
        
        # Should timeout and return None
        self.assertIsNone(result)
        
    def test_crash_recovery(self):
        """Test crash recovery mechanisms"""
        # Simulate crash scenario
        self.crash_prevention.record_crash("test_function", "ValueError")
        
        # Check crash history
        crashes = self.crash_prevention.get_crash_history()
        self.assertEqual(len(crashes), 1)
        self.assertEqual(crashes[0]['function'], "test_function")
        
    def test_safe_file_operations(self):
        """Test safe file operation wrappers"""
        # Safe file read
        content = self.crash_prevention.safe_file_read("nonexistent.txt")
        self.assertIsNone(content)
        
        # Safe file write
        success = self.crash_prevention.safe_file_write(
            "/invalid/path/file.txt",
            "content"
        )
        self.assertFalse(success)
        
    def test_null_pointer_checks(self):
        """Test null pointer prevention"""
        @self.crash_prevention.check_not_null
        def process_data(data):
            return data.upper()
            
        # Valid data
        result = process_data("hello")
        self.assertEqual(result, "HELLO")
        
        # Null data
        result = process_data(None)
        self.assertIsNone(result)


class TestMemoryManager(unittest.TestCase):
    """Test memory management functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.memory_manager = MemoryManager()
        tracemalloc.start()
        
    def tearDown(self):
        """Clean up after tests"""
        tracemalloc.stop()
        gc.collect()
        
    def test_memory_monitoring(self):
        """Test memory usage monitoring"""
        # Get initial memory
        initial_memory = self.memory_manager.get_memory_usage()
        
        # Allocate memory
        large_list = [i for i in range(1000000)]
        
        # Check increased memory
        current_memory = self.memory_manager.get_memory_usage()
        self.assertGreater(current_memory, initial_memory)
        
        # Clean up
        del large_list
        gc.collect()
        
    def test_memory_leak_detection(self):
        """Test memory leak detection"""
        # Simulate potential leak
        leaky_list = []
        
        def create_leak():
            for i in range(1000):
                leaky_list.append([0] * 1000)
                
        # Monitor for leaks
        self.memory_manager.start_monitoring()
        create_leak()
        leaks = self.memory_manager.check_for_leaks()
        
        # Should detect growth
        self.assertTrue(len(leaks) > 0 or self.memory_manager.memory_growing())
        
        # Clean up
        leaky_list.clear()
        
    def test_memory_limits(self):
        """Test memory limit enforcement"""
        # Set memory limit (in MB)
        self.memory_manager.set_memory_limit(100)
        
        # Try to allocate beyond limit
        try:
            huge_list = []
            for _ in range(10000):
                if self.memory_manager.check_memory_limit():
                    huge_list.append([0] * 10000)
                else:
                    break
        except MemoryError:
            pass
            
        # Should have stopped before limit
        self.assertTrue(self.memory_manager.is_within_limit())
        
    def test_garbage_collection(self):
        """Test forced garbage collection"""
        # Create garbage
        for _ in range(1000):
            temp = [i for i in range(100)]
            
        # Force collection
        collected = self.memory_manager.force_garbage_collection()
        
        # Should have collected objects
        self.assertGreater(collected, 0)
        
    def test_memory_optimization(self):
        """Test memory optimization techniques"""
        # Test object pooling
        pool = self.memory_manager.create_object_pool(list, size=10)
        
        # Get objects from pool
        obj1 = pool.get()
        obj2 = pool.get()
        
        # Return to pool
        pool.return_object(obj1)
        pool.return_object(obj2)
        
        # Pool should reuse objects
        obj3 = pool.get()
        self.assertIs(obj3, obj1)  # Same object reused
        
    def test_cache_management(self):
        """Test cache memory management"""
        cache = self.memory_manager.create_limited_cache(max_size_mb=10)
        
        # Add items to cache
        for i in range(100):
            cache.set(f"key_{i}", [0] * 1000)
            
        # Cache should limit memory
        cache_size = cache.get_size_mb()
        self.assertLessEqual(cache_size, 10)
        
    def test_memory_profiling(self):
        """Test memory profiling"""
        @self.memory_manager.profile_memory
        def memory_intensive_function():
            data = []
            for i in range(10000):
                data.append([i] * 100)
            return len(data)
            
        result = memory_intensive_function()
        
        # Get profile
        profile = self.memory_manager.get_last_profile()
        
        self.assertIn('peak_memory', profile)
        self.assertIn('memory_used', profile)
        self.assertEqual(result, 10000)


class TestOfflineDetector(unittest.TestCase):
    """Test offline detection functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.offline_detector = OfflineDetector()
        
    @patch('requests.get')
    def test_internet_connectivity(self, mock_get):
        """Test internet connectivity detection"""
        # Simulate online
        mock_get.return_value.status_code = 200
        
        is_online = self.offline_detector.check_internet()
        self.assertTrue(is_online)
        
        # Simulate offline
        mock_get.side_effect = Exception("Connection error")
        
        is_online = self.offline_detector.check_internet()
        self.assertFalse(is_online)
        
    def test_dns_resolution(self):
        """Test DNS resolution check"""
        # Test with known domain
        can_resolve = self.offline_detector.check_dns("google.com")
        
        # Should resolve if online
        # Note: This test depends on actual network
        self.assertIsNotNone(can_resolve)
        
    def test_offline_mode_switching(self):
        """Test automatic offline mode switching"""
        # Set offline mode
        self.offline_detector.set_offline_mode(True)
        
        self.assertTrue(self.offline_detector.is_offline_mode)
        
        # Operations should use offline alternatives
        result = self.offline_detector.fetch_data("http://example.com")
        self.assertIsNone(result)  # Should not attempt network request
        
    def test_offline_cache(self):
        """Test offline cache functionality"""
        # Cache data while online
        self.offline_detector.cache_for_offline("key1", "value1")
        
        # Retrieve in offline mode
        self.offline_detector.set_offline_mode(True)
        cached = self.offline_detector.get_cached("key1")
        
        self.assertEqual(cached, "value1")
        
    def test_network_retry_logic(self):
        """Test network retry with exponential backoff"""
        mock_func = Mock(side_effect=[Exception(), Exception(), "success"])
        
        result = self.offline_detector.retry_network_operation(
            mock_func,
            max_retries=3
        )
        
        self.assertEqual(result, "success")
        self.assertEqual(mock_func.call_count, 3)
        
    def test_offline_fallback(self):
        """Test offline fallback mechanisms"""
        # Define online and offline functions
        online_func = Mock(side_effect=Exception("Network error"))
        offline_func = Mock(return_value="offline_result")
        
        result = self.offline_detector.with_fallback(
            online_func,
            offline_func
        )
        
        self.assertEqual(result, "offline_result")
        online_func.assert_called_once()
        offline_func.assert_called_once()
        
    def test_connection_monitoring(self):
        """Test continuous connection monitoring"""
        # Start monitoring
        self.offline_detector.start_monitoring(interval=1)
        
        # Simulate connection changes
        time.sleep(2)
        
        # Get connection history
        history = self.offline_detector.get_connection_history()
        
        self.assertGreater(len(history), 0)
        
        # Stop monitoring
        self.offline_detector.stop_monitoring()


class TestBBoxValidator(unittest.TestCase):
    """Test bounding box validation functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.validator = BBoxValidator()
        self.image_size = (1920, 1080)
        
    def test_valid_bbox(self):
        """Test validation of valid bounding boxes"""
        valid_bbox = {
            'x': 100,
            'y': 100,
            'width': 200,
            'height': 150
        }
        
        is_valid = self.validator.validate(valid_bbox, self.image_size)
        self.assertTrue(is_valid)
        
    def test_invalid_coordinates(self):
        """Test detection of invalid coordinates"""
        # Negative coordinates
        invalid_bbox = {
            'x': -10,
            'y': 100,
            'width': 200,
            'height': 150
        }
        
        is_valid = self.validator.validate(invalid_bbox, self.image_size)
        self.assertFalse(is_valid)
        
    def test_out_of_bounds(self):
        """Test detection of out-of-bounds boxes"""
        # Extends beyond image
        oob_bbox = {
            'x': 1800,
            'y': 1000,
            'width': 200,
            'height': 150
        }
        
        is_valid = self.validator.validate(oob_bbox, self.image_size)
        self.assertFalse(is_valid)
        
    def test_bbox_correction(self):
        """Test automatic bbox correction"""
        # Slightly out of bounds
        bbox = {
            'x': 1850,
            'y': 1000,
            'width': 100,
            'height': 100
        }
        
        corrected = self.validator.correct(bbox, self.image_size)
        
        # Should be clipped to image bounds
        self.assertLessEqual(corrected['x'] + corrected['width'], 1920)
        self.assertLessEqual(corrected['y'] + corrected['height'], 1080)
        
    def test_zero_size_bbox(self):
        """Test handling of zero-size bounding boxes"""
        zero_bbox = {
            'x': 100,
            'y': 100,
            'width': 0,
            'height': 0
        }
        
        is_valid = self.validator.validate(zero_bbox, self.image_size)
        self.assertFalse(is_valid)
        
    def test_bbox_intersection(self):
        """Test bounding box intersection calculation"""
        bbox1 = {'x': 100, 'y': 100, 'width': 200, 'height': 200}
        bbox2 = {'x': 200, 'y': 200, 'width': 200, 'height': 200}
        
        intersection = self.validator.calculate_intersection(bbox1, bbox2)
        
        self.assertEqual(intersection['x'], 200)
        self.assertEqual(intersection['y'], 200)
        self.assertEqual(intersection['width'], 100)
        self.assertEqual(intersection['height'], 100)
        
    def test_bbox_union(self):
        """Test bounding box union calculation"""
        bbox1 = {'x': 100, 'y': 100, 'width': 100, 'height': 100}
        bbox2 = {'x': 150, 'y': 150, 'width': 100, 'height': 100}
        
        union = self.validator.calculate_union(bbox1, bbox2)
        
        self.assertEqual(union['x'], 100)
        self.assertEqual(union['y'], 100)
        self.assertEqual(union['width'], 150)
        self.assertEqual(union['height'], 150)
        
    def test_iou_calculation(self):
        """Test Intersection over Union calculation"""
        bbox1 = {'x': 100, 'y': 100, 'width': 100, 'height': 100}
        bbox2 = {'x': 150, 'y': 150, 'width': 100, 'height': 100}
        
        iou = self.validator.calculate_iou(bbox1, bbox2)
        
        # Should be between 0 and 1
        self.assertGreaterEqual(iou, 0)
        self.assertLessEqual(iou, 1)
        
        # Exact same boxes should have IoU of 1
        same_iou = self.validator.calculate_iou(bbox1, bbox1)
        self.assertEqual(same_iou, 1.0)


class TestErrorHandler(unittest.TestCase):
    """Test comprehensive error handling"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.error_handler = ErrorHandler()
        
    def test_error_categorization(self):
        """Test error categorization"""
        errors = [
            (ValueError("Invalid value"), "validation"),
            (IOError("File not found"), "io"),
            (MemoryError("Out of memory"), "resource"),
            (ConnectionError("Network error"), "network")
        ]
        
        for error, expected_category in errors:
            category = self.error_handler.categorize(error)
            self.assertEqual(category, expected_category)
            
    def test_error_recovery_strategies(self):
        """Test different recovery strategies"""
        # Retry strategy
        retry_count = [0]
        
        def flaky_function():
            retry_count[0] += 1
            if retry_count[0] < 3:
                raise ConnectionError("Temporary error")
            return "success"
            
        result = self.error_handler.with_retry(flaky_function, max_retries=5)
        self.assertEqual(result, "success")
        self.assertEqual(retry_count[0], 3)
        
    def test_error_aggregation(self):
        """Test error aggregation and reporting"""
        # Log multiple errors
        for i in range(5):
            self.error_handler.log_error(ValueError(f"Error {i}"))
            
        for i in range(3):
            self.error_handler.log_error(IOError(f"IO Error {i}"))
            
        # Get aggregated report
        report = self.error_handler.get_error_report()
        
        self.assertEqual(report['total_errors'], 8)
        self.assertEqual(report['by_type']['ValueError'], 5)
        self.assertEqual(report['by_type']['IOError'], 3)


class TestResourceMonitor(unittest.TestCase):
    """Test resource monitoring"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.monitor = ResourceMonitor()
        
    def test_cpu_monitoring(self):
        """Test CPU usage monitoring"""
        cpu_usage = self.monitor.get_cpu_usage()
        
        self.assertGreaterEqual(cpu_usage, 0)
        self.assertLessEqual(cpu_usage, 100)
        
    def test_memory_monitoring(self):
        """Test memory usage monitoring"""
        memory_info = self.monitor.get_memory_info()
        
        self.assertIn('used', memory_info)
        self.assertIn('available', memory_info)
        self.assertIn('percent', memory_info)
        
    def test_disk_monitoring(self):
        """Test disk usage monitoring"""
        disk_info = self.monitor.get_disk_info()
        
        self.assertIn('used', disk_info)
        self.assertIn('free', disk_info)
        self.assertIn('percent', disk_info)
        
    def test_resource_alerts(self):
        """Test resource usage alerts"""
        # Set thresholds
        self.monitor.set_cpu_threshold(80)
        self.monitor.set_memory_threshold(90)
        
        # Check for alerts
        alerts = self.monitor.check_alerts()
        
        # Alerts depend on current system state
        self.assertIsInstance(alerts, list)
        
    def test_resource_history(self):
        """Test resource usage history tracking"""
        # Record samples
        for _ in range(5):
            self.monitor.record_sample()
            time.sleep(0.1)
            
        history = self.monitor.get_history()
        
        self.assertEqual(len(history), 5)
        
        # Calculate averages
        averages = self.monitor.calculate_averages()
        
        self.assertIn('avg_cpu', averages)
        self.assertIn('avg_memory', averages)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)