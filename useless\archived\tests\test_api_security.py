"""
Test suite for API Security implementation
Tests signature generation, timestamp validation, nonce management, and complete request flows
"""

import unittest
import asyncio
import time
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add parent directory to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.security.api_security import APISecurityManager, SecurityContext
from showforai.security.nonce_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, RedisNonceManager
from showforai.security.secure_config import SecureAP<PERSON>KeyManager, APIKeyRotationScheduler
from showforai.api.adapter import UnifiedAPIAdapter, create_api_adapter


class TestAPISecurityManager(unittest.TestCase):
    """Test API Security Manager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.secret_key = "test_secret_key_12345"
        self.security_manager = APISecurityManager(secret_key=self.secret_key)
        
    def test_generate_nonce(self):
        """Test nonce generation"""
        nonce1 = self.security_manager.generate_nonce()
        nonce2 = self.security_manager.generate_nonce()
        
        # Nonces should be unique
        self.assertNotEqual(nonce1, nonce2)
        
        # Nonces should be non-empty strings
        self.assertTrue(isinstance(nonce1, str))
        self.assertTrue(len(nonce1) > 0)
        
    def test_generate_timestamp(self):
        """Test timestamp generation"""
        timestamp = self.security_manager.generate_timestamp()
        
        # Timestamp should be an integer
        self.assertTrue(isinstance(timestamp, int))
        
        # Timestamp should be close to current time
        current_time = int(time.time())
        self.assertLessEqual(abs(timestamp - current_time), 1)
        
    def test_create_signature(self):
        """Test signature creation"""
        method = "POST"
        url = "https://api.example.com/endpoint"
        params = {"key": "value"}
        body = json.dumps({"data": "test"})
        timestamp = 1234567890
        nonce = "test-nonce"
        
        signature = self.security_manager.create_signature(
            method=method,
            url=url,
            params=params,
            body=body,
            timestamp=timestamp,
            nonce=nonce
        )
        
        # Signature should be a non-empty string
        self.assertTrue(isinstance(signature, str))
        self.assertTrue(len(signature) > 0)
        
        # Same inputs should produce same signature
        signature2 = self.security_manager.create_signature(
            method=method,
            url=url,
            params=params,
            body=body,
            timestamp=timestamp,
            nonce=nonce
        )
        self.assertEqual(signature, signature2)
        
        # Different inputs should produce different signatures
        signature3 = self.security_manager.create_signature(
            method=method,
            url=url,
            params=params,
            body=body,
            timestamp=timestamp,
            nonce="different-nonce"
        )
        self.assertNotEqual(signature, signature3)
        
    def test_verify_timestamp(self):
        """Test timestamp verification"""
        # Valid timestamp (current time)
        current_timestamp = int(time.time())
        valid, error = self.security_manager.verify_timestamp(current_timestamp)
        self.assertTrue(valid)
        self.assertIsNone(error)
        
        # Expired timestamp (6 minutes old)
        old_timestamp = current_timestamp - 360
        valid, error = self.security_manager.verify_timestamp(old_timestamp)
        self.assertFalse(valid)
        self.assertIsNotNone(error)
        self.assertIn("expired", error.lower())
        
        # Future timestamp (more than 30 seconds)
        future_timestamp = current_timestamp + 60
        valid, error = self.security_manager.verify_timestamp(future_timestamp)
        self.assertFalse(valid)
        self.assertIsNotNone(error)
        self.assertIn("future", error.lower())
        
    def test_verify_signature(self):
        """Test signature verification"""
        method = "POST"
        url = "https://api.example.com/endpoint"
        timestamp = int(time.time())
        nonce = "test-nonce"
        
        # Create a valid signature
        signature = self.security_manager.create_signature(
            method=method,
            url=url,
            timestamp=timestamp,
            nonce=nonce
        )
        
        # Verify the valid signature
        valid, error = self.security_manager.verify_signature(
            signature=signature,
            method=method,
            url=url,
            timestamp=timestamp,
            nonce=nonce
        )
        self.assertTrue(valid)
        self.assertIsNone(error)
        
        # Verify with wrong signature
        valid, error = self.security_manager.verify_signature(
            signature="wrong-signature",
            method=method,
            url=url,
            timestamp=timestamp,
            nonce=nonce
        )
        self.assertFalse(valid)
        self.assertEqual(error, "Invalid signature")
        
    def test_create_secure_headers(self):
        """Test secure header creation"""
        method = "GET"
        url = "https://api.example.com/data"
        
        headers = self.security_manager.create_secure_headers(
            method=method,
            url=url
        )
        
        # Check required headers are present
        self.assertIn('X-Timestamp', headers)
        self.assertIn('X-Nonce', headers)
        self.assertIn('X-Signature', headers)
        
        # Headers should have valid values
        self.assertTrue(headers['X-Timestamp'].isdigit())
        self.assertTrue(len(headers['X-Nonce']) > 0)
        self.assertTrue(len(headers['X-Signature']) > 0)
        
    def test_verify_request(self):
        """Test complete request verification"""
        method = "POST"
        url = "https://api.example.com/endpoint"
        body = json.dumps({"test": "data"})
        
        # Create secure headers
        headers = self.security_manager.create_secure_headers(
            method=method,
            url=url,
            body=body
        )
        
        # Mock nonce verification
        nonce_verifier = Mock(return_value=True)
        
        # Verify the request
        valid, error = self.security_manager.verify_request(
            method=method,
            url=url,
            headers=headers,
            body=body,
            verify_nonce_callback=nonce_verifier
        )
        
        self.assertTrue(valid)
        self.assertIsNone(error)
        
        # Verify nonce was checked
        nonce_verifier.assert_called_once_with(headers['X-Nonce'])
        
    def test_api_key_generation(self):
        """Test API key generation"""
        user_id = "test-user-123"
        
        key_data = self.security_manager.generate_api_key(user_id)
        
        # Check key data structure
        self.assertIn('api_key', key_data)
        self.assertIn('user_id', key_data)
        self.assertIn('created_at', key_data)
        self.assertIn('expires_at', key_data)
        self.assertIn('signature', key_data)
        
        # Verify user_id matches
        self.assertEqual(key_data['user_id'], user_id)
        
        # Verify dates are valid
        created = datetime.fromisoformat(key_data['created_at'])
        expires = datetime.fromisoformat(key_data['expires_at'])
        self.assertLess(created, expires)


class TestNonceManager(unittest.TestCase):
    """Test Nonce Manager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.nonce_manager = NonceManager(ttl=5, cleanup_interval=1)
        
    def tearDown(self):
        """Clean up after tests"""
        self.nonce_manager.shutdown()
        
    def test_add_nonce(self):
        """Test adding nonces"""
        nonce = "test-nonce-1"
        
        # First add should succeed
        result = self.nonce_manager.add_nonce(nonce)
        self.assertTrue(result)
        
        # Second add of same nonce should fail
        result = self.nonce_manager.add_nonce(nonce)
        self.assertFalse(result)
        
    def test_verify_nonce(self):
        """Test nonce verification"""
        nonce = "test-nonce-2"
        
        # First verification should succeed and add the nonce
        result = self.nonce_manager.verify_nonce(nonce)
        self.assertTrue(result)
        
        # Second verification should fail (replay attack)
        result = self.nonce_manager.verify_nonce(nonce)
        self.assertFalse(result)
        
    def test_is_nonce_used(self):
        """Test checking if nonce is used"""
        nonce = "test-nonce-3"
        
        # Initially should not be used
        self.assertFalse(self.nonce_manager.is_nonce_used(nonce))
        
        # Add the nonce
        self.nonce_manager.add_nonce(nonce)
        
        # Now should be used
        self.assertTrue(self.nonce_manager.is_nonce_used(nonce))
        
    def test_cleanup_expired(self):
        """Test cleanup of expired nonces"""
        # Add a nonce
        nonce = "test-nonce-4"
        self.nonce_manager.add_nonce(nonce)
        
        # Initially should be present
        self.assertTrue(self.nonce_manager.is_nonce_used(nonce))
        
        # Wait for expiration (TTL is 5 seconds in setUp)
        time.sleep(6)
        
        # Run cleanup
        removed_count = self.nonce_manager.cleanup_expired()
        self.assertEqual(removed_count, 1)
        
        # Nonce should no longer be present
        self.assertFalse(self.nonce_manager.is_nonce_used(nonce))
        
    def test_get_stats(self):
        """Test getting nonce statistics"""
        # Add some nonces
        self.nonce_manager.add_nonce("nonce1")
        self.nonce_manager.add_nonce("nonce2")
        
        stats = self.nonce_manager.get_stats()
        
        # Check stats structure
        self.assertIn('total', stats)
        self.assertIn('active', stats)
        self.assertIn('expired', stats)
        self.assertIn('ttl', stats)
        
        # Verify counts
        self.assertEqual(stats['total'], 2)
        self.assertEqual(stats['active'], 2)
        self.assertEqual(stats['expired'], 0)


class TestSecureAPIKeyManager(unittest.TestCase):
    """Test Secure API Key Manager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.key_manager = SecureAPIKeyManager()
        
    def test_generate_api_secret(self):
        """Test API secret generation"""
        secret1 = self.key_manager.generate_api_secret()
        secret2 = self.key_manager.generate_api_secret()
        
        # Secrets should be unique
        self.assertNotEqual(secret1, secret2)
        
        # Secrets should be hex strings of appropriate length
        self.assertEqual(len(secret1), 64)  # 32 bytes = 64 hex chars
        
    @patch('keyring.set_password')
    @patch('keyring.get_password')
    def test_store_and_retrieve_api_key(self, mock_get, mock_set):
        """Test storing and retrieving API keys"""
        key_name = "test-key"
        api_key = "secret-api-key-123"
        
        # Configure mock for retrieval
        stored_data = json.dumps({
            'key': api_key,
            'stored_at': datetime.now().isoformat(),
            'metadata': {}
        })
        mock_get.return_value = stored_data
        
        # Store the key
        result = self.key_manager.store_api_key(key_name, api_key)
        self.assertTrue(result)
        
        # Retrieve the key
        retrieved_key = self.key_manager.retrieve_api_key(key_name)
        self.assertEqual(retrieved_key, api_key)
        
    @patch('keyring.delete_password')
    def test_delete_api_key(self, mock_delete):
        """Test deleting API keys"""
        key_name = "test-key-to-delete"
        
        result = self.key_manager.delete_api_key(key_name)
        self.assertTrue(result)
        
        # Verify delete was called
        mock_delete.assert_called_once()


class TestIntegration(unittest.TestCase):
    """Integration tests for API security with adapter"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.adapter = None
        
    def tearDown(self):
        """Clean up after tests"""
        if self.adapter:
            self.adapter.cleanup()
            
    @patch('showforai.security.config_manager.SecureConfigManager.get_secret')
    def test_adapter_with_security(self, mock_get_secret):
        """Test UnifiedAPIAdapter with security enabled"""
        # Mock the API secret
        mock_get_secret.return_value = "test-api-secret"
        
        # Create adapter with security enabled
        self.adapter = create_api_adapter(enable_security=True)
        
        # Verify security components are initialized
        self.assertTrue(self.adapter.enable_security)
        self.assertTrue(hasattr(self.adapter, 'security_manager'))
        self.assertTrue(hasattr(self.adapter, 'nonce_manager'))
        
    def test_adapter_without_security(self):
        """Test UnifiedAPIAdapter with security disabled"""
        # Create adapter with security disabled
        self.adapter = create_api_adapter(enable_security=False)
        
        # Verify security is disabled
        self.assertFalse(self.adapter.enable_security)
        
    @patch('showforai.security.config_manager.SecureConfigManager.get_secret')
    def test_create_secure_request(self, mock_get_secret):
        """Test creating a secure request"""
        mock_get_secret.return_value = "test-api-secret"
        
        self.adapter = create_api_adapter(enable_security=True)
        
        # Create a secure request
        request_data = self.adapter.create_secure_request(
            method="POST",
            url="https://api.example.com/test",
            body=json.dumps({"test": "data"})
        )
        
        # Verify request structure
        self.assertIn('headers', request_data)
        self.assertIn('X-Timestamp', request_data['headers'])
        self.assertIn('X-Nonce', request_data['headers'])
        self.assertIn('X-Signature', request_data['headers'])
        
    @patch('showforai.security.config_manager.SecureConfigManager.get_secret')
    async def test_verify_api_security(self, mock_get_secret):
        """Test API security verification"""
        mock_get_secret.return_value = "test-api-secret"
        
        self.adapter = create_api_adapter(enable_security=True)
        
        # Create a secure request
        request_data = self.adapter.create_secure_request(
            method="GET",
            url="https://api.example.com/data"
        )
        
        # Verify the request
        valid, error = await self.adapter.verify_api_security(request_data)
        
        self.assertTrue(valid)
        self.assertIsNone(error)


class TestSecurityContext(unittest.TestCase):
    """Test Security Context Manager"""
    
    def test_security_context(self):
        """Test security context manager"""
        security_manager = APISecurityManager(secret_key="test-key")
        
        with SecurityContext(security_manager) as context:
            # Context should have request_id
            self.assertTrue(hasattr(context, 'request_id'))
            self.assertTrue(len(context.request_id) > 0)
            
            # Simulate some work
            time.sleep(0.1)
            
        # Context should complete without errors
        self.assertTrue(True)  # If we get here, context exited cleanly


def run_async_test(coro):
    """Helper to run async tests"""
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)