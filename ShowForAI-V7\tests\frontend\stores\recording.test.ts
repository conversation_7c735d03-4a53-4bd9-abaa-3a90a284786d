import { describe, it, expect, vi, beforeEach } from 'vitest'
import { act, renderHook } from '@testing-library/react'
import { useRecordingStore } from '@stores/recording'
import { invoke } from '@tauri-apps/api/tauri'

vi.mock('@tauri-apps/api/tauri')

const mockInvoke = invoke as any

describe('useRecordingStore', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset store state
    useRecordingStore.setState({
      isRecording: false,
      recordedActions: [],
      currentAction: null,
      error: null,
      recordingSettings: {
        captureInterval: 100,
        quality: 'high',
        includeClicks: true,
        includeKeyboard: true,
        includeScrolling: true
      }
    })
  })

  it('initializes with default state', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    expect(result.current.isRecording).toBe(false)
    expect(result.current.recordedActions).toEqual([])
    expect(result.current.currentAction).toBeNull()
    expect(result.current.error).toBeNull()
  })

  it('starts recording successfully', async () => {
    mockInvoke.mockResolvedValue({ success: true })
    
    const { result } = renderHook(() => useRecordingStore())
    
    await act(async () => {
      await result.current.startRecording()
    })
    
    expect(result.current.isRecording).toBe(true)
    expect(result.current.error).toBeNull()
    expect(mockInvoke).toHaveBeenCalledWith('start_recording')
  })

  it('handles recording start error', async () => {
    const errorMessage = 'Failed to start recording'
    mockInvoke.mockRejectedValue(new Error(errorMessage))
    
    const { result } = renderHook(() => useRecordingStore())
    
    await act(async () => {
      await result.current.startRecording()
    })
    
    expect(result.current.isRecording).toBe(false)
    expect(result.current.error).toBe(errorMessage)
  })

  it('stops recording successfully', async () => {
    const mockActions = [
      { type: 'click', x: 100, y: 200, timestamp: Date.now() },
      { type: 'type', text: 'hello', timestamp: Date.now() + 1000 }
    ]
    mockInvoke.mockResolvedValue({ success: true, actions: mockActions })
    
    const { result } = renderHook(() => useRecordingStore())
    
    // First start recording
    act(() => {
      result.current.setRecording(true)
    })
    
    await act(async () => {
      await result.current.stopRecording()
    })
    
    expect(result.current.isRecording).toBe(false)
    expect(result.current.recordedActions).toEqual(mockActions)
    expect(mockInvoke).toHaveBeenCalledWith('stop_recording')
  })

  it('handles recording stop error', async () => {
    const errorMessage = 'Failed to stop recording'
    mockInvoke.mockRejectedValue(new Error(errorMessage))
    
    const { result } = renderHook(() => useRecordingStore())
    
    // First start recording
    act(() => {
      result.current.setRecording(true)
    })
    
    await act(async () => {
      await result.current.stopRecording()
    })
    
    expect(result.current.isRecording).toBe(true) // Should remain true on error
    expect(result.current.error).toBe(errorMessage)
  })

  it('updates recording settings', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    const newSettings = {
      captureInterval: 200,
      quality: 'medium' as const,
      includeClicks: false,
      includeKeyboard: true,
      includeScrolling: false
    }
    
    act(() => {
      result.current.updateSettings(newSettings)
    })
    
    expect(result.current.recordingSettings).toEqual(newSettings)
  })

  it('adds action to recording', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    const action = {
      type: 'click' as const,
      x: 150,
      y: 250,
      timestamp: Date.now()
    }
    
    act(() => {
      result.current.addAction(action)
    })
    
    expect(result.current.recordedActions).toContain(action)
  })

  it('clears recording data', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    // Add some data first
    act(() => {
      result.current.addAction({
        type: 'click',
        x: 100,
        y: 200,
        timestamp: Date.now()
      })
      result.current.setError('Some error')
    })
    
    act(() => {
      result.current.clearRecording()
    })
    
    expect(result.current.recordedActions).toEqual([])
    expect(result.current.currentAction).toBeNull()
    expect(result.current.error).toBeNull()
  })
})