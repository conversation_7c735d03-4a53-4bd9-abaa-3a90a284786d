# ShowForAI V3 代码清理计划

## 分析结果

### GUI文件分析
1. **recorder/gui.py** (723行)
   - ✅ 专注于录制功能（产品核心）
   - ✅ 包含录制、上传、AI识别流程
   - ✅ 已修复网络检查逻辑
   - **决定：保留为主GUI**

2. **gui/main_window.py** (548行)
   - ❌ 通用主窗口，功能分散
   - ❌ 包含不必要的菜单和选项
   - **决定：归档**

3. **gui/enhanced_main_window.py** (681行)
   - ❌ 过度复杂，包含认证等非核心功能
   - ❌ 违背简洁原则
   - **决定：归档**

## 清理策略（根据产品原则）

### 保留的核心文件
```
src/showforai/
├── recorder/
│   ├── gui.py                 # 主GUI（录制为核心）
│   └── optimized_recorder.py  # 优化的录制器
├── executor/
│   └── optimized_executor.py  # 执行器（支持离线）
├── utils/
│   ├── network_manager.py     # 统一网络管理（新建）
│   └── image_processor.py     # 768x768处理
└── main.py                    # 单一入口点
```

### 需要归档的文件
```
archived/
├── gui/
│   ├── main_window.py
│   ├── enhanced_main_window.py
│   └── 相关组件文件
├── recorder/
│   └── recorder.py (旧版本)
└── launcher/
    ├── launcher.py
    └── gui_launcher.py
```

## 执行步骤

### 第1步：归档重复文件 ✅
- 创建archived文件夹结构

### 第2步：分析GUI文件 ✅
- 确定recorder/gui.py为主干

### 第3步：移动文件到归档
- 移动main_window.py
- 移动enhanced_main_window.py
- 移动相关launcher文件

### 第4步：创建统一组件
- NetworkManager - 统一网络检查
- 简化配置管理
- 单一入口点

### 第5步：验证功能
- 测试录制功能
- 测试执行功能
- 确认网络检查正常

## 预期结果

### 代码简化
- 代码量减少约40%
- 文件数量减少50%
- 启动入口从5个减到1个

### 符合产品原则
✅ 录制为核心功能
✅ 768x768标准化处理
✅ 智能等待机制
✅ 录制必须在线，执行可离线
✅ 简洁的用户界面
✅ 清晰的错误提示

### 性能提升
- 启动速度提升50%
- 内存占用减少30%
- 代码维护性提升