#!/usr/bin/env python3
"""
Test script to verify 768x768 standardization implementation.
"""

import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from showforai.ai.image_processor import ImageProcessor
from showforai.ai.bbox_processor import BboxProcessor
from showforai.ai.element_detector import ElementDetector
from PIL import Image
import numpy as np
import io


def test_image_processor():
    """Test ImageProcessor 768x768 standardization."""
    print("\n=== Testing ImageProcessor ===")
    
    processor = ImageProcessor()
    
    # Create a test image (1920x1080)
    test_img = Image.new('RGB', (1920, 1080), color='blue')
    img_bytes = io.BytesIO()
    test_img.save(img_bytes, format='PNG')
    img_data = img_bytes.getvalue()
    
    # Test resize_to_standard
    resized_data, original_res = processor.resize_to_standard(img_data)
    resized_img = Image.open(io.BytesIO(resized_data))
    
    assert resized_img.size == (768, 768), f"Expected (768, 768), got {resized_img.size}"
    assert original_res == (1920, 1080), f"Expected (1920, 1080), got {original_res}"
    print(f"✓ resize_to_standard: {original_res} -> {resized_img.size}")
    
    # Test coordinate conversion
    x, y = 960, 540  # Center of 1920x1080
    std_x, std_y = processor.original_to_standard(x, y, 1920, 1080)
    assert std_x == 384, f"Expected 384, got {std_x}"  # 960 * 768 / 1920 = 384
    assert std_y == 384, f"Expected 384, got {std_y}"  # 540 * 768 / 1080 = 384
    print(f"✓ original_to_standard: ({x}, {y}) -> ({std_x}, {std_y})")
    
    # Test reverse conversion
    orig_x, orig_y = processor.standard_to_original(std_x, std_y, 1920, 1080)
    assert orig_x == x, f"Expected {x}, got {orig_x}"
    assert orig_y == y, f"Expected {y}, got {orig_y}"
    print(f"✓ standard_to_original: ({std_x}, {std_y}) -> ({orig_x}, {orig_y})")
    
    # Test preprocess_for_ai
    processed_data, metadata = processor.preprocess_for_ai(img_data)
    processed_img = Image.open(io.BytesIO(processed_data))
    
    assert processed_img.size == (768, 768), f"Expected (768, 768), got {processed_img.size}"
    assert metadata['original_width'] == 1920
    assert metadata['original_height'] == 1080
    assert metadata['standard_width'] == 768
    assert metadata['standard_height'] == 768
    print(f"✓ preprocess_for_ai: Metadata correct")
    
    print("✅ ImageProcessor tests passed!")


def test_bbox_processor():
    """Test BboxProcessor 768x768 handling."""
    print("\n=== Testing BboxProcessor ===")
    
    processor = BboxProcessor()
    
    # Test validate_bbox
    valid_bbox = {'x': 100, 'y': 100, 'width': 200, 'height': 200}
    assert processor.validate_bbox(valid_bbox), "Valid bbox should pass"
    print(f"✓ validate_bbox: Valid bbox accepted")
    
    invalid_bbox = {'x': 700, 'y': 700, 'width': 200, 'height': 200}  # Goes beyond 768
    assert not processor.validate_bbox(invalid_bbox), "Invalid bbox should fail"
    print(f"✓ validate_bbox: Out-of-bounds bbox rejected")
    
    # Test bbox conversion to original
    bbox_768 = {'x': 384, 'y': 384, 'width': 100, 'height': 100}
    bbox_orig = processor.bbox_to_original(bbox_768, 1920, 1080)
    
    expected_x = int(384 * 1920 / 768)  # 960
    expected_y = int(384 * 1080 / 768)  # 540
    expected_w = int(100 * 1920 / 768)  # 250
    expected_h = int(100 * 1080 / 768)  # 140
    
    assert bbox_orig['x'] == expected_x, f"Expected {expected_x}, got {bbox_orig['x']}"
    assert bbox_orig['y'] == expected_y, f"Expected {expected_y}, got {bbox_orig['y']}"
    assert bbox_orig['width'] == expected_w, f"Expected {expected_w}, got {bbox_orig['width']}"
    assert bbox_orig['height'] == expected_h, f"Expected {expected_h}, got {bbox_orig['height']}"
    print(f"✓ bbox_to_original: 768x768 -> 1920x1080 conversion correct")
    
    # Test bbox conversion to standard
    bbox_orig = {'x': 960, 'y': 540, 'width': 250, 'height': 140}
    bbox_std = processor.bbox_to_standard(bbox_orig, 1920, 1080)
    
    assert bbox_std['x'] == 384, f"Expected 384, got {bbox_std['x']}"
    assert bbox_std['y'] == 384, f"Expected 384, got {bbox_std['y']}"
    print(f"✓ bbox_to_standard: 1920x1080 -> 768x768 conversion correct")
    
    # Test process_bboxes
    bboxes = [
        {'x': 960, 'y': 540, 'width': 250, 'height': 140},
        {'x': 100, 'y': 100, 'width': 200, 'height': 200}
    ]
    processed = processor.process_bboxes(bboxes, from_resolution=(1920, 1080))
    
    assert len(processed) == 2, f"Expected 2 bboxes, got {len(processed)}"
    assert all(processor.validate_bbox(bbox) for bbox in processed), "All processed bboxes should be valid"
    print(f"✓ process_bboxes: Converted and validated {len(processed)} bboxes")
    
    print("✅ BboxProcessor tests passed!")


def test_element_detector_mock():
    """Test ElementDetector coordinate handling (mock test without server)."""
    print("\n=== Testing ElementDetector (Mock) ===")
    
    # Note: This is a mock test since we don't have access to the actual AI server
    detector = ElementDetector()
    
    # Test that detector has the new parameters
    import inspect
    detect_sig = inspect.signature(detector.detect_element)
    assert 'original_resolution' in detect_sig.parameters, "detect_element should have original_resolution parameter"
    print(f"✓ detect_element has original_resolution parameter")
    
    detect_mode_sig = inspect.signature(detector.detect_with_mode)
    assert 'original_resolution' in detect_mode_sig.parameters, "detect_with_mode should have original_resolution parameter"
    print(f"✓ detect_with_mode has original_resolution parameter")
    
    print("✅ ElementDetector interface tests passed!")


def test_constants():
    """Test that all components use consistent 768x768 resolution."""
    print("\n=== Testing Constants ===")
    
    from showforai.ai.image_processor import ImageProcessor
    from showforai.ai.bbox_processor import BboxProcessor
    
    img_proc = ImageProcessor()
    bbox_proc = BboxProcessor()
    
    assert img_proc.STANDARD_RESOLUTION == (768, 768), f"ImageProcessor should use (768, 768)"
    assert bbox_proc.STANDARD_RESOLUTION == (768, 768), f"BboxProcessor should use (768, 768)"
    
    print(f"✓ ImageProcessor STANDARD_RESOLUTION: {img_proc.STANDARD_RESOLUTION}")
    print(f"✓ BboxProcessor STANDARD_RESOLUTION: {bbox_proc.STANDARD_RESOLUTION}")
    
    print("✅ All components use consistent 768x768 resolution!")


def main():
    """Run all tests."""
    print("=" * 50)
    print("Testing 768x768 Standardization Implementation")
    print("=" * 50)
    
    try:
        test_constants()
        test_image_processor()
        test_bbox_processor()
        test_element_detector_mock()
        
        print("\n" + "=" * 50)
        print("✅ ALL TESTS PASSED!")
        print("=" * 50)
        print("\nSummary:")
        print("- ImageProcessor correctly resizes to 768x768")
        print("- Coordinate conversion works bidirectionally")
        print("- BboxProcessor validates and converts bboxes correctly")
        print("- ElementDetector has proper interface for resolution handling")
        print("- All components use consistent 768x768 standard resolution")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()