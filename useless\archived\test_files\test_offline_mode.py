"""
Test offline mode functionality.
Verifies that:
1. Recording is disabled when offline
2. <PERSON>ript execution works offline
3. UI provides clear feedback
"""
import sys
import socket
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from showforai.sync.offline_manager import OfflineModeManager
from showforai.recorder.recorder import Recorder
from showforai.executor.executor import <PERSON><PERSON>tExecutor


def test_offline_manager():
    """Test offline manager functionality."""
    print("Testing Offline Manager...")
    
    manager = OfflineModeManager()
    
    # Test network detection
    print(f"Current network status: {'Online' if manager.is_online() else 'Offline'}")
    
    # Test recording permission
    print(f"Recording allowed: {manager.is_recording_allowed()}")
    
    # Test execution permission
    print(f"Execution allowed: {manager.is_execution_allowed()}")
    
    # Test messages
    print("\nOffline message:")
    print(manager.get_offline_message())
    
    print("\nRecording disabled message:")
    print(manager.get_recording_disabled_message())
    
    print("✓ Offline manager tests passed")


def test_offline_recording():
    """Test that recording is properly disabled when offline."""
    print("\nTesting Offline Recording...")
    
    # Mock offline state
    with patch('socket.gethostbyname', side_effect=socket.gaierror):
        manager = OfflineModeManager()
        
        # Verify offline detection
        assert not manager.is_online(), "Should detect offline state"
        
        # Verify recording is disabled
        assert not manager.is_recording_allowed(), "Recording should be disabled offline"
        
        # Verify execution is still allowed
        assert manager.is_execution_allowed(), "Execution should be allowed offline"
        
        print("✓ Recording correctly disabled in offline mode")


def test_online_recording():
    """Test that recording works when online."""
    print("\nTesting Online Recording...")
    
    # Mock online state
    with patch('socket.gethostbyname', return_value='*******'):
        manager = OfflineModeManager()
        
        # Verify online detection
        assert manager.is_online(), "Should detect online state"
        
        # Verify recording is allowed
        assert manager.is_recording_allowed(), "Recording should be allowed online"
        
        # Verify execution is allowed
        assert manager.is_execution_allowed(), "Execution should be allowed online"
        
        print("✓ Recording correctly enabled in online mode")


def test_status_callbacks():
    """Test network status change callbacks."""
    print("\nTesting Status Callbacks...")
    
    manager = OfflineModeManager()
    
    # Track callback invocations
    callback_invoked = []
    
    def test_callback(is_online):
        callback_invoked.append(is_online)
    
    # Add callback
    manager.add_status_callback(test_callback)
    
    # Simulate status change
    old_status = manager._is_online
    manager._is_online = not old_status
    manager._notify_status_change(not old_status)
    
    # Verify callback was invoked
    assert len(callback_invoked) == 1, "Callback should be invoked on status change"
    assert callback_invoked[0] == (not old_status), "Callback should receive correct status"
    
    # Remove callback
    manager.remove_status_callback(test_callback)
    
    # Simulate another change
    manager._notify_status_change(old_status)
    
    # Verify callback was not invoked after removal
    assert len(callback_invoked) == 1, "Callback should not be invoked after removal"
    
    print("✓ Status callbacks working correctly")


def test_recorder_network_check():
    """Test that recorder checks network before starting."""
    print("\nTesting Recorder Network Check...")
    
    # Mock offline state
    with patch('showforai.utils.network_checker.is_online', return_value=False):
        with patch('showforai.utils.network_checker.check_ai_service', return_value=False):
            recorder = Recorder()
            
            # Verify network check returns False
            assert not recorder.check_network_status(), "Recorder should detect offline state"
            
            # Try to start recording (should raise error)
            try:
                recorder.start_recording()
                assert False, "Should raise error when trying to record offline"
            except RuntimeError as e:
                assert "录制需要网络连接" in str(e), "Should have correct error message"
                print(f"✓ Recorder correctly blocked offline recording: {e}")


def main():
    """Run all tests."""
    print("=" * 50)
    print("Testing Offline Mode Implementation")
    print("=" * 50)
    
    try:
        test_offline_manager()
        test_offline_recording()
        test_online_recording()
        test_status_callbacks()
        test_recorder_network_check()
        
        print("\n" + "=" * 50)
        print("All Offline Mode Tests PASSED! ✓")
        print("=" * 50)
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()