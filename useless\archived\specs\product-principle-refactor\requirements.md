# 产品原则重构 - 需求定义

## 1. 项目背景与目标

### 1.1 背景说明
ShowForAI V3当前实现存在多个不符合产品原则的问题，需要进行全面重构以确保：
- 严格遵守产品原则文档的所有要求
- 提升用户体验，特别是GUI界面
- 修复所有已知bug和缺陷
- 实现真正的纯图像识别方案

### 1.2 核心目标
- **质量优先**：宁可失败也不误操作，检测阈值必须≥0.80
- **用户体验**：提供直观、流畅、可靠的操作体验
- **产品合规**：每个功能都严格符合产品原则

## 2. 功能需求（EARS格式）

### 2.1 核心功能合规性改造

#### FR-001: 纯图像识别实现
**WHEN** 系统执行任何元素定位操作
**THEN** 系统SHALL 仅使用基于图像内容的识别方法
**AND** 系统SHALL NOT 使用DOM、Accessibility API或其他非图像方式

#### FR-002: 质量优先原则
**WHEN** 系统进行图像匹配
**IF** 匹配置信度 < 0.80
**THEN** 系统SHALL 报告匹配失败
**AND** 系统SHALL NOT 降低阈值以强行成功

#### FR-003: 768×768标准化处理
**WHEN** 用户录制脚本
**THEN** 系统SHALL 将所有截图统一缩放为768×768分辨率
**AND** 系统SHALL 基于768×768坐标系进行所有BBOX计算

#### FR-004: 离线模式功能区分
**WHEN** 系统处于离线状态
**THEN** 系统SHALL 禁用录制功能并显示"录制需要网络连接以进行AI识别"
**AND** 系统SHALL 允许执行已下载的本地脚本

### 2.2 智能等待机制

#### FR-005: 辅助模式等待策略
**WHEN** 用户选择辅助模式执行
**THEN** 系统SHALL 在后台持续识别目标元素
**AND** 系统SHALL 无限等待直到找到元素或用户手动停止

#### FR-006: 主动模式等待策略
**WHEN** 用户选择主动模式执行
**THEN** 系统SHALL 首先等待录制时的操作间隔时间
**IF** 首次识别失败
**THEN** 系统SHALL 每隔1秒重新识别直到成功

#### FR-007: 步进执行保证
**WHEN** 脚本执行过程中
**IF** 当前步骤未成功识别
**THEN** 系统SHALL NOT 继续执行下一步
**AND** 系统SHALL 持续尝试当前步骤直到成功或超时

### 2.3 用户体验优化

#### FR-008: 实时状态反馈
**WHEN** 系统执行任何耗时操作
**THEN** 系统SHALL 显示具体的进度信息
**INCLUDING** "正在上传录制数据..."、"正在识别界面元素..."、"正在执行第X/Y步..."等

#### FR-009: 错误提示友好化
**WHEN** 系统遇到错误
**THEN** 系统SHALL 显示用户可理解的错误信息
**AND** 系统SHALL 提供明确的解决建议或下一步操作指引

#### FR-010: GUI布局优化
**WHEN** 用户打开应用程序
**THEN** 系统SHALL 显示清晰的功能分区
**WITH** 录制区、脚本管理区、执行监控区、设置区明确分离

### 2.4 录制流程优化

#### FR-011: 持续缓存机制
**WHEN** 用户开始录制
**THEN** 系统SHALL 以10FPS持续截取屏幕
**AND** 系统SHALL 维护最多15帧的循环缓冲区

#### FR-012: 点击前图像获取
**WHEN** 用户执行点击操作
**THEN** 系统SHALL 从缓冲区获取点击前第3帧（约0.3秒前）的图像
**TO** 避免鼠标指针和点击效果的干扰

#### FR-013: 操作间隔记录
**WHEN** 用户执行连续操作
**THEN** 系统SHALL 精确记录相邻操作之间的时间间隔（精确到毫秒）
**FOR** 执行时的智能等待参考

### 2.5 执行流程优化

#### FR-014: 多级识别降级策略
**WHEN** 系统执行元素识别
**THEN** 系统SHALL 按顺序尝试：
1. 直接模板匹配（阈值≥0.85）
2. ORB特征点匹配（至少15个匹配点）
3. SIFT特征点匹配（至少20个匹配点）
4. 多尺度模板匹配（阈值≥0.80）

#### FR-015: 分辨率适配
**WHEN** 执行脚本的屏幕分辨率与录制时不同
**THEN** 系统SHALL 根据分辨率比例智能缩放模板图像
**AND** 系统SHALL 使用特征点匹配确保识别准确性

### 2.6 安全性需求

#### FR-016: API请求签名
**WHEN** 系统发送任何API请求
**THEN** 系统SHALL 添加时间戳和nonce
**AND** 系统SHALL 使用HMAC-SHA256进行请求签名

#### FR-017: 重放攻击防护
**WHEN** 服务器接收API请求
**IF** 请求时间戳超过5分钟或nonce已使用
**THEN** 服务器SHALL 拒绝该请求

## 3. 非功能需求

### 3.1 性能要求

#### NFR-001: 录制性能
**WHEN** 系统进行屏幕录制
**THEN** 系统SHALL 保持10FPS的稳定帧率
**AND** CPU占用率SHALL NOT 超过15%

#### NFR-002: 识别速度
**WHEN** 系统执行元素识别
**THEN** 单次识别SHALL 在500ms内完成
**EXCEPT** SIFT特征匹配可延长至1000ms

### 3.2 可用性要求

#### NFR-003: 界面响应
**WHEN** 用户执行任何UI操作
**THEN** 界面SHALL 在100ms内给出视觉反馈

#### NFR-004: 帮助系统
**WHEN** 用户首次使用或遇到困难
**THEN** 系统SHALL 提供上下文相关的帮助提示

### 3.3 可靠性要求

#### NFR-005: 错误恢复
**WHEN** 系统遇到可恢复错误
**THEN** 系统SHALL 自动尝试恢复
**AND** 系统SHALL 记录错误日志供调试

#### NFR-006: 数据完整性
**WHEN** 系统保存或传输数据
**THEN** 系统SHALL 确保数据完整性
**WITH** 校验和验证机制

## 4. 约束条件

### 4.1 技术约束
- Python 3.8+ 环境
- PyQt6 GUI框架
- OpenCV 图像处理
- SQLite 本地存储
- Supabase 云端服务

### 4.2 业务约束
- 不使用DOM或其他非图像定位方式
- 不降低检测阈值以提高"成功率"
- 离线模式下禁用录制功能
- 保持768×768标准化处理流程

## 5. 验收标准

### 5.1 功能验收
- [ ] 所有图像识别均使用纯图像方法
- [ ] 检测阈值严格≥0.80
- [ ] 768×768标准化正确实现
- [ ] 离线/在线功能正确区分
- [ ] 智能等待机制按设计工作

### 5.2 用户体验验收
- [ ] GUI界面清晰直观
- [ ] 操作流程顺畅
- [ ] 错误提示友好
- [ ] 进度反馈及时
- [ ] 帮助系统完善

### 5.3 性能验收
- [ ] 录制稳定10FPS
- [ ] 识别速度达标
- [ ] 界面响应及时
- [ ] 资源占用合理

## 6. 风险评估

### 6.1 技术风险
- **风险**：特征点匹配在某些场景下可能失败
- **缓解**：提供多级降级策略，保持高质量阈值

### 6.2 用户体验风险
- **风险**：严格的质量标准可能导致识别失败率增加
- **缓解**：提供清晰的失败原因说明和解决建议

### 6.3 兼容性风险
- **风险**：不同分辨率屏幕的适配问题
- **缓解**：使用768×768标准化和智能缩放策略

---

**文档状态**：待审核
**创建时间**：2025-01-13
**版本**：1.0