"""
Test suite for the multi-level recognition degradation system.

Tests the 3-level degradation strategy:
1. SIFT (Precise) → ORB (Fast) → Template (Basic)
2. Intelligent triggering based on performance
3. Automatic recovery when performance improves
"""

import unittest
import numpy as np
import cv2
import time
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from src.showforai.recognition import (
    DegradationManager,
    DegradationLevel,
    DegradationResult,
    PerformanceMonitor,
    RecoveryManager
)
from src.showforai.recognition.recovery_manager import RecoveryStrategy


class TestDegradationManager(unittest.TestCase):
    """Test the degradation manager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.manager = DegradationManager(enable_recovery=True)
        
        # Create test images
        self.screenshot = np.random.randint(0, 255, (768, 768, 3), dtype=np.uint8)
        self.template = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    def test_initialization(self):
        """Test manager initialization."""
        self.assertEqual(self.manager.current_level, DegradationLevel.SIFT)
        self.assertTrue(self.manager.enable_recovery)
        self.assertIsNotNone(self.manager.sift)
        self.assertIsNotNone(self.manager.orb)
    
    def test_sift_matching(self):
        """Test SIFT feature matching."""
        # Create images with features
        screenshot = np.zeros((500, 500, 3), dtype=np.uint8)
        template = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # Add some features
        cv2.rectangle(screenshot, (200, 200), (300, 300), (255, 255, 255), -1)
        cv2.rectangle(template, (0, 0), (100, 100), (255, 255, 255), -1)
        
        result = self.manager._match_sift(screenshot, template)
        
        self.assertIn('found', result)
        self.assertIn('confidence', result)
        if result['found']:
            self.assertIn('location', result)
            self.assertIn('bbox', result)
    
    def test_orb_matching(self):
        """Test ORB feature matching."""
        # Create images with features
        screenshot = np.zeros((500, 500, 3), dtype=np.uint8)
        template = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # Add some features
        cv2.circle(screenshot, (250, 250), 50, (255, 255, 255), -1)
        cv2.circle(template, (50, 50), 50, (255, 255, 255), -1)
        
        result = self.manager._match_orb(screenshot, template)
        
        self.assertIn('found', result)
        self.assertIn('confidence', result)
    
    def test_template_matching(self):
        """Test basic template matching."""
        # Create a screenshot with the template embedded
        screenshot = np.zeros((500, 500, 3), dtype=np.uint8)
        template = np.ones((50, 50, 3), dtype=np.uint8) * 255
        
        # Place template in screenshot
        screenshot[100:150, 100:150] = template
        
        result = self.manager._match_template(screenshot, template)
        
        self.assertTrue(result['found'])
        self.assertGreater(result['confidence'], 0.9)
        self.assertEqual(result['location'], (100, 100))
    
    def test_multi_level_degradation(self):
        """Test multi-level degradation sequence."""
        # Start at SIFT level
        self.assertEqual(self.manager.current_level, DegradationLevel.SIFT)
        
        # Simulate poor SIFT performance
        for _ in range(5):
            self.manager.metrics[DegradationLevel.SIFT].update(False, 1200)
        
        # Check if should degrade
        should_degrade, reason = self.manager.metrics[DegradationLevel.SIFT].should_degrade()
        self.assertTrue(should_degrade)
        
        # Perform match which should trigger degradation
        result = self.manager.match(self.screenshot, self.template)
        
        self.assertIn(DegradationLevel.SIFT, result.levels_tried)
        self.assertIsNotNone(result.degradation_reason)
    
    def test_intelligent_triggering(self):
        """Test intelligent triggering based on performance."""
        # Test SIFT > 1000ms triggers degradation
        for _ in range(3):
            self.manager.metrics[DegradationLevel.SIFT].update(True, 1100)
        
        should_degrade, reason = self.manager.metrics[DegradationLevel.SIFT].should_degrade()
        self.assertTrue(should_degrade)
        self.assertIn("1000ms", reason)
        
        # Test ORB > 500ms triggers degradation
        for _ in range(3):
            self.manager.metrics[DegradationLevel.ORB].update(True, 600)
        
        should_degrade, reason = self.manager.metrics[DegradationLevel.ORB].should_degrade()
        self.assertTrue(should_degrade)
        self.assertIn("500ms", reason)
    
    def test_recovery_mechanism(self):
        """Test automatic recovery to higher levels."""
        # Start at TEMPLATE level
        self.manager.current_level = DegradationLevel.TEMPLATE
        
        # Simulate good performance
        for _ in range(10):
            self.manager.metrics[DegradationLevel.TEMPLATE].update(True, 150)
        
        # Check if can recover
        can_recover = self.manager.metrics[DegradationLevel.TEMPLATE].can_recover()
        self.assertTrue(can_recover)
    
    def test_force_level(self):
        """Test forcing a specific level."""
        result = self.manager.match(
            self.screenshot,
            self.template,
            force_level=DegradationLevel.TEMPLATE
        )
        
        self.assertEqual(result.levels_tried[0], DegradationLevel.TEMPLATE)
    
    def test_performance_metrics(self):
        """Test performance metrics tracking."""
        # Perform several matches
        for i in range(5):
            success = i % 2 == 0
            time_ms = 100 + i * 50
            self.manager.metrics[DegradationLevel.SIFT].update(success, time_ms)
        
        metrics = self.manager.metrics[DegradationLevel.SIFT]
        self.assertEqual(metrics.total_attempts, 5)
        self.assertEqual(metrics.success_count, 3)
        self.assertEqual(metrics.failure_count, 2)
        self.assertAlmostEqual(metrics.success_rate, 0.6, places=2)


class TestPerformanceMonitor(unittest.TestCase):
    """Test the performance monitor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.monitor = PerformanceMonitor(history_size=100)
    
    def test_record_match(self):
        """Test recording match attempts."""
        snapshot = self.monitor.record_match(
            level="SIFT",
            success=True,
            time_ms=250.5,
            confidence=0.95
        )
        
        self.assertEqual(snapshot.level, "SIFT")
        self.assertTrue(snapshot.success)
        self.assertEqual(snapshot.time_ms, 250.5)
        self.assertEqual(snapshot.confidence, 0.95)
        
        self.assertEqual(self.monitor.total_matches, 1)
        self.assertEqual(self.monitor.successful_matches, 1)
    
    def test_performance_state(self):
        """Test performance state determination."""
        # Simulate excellent performance
        for _ in range(10):
            self.monitor.record_match("SIFT", True, 150, 0.98)
        
        state = self.monitor.get_current_state()
        self.assertIn(state.value, ["excellent", "good"])
        
        # Simulate poor performance
        for _ in range(10):
            self.monitor.record_match("SIFT", False, 1500, 0.0)
        
        state = self.monitor.get_current_state()
        self.assertIn(state.value, ["poor", "critical"])
    
    def test_degradation_detection(self):
        """Test degradation trigger detection."""
        # Simulate slow SIFT performance
        for _ in range(3):
            self.monitor.record_match("SIFT", True, 1200, 0.85)
        
        should_degrade, reason = self.monitor.should_degrade("SIFT")
        self.assertTrue(should_degrade)
        self.assertIn("1", reason)  # Should mention time threshold
    
    def test_recovery_detection(self):
        """Test recovery opportunity detection."""
        # Simulate good TEMPLATE performance
        for _ in range(5):
            self.monitor.record_match("TEMPLATE", True, 150, 0.9)
        
        can_recover = self.monitor.can_recover("TEMPLATE")
        self.assertTrue(can_recover)
    
    def test_report_generation(self):
        """Test performance report generation."""
        # Add some test data
        for i in range(20):
            success = i % 3 != 0
            time_ms = 200 + i * 10
            self.monitor.record_match("SIFT", success, time_ms, 0.8 if success else 0.0)
        
        report = self.monitor.generate_report(period_hours=1.0)
        
        self.assertEqual(report.total_matches, 20)
        self.assertGreater(report.successful_matches, 0)
        self.assertGreater(report.average_time_ms, 0)
        self.assertIsNotNone(report.overall_state)
        self.assertIsInstance(report.recommendations, list)
    
    def test_degradation_event_recording(self):
        """Test recording degradation events."""
        self.monitor.record_degradation("SIFT", "ORB", "High latency")
        
        self.assertEqual(len(self.monitor.degradation_events), 1)
        event = self.monitor.degradation_events[0]
        self.assertEqual(event['from_level'], "SIFT")
        self.assertEqual(event['to_level'], "ORB")
        self.assertEqual(event['reason'], "High latency")


class TestRecoveryManager(unittest.TestCase):
    """Test the recovery manager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.recovery_mgr = RecoveryManager(strategy=RecoveryStrategy.BALANCED)
    
    def test_initialization(self):
        """Test recovery manager initialization."""
        self.assertEqual(self.recovery_mgr.strategy, RecoveryStrategy.BALANCED)
        self.assertTrue(self.recovery_mgr.recovery_enabled)
        self.assertEqual(self.recovery_mgr.consecutive_failures, 0)
    
    def test_performance_update(self):
        """Test performance tracking update."""
        self.recovery_mgr.update_performance("ORB", True, 250)
        
        perf_data = list(self.recovery_mgr.recent_performance["ORB"])
        self.assertEqual(len(perf_data), 1)
        self.assertTrue(perf_data[0]['success'])
        self.assertEqual(perf_data[0]['time_ms'], 250)
    
    def test_recovery_conditions(self):
        """Test recovery condition checking."""
        # Simulate good TEMPLATE performance
        for _ in range(15):
            self.recovery_mgr.update_performance("TEMPLATE", True, 180)
        
        should_recover, target, reason = self.recovery_mgr.should_recover("TEMPLATE")
        
        self.assertTrue(should_recover)
        self.assertEqual(target, "ORB")
        self.assertIn("improved", reason.lower())
    
    def test_cooldown_management(self):
        """Test cooldown period management."""
        # Attempt recovery
        attempt = self.recovery_mgr.attempt_recovery("TEMPLATE", "ORB")
        
        self.assertIsNotNone(self.recovery_mgr.cooldown_until)
        
        # Check if in cooldown
        should_recover, _, reason = self.recovery_mgr.should_recover("TEMPLATE")
        self.assertFalse(should_recover)
        self.assertIn("cooldown", reason.lower())
    
    def test_consecutive_failures(self):
        """Test consecutive failure tracking."""
        # Simulate failures
        for _ in range(3):
            attempt = self.recovery_mgr.attempt_recovery("TEMPLATE", "ORB")
            self.recovery_mgr.confirm_recovery(False, "Test failure")
        
        self.assertEqual(self.recovery_mgr.consecutive_failures, 3)
        
        # Clear cooldown to test failure blocking
        self.recovery_mgr.cooldown_until = None
        
        # Should not allow recovery after too many failures
        should_recover, _, reason = self.recovery_mgr.should_recover("TEMPLATE")
        self.assertFalse(should_recover)
        self.assertIn("failures", reason.lower())
    
    def test_rollback_detection(self):
        """Test rollback detection."""
        # Simulate recent recovery
        attempt = self.recovery_mgr.attempt_recovery("ORB", "SIFT")
        self.recovery_mgr.confirm_recovery(True)
        
        # Simulate poor performance at new level
        for _ in range(5):
            self.recovery_mgr.update_performance("SIFT", False, 2000)
        
        should_rollback, target, reason = self.recovery_mgr.should_rollback("SIFT")
        
        self.assertTrue(should_rollback)
        self.assertEqual(target, "ORB")
        self.assertIn("failure", reason.lower())
    
    def test_strategy_change(self):
        """Test changing recovery strategy."""
        self.recovery_mgr.set_strategy(RecoveryStrategy.AGGRESSIVE)
        
        self.assertEqual(self.recovery_mgr.strategy, RecoveryStrategy.AGGRESSIVE)
        
        # Check that conditions changed
        conditions = self.recovery_mgr.conditions['TEMPLATE']['to_ORB']
        self.assertEqual(conditions.cooldown_seconds, 15)  # Aggressive has shorter cooldown
    
    def test_recovery_history(self):
        """Test recovery history tracking."""
        # Perform some recoveries
        for i in range(3):
            attempt = self.recovery_mgr.attempt_recovery("TEMPLATE", "ORB")
            self.recovery_mgr.confirm_recovery(i % 2 == 0)
        
        history = self.recovery_mgr.get_history(limit=5)
        
        self.assertEqual(len(history), 3)
        self.assertEqual(history[0]['from_level'], "TEMPLATE")
        self.assertEqual(history[0]['to_level'], "ORB")


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.degradation_mgr = DegradationManager(enable_recovery=True)
        self.performance_monitor = PerformanceMonitor()
        self.recovery_mgr = RecoveryManager(strategy=RecoveryStrategy.BALANCED)
    
    def test_complete_degradation_flow(self):
        """Test complete degradation flow from SIFT to TEMPLATE."""
        # Create test images
        screenshot = np.zeros((500, 500, 3), dtype=np.uint8)
        template = np.ones((50, 50, 3), dtype=np.uint8) * 255
        screenshot[100:150, 100:150] = template
        
        # Start at SIFT
        self.assertEqual(self.degradation_mgr.current_level, DegradationLevel.SIFT)
        
        # Simulate poor SIFT performance to trigger degradation
        for _ in range(5):
            self.degradation_mgr.metrics[DegradationLevel.SIFT].update(False, 1200)
        
        # Perform match
        result = self.degradation_mgr.match(screenshot, template)
        
        # Record in performance monitor
        self.performance_monitor.record_match(
            result.level.name if result.level else "UNKNOWN",
            result.found,
            result.time_ms,
            result.confidence
        )
        
        # Check if degradation was recorded
        if result.degradation_reason:
            self.performance_monitor.record_degradation(
                "SIFT",
                self.degradation_mgr.current_level.name,
                result.degradation_reason
            )
        
        self.assertIsNotNone(result)
        self.assertGreater(len(result.levels_tried), 0)
    
    def test_recovery_flow(self):
        """Test complete recovery flow from TEMPLATE to SIFT."""
        # Start at TEMPLATE level
        self.degradation_mgr.current_level = DegradationLevel.TEMPLATE
        
        # Simulate good performance
        for _ in range(10):
            self.degradation_mgr.metrics[DegradationLevel.TEMPLATE].update(True, 150)
            self.recovery_mgr.update_performance("TEMPLATE", True, 150)
            self.performance_monitor.record_match("TEMPLATE", True, 150, 0.9)
        
        # Check recovery conditions
        should_recover, target, reason = self.recovery_mgr.should_recover("TEMPLATE")
        
        if should_recover:
            # Attempt recovery
            attempt = self.recovery_mgr.attempt_recovery("TEMPLATE", target)
            
            # Update degradation manager
            self.degradation_mgr.current_level = DegradationLevel.ORB
            
            # Confirm recovery based on next performance
            self.recovery_mgr.confirm_recovery(True)
            
            # Record in performance monitor
            self.performance_monitor.record_recovery("TEMPLATE", "ORB")
        
        # Verify state
        self.assertIsNotNone(self.recovery_mgr.last_recovery)
    
    def test_performance_report_with_degradation(self):
        """Test performance report including degradation events."""
        # Simulate activity
        for i in range(30):
            level = ["SIFT", "ORB", "TEMPLATE"][i % 3]
            success = i % 4 != 0
            time_ms = 200 + i * 20
            
            self.performance_monitor.record_match(level, success, time_ms, 0.8 if success else 0.0)
            
            if i == 10:
                self.performance_monitor.record_degradation("SIFT", "ORB", "Test degradation")
            if i == 20:
                self.performance_monitor.record_recovery("ORB", "SIFT")
        
        # Generate report
        report = self.performance_monitor.generate_report(period_hours=1.0)
        
        self.assertEqual(report.total_matches, 30)
        self.assertEqual(report.degradation_count, 1)
        self.assertEqual(report.recovery_count, 1)
        self.assertIn("SIFT", report.level_stats)
        self.assertIn("ORB", report.level_stats)
        self.assertIn("TEMPLATE", report.level_stats)


def run_tests():
    """Run all tests."""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestDegradationManager))
    suite.addTests(loader.loadTestsFromTestCase(TestPerformanceMonitor))
    suite.addTests(loader.loadTestsFromTestCase(TestRecoveryManager))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "="*50)
    print("Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)