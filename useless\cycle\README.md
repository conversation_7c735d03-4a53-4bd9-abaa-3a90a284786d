# 边界框可视化工具

这个工具用于可视化AI模型检测出的UI元素边界框。

## 功能特点

- 在原图上绘制红色边界框
- 显示中心点（绿色十字）
- 显示元素描述和文本内容
- 显示坐标和尺寸信息
- 支持批量处理
- 生成HTML预览页面

## 使用方法

### 1. 单张图片测试

```python
python visualize_bbox.py
```

或者指定图片和JSON数据：

```python
python visualize_bbox.py "图片路径" '{"element": {...}}'
```

### 2. 批量测试

编辑 `batch_visualize.py` 中的 `test_cases` 列表，添加你的测试案例：

```python
test_cases = [
    {
        "name": "测试名称",
        "image": r"图片路径",
        "result": {
            "element": {
                "bounding_box": {
                    "x": 330,
                    "y": 388,
                    "width": 108,
                    "height": 82
                },
                "description": "元素描述",
                "text_content": "文本内容"
            }
        }
    }
]
```

然后运行：

```bash
python batch_visualize.py
```

## 输出说明

- **output_时间戳/** - 输出文件夹
  - **xxx_bbox.png** - 带边界框的图片
  - **xxx_result.json** - JSON结果
  - **preview.html** - HTML预览页面

## 依赖

```bash
pip install pillow
```

## 边界框说明

- 红色矩形：检测到的元素边界
- 绿色十字：元素中心点
- 红色文字：元素描述（显示在边界框上方）
- 蓝色文字：元素文本内容（显示在边界框内）
- 黑色文字：坐标和尺寸信息（显示在左上角）