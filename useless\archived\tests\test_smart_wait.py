"""
Test suite for Smart Wait Mechanism
Tests auxiliary mode, active mode, mixed mode, and timeout handling
"""

import unittest
import time
import asyncio
from unittest.mock import Mock, patch, MagicMock, AsyncMock
import sys
from pathlib import Path
import numpy as np
from PIL import Image

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.smart_wait import SmartWaitManager, WaitStrategy, WaitResult


class TestWaitStrategy(unittest.TestCase):
    """Test Wait Strategy configurations"""
    
    def test_auxiliary_strategy(self):
        """Test auxiliary wait strategy"""
        strategy = WaitStrategy.AUXILIARY
        
        self.assertEqual(strategy.value, 'auxiliary')
        self.assertEqual(strategy.name, 'AUXILIARY')
        
    def test_active_strategy(self):
        """Test active wait strategy"""
        strategy = WaitStrategy.ACTIVE
        
        self.assertEqual(strategy.value, 'active')
        self.assertEqual(strategy.name, 'ACTIVE')
        
    def test_mixed_strategy(self):
        """Test mixed wait strategy"""
        strategy = WaitStrategy.MIXED
        
        self.assertEqual(strategy.value, 'mixed')
        self.assertEqual(strategy.name, 'MIXED')
        
    def test_strategy_comparison(self):
        """Test strategy comparison and equality"""
        strategy1 = WaitStrategy.AUXILIARY
        strategy2 = WaitStrategy.AUXILIARY
        strategy3 = WaitStrategy.ACTIVE
        
        self.assertEqual(strategy1, strategy2)
        self.assertNotEqual(strategy1, strategy3)


class TestSmartWaitManager(unittest.TestCase):
    """Test Smart Wait Manager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.wait_manager = SmartWaitManager(
            strategy=WaitStrategy.MIXED,
            max_wait_time=10.0
        )
        
        # Create mock components
        self.mock_detector = Mock()
        self.mock_monitor = Mock()
        
    def test_initialization(self):
        """Test wait manager initialization"""
        self.assertEqual(self.wait_manager.strategy, WaitStrategy.MIXED)
        self.assertEqual(self.wait_manager.max_wait_time, 10.0)
        self.assertFalse(self.wait_manager.is_waiting)
        
    def test_auxiliary_wait(self):
        """Test auxiliary mode waiting"""
        manager = SmartWaitManager(strategy=WaitStrategy.AUXILIARY)
        
        # Mock auxiliary detector
        mock_detector = Mock()
        mock_detector.detect_completion.side_effect = [False, False, True]
        
        start_time = time.time()
        result = manager.wait_for_completion(
            target_element=None,
            auxiliary_detector=mock_detector
        )
        elapsed = time.time() - start_time
        
        self.assertTrue(result.success)
        self.assertEqual(result.strategy_used, WaitStrategy.AUXILIARY)
        self.assertLess(elapsed, 5.0)
        
    def test_active_wait(self):
        """Test active mode waiting"""
        manager = SmartWaitManager(strategy=WaitStrategy.ACTIVE)
        
        # Mock active monitoring
        mock_monitor = Mock()
        mock_monitor.check_element.side_effect = [False, False, True]
        
        target_element = {'type': 'button', 'id': 'submit'}
        
        result = manager.wait_for_element(
            target_element=target_element,
            active_monitor=mock_monitor
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.strategy_used, WaitStrategy.ACTIVE)
        
    def test_mixed_mode_wait(self):
        """Test mixed mode waiting (auxiliary + active)"""
        manager = SmartWaitManager(strategy=WaitStrategy.MIXED)
        
        # Mock both detectors
        mock_auxiliary = Mock()
        mock_active = Mock()
        
        # Auxiliary succeeds first
        mock_auxiliary.detect_completion.side_effect = [False, True]
        mock_active.check_element.return_value = False
        
        result = manager.wait_mixed_mode(
            target_element={'id': 'test'},
            auxiliary_detector=mock_auxiliary,
            active_monitor=mock_active
        )
        
        self.assertTrue(result.success)
        self.assertIn('auxiliary', result.method_succeeded)
        
    def test_timeout_handling(self):
        """Test timeout handling in wait operations"""
        manager = SmartWaitManager(
            strategy=WaitStrategy.ACTIVE,
            max_wait_time=1.0  # 1 second timeout
        )
        
        # Mock monitor that never succeeds
        mock_monitor = Mock()
        mock_monitor.check_element.return_value = False
        
        start_time = time.time()
        result = manager.wait_for_element(
            target_element={'id': 'test'},
            active_monitor=mock_monitor,
            check_interval=0.1
        )
        elapsed = time.time() - start_time
        
        self.assertFalse(result.success)
        self.assertEqual(result.reason, 'timeout')
        self.assertAlmostEqual(elapsed, 1.0, delta=0.2)
        
    def test_adaptive_wait_time(self):
        """Test adaptive wait time adjustment"""
        manager = SmartWaitManager(adaptive=True)
        
        # Simulate successful waits with different durations
        manager.record_wait_time(0.5)
        manager.record_wait_time(0.7)
        manager.record_wait_time(0.6)
        
        # Adaptive timeout should adjust
        adaptive_timeout = manager.calculate_adaptive_timeout()
        
        # Should be based on historical data
        self.assertGreater(adaptive_timeout, 0.6)
        self.assertLess(adaptive_timeout, 2.0)
        
    def test_wait_cancellation(self):
        """Test cancelling wait operations"""
        manager = SmartWaitManager()
        
        # Start wait in thread
        import threading
        
        result_container = []
        
        def wait_task():
            mock_monitor = Mock()
            mock_monitor.check_element.return_value = False
            
            result = manager.wait_for_element(
                target_element={'id': 'test'},
                active_monitor=mock_monitor,
                check_interval=0.1
            )
            result_container.append(result)
            
        thread = threading.Thread(target=wait_task)
        thread.start()
        
        # Cancel after short delay
        time.sleep(0.2)
        manager.cancel_wait()
        
        thread.join(timeout=1.0)
        
        # Should have been cancelled
        if result_container:
            self.assertFalse(result_container[0].success)
            self.assertEqual(result_container[0].reason, 'cancelled')
            
    def test_parallel_waits(self):
        """Test parallel wait operations"""
        manager = SmartWaitManager()
        
        # Create multiple wait tasks
        def create_wait_task(element_id, delay):
            def task():
                mock_monitor = Mock()
                mock_monitor.check_element.side_effect = [False] * delay + [True]
                
                return manager.wait_for_element(
                    target_element={'id': element_id},
                    active_monitor=mock_monitor,
                    check_interval=0.1
                )
            return task
            
        # Run parallel waits
        import concurrent.futures
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [
                executor.submit(create_wait_task('element1', 2)),
                executor.submit(create_wait_task('element2', 3)),
                executor.submit(create_wait_task('element3', 1))
            ]
            
            results = [f.result()() for f in futures]
            
        # All should succeed
        for result in results:
            self.assertTrue(result.success)
            
    def test_wait_statistics(self):
        """Test wait operation statistics"""
        manager = SmartWaitManager()
        
        # Record various wait operations
        manager.record_wait_time(1.0, success=True)
        manager.record_wait_time(2.0, success=True)
        manager.record_wait_time(10.0, success=False)  # Timeout
        manager.record_wait_time(0.5, success=True)
        
        stats = manager.get_statistics()
        
        # Check statistics
        self.assertEqual(stats['total_waits'], 4)
        self.assertEqual(stats['successful_waits'], 3)
        self.assertEqual(stats['failed_waits'], 1)
        self.assertEqual(stats['success_rate'], 0.75)
        self.assertAlmostEqual(stats['average_wait_time'], 1.17, delta=0.01)
        
    def test_confidence_based_wait(self):
        """Test confidence-based dynamic waiting"""
        manager = SmartWaitManager()
        
        # Mock detector with increasing confidence
        mock_detector = Mock()
        confidence_values = [0.3, 0.5, 0.7, 0.9]
        mock_detector.detect_with_confidence.side_effect = confidence_values
        
        result = manager.wait_with_confidence(
            target_element={'id': 'test'},
            detector=mock_detector,
            confidence_threshold=0.85
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.final_confidence, 0.9)
        self.assertEqual(result.attempts, 4)


class TestWaitStrategies(unittest.TestCase):
    """Test different wait strategies in detail"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_image = Image.new('RGB', (100, 100))
        
    def test_pixel_change_detection(self):
        """Test waiting based on pixel changes"""
        manager = SmartWaitManager()
        
        # Create images with progressive changes
        images = []
        for i in range(5):
            img = Image.new('RGB', (100, 100), color=(i*50, i*50, i*50))
            images.append(img)
            
        # Mock screen capture
        capture_index = [0]
        
        def mock_capture():
            idx = capture_index[0]
            capture_index[0] = min(idx + 1, len(images) - 1)
            return images[idx]
            
        result = manager.wait_for_pixel_change(
            capture_function=mock_capture,
            change_threshold=0.1
        )
        
        self.assertTrue(result.success)
        self.assertGreater(result.change_detected, 0.1)
        
    def test_element_appearance_wait(self):
        """Test waiting for element appearance"""
        manager = SmartWaitManager()
        
        # Mock element detection
        detection_count = [0]
        
        def mock_detect(image, element):
            detection_count[0] += 1
            return detection_count[0] >= 3  # Appear after 3 checks
            
        result = manager.wait_for_appearance(
            element={'type': 'button'},
            detect_function=mock_detect,
            max_attempts=5
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.attempts, 3)
        
    def test_element_disappearance_wait(self):
        """Test waiting for element disappearance"""
        manager = SmartWaitManager()
        
        # Mock element detection (starts present, then disappears)
        detection_count = [0]
        
        def mock_detect(image, element):
            detection_count[0] += 1
            return detection_count[0] <= 2  # Disappear after 2 checks
            
        result = manager.wait_for_disappearance(
            element={'type': 'loading'},
            detect_function=mock_detect,
            max_attempts=5
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.attempts, 3)
        
    def test_stability_wait(self):
        """Test waiting for screen stability"""
        manager = SmartWaitManager()
        
        # Create sequence of images that stabilize
        stable_image = Image.new('RGB', (100, 100), color='blue')
        images = [
            Image.new('RGB', (100, 100), color='red'),
            Image.new('RGB', (100, 100), color='green'),
            stable_image,
            stable_image,
            stable_image
        ]
        
        capture_index = [0]
        
        def mock_capture():
            idx = capture_index[0]
            capture_index[0] = min(idx + 1, len(images) - 1)
            return images[idx]
            
        result = manager.wait_for_stability(
            capture_function=mock_capture,
            stable_frames=2,
            check_interval=0.1
        )
        
        self.assertTrue(result.success)
        self.assertGreater(result.stable_duration, 0.1)


class TestAsyncWait(unittest.TestCase):
    """Test asynchronous wait operations"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
    def tearDown(self):
        """Clean up event loop"""
        self.loop.close()
        
    async def test_async_wait_for_element(self):
        """Test async wait for element"""
        manager = SmartWaitManager()
        
        # Mock async detector
        async_detector = AsyncMock()
        async_detector.detect_async.side_effect = [False, False, True]
        
        result = await manager.async_wait_for_element(
            element={'id': 'test'},
            async_detector=async_detector
        )
        
        self.assertTrue(result.success)
        
    async def test_async_parallel_waits(self):
        """Test async parallel wait operations"""
        manager = SmartWaitManager()
        
        # Create async wait tasks
        async def wait_task(element_id, delay):
            async_detector = AsyncMock()
            async_detector.detect_async.side_effect = [False] * delay + [True]
            
            return await manager.async_wait_for_element(
                element={'id': element_id},
                async_detector=async_detector
            )
            
        # Run parallel async waits
        results = await asyncio.gather(
            wait_task('element1', 2),
            wait_task('element2', 1),
            wait_task('element3', 3)
        )
        
        # All should succeed
        for result in results:
            self.assertTrue(result.success)
            
    def test_async_wait_integration(self):
        """Test async wait integration"""
        self.loop.run_until_complete(self.test_async_wait_for_element())
        
    def test_async_parallel_integration(self):
        """Test async parallel wait integration"""
        self.loop.run_until_complete(self.test_async_parallel_waits())


class TestWaitOptimization(unittest.TestCase):
    """Test wait optimization strategies"""
    
    def test_predictive_wait(self):
        """Test predictive wait timing based on patterns"""
        manager = SmartWaitManager()
        
        # Train with pattern (element appears after ~0.5s)
        for _ in range(5):
            manager.record_wait_time(0.5, element_type='button')
            
        # Prediction should be close to pattern
        predicted = manager.predict_wait_time('button')
        self.assertAlmostEqual(predicted, 0.5, delta=0.1)
        
    def test_dynamic_interval_adjustment(self):
        """Test dynamic check interval adjustment"""
        manager = SmartWaitManager()
        
        # Start with default interval
        interval = manager.get_check_interval()
        self.assertEqual(interval, 0.1)
        
        # After fast detections, interval should decrease
        for _ in range(3):
            manager.record_detection_speed(0.05)
            
        new_interval = manager.get_check_interval()
        self.assertLess(new_interval, interval)
        
    def test_resource_aware_waiting(self):
        """Test resource-aware wait strategies"""
        manager = SmartWaitManager(resource_aware=True)
        
        # Mock resource monitor
        mock_resource = Mock()
        mock_resource.get_cpu_usage.return_value = 0.9  # High CPU
        
        # Should use longer intervals when resources are constrained
        interval = manager.get_adaptive_interval(mock_resource)
        self.assertGreater(interval, 0.1)
        
    def test_wait_queue_management(self):
        """Test wait queue for multiple pending operations"""
        manager = SmartWaitManager()
        
        # Queue multiple wait operations
        queue = manager.create_wait_queue()
        
        queue.add_wait('element1', priority=1)
        queue.add_wait('element2', priority=3)
        queue.add_wait('element3', priority=2)
        
        # Should process by priority
        order = queue.get_processing_order()
        self.assertEqual(order[0], 'element2')  # Highest priority
        self.assertEqual(order[1], 'element3')
        self.assertEqual(order[2], 'element1')  # Lowest priority


if __name__ == '__main__':
    # Run tests with coverage
    unittest.main(verbosity=2)