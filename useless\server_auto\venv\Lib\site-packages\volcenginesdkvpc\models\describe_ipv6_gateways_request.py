# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeIpv6GatewaysRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ipv6_gateway_ids': 'list[str]',
        'max_results': 'int',
        'name': 'str',
        'next_token': 'str',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeIpv6GatewaysInput]',
        'vpc_ids': 'list[str]'
    }

    attribute_map = {
        'ipv6_gateway_ids': 'Ipv6GatewayIds',
        'max_results': 'MaxResults',
        'name': 'Name',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters',
        'vpc_ids': 'VpcIds'
    }

    def __init__(self, ipv6_gateway_ids=None, max_results=None, name=None, next_token=None, project_name=None, tag_filters=None, vpc_ids=None, _configuration=None):  # noqa: E501
        """DescribeIpv6GatewaysRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ipv6_gateway_ids = None
        self._max_results = None
        self._name = None
        self._next_token = None
        self._project_name = None
        self._tag_filters = None
        self._vpc_ids = None
        self.discriminator = None

        if ipv6_gateway_ids is not None:
            self.ipv6_gateway_ids = ipv6_gateway_ids
        if max_results is not None:
            self.max_results = max_results
        if name is not None:
            self.name = name
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_ids is not None:
            self.vpc_ids = vpc_ids

    @property
    def ipv6_gateway_ids(self):
        """Gets the ipv6_gateway_ids of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The ipv6_gateway_ids of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ipv6_gateway_ids

    @ipv6_gateway_ids.setter
    def ipv6_gateway_ids(self, ipv6_gateway_ids):
        """Sets the ipv6_gateway_ids of this DescribeIpv6GatewaysRequest.


        :param ipv6_gateway_ids: The ipv6_gateway_ids of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: list[str]
        """

        self._ipv6_gateway_ids = ipv6_gateway_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The max_results of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeIpv6GatewaysRequest.


        :param max_results: The max_results of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def name(self):
        """Gets the name of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The name of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeIpv6GatewaysRequest.


        :param name: The name of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def next_token(self):
        """Gets the next_token of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The next_token of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeIpv6GatewaysRequest.


        :param next_token: The next_token of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The project_name of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeIpv6GatewaysRequest.


        :param project_name: The project_name of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The tag_filters of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeIpv6GatewaysInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeIpv6GatewaysRequest.


        :param tag_filters: The tag_filters of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: list[TagFilterForDescribeIpv6GatewaysInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_ids(self):
        """Gets the vpc_ids of this DescribeIpv6GatewaysRequest.  # noqa: E501


        :return: The vpc_ids of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_ids

    @vpc_ids.setter
    def vpc_ids(self, vpc_ids):
        """Sets the vpc_ids of this DescribeIpv6GatewaysRequest.


        :param vpc_ids: The vpc_ids of this DescribeIpv6GatewaysRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_ids = vpc_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeIpv6GatewaysRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeIpv6GatewaysRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeIpv6GatewaysRequest):
            return True

        return self.to_dict() != other.to_dict()
