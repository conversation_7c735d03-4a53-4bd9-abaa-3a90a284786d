package com.showforai.executor.di

import android.content.Context
import com.showforai.executor.core.engine.DSLExecutionEngine
import com.showforai.executor.core.location.LocationStrategyManager
import com.showforai.executor.core.location.VisualMatchingStrategy
import com.showforai.executor.core.location.OCRStrategy
import com.showforai.executor.core.location.CoordinateFallbackStrategy
import com.showforai.executor.core.actions.ActionExecutorManager
import com.showforai.executor.core.vision.OpenCVManager
import com.showforai.executor.core.vision.OCRProcessor
import com.showforai.executor.data.repositories.ScriptRepository
import com.showforai.executor.data.repositories.ScriptRepositoryImpl
import com.showforai.executor.data.repositories.PermissionRepository
import com.showforai.executor.data.repositories.PermissionRepositoryImpl
import com.showforai.executor.utils.FileManager
import com.showforai.executor.utils.PermissionManager
import com.showforai.executor.utils.ScreenUtils
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 应用程序级别的依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    /**
     * 提供Moshi JSON解析器
     */
    @Provides
    @Singleton
    fun provideMoshi(): Moshi {
        return Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
    }

    /**
     * 提供文件管理器
     */
    @Provides
    @Singleton
    fun provideFileManager(
        @ApplicationContext context: Context,
        moshi: Moshi
    ): FileManager {
        return FileManager(context, moshi)
    }

    /**
     * 提供权限管理器
     */
    @Provides
    @Singleton
    fun providePermissionManager(
        @ApplicationContext context: Context
    ): PermissionManager {
        return PermissionManager(context)
    }

    /**
     * 提供屏幕工具类
     */
    @Provides
    @Singleton
    fun provideScreenUtils(
        @ApplicationContext context: Context
    ): ScreenUtils {
        return ScreenUtils(context)
    }

    /**
     * 提供OpenCV管理器
     */
    @Provides
    @Singleton
    fun provideOpenCVManager(
        @ApplicationContext context: Context
    ): OpenCVManager {
        return OpenCVManager(context)
    }

    /**
     * 提供OCR处理器
     */
    @Provides
    @Singleton
    fun provideOCRProcessor(
        @ApplicationContext context: Context
    ): OCRProcessor {
        return OCRProcessor(context)
    }

    /**
     * 提供视觉匹配策略
     */
    @Provides
    @Singleton
    fun provideVisualMatchingStrategy(
        openCVManager: OpenCVManager,
        screenUtils: ScreenUtils
    ): VisualMatchingStrategy {
        return VisualMatchingStrategy(openCVManager, screenUtils)
    }

    /**
     * 提供OCR策略
     */
    @Provides
    @Singleton
    fun provideOCRStrategy(
        ocrProcessor: OCRProcessor,
        screenUtils: ScreenUtils
    ): OCRStrategy {
        return OCRStrategy(ocrProcessor, screenUtils)
    }

    /**
     * 提供坐标降级策略
     */
    @Provides
    @Singleton
    fun provideCoordinateFallbackStrategy(
        screenUtils: ScreenUtils
    ): CoordinateFallbackStrategy {
        return CoordinateFallbackStrategy(screenUtils)
    }

    /**
     * 提供定位策略管理器
     */
    @Provides
    @Singleton
    fun provideLocationStrategyManager(
        visualStrategy: VisualMatchingStrategy,
        ocrStrategy: OCRStrategy,
        coordinateStrategy: CoordinateFallbackStrategy
    ): LocationStrategyManager {
        return LocationStrategyManager(
            listOf(visualStrategy, ocrStrategy, coordinateStrategy)
        )
    }

    /**
     * 提供动作执行器管理器
     */
    @Provides
    @Singleton
    fun provideActionExecutorManager(
        @ApplicationContext context: Context,
        screenUtils: ScreenUtils
    ): ActionExecutorManager {
        return ActionExecutorManager(context, screenUtils)
    }

    /**
     * 提供DSL执行引擎
     */
    @Provides
    @Singleton
    fun provideDSLExecutionEngine(
        @ApplicationContext context: Context,
        locationManager: LocationStrategyManager,
        actionManager: ActionExecutorManager,
        fileManager: FileManager
    ): DSLExecutionEngine {
        return DSLExecutionEngine(context, locationManager, actionManager, fileManager)
    }

    /**
     * 提供脚本仓库
     */
    @Provides
    @Singleton
    fun provideScriptRepository(
        fileManager: FileManager
    ): ScriptRepository {
        return ScriptRepositoryImpl(fileManager)
    }

    /**
     * 提供权限仓库
     */
    @Provides
    @Singleton
    fun providePermissionRepository(
        permissionManager: PermissionManager
    ): PermissionRepository {
        return PermissionRepositoryImpl(permissionManager)
    }
}
