# ShowForAI Client SDK

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
编辑 `config.json`，填入你的API密钥：
```json
{
  "server": {
    "production": {
      "url": "http://*************:8080",
      "api_key": "YOUR_API_KEY_HERE"
    }
  }
}
```

### 3. 使用示例

#### 基础用法
```python
from showforai_client import ShowForAIClient

# 创建客户端
client = ShowForAIClient(
    api_key="your_api_key",
    server_url="http://*************:8080"
)

# 检测元素
result = client.detect_element(
    image_path="screenshot.png",
    click_x=400,
    click_y=300,
    mode="basic"
)

if result['success']:
    print(f"边界框: {result['data']['bounding_box']}")
    print(f"置信度: {result['data']['confidence']}")
```

#### 批量检测
```python
# 批量检测多张图片
tasks = [
    ("image1.png", 400, 300),
    ("image2.png", 500, 400),
    ("image3.png", 300, 200)
]

results = client.detect_batch(tasks, mode="enhanced")
```

### 4. 运行示例应用
```bash
python example_app.py
```

## API说明

### 检测模式
- **basic**: 快速模式，68.4%成功率，$0.001/图
- **enhanced**: 增强模式，78.9%成功率，$0.002/图

### 方法说明

#### `detect_element(image_path, click_x, click_y, mode="basic")`
检测指定坐标的UI元素

**参数:**
- `image_path`: 图片文件路径
- `click_x`: 点击X坐标（0-768）
- `click_y`: 点击Y坐标（0-768）
- `mode`: 检测模式（"basic"或"enhanced"）

**返回:**
```json
{
  "success": true,
  "data": {
    "bounding_box": [x1, y1, x2, y2],
    "confidence": 0.85,
    "detection_round": 1,
    "processing_time": 1234
  }
}
```

#### `detect_batch(tasks, mode="basic", max_workers=3)`
批量检测多个图片

**参数:**
- `tasks`: 任务列表，每个任务为`(image_path, click_x, click_y)`
- `mode`: 检测模式
- `max_workers`: 最大并发数

#### `health_check()`
检查服务器健康状态

#### `get_stats()`
获取服务器统计信息

## 错误处理

SDK会自动处理以下错误：
- 网络超时（自动重试）
- 服务器错误（自动重试）
- 认证失败（抛出异常）
- 速率限制（抛出异常）

## 安全性

- API密钥通过Bearer Token认证
- 支持HTTPS（需要服务器配置）
- 可选的请求签名验证
- 支持数据加密传输（SecureShowForAIClient）

## 性能优化

- 连接池复用
- 自动重试机制
- 批量处理支持
- 并发请求限制

## 故障排查

### 连接失败
```python
# 检查服务器状态
try:
    health = client.health_check()
    print("服务器正常")
except Exception as e:
    print(f"连接失败: {e}")
```

### 认证失败
确保使用正确的API密钥，密钥在服务器部署时生成。

### 检测失败
- 检查图片格式（支持png, jpg, jpeg, webp）
- 检查图片大小（最大10MB）
- 确保坐标在有效范围内（0-768）

## 技术支持

- 服务器状态: http://*************:8080/health
- 部署文档: ../UBUNTU_DEPLOYMENT.md
- API文档: ../server/API_DOCUMENTATION_V2.md