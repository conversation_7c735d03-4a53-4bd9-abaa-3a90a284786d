"""
Main recorder module for ShowForAI V3.

This module coordinates all recording components and manages the recording session.
"""

import json
import time
import uuid
import platform
import sys
import hashlib
from pathlib import Path
from enum import Enum
from typing import Optional, Dict, Any, Callable
from datetime import datetime
import threading
import getmac

from loguru import logger

from .action_buffer import ActionBuffer, ActionType
from .screen_capture import ScreenCapture
from .mouse_listener import MouseListener
from .keyboard_listener import KeyboardListener
from showforai.config import Config
from showforai.auth import AuthManager
from showforai.ai.image_processor import ImageProcessor
from showforai.utils.network_checker import is_online, check_ai_service
from showforai.buffer_manager import CircularFrameBuffer
from showforai.utils.time_utils import get_timestamp, get_unix_timestamp, calculate_interval


class RecorderState(Enum):
    """Recording state enumeration."""
    IDLE = "idle"
    RECORDING = "recording"
    PAUSED = "paused"
    PROCESSING = "processing"


class Recorder:
    """Main recorder class that coordinates all recording components."""
    
    def __init__(self, output_dir: str = "recordings", config: Optional[Config] = None):
        """
        Initialize the recorder.
        
        Args:
            output_dir: Base directory for recordings
            config: Optional configuration object
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.config = config or Config()
        
        # Components
        self.screen_capture = ScreenCapture(
            output_dir=str(self.output_dir / "screenshots")
        )
        # Initialize CircularFrameBuffer for 15-frame buffer at 10 FPS
        self.frame_buffer = CircularFrameBuffer(max_frames=15, fps=10)
        # Pass continuous capture reference to action buffer for timestamp management
        self.action_buffer = ActionBuffer(continuous_capture=self.screen_capture.continuous_capture)
        self.mouse_listener = MouseListener()
        self.keyboard_listener = KeyboardListener()
        self.image_processor = ImageProcessor()  # For 768x768 standardization
        
        # State
        self.state = RecorderState.IDLE
        self.session_id: Optional[str] = None
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        
        # Callbacks
        self.on_recording_started: Optional[Callable] = None
        self.on_recording_stopped: Optional[Callable] = None
        self.on_action_recorded: Optional[Callable] = None
        
        # Setup event handlers
        self._setup_event_handlers()
        
        # Statistics
        self.stats = {
            'total_actions': 0,
            'clicks': 0,
            'key_presses': 0,
            'screenshots': 0,
            'duration': 0
        }
        
        logger.info(f"Recorder initialized: output_dir={output_dir}")
    
    def _setup_event_handlers(self) -> None:
        """Setup event handlers for input listeners."""
        # Mouse handlers
        self.mouse_listener.set_on_click_callback(self._on_click)
        self.mouse_listener.set_on_scroll_callback(self._on_scroll)
        
        # Keyboard handlers
        self.keyboard_listener.set_on_press_callback(self._on_key_press)
        self.keyboard_listener.set_on_f9_callback(self._on_f9_pressed)
    
    def check_network_status(self) -> bool:
        """
        Check if network is available for recording.
        Recording requires network for AI recognition services.
        
        Returns:
            True if network is available, False otherwise
        """
        if not is_online():
            logger.warning("Network is offline - recording requires network connection")
            return False
        
        if not check_ai_service():
            logger.warning("AI service is not accessible - recording requires AI service")
            return False
        
        return True
    
    def start_recording(self, session_name: Optional[str] = None) -> bool:
        """
        Start a new recording session.
        
        Args:
            session_name: Optional name for the session
            
        Returns:
            True if recording started successfully
        
        Raises:
            RuntimeError: If network is not available
        """
        if self.state != RecorderState.IDLE:
            logger.warning(f"Cannot start recording in state: {self.state}")
            return False
        
        # Check network status - recording requires network for AI recognition
        if not self.check_network_status():
            error_msg = "录制需要网络连接以进行AI识别"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        try:
            # Generate session ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.session_id = session_name or f"recording_{timestamp}"
            
            # Create session directory
            session_dir = self.output_dir / self.session_id
            session_dir.mkdir(parents=True, exist_ok=True)
            
            # Update screen capture output directory
            self.screen_capture.output_dir = session_dir / "screenshots"
            self.screen_capture.output_dir.mkdir(parents=True, exist_ok=True)
            
            # Clear action buffer
            self.action_buffer.clear()
            
            # Reset statistics
            self.stats = {
                'total_actions': 0,
                'clicks': 0,
                'key_presses': 0,
                'screenshots': 0,
                'duration': 0
            }
            
            # Start components
            self.screen_capture.start()
            self.frame_buffer.start_capture()  # Start the CircularFrameBuffer
            self.mouse_listener.start()
            self.keyboard_listener.start()
            
            # Update state
            self.state = RecorderState.RECORDING
            # Use high-precision timer for recording start
            self.start_time = time.perf_counter() if hasattr(time, 'perf_counter') else time.time()
            
            # Trigger callback
            if self.on_recording_started:
                self.on_recording_started()
            
            logger.info(f"Recording started: {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            self.state = RecorderState.IDLE
            return False
    
    def stop_recording(self) -> bool:
        """
        Stop the current recording session.
        
        Returns:
            True if recording stopped successfully
        """
        if self.state != RecorderState.RECORDING:
            logger.warning(f"Cannot stop recording in state: {self.state}")
            return False
        
        try:
            # Update state
            self.state = RecorderState.PROCESSING
            # Use high-precision timer for recording end
            self.end_time = time.perf_counter() if hasattr(time, 'perf_counter') else time.time()
            
            # Stop components
            self.mouse_listener.stop()
            self.keyboard_listener.stop()
            self.frame_buffer.stop_capture()  # Stop the CircularFrameBuffer
            self.screen_capture.stop()
            
            # Calculate statistics
            if self.start_time and self.end_time:
                self.stats['duration'] = self.end_time - self.start_time
            
            # Save recording
            output_file = self._save_recording()
            
            # Update state
            self.state = RecorderState.IDLE
            
            # Trigger callback
            if self.on_recording_stopped:
                self.on_recording_stopped(str(output_file))
            
            logger.info(f"Recording stopped: {self.session_id}")
            logger.info(f"Recording saved to: {output_file}")
            logger.info(f"Statistics: {self.stats}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            self.state = RecorderState.IDLE
            return False
    
    def pause_recording(self) -> bool:
        """
        Pause the current recording.
        
        Returns:
            True if paused successfully
        """
        if self.state != RecorderState.RECORDING:
            logger.warning(f"Cannot pause recording in state: {self.state}")
            return False
        
        self.state = RecorderState.PAUSED
        logger.info("Recording paused")
        return True
    
    def resume_recording(self) -> bool:
        """
        Resume a paused recording.
        
        Returns:
            True if resumed successfully
        """
        if self.state != RecorderState.PAUSED:
            logger.warning(f"Cannot resume recording in state: {self.state}")
            return False
        
        self.state = RecorderState.RECORDING
        logger.info("Recording resumed")
        return True
    
    def _on_click(self, x: int, y: int, button: str, pressed: bool) -> None:
        """
        Handle mouse click events.
        
        Args:
            x: X coordinate
            y: Y coordinate
            button: Button identifier
            pressed: True if pressed
        """
        if self.state != RecorderState.RECORDING:
            return
        
        # Use high-precision timestamp (perf_counter for relative timing)
        timestamp = time.perf_counter() if hasattr(time, 'perf_counter') else time.time()
        
        # Get clean frame from CircularFrameBuffer (3 frames before click)
        screenshot = None
        frame_data = self.frame_buffer.get_frame_before_click(timestamp, seconds_before=0.3)
        
        if frame_data:
            # Save the frame as a screenshot file
            screenshot = self._save_frame_as_screenshot(frame_data)
            # Standardize screenshot to 768x768 for storage
            if screenshot:
                screenshot = self._standardize_screenshot(screenshot)
        else:
            # Fallback to old method if buffer doesn't have frames yet
            logger.warning("No frame available from CircularFrameBuffer, using fallback")
            if button == 'drag':
                end_x, end_y = pressed
                screenshot = self.screen_capture.capture_clean_click_screenshot(end_x, end_y)
            else:
                screenshot = self.screen_capture.capture_clean_click_screenshot(x, y)
            
            if screenshot:
                screenshot = self._standardize_screenshot(screenshot)
        
        # Handle different click types
        if button == 'drag':
            # Drag action - pressed contains end position
            end_x, end_y = pressed
            self.action_buffer.add_drag(x, y, end_x, end_y, timestamp, screenshot)
            self.stats['clicks'] += 1
            if screenshot:
                self.stats['screenshots'] += 1
        elif button == 'double_click':
            self.action_buffer.add_double_click(x, y, timestamp, screenshot)
            self.stats['clicks'] += 1
            if screenshot:
                self.stats['screenshots'] += 1
        elif button == 'right':
            self.action_buffer.add_right_click(x, y, timestamp, screenshot)
            self.stats['clicks'] += 1
            if screenshot:
                self.stats['screenshots'] += 1
        else:
            # Regular left click
            self.action_buffer.add_click(x, y, timestamp, screenshot)
            self.stats['clicks'] += 1
            if screenshot:
                self.stats['screenshots'] += 1
        
        self.stats['total_actions'] += 1
        
        # Trigger callback
        if self.on_action_recorded:
            self.on_action_recorded(self.action_buffer.actions[-1])
    
    def _on_scroll(self, x: int, y: int, dx: int, dy: int) -> None:
        """
        Handle mouse scroll events.
        
        Args:
            x: X coordinate
            y: Y coordinate
            dx: Horizontal scroll delta
            dy: Vertical scroll delta
        """
        if self.state != RecorderState.RECORDING:
            return
        
        # Use high-precision timestamp
        timestamp = time.perf_counter() if hasattr(time, 'perf_counter') else time.time()
        self.action_buffer.add_scroll(x, y, dx, dy, timestamp)
        self.stats['total_actions'] += 1
        
        # Trigger callback
        if self.on_action_recorded:
            self.on_action_recorded(self.action_buffer.actions[-1])
    
    def _on_key_press(self, key: str) -> None:
        """
        Handle key press events.
        
        Args:
            key: The key that was pressed
        """
        if self.state != RecorderState.RECORDING:
            return
        
        # Check for F9 (handled separately)
        if key.lower() == 'f9':
            return
        
        # Use high-precision timestamp
        timestamp = time.perf_counter() if hasattr(time, 'perf_counter') else time.time()
        action = self.action_buffer.add_key_press(key, timestamp)
        
        if action:  # None if buffered for TYPE action
            self.stats['key_presses'] += 1
            self.stats['total_actions'] += 1
            
            # Trigger callback
            if self.on_action_recorded:
                self.on_action_recorded(action)
    
    def _on_f9_pressed(self) -> None:
        """Handle F9 key press (stop recording hotkey)."""
        if self.state == RecorderState.RECORDING:
            logger.info("F9 pressed - stopping recording")
            self.stop_recording()
    
    def _save_frame_as_screenshot(self, frame_data: dict) -> Optional[str]:
        """
        Save a frame from the CircularFrameBuffer as a screenshot file.
        
        Args:
            frame_data: Frame data from CircularFrameBuffer
            
        Returns:
            Path to the saved screenshot or None on error
        """
        try:
            from PIL import Image
            import io
            
            # Convert frame bytes to PIL Image
            img = Image.open(io.BytesIO(frame_data['frame']))
            
            # Generate filename
            timestamp_ms = int(frame_data['timestamp'] * 1000)
            frame_num = frame_data['frame_number']
            filename = f"frame_{timestamp_ms}_{frame_num:06d}.png"
            
            # Save to screenshots directory
            session_dir = self.output_dir / self.session_id if self.session_id else self.output_dir
            screenshot_path = session_dir / "screenshots" / filename
            screenshot_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save the image
            img.save(screenshot_path, 'PNG', optimize=True)
            
            logger.debug(f"Saved frame #{frame_num} as screenshot: {filename}")
            return str(screenshot_path)
            
        except Exception as e:
            logger.error(f"Error saving frame as screenshot: {e}")
            return None
    
    def _standardize_screenshot(self, screenshot_path: str) -> str:
        """
        Standardize screenshot to 768x768 resolution.
        
        Args:
            screenshot_path: Path to the original screenshot
            
        Returns:
            Path to the standardized 768x768 screenshot
        """
        try:
            # Read the original screenshot
            from PIL import Image
            import io
            from pathlib import Path
            
            screenshot_file = Path(screenshot_path)
            if not screenshot_file.exists():
                return screenshot_path
            
            # Open and resize to 768x768
            img = Image.open(screenshot_file)
            original_size = img.size
            
            # Resize to standard 768x768
            resized = img.resize((768, 768), Image.Resampling.LANCZOS)
            
            # Save with same filename (overwrite original)
            resized.save(screenshot_file, 'PNG')
            
            logger.debug(f"Standardized screenshot from {original_size} to 768x768: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            logger.error(f"Error standardizing screenshot: {e}")
            return screenshot_path
    
    def _capture_screenshot(self) -> Optional[str]:
        """
        Capture a screenshot.
        
        Returns:
            Filename of the screenshot or None
        """
        return self.screen_capture.capture_screenshot()
    
    def _save_recording(self) -> Path:
        """
        Save the recording to a JSON file.
        
        Returns:
            Path to the saved recording file
        """
        if not self.session_id:
            raise ValueError("No session ID set")
        
        # Get screen resolution
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            root.destroy()
            resolution = f"{screen_width}x{screen_height}"
        except:
            resolution = "unknown"
        
        # Get device fingerprint hash
        try:
            mac_address = getmac.get_mac_address()
            device_str = f"{platform.node()}_{mac_address}"
            device_id = hashlib.sha256(device_str.encode()).hexdigest()[:16]
        except:
            device_id = hashlib.sha256(f"{platform.node()}_unknown".encode()).hexdigest()[:16]
        
        # Prepare recording data according to product spec
        recording_data = {
            'version': '1.0',
            'metadata': {
                'version': '1.0',  # Script format version
                'session_id': self.session_id,
                'screen_resolution': resolution,
                'device_id': device_id,
                'recorded_at': datetime.fromtimestamp(self.start_time).isoformat() + 'Z' if self.start_time else None,
                'duration_ms': round(self.stats['duration'] * 1000) if self.stats.get('duration') else 0,
                'platform': platform.system().lower(),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'standardized_resolution': '768x768'  # All screenshots are standardized to 768x768
            },
            'actions': self.action_buffer.to_dict(use_milliseconds=True)
        }
        
        # Add user info if authenticated (skip for now as it's async)
        # TODO: Implement async user info retrieval
        pass
        
        # Save to file
        session_dir = self.output_dir / self.session_id
        output_file = session_dir / 'recording.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(recording_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Recording saved: {output_file}")
        
        return output_file
    
    def load_recording(self, filepath: Path) -> Dict[str, Any]:
        """
        Load a recording from file.
        
        Args:
            filepath: Path to the recording file
            
        Returns:
            Recording data dictionary
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_state(self) -> RecorderState:
        """
        Get the current recorder state.
        
        Returns:
            Current state
        """
        return self.state
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get current recording statistics.
        
        Returns:
            Statistics dictionary
        """
        stats = self.stats.copy()
        
        # Add current duration if recording
        if self.state == RecorderState.RECORDING and self.start_time:
            stats['duration'] = get_timestamp() - self.start_time
        
        # Add action buffer statistics
        stats.update(self.action_buffer.get_statistics())
        
        # Add screen capture statistics
        stats.update(self.screen_capture.stats)
        
        # Add CircularFrameBuffer statistics
        buffer_stats = self.frame_buffer.get_buffer_stats()
        stats['frame_buffer'] = buffer_stats
        
        return stats
    
    def cleanup(self) -> None:
        """Cleanup resources."""
        if self.state == RecorderState.RECORDING:
            self.stop_recording()
        
        # Stop all components
        self.mouse_listener.stop()
        self.keyboard_listener.stop()
        self.frame_buffer.stop_capture()  # Stop the CircularFrameBuffer
        self.screen_capture.stop()
        
        logger.info("Recorder cleaned up")
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            self.cleanup()
        except:
            pass


def validate_recording_format(data: Dict[str, Any]) -> bool:
    """
    Validate recording data format.
    
    Args:
        data: Recording data to validate
        
    Returns:
        True if format is valid
    """
    # Check required top-level fields
    if 'version' not in data:
        return False
    if 'metadata' not in data:
        return False
    if 'actions' not in data:
        return False
    
    # Check metadata fields
    metadata = data['metadata']
    required_metadata = ['screen_resolution', 'device_id', 'recorded_at', 'duration_ms']
    for field in required_metadata:
        if field not in metadata:
            return False
    
    # Check actions format
    actions = data['actions']
    if not isinstance(actions, list):
        return False
    
    for action in actions:
        if 'sequence' not in action:
            return False
        if 'type' not in action:
            return False
        if 'timestamp' not in action:
            return False
        if 'delay_ms' not in action:
            return False
    
    return True


def convert_to_new_format(old_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert old format recording to new format.
    
    Args:
        old_data: Old format recording data
        
    Returns:
        New format recording data
    """
    new_data = {
        'version': '1.0',
        'metadata': {
            'screen_resolution': 'unknown',
            'device_id': old_data.get('session_id', 'unknown'),
            'recorded_at': datetime.now().isoformat() + 'Z',
            'duration_ms': 0
        },
        'actions': []
    }
    
    # Convert actions
    if 'actions' in old_data:
        for i, old_action in enumerate(old_data['actions']):
            new_action = {
                'sequence': i + 1,
                'type': old_action.get('type', 'unknown'),
                'timestamp': round(old_action.get('timestamp', 0) * 1000),  # Convert to milliseconds
                'delay_ms': 0
            }
            
            # Convert position
            if 'x' in old_action and 'y' in old_action:
                new_action['position'] = {
                    'x': old_action['x'],
                    'y': old_action['y']
                }
            
            # Copy other fields
            if 'screenshot' in old_action:
                new_action['screenshot'] = old_action['screenshot']
            if 'text' in old_action:
                new_action['text'] = old_action['text']
            if 'key' in old_action:
                new_action['key'] = old_action['key']
            
            new_data['actions'].append(new_action)
    
    # Calculate delays
    for i in range(1, len(new_data['actions'])):
        delay = new_data['actions'][i]['timestamp'] - new_data['actions'][i-1]['timestamp']
        new_data['actions'][i]['delay_ms'] = delay
    
    # Calculate duration
    if len(new_data['actions']) > 0:
        duration = new_data['actions'][-1]['timestamp'] - new_data['actions'][0]['timestamp']
        new_data['metadata']['duration_ms'] = duration
    
    return new_data