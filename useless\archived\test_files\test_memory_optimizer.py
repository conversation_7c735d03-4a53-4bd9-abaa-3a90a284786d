"""
Test Memory Optimizer functionality
"""

import gc
import time
import unittest
import numpy as np
import psutil
from unittest.mock import Mock, patch
from src.showforai.optimization.memory_optimizer import (
    MemoryOptimizer, MemoryPool, ImageBuffer, MemoryLimitController,
    SmartGarbageCollector, MemoryLeakDetector, SlottedDataClass,
    get_memory_optimizer, memory_efficient
)


class TestObject:
    """Test object for pooling"""
    def __init__(self):
        self.data = [0] * 100
        self.reset_called = False
        
    def reset(self):
        self.data = [0] * 100
        self.reset_called = True


class TestMemoryPool(unittest.TestCase):
    """Test memory pool functionality"""
    
    def setUp(self):
        self.pool = MemoryPool(TestObject, max_size=5)
        
    def test_acquire_and_release(self):
        """Test acquiring and releasing objects"""
        # Acquire object
        obj1 = self.pool.acquire()
        self.assertIsInstance(obj1, TestObject)
        self.assertEqual(self.pool.created_count, 1)
        
        # Release object
        self.pool.release(obj1)
        
        # Acquire again - should reuse
        obj2 = self.pool.acquire()
        self.assertEqual(self.pool.reused_count, 1)
        
    def test_pool_limit(self):
        """Test pool size limit"""
        objects = []
        
        # Create more than pool size
        for i in range(10):
            obj = self.pool.acquire()
            objects.append(obj)
        
        # Release all
        for obj in objects:
            self.pool.release(obj)
        
        # Pool should only keep max_size
        self.assertLessEqual(len(self.pool.pool), self.pool.max_size)
        
    def test_reset_on_release(self):
        """Test that objects are reset when released"""
        obj = self.pool.acquire()
        obj.data[0] = 999  # Modify object
        
        self.pool.release(obj)
        
        # Object should have been reset
        self.assertTrue(obj.reset_called)
        
    def test_pool_stats(self):
        """Test pool statistics"""
        # Create and reuse some objects
        obj1 = self.pool.acquire()
        self.pool.release(obj1)
        obj2 = self.pool.acquire()
        
        stats = self.pool.get_stats()
        
        self.assertEqual(stats['created'], 1)
        self.assertEqual(stats['reused'], 1)
        self.assertGreater(stats['reuse_rate'], 0)


class TestSlottedDataClass(unittest.TestCase):
    """Test slotted data class"""
    
    def test_slots_enforcement(self):
        """Test that slots are enforced"""
        class TestSlotted(SlottedDataClass):
            __slots__ = ('x', 'y')
            
            def __init__(self):
                self.x = 1
                self.y = 2
        
        obj = TestSlotted()
        
        # Should be able to access defined slots
        self.assertEqual(obj.x, 1)
        self.assertEqual(obj.y, 2)
        
        # Should not be able to add new attributes
        with self.assertRaises(AttributeError):
            obj.z = 3
    
    def test_missing_slots_raises_error(self):
        """Test that missing slots definition raises error"""
        with self.assertRaises(TypeError):
            class BadSlotted(SlottedDataClass):
                # No __slots__ defined
                pass


class TestImageBuffer(unittest.TestCase):
    """Test image buffer functionality"""
    
    def setUp(self):
        self.buffer = ImageBuffer()
        self.test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
    def test_set_and_get_image(self):
        """Test setting and getting image"""
        self.buffer.set_image(self.test_image, compress=False)
        
        retrieved = self.buffer.get_image()
        
        self.assertIsNotNone(retrieved)
        np.testing.assert_array_equal(retrieved, self.test_image)
        
    def test_compression(self):
        """Test image compression"""
        self.buffer.set_image(self.test_image, compress=True)
        
        self.assertTrue(self.buffer.compressed)
        
        # Memory should be less when compressed
        compressed_size = self.buffer.get_memory_usage()
        
        self.buffer.set_image(self.test_image, compress=False)
        uncompressed_size = self.buffer.get_memory_usage()
        
        self.assertLess(compressed_size, uncompressed_size)
        
    def test_reset(self):
        """Test buffer reset"""
        self.buffer.set_image(self.test_image)
        self.buffer.reset()
        
        self.assertIsNone(self.buffer.data)
        self.assertEqual(self.buffer.width, 0)
        self.assertEqual(self.buffer.height, 0)
        self.assertIsNone(self.buffer.get_image())


class TestMemoryLimitController(unittest.TestCase):
    """Test memory limit controller"""
    
    def setUp(self):
        self.controller = MemoryLimitController(max_memory_mb=500)
        
    def test_memory_info(self):
        """Test memory information retrieval"""
        info = self.controller.get_memory_info()
        
        self.assertIn('rss_mb', info)
        self.assertIn('vms_mb', info)
        self.assertIn('percent', info)
        self.assertIn('available_mb', info)
        
        # Values should be positive
        self.assertGreater(info['rss_mb'], 0)
        
    def test_usage_level(self):
        """Test usage level detection"""
        level = self.controller.get_usage_level()
        
        self.assertIn(level, ['normal', 'warning', 'critical'])
        
    def test_within_limit(self):
        """Test limit checking"""
        # Should be within limit for reasonable value
        self.controller.max_memory_bytes = 10 * 1024 * 1024 * 1024  # 10GB
        self.assertTrue(self.controller.is_within_limit())
        
        # Should exceed limit for tiny value
        self.controller.max_memory_bytes = 1  # 1 byte
        self.assertFalse(self.controller.is_within_limit())
        
    def test_callbacks(self):
        """Test callback system"""
        callback_called = []
        
        def test_callback(level, info):
            callback_called.append((level, info))
        
        self.controller.add_callback(test_callback)
        
        # Manually trigger callback
        self.controller._trigger_cleanup()
        
        # Callback might not be called immediately without monitoring
        # Just verify the mechanism exists
        self.assertEqual(len(self.controller.callbacks), 1)


class TestSmartGarbageCollector(unittest.TestCase):
    """Test smart garbage collector"""
    
    def setUp(self):
        self.gc_manager = SmartGarbageCollector()
        
    def test_collect(self):
        """Test garbage collection"""
        # Create some garbage
        for i in range(100):
            _ = [0] * 1000
        
        result = self.gc_manager.collect()
        
        self.assertIn('collected', result)
        self.assertIn('before', result)
        self.assertIn('after', result)
        self.assertIn('freed_objects', result)
        
        self.assertGreaterEqual(result['collected'], 0)
        
    def test_gc_phases(self):
        """Test GC optimization for different phases"""
        # Test each phase
        for phase in ['loading', 'processing', 'idle']:
            self.gc_manager.optimize_for_phase(phase)
            
            if phase == 'loading':
                self.assertFalse(gc.isenabled())
            else:
                self.assertTrue(gc.isenabled())
        
        # Restore default
        self.gc_manager.configure_gc()
        
    def test_disabled_context(self):
        """Test GC disabled context manager"""
        gc.enable()  # Ensure enabled
        
        with self.gc_manager.disabled():
            self.assertFalse(gc.isenabled())
        
        self.assertTrue(gc.isenabled())


class TestMemoryLeakDetector(unittest.TestCase):
    """Test memory leak detector"""
    
    def setUp(self):
        self.detector = MemoryLeakDetector()
        
    def test_tracking(self):
        """Test memory tracking"""
        self.detector.start_tracking()
        self.assertTrue(self.detector.tracking)
        
        self.detector.stop_tracking()
        self.assertFalse(self.detector.tracking)
        
    def test_snapshots(self):
        """Test snapshot functionality"""
        self.detector.start_tracking()
        
        # Take snapshots
        self.detector.take_snapshot("before")
        
        # Allocate some memory
        data = [0] * 10000
        
        self.detector.take_snapshot("after")
        
        # Compare snapshots
        diffs = self.detector.compare_snapshots("before", "after", top=5)
        
        # Should have some differences
        self.assertIsInstance(diffs, list)
        
        self.detector.stop_tracking()
        
    def test_object_tracking(self):
        """Test object leak tracking"""
        obj = TestObject()
        
        self.detector.track_object(obj)
        
        # Object should be tracked
        self.assertIn(obj, self.detector.tracked_objects)
        
        # Delete object
        del obj
        gc.collect()
        
        # Check for leaks (should be empty after deletion)
        leaks = self.detector.get_leaked_objects()
        # The weak reference should be gone
        self.assertEqual(len(leaks), 0)


class TestMemoryOptimizer(unittest.TestCase):
    """Test main memory optimizer"""
    
    def setUp(self):
        self.optimizer = MemoryOptimizer(target_memory_mb=500)
        
    def tearDown(self):
        if hasattr(self, 'optimizer'):
            self.optimizer.shutdown()
            
    def test_initialization(self):
        """Test optimizer initialization"""
        self.optimizer.initialize()
        
        self.assertTrue(self.optimizer.memory_controller.monitoring)
        self.assertTrue(self.optimizer.leak_detector.tracking)
        
    def test_buffer_pool(self):
        """Test buffer pool access"""
        # Get buffer from pool
        buffer = self.optimizer.get_buffer('image_buffer')
        
        self.assertIsInstance(buffer, ImageBuffer)
        
        # Release buffer
        self.optimizer.release_buffer(buffer, 'image_buffer')
        
        # Check pool stats
        stats = self.optimizer.pools['image_buffer'].get_stats()
        self.assertGreater(stats['created'], 0)
        
    def test_memory_cleanup(self):
        """Test memory cleanup"""
        initial_stats = self.optimizer.stats['cleanups']
        
        self.optimizer.cleanup_memory()
        
        self.assertEqual(self.optimizer.stats['cleanups'], initial_stats + 1)
        
    def test_emergency_cleanup(self):
        """Test emergency cleanup"""
        initial_stats = self.optimizer.stats['emergency_cleanups']
        
        self.optimizer.emergency_cleanup()
        
        self.assertEqual(self.optimizer.stats['emergency_cleanups'], initial_stats + 1)
        
    def test_memory_efficient_context(self):
        """Test memory efficient operation context"""
        self.optimizer.initialize()
        
        with self.optimizer.memory_efficient_operation("test_phase"):
            # Should be in test phase
            # Allocate some memory
            data = np.random.rand(1000, 1000)
            
        # GC should have been optimized
        self.assertTrue(gc.isenabled())
        
    def test_memory_stats(self):
        """Test memory statistics"""
        self.optimizer.initialize()
        
        stats = self.optimizer.get_memory_stats()
        
        self.assertIn('current_mb', stats)
        self.assertIn('target_mb', stats)
        self.assertIn('peak_mb', stats)
        self.assertIn('usage_level', stats)
        self.assertIn('gc_stats', stats)
        self.assertIn('pool_stats', stats)
        
        self.assertEqual(stats['target_mb'], 500)


class TestGlobalFunctions(unittest.TestCase):
    """Test global convenience functions"""
    
    def test_get_memory_optimizer(self):
        """Test global optimizer instance"""
        opt1 = get_memory_optimizer()
        opt2 = get_memory_optimizer()
        
        self.assertIs(opt1, opt2)  # Should be same instance
        
    def test_memory_efficient_context(self):
        """Test memory efficient context manager"""
        with memory_efficient("test"):
            # Should work without errors
            pass


def run_memory_stress_test():
    """Run memory stress test"""
    print("\n=== Memory Optimizer Stress Test ===")
    
    optimizer = MemoryOptimizer(target_memory_mb=500)
    optimizer.initialize()
    
    print(f"Initial memory: {optimizer.memory_controller.get_memory_info()['rss_mb']:.1f}MB")
    
    # Simulate memory-intensive operations
    print("\n1. Testing buffer pooling...")
    buffers = []
    for i in range(20):
        buffer = optimizer.get_buffer('image_buffer')
        image = np.random.randint(0, 255, (768, 768, 3), dtype=np.uint8)
        buffer.set_image(image, compress=True)
        buffers.append(buffer)
    
    mem_after_alloc = optimizer.memory_controller.get_memory_info()['rss_mb']
    print(f"   After allocation: {mem_after_alloc:.1f}MB")
    
    # Release buffers
    for buffer in buffers:
        optimizer.release_buffer(buffer, 'image_buffer')
    
    # Force cleanup
    optimizer.cleanup_memory()
    
    mem_after_release = optimizer.memory_controller.get_memory_info()['rss_mb']
    print(f"   After release: {mem_after_release:.1f}MB")
    
    # Test memory efficient operation
    print("\n2. Testing memory-efficient operation...")
    with optimizer.memory_efficient_operation("processing"):
        # Simulate processing
        large_data = np.random.rand(2000, 2000)
        result = np.fft.fft2(large_data)
        
        mem_during = optimizer.memory_controller.get_memory_info()['rss_mb']
        print(f"   During processing: {mem_during:.1f}MB")
    
    # After context, should clean up
    mem_after_context = optimizer.memory_controller.get_memory_info()['rss_mb']
    print(f"   After context: {mem_after_context:.1f}MB")
    
    # Get final statistics
    print("\n3. Final Statistics:")
    stats = optimizer.get_memory_stats()
    
    print(f"   Current memory: {stats['current_mb']:.1f}MB")
    print(f"   Peak memory: {stats['peak_mb']:.1f}MB")
    print(f"   Target memory: {stats['target_mb']}MB")
    print(f"   Usage level: {stats['usage_level']}")
    print(f"   Cleanups performed: {stats['cleanups']}")
    print(f"   Emergency cleanups: {stats['emergency_cleanups']}")
    
    # Pool statistics
    print("\n4. Pool Statistics:")
    for name, pool_stats in stats['pool_stats'].items():
        print(f"   {name}: {pool_stats['reuse_rate']:.1f}% reuse rate")
    
    # Check if within target
    if stats['current_mb'] <= stats['target_mb']:
        print(f"\n✓ SUCCESS: Memory usage {stats['current_mb']:.1f}MB within target {stats['target_mb']}MB")
    else:
        print(f"\n✗ WARNING: Memory usage {stats['current_mb']:.1f}MB exceeds target {stats['target_mb']}MB")
    
    optimizer.shutdown()


if __name__ == '__main__':
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run stress test
    run_memory_stress_test()