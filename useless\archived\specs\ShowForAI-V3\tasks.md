# ShowForAI-V3 Task List

## P3 - Minor Issue Fixes

### Task 13: Unify Screenshot File Format
[x] Standardize all screenshots to PNG format
   - Update screen_capture.py to save only as PNG
   - Update script_generator.py to use PNG format
   - Remove JPEG-related code
   - Test: Write unit test to verify only PNG files are created

### Task 14: Add Help Documentation Links
[ ] Create "Why Network is Required" documentation
   - Add help button to main GUI
   - Create help dialog with network explanation
   - Add link to online documentation
   - Test: Manually verify help dialog shows correctly

### Task 15: Enhance Logging System
[ ] Add comprehensive logging
   - Add logging for key operations (recording start/stop, script execution, AI calls)
   - Implement log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
   - Add log rotation mechanism (max 10MB, keep 5 files)
   - Test: Write unit test to verify log rotation works

## P4 - Code Cleanup

### Task 16: Remove Redundant Code
[ ] Delete unnecessary features per product principles
   - Remove non-image recognition locating methods
   - Delete device heartbeat monitoring code
   - Clean up 2FA authentication preparation code
   - Remove undo system related code
   - Delete extra keyboard shortcuts (keep only F9)
   - Clean up unused imports and functions
   - Test: Run full test suite to ensure nothing breaks

## P5 - Performance Optimization

### Task 17: Memory Optimization
[ ] Optimize memory usage
   - Optimize circular buffer memory management
   - Implement image cache cleanup mechanism
   - Add memory leak detection
   - Test: Write performance test to verify memory usage stays under 500MB

### Task 18: CPU Optimization
[ ] Optimize CPU usage
   - Optimize 10FPS screenshot CPU usage
   - Implement image processing parallelization
   - Add CPU usage monitoring
   - Test: Write performance test to verify CPU usage stays under 5%

### Task 19: Network Optimization
[ ] Optimize network operations
   - Implement batch upload mechanism
   - Add image compression optimization
   - Implement request merging
   - Test: Write unit test to verify batch upload works

## Rules & Tips
- All changes must maintain backward compatibility with existing scripts
- PNG format is chosen for consistency and lossless compression
- Logging should not impact performance (async where possible)
- Code cleanup should be done carefully to avoid breaking dependencies
- Performance optimizations should be measured before and after