{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeBuildCommand": "npm run build", "beforeDevCommand": "npm run dev", "devPath": "http://localhost:3000", "distDir": "../dist"}, "package": {"productName": "ShowForAI", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true}, "fs": {"all": true, "scope": ["$APPDATA", "$APPCONFIG", "$APPLOCAL", "$TEMP"]}, "path": {"all": true}, "dialog": {"all": true}, "globalShortcut": {"all": true}, "os": {"all": true}}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.showforai.app", "longDescription": "ShowForAI - AI-powered screen recording and automation tool", "macOS": {"entitlements": "src-tauri/entitlements.plist", "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": "Developer ID Application: ShowForAI Team", "hardenedRuntime": true, "minimumSystemVersion": "10.15"}, "resources": [], "shortDescription": "AI screen recording tool", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "updater": {"active": true, "endpoints": ["https://releases.showforai.com/{{target}}/{{arch}}/{{current_version}}"], "dialog": true, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IEUxRkNCN0M4OEU5RkE3RTEK"}, "windows": [{"fullscreen": false, "height": 800, "resizable": true, "title": "ShowForAI", "width": 1200, "minWidth": 800, "minHeight": 600}]}}