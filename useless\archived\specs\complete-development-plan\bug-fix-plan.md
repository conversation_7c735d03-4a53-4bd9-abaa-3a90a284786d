# ShowForAI V3 Bug修复专项计划

## 1. Bug分类统计

### 1.1 当前Bug概况
基于代码扫描和分析，发现以下问题：
- **TODO标记**：50+ 处未完成功能
- **崩溃类Bug**：4个高危问题
- **功能类Bug**：12个核心功能缺陷
- **性能类Bug**：8个性能瓶颈
- **界面类Bug**：15个UI问题
- **兼容性Bug**：6个跨平台问题

### 1.2 Bug严重程度分级
- **P0 - 致命**：导致程序崩溃或数据丢失
- **P1 - 严重**：核心功能无法使用
- **P2 - 一般**：功能受限但有变通方案
- **P3 - 轻微**：用户体验问题
- **P4 - 建议**：优化改进项

## 2. 崩溃类Bug（P0）

### BUG-001: 网络断开导致程序崩溃
**问题描述**：录制过程中网络断开会导致程序异常终止
**复现步骤**：
1. 开始录制
2. 断开网络连接
3. 程序崩溃

**根因分析**：
- 网络异常未被正确捕获
- 缺少重连机制
- 错误传播未被阻止

**修复方案**：
```python
# network_resilience.py
class NetworkResilientRecorder:
    def handle_network_error(self, error):
        """网络错误处理"""
        try:
            if isinstance(error, ConnectionError):
                self.pause_recording()
                self.show_reconnect_dialog()
                self.attempt_reconnect()
            else:
                self.graceful_shutdown()
        except Exception as e:
            logger.error(f"Error handling network error: {e}")
            self.emergency_save()
```

**测试方法**：
1. 模拟网络断开
2. 验证程序不崩溃
3. 检查数据完整性

---

### BUG-002: 内存溢出崩溃
**问题描述**：长时间录制导致内存占用持续增长最终崩溃
**复现步骤**：
1. 开始录制
2. 持续录制30分钟以上
3. 内存占用超过2GB后崩溃

**根因分析**：
- 截图缓存未及时释放
- 循环缓冲区实现有问题
- 内存泄漏

**修复方案**：
```python
# memory_management.py
class MemoryManagedBuffer:
    def __init__(self, max_frames=15):
        self.max_frames = max_frames
        self.buffer = deque(maxlen=max_frames)
        self.memory_limit = 100 * 1024 * 1024  # 100MB
        
    def add_frame(self, frame):
        # 压缩图像
        compressed = self.compress_frame(frame)
        
        # 检查内存使用
        if self.get_memory_usage() > self.memory_limit:
            self.cleanup_old_frames()
        
        self.buffer.append(compressed)
    
    def cleanup_old_frames(self):
        # 释放最旧的帧
        while self.get_memory_usage() > self.memory_limit * 0.8:
            if self.buffer:
                self.buffer.popleft()
            gc.collect()
```

---

### BUG-003: 文件权限导致崩溃
**问题描述**：无写入权限时程序直接崩溃
**复现步骤**：
1. 选择只读目录作为保存位置
2. 开始录制
3. 程序崩溃

**修复方案**：
```python
# file_permission_handler.py
class SafeFileHandler:
    @staticmethod
    def safe_write(filepath, data):
        """安全写入文件"""
        try:
            # 检查权限
            if not os.access(os.path.dirname(filepath), os.W_OK):
                # 尝试备用位置
                filepath = self.get_fallback_path(filepath)
            
            with open(filepath, 'w') as f:
                f.write(data)
            return True
        except PermissionError:
            logger.error(f"Permission denied: {filepath}")
            return False
```

---

### BUG-004: 异常输入导致崩溃
**问题描述**：特殊字符输入导致程序崩溃
**复现步骤**：
1. 在脚本名称中输入特殊字符（如：\0, 💀）
2. 保存脚本
3. 程序崩溃

**修复方案**：
```python
# input_sanitizer.py
class InputSanitizer:
    @staticmethod
    def sanitize_filename(name):
        """清理文件名"""
        # 移除危险字符
        dangerous_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*', '\0']
        for char in dangerous_chars:
            name = name.replace(char, '_')
        
        # 限制长度
        max_length = 255
        if len(name) > max_length:
            name = name[:max_length]
        
        # 确保非空
        if not name:
            name = "unnamed_script"
        
        return name
```

## 3. 功能类Bug（P1）

### BUG-005: 录制缓冲区不工作
**问题描述**：15帧循环缓冲区未正确实现
**当前状态**：部分实现，功能不完整

**修复方案**：
```python
# circular_buffer_fix.py
class CircularFrameBuffer:
    def __init__(self, max_frames=15):
        self.max_frames = max_frames
        self.buffer = []
        self.current_index = 0
        self.is_full = False
        
    def add(self, frame):
        timestamp = time.time()
        
        if len(self.buffer) < self.max_frames:
            self.buffer.append((timestamp, frame))
        else:
            self.buffer[self.current_index] = (timestamp, frame)
            self.is_full = True
        
        self.current_index = (self.current_index + 1) % self.max_frames
    
    def get_frame_before_time(self, target_time, seconds_before=0.3):
        """获取目标时间前N秒的帧"""
        target_timestamp = target_time - seconds_before
        
        # 找到最接近的帧
        closest_frame = None
        min_diff = float('inf')
        
        for timestamp, frame in self.buffer:
            diff = abs(timestamp - target_timestamp)
            if diff < min_diff:
                min_diff = diff
                closest_frame = frame
        
        return closest_frame
```

---

### BUG-006: 操作间隔记录不准确
**问题描述**：操作之间的时间间隔记录误差较大
**影响**：智能等待机制无法正确工作

**修复方案**：
```python
# precise_timing.py
class PreciseTimingRecorder:
    def __init__(self):
        self.last_action_time = None
        self.use_perf_counter = True  # 使用高精度计时器
        
    def record_action(self, action):
        current_time = time.perf_counter() if self.use_perf_counter else time.time()
        
        if self.last_action_time is not None:
            # 精确到毫秒
            interval = round((current_time - self.last_action_time) * 1000) / 1000
            action['interval'] = interval
        else:
            action['interval'] = 0
        
        action['timestamp'] = current_time
        self.last_action_time = current_time
        
        return action
```

---

### BUG-007: BBOX裁切坐标错误
**问题描述**：BBOX裁切时坐标系转换错误
**影响**：裁切出的元素图像不正确

**修复方案**：
```python
# bbox_coordinate_fix.py
class BBoxCutter:
    def __init__(self):
        self.standard_size = (768, 768)
    
    def cut_element(self, image_768, bbox):
        """
        从768×768图像中裁切元素
        bbox: (x, y, width, height) 基于768×768坐标系
        """
        # 验证输入
        assert image_768.shape[:2] == self.standard_size, "Image must be 768x768"
        
        x, y, w, h = bbox
        
        # 边界检查
        x = max(0, min(x, self.standard_size[0]))
        y = max(0, min(y, self.standard_size[1]))
        w = min(w, self.standard_size[0] - x)
        h = min(h, self.standard_size[1] - y)
        
        # 裁切
        element = image_768[y:y+h, x:x+w]
        
        return element
```

---

### BUG-008: 多级识别降级不完整
**问题描述**：特征点匹配降级逻辑未完全实现

**修复方案**：
```python
# multi_level_matcher_fix.py
class MultiLevelMatcher:
    def match(self, template, screenshot):
        """完整的多级匹配"""
        # 第1级：模板匹配
        result = self.template_match(template, screenshot)
        if result.confidence >= 0.85:
            return result
        
        # 第2级：ORB匹配
        result = self.orb_match(template, screenshot)
        if result and result.matches >= 15:
            return result
        
        # 第3级：SIFT匹配
        result = self.sift_match(template, screenshot)
        if result and result.matches >= 20:
            return result
        
        # 第4级：多尺度匹配
        result = self.multiscale_match(template, screenshot)
        if result.confidence >= 0.80:
            return result
        
        # 失败
        return None
```

## 4. 性能类Bug（P1）

### BUG-009: 截图CPU占用过高
**问题描述**：10FPS截图导致CPU占用超过30%
**影响**：系统卡顿，影响用户正常使用

**修复方案**：
```python
# optimized_capture.py
class OptimizedScreenCapture:
    def __init__(self):
        self.use_hardware_acceleration = self.check_hardware_support()
        self.capture_region = None  # 可选的区域截图
        
    def capture_screen(self):
        """优化的截图方法"""
        if self.use_hardware_acceleration:
            # 使用硬件加速
            return self.hardware_capture()
        else:
            # 优化的软件截图
            if self.capture_region:
                # 只截取感兴趣区域
                return self.capture_region_only()
            else:
                # 降低质量换取性能
                return self.fast_capture()
    
    def fast_capture(self):
        """快速截图模式"""
        # 使用mss库替代pyautogui
        with mss.mss() as sct:
            monitor = sct.monitors[1]
            img = sct.grab(monitor)
            return np.array(img)
```

---

### BUG-010: 图像匹配性能差
**问题描述**：SIFT匹配耗时超过2秒

**修复方案**：
```python
# optimized_matching.py
class OptimizedMatcher:
    def __init__(self):
        self.feature_cache = {}
        self.use_gpu = cv2.cuda.getCudaEnabledDeviceCount() > 0
        
    def extract_features(self, image):
        """缓存特征提取结果"""
        img_hash = hashlib.md5(image.tobytes()).hexdigest()
        
        if img_hash in self.feature_cache:
            return self.feature_cache[img_hash]
        
        if self.use_gpu:
            # GPU加速
            features = self.gpu_extract_features(image)
        else:
            # CPU优化
            features = self.cpu_extract_features(image)
        
        self.feature_cache[img_hash] = features
        return features
```

## 5. 界面类Bug（P2）

### BUG-011: 按钮无响应
**问题描述**：快速点击按钮时无响应

**修复方案**：
```python
# button_response_fix.py
class ResponsiveButton(QPushButton):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.click_timer = QTimer()
        self.click_timer.timeout.connect(self.enable)
        
    def mousePressEvent(self, event):
        if self.isEnabled():
            self.setEnabled(False)
            self.click_timer.start(200)  # 200ms后重新启用
            super().mousePressEvent(event)
```

---

### BUG-012: 状态栏不更新
**问题描述**：执行过程中状态栏信息不更新

**修复方案**：
```python
# status_bar_fix.py
class ThreadSafeStatusBar(QStatusBar):
    update_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.update_signal.connect(self.showMessage)
        
    def thread_safe_update(self, message):
        """线程安全的更新"""
        self.update_signal.emit(message)
```

---

### BUG-013: 界面卡死
**问题描述**：执行耗时操作时界面无响应

**修复方案**：
```python
# ui_responsive_fix.py
class ResponsiveExecutor(QThread):
    progress_signal = pyqtSignal(int, str)
    
    def __init__(self, task):
        super().__init__()
        self.task = task
        
    def run(self):
        """在独立线程中执行"""
        try:
            for i, step in enumerate(self.task.steps):
                self.progress_signal.emit(i, f"执行步骤 {i+1}")
                step.execute()
        except Exception as e:
            self.progress_signal.emit(-1, f"错误: {e}")
```

## 6. 兼容性Bug（P2）

### BUG-014: Windows路径问题
**问题描述**：Windows下路径分隔符导致错误

**修复方案**：
```python
# path_compatibility.py
class CrossPlatformPath:
    @staticmethod
    def normalize(path):
        """规范化路径"""
        # 使用pathlib处理
        return Path(path).resolve()
    
    @staticmethod
    def join(*parts):
        """跨平台路径拼接"""
        return Path(*parts)
```

---

### BUG-015: macOS权限问题
**问题描述**：macOS下屏幕录制权限未处理

**修复方案**：
```python
# macos_permission.py
class MacOSPermissionHandler:
    @staticmethod
    def check_screen_recording_permission():
        """检查屏幕录制权限"""
        if platform.system() == 'Darwin':
            # 检查权限
            has_permission = self.test_screenshot()
            if not has_permission:
                self.request_permission()
                return False
        return True
```

## 7. TODO清理计划

### 需要实现的TODO（20个）
| 位置 | TODO内容 | 优先级 | 预计工时 |
|------|----------|--------|----------|
| gui/enhanced_main_window.py:470 | Get actual recording status | P1 | 2h |
| gui/enhanced_main_window.py:486 | Implement progress display | P1 | 4h |
| api/adapter.py:943 | Implement share module | P2 | 8h |
| gui/script_editor.py:639 | Show dialog to add action | P2 | 4h |
| recorder/gui.py:524 | Trigger AI processing | P1 | 6h |

### 可以移除的TODO（15个）
- 过时的功能需求
- 已有替代实现
- 不再需要的优化

### 需要转为文档的TODO（15个）
- 未来版本功能
- 架构设计说明
- 性能优化建议

## 8. 测试计划

### 8.1 单元测试
```python
# test_bug_fixes.py
class TestBugFixes(unittest.TestCase):
    def test_network_resilience(self):
        """测试网络弹性"""
        pass
    
    def test_memory_management(self):
        """测试内存管理"""
        pass
    
    def test_circular_buffer(self):
        """测试循环缓冲区"""
        pass
```

### 8.2 集成测试
```python
# test_integration_fixes.py
class TestIntegrationFixes:
    def test_record_to_execute(self):
        """测试完整流程"""
        pass
    
    def test_error_recovery(self):
        """测试错误恢复"""
        pass
```

### 8.3 回归测试
- 确保修复不引入新问题
- 验证相关功能正常
- 性能不退化

## 9. 修复时间表

### 第1周：P0 Bug修复
- [ ] BUG-001: 网络断开崩溃
- [ ] BUG-002: 内存溢出
- [ ] BUG-003: 文件权限崩溃
- [ ] BUG-004: 异常输入崩溃

### 第2周：P1功能Bug
- [ ] BUG-005: 录制缓冲区
- [ ] BUG-006: 操作间隔
- [ ] BUG-007: BBOX裁切
- [ ] BUG-008: 多级识别

### 第3周：P1性能Bug
- [ ] BUG-009: CPU占用
- [ ] BUG-010: 匹配性能

### 第4周：P2 Bug和TODO清理
- [ ] BUG-011~015: 界面和兼容性
- [ ] TODO清理和实现

## 10. 验收标准

### 崩溃类
- [ ] 24小时稳定运行测试通过
- [ ] 异常输入不导致崩溃
- [ ] 网络断开可恢复

### 功能类
- [ ] 核心功能全部正常
- [ ] 测试用例100%通过
- [ ] 无P0/P1级别Bug

### 性能类
- [ ] CPU占用 < 15%
- [ ] 内存占用 < 500MB
- [ ] 识别速度 < 500ms

### 界面类
- [ ] 所有控件响应正常
- [ ] 无界面卡死现象
- [ ] 状态实时更新

## 11. 风险和缓解

| 风险 | 可能性 | 影响 | 缓解措施 |
|------|--------|------|----------|
| 修复引入新Bug | 高 | 高 | 充分测试，代码审查 |
| 性能优化不达标 | 中 | 中 | 分阶段优化，设置基线 |
| 兼容性问题 | 中 | 低 | 多平台测试 |

---

**文档状态**：完成
**创建时间**：2025-01-13
**版本**：1.0
**预计总工时**：4周
**Bug总数**：45个
**P0/P1优先修复**：16个