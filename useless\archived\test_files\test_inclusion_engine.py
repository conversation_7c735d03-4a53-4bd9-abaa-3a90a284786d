"""
Test Force Inclusion Rules Engine
"""

import unittest
import json
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List
import tempfile
import os

from src.showforai.rules.inclusion_engine import (
    InclusionEngine, InclusionRule, ElementType, MatchMethod,
    RuleCondition, BoundingBox, TemplateDetector, 
    ColorDetector, ShapeDetector, get_inclusion_engine
)


class TestInclusionEngine(unittest.TestCase):
    """Test inclusion rules engine"""
    
    def setUp(self):
        """Set up test environment"""
        self.engine = InclusionEngine()
        self.test_image = np.zeros((600, 800, 3), dtype=np.uint8)
        
        # Create test elements in image
        # Blue button
        cv2.rectangle(self.test_image, (100, 100), (200, 150), (255, 100, 0), -1)
        
        # Red notification
        cv2.rectangle(self.test_image, (300, 50), (500, 100), (0, 0, 255), -1)
        
        # Green circle
        cv2.circle(self.test_image, (600, 300), 50, (0, 255, 0), -1)
        
        # Dialog box outline
        cv2.rectangle(self.test_image, (200, 200), (600, 400), (200, 200, 200), 2)
    
    def test_bounding_box_operations(self):
        """Test bounding box methods"""
        box1 = BoundingBox(10, 10, 100, 100)
        box2 = BoundingBox(50, 50, 50, 50)
        box3 = BoundingBox(200, 200, 50, 50)
        
        # Test contains
        self.assertTrue(box1.contains(box2))
        self.assertFalse(box1.contains(box3))
        
        # Test overlaps
        self.assertTrue(box1.overlaps(box2))
        self.assertFalse(box1.overlaps(box3))
        
        # Test expand
        expanded = box1.expand(10)
        self.assertEqual(expanded.x, 0)  # Clamped to 0
        self.assertEqual(expanded.width, 120)
    
    def test_rule_conditions(self):
        """Test rule condition evaluation"""
        condition = RuleCondition("app_name", "contains", "chrome")
        
        context1 = {"app_name": "Google Chrome"}
        context2 = {"app_name": "Firefox"}
        context3 = {}
        
        self.assertTrue(condition.evaluate(context1))
        self.assertFalse(condition.evaluate(context2))
        self.assertFalse(condition.evaluate(context3))
        
        # Test other operators
        eq_condition = RuleCondition("version", "equals", "1.0")
        self.assertTrue(eq_condition.evaluate({"version": "1.0"}))
        self.assertFalse(eq_condition.evaluate({"version": "2.0"}))
        
        start_condition = RuleCondition("path", "startswith", "/home")
        self.assertTrue(start_condition.evaluate({"path": "/home/<USER>"}))
        self.assertFalse(start_condition.evaluate({"path": "/usr/bin"}))
    
    def test_rule_creation_and_serialization(self):
        """Test rule creation and JSON serialization"""
        rule = InclusionRule(
            id="test_rule",
            name="Test Rule",
            description="A test rule",
            element_type=ElementType.BUTTON,
            match_method=MatchMethod.COLOR,
            priority=5,
            conditions=[
                RuleCondition("app", "equals", "test")
            ],
            parameters={"threshold": 0.8},
            validation={"required": True}
        )
        
        # Test serialization
        rule_dict = rule.to_dict()
        self.assertEqual(rule_dict['id'], "test_rule")
        self.assertEqual(rule_dict['element_type'], "button")
        self.assertEqual(rule_dict['match_method'], "color")
        
        # Test deserialization
        restored = InclusionRule.from_dict(rule_dict)
        self.assertEqual(restored.id, rule.id)
        self.assertEqual(restored.element_type, rule.element_type)
        self.assertEqual(len(restored.conditions), 1)
    
    def test_color_detector(self):
        """Test color-based detection"""
        detector = ColorDetector()
        
        # Create rule for blue detection
        rule = InclusionRule(
            id="blue_button",
            name="Blue Button",
            description="Detect blue buttons",
            element_type=ElementType.BUTTON,
            match_method=MatchMethod.COLOR,
            parameters={
                'color_range': {
                    'lower': [100, 50, 50],  # Blue in HSV
                    'upper': [130, 255, 255]
                },
                'min_area': 100
            }
        )
        
        # Convert test image to HSV for blue detection
        hsv_image = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2HSV)
        
        # Detect blue regions
        bboxes = detector.detect(self.test_image, rule)
        
        # Should find at least one blue region
        self.assertGreater(len(bboxes), 0)
        
        # Validate detection
        if bboxes:
            is_valid = detector.validate(bboxes[0], self.test_image, rule)
            self.assertTrue(is_valid)
    
    def test_shape_detector(self):
        """Test shape-based detection"""
        detector = ShapeDetector()
        
        # Rule for rectangle detection
        rect_rule = InclusionRule(
            id="dialog",
            name="Dialog Box",
            description="Detect dialog boxes",
            element_type=ElementType.DIALOG,
            match_method=MatchMethod.SHAPE,
            parameters={
                'shape': 'rectangle',
                'min_width': 50,
                'min_height': 50
            }
        )
        
        # Detect rectangles
        bboxes = detector.detect(self.test_image, rect_rule)
        
        # Should find rectangles
        self.assertGreater(len(bboxes), 0)
        
        # Rule for circle detection
        circle_rule = InclusionRule(
            id="indicator",
            name="Circle Indicator",
            description="Detect circular indicators",
            element_type=ElementType.CUSTOM,
            match_method=MatchMethod.SHAPE,
            parameters={
                'shape': 'circle',
                'min_radius': 30,
                'max_radius': 100
            }
        )
        
        # Detect circles
        circle_boxes = detector.detect(self.test_image, circle_rule)
        
        # Should find the green circle
        self.assertGreater(len(circle_boxes), 0)
    
    def test_template_detector(self):
        """Test template matching detector"""
        detector = TemplateDetector()
        
        # Create a template (small blue rectangle)
        template = np.zeros((50, 100, 3), dtype=np.uint8)
        template[:, :] = (255, 100, 0)  # Blue color
        
        # Save template temporarily
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            cv2.imwrite(tmp.name, template)
            template_path = tmp.name
        
        try:
            rule = InclusionRule(
                id="template_match",
                name="Template Match",
                description="Match template",
                element_type=ElementType.CUSTOM,
                match_method=MatchMethod.TEMPLATE,
                parameters={
                    'template_path': template_path,
                    'threshold': 0.7
                },
                validation={
                    'min_confidence': 0.6
                }
            )
            
            # Should find the blue button
            bboxes = detector.detect(self.test_image, rule)
            self.assertGreater(len(bboxes), 0)
            
            # Validate
            if bboxes:
                is_valid = detector.validate(bboxes[0], self.test_image, rule)
                self.assertTrue(is_valid)
        
        finally:
            # Clean up template file
            if os.path.exists(template_path):
                os.unlink(template_path)
    
    def test_engine_rule_management(self):
        """Test engine rule management"""
        # Add rules
        rule1 = InclusionRule(
            id="rule1",
            name="Rule 1",
            description="First rule",
            element_type=ElementType.BUTTON,
            match_method=MatchMethod.COLOR,
            priority=10
        )
        
        rule2 = InclusionRule(
            id="rule2",
            name="Rule 2",
            description="Second rule",
            element_type=ElementType.DIALOG,
            match_method=MatchMethod.SHAPE,
            priority=5
        )
        
        self.engine.add_rule(rule1)
        self.engine.add_rule(rule2)
        
        # Test retrieval
        self.assertEqual(self.engine.get_rule("rule1").name, "Rule 1")
        
        # Test listing (should be sorted by priority)
        rules = self.engine.list_rules()
        self.assertEqual(len(rules), 2)
        self.assertEqual(rules[0].id, "rule1")  # Higher priority first
        
        # Test removal
        self.engine.remove_rule("rule1")
        self.assertIsNone(self.engine.get_rule("rule1"))
        self.assertEqual(len(self.engine.list_rules()), 1)
    
    def test_engine_detection(self):
        """Test full engine detection workflow"""
        # Create default rules
        self.engine.create_default_rules()
        
        # Add a simple color rule for our test image
        blue_rule = InclusionRule(
            id="blue_elements",
            name="Blue Elements",
            description="Detect blue UI elements",
            element_type=ElementType.BUTTON,
            match_method=MatchMethod.COLOR,
            priority=10,
            parameters={
                'color_range': {
                    'lower': [100, 50, 50],
                    'upper': [130, 255, 255]
                },
                'min_area': 1000
            }
        )
        self.engine.add_rule(blue_rule)
        
        # Detect elements
        context = {"app_name": "test_app"}
        results = self.engine.detect_elements(self.test_image, context)
        
        # Should detect blue elements
        self.assertIn("blue_elements", results)
        self.assertGreater(len(results["blue_elements"]), 0)
    
    def test_ensure_inclusion(self):
        """Test ensure inclusion functionality"""
        # Create some test bounding boxes
        detected = {
            "rule1": [
                BoundingBox(100, 100, 100, 50),
                BoundingBox(300, 50, 200, 50)
            ],
            "rule2": [
                BoundingBox(200, 200, 400, 200)
            ]
        }
        
        # Ensure inclusion without cropping
        result_img, boxes = self.engine.ensure_inclusion(
            self.test_image, detected, {"crop_to_elements": False}
        )
        
        # Should return original image
        self.assertEqual(result_img.shape, self.test_image.shape)
        self.assertEqual(len(boxes), 3)
        
        # Ensure inclusion with cropping
        result_img, boxes = self.engine.ensure_inclusion(
            self.test_image, detected, {"crop_to_elements": True}
        )
        
        # Should return cropped image
        self.assertLess(result_img.shape[0], self.test_image.shape[0])
        self.assertLess(result_img.shape[1], self.test_image.shape[1])
    
    def test_screenshot_validation(self):
        """Test screenshot validation"""
        # Add required rule
        required_rule = InclusionRule(
            id="required_button",
            name="Required Button",
            description="Must have this button",
            element_type=ElementType.BUTTON,
            match_method=MatchMethod.COLOR,
            parameters={
                'color_range': {
                    'lower': [0, 0, 200],  # Red
                    'upper': [10, 255, 255]
                },
                'min_area': 100
            },
            validation={
                'required': True,
                'min_count': 1
            }
        )
        
        self.engine.add_rule(required_rule)
        
        # Validate - should find red rectangle
        is_valid, errors = self.engine.validate_screenshot(self.test_image)
        
        # The validation depends on color detection working correctly
        # For this test, we just verify the validation runs
        self.assertIsInstance(is_valid, bool)
        self.assertIsInstance(errors, list)
    
    def test_config_persistence(self):
        """Test saving and loading configuration"""
        # Create temporary config file
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as tmp:
            config_path = Path(tmp.name)
        
        try:
            engine = InclusionEngine(config_path)
            
            # Add some rules
            engine.create_default_rules()
            
            # Save
            engine.save_rules()
            
            # Create new engine and load
            engine2 = InclusionEngine(config_path)
            
            # Should have same rules
            self.assertEqual(len(engine2.rules), len(engine.rules))
            
            for rule_id in engine.rules:
                self.assertIn(rule_id, engine2.rules)
                self.assertEqual(
                    engine2.rules[rule_id].name,
                    engine.rules[rule_id].name
                )
        
        finally:
            # Clean up
            if config_path.exists():
                config_path.unlink()


def run_performance_test():
    """Test performance of inclusion engine"""
    print("\n" + "="*50)
    print("INCLUSION ENGINE PERFORMANCE TEST")
    print("="*50)
    
    import time
    
    engine = InclusionEngine()
    
    # Create large test image
    large_image = np.random.randint(0, 255, (1920, 1080, 3), dtype=np.uint8)
    
    # Add multiple elements
    for i in range(20):
        x = np.random.randint(0, 1800)
        y = np.random.randint(0, 1000)
        color = (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255))
        cv2.rectangle(large_image, (x, y), (x+100, y+50), color, -1)
    
    # Create multiple rules
    for i in range(10):
        rule = InclusionRule(
            id=f"perf_rule_{i}",
            name=f"Performance Rule {i}",
            description=f"Test rule {i}",
            element_type=ElementType.BUTTON,
            match_method=MatchMethod.SHAPE if i % 2 == 0 else MatchMethod.COLOR,
            priority=i,
            parameters={
                'shape': 'rectangle',
                'min_width': 20,
                'min_height': 20
            } if i % 2 == 0 else {
                'color_range': {
                    'lower': [i*10, 50, 50],
                    'upper': [(i+1)*10, 255, 255]
                },
                'min_area': 100
            }
        )
        engine.add_rule(rule)
    
    # Test detection performance
    start = time.time()
    results = engine.detect_elements(large_image)
    detection_time = (time.time() - start) * 1000
    
    print(f"\nDetection Performance:")
    print(f"  Image size: 1920x1080")
    print(f"  Rules: {len(engine.rules)}")
    print(f"  Detection time: {detection_time:.1f}ms")
    print(f"  Elements found: {sum(len(boxes) for boxes in results.values())}")
    
    # Test validation performance
    start = time.time()
    is_valid, errors = engine.validate_screenshot(large_image)
    validation_time = (time.time() - start) * 1000
    
    print(f"\nValidation Performance:")
    print(f"  Validation time: {validation_time:.1f}ms")
    print(f"  Valid: {is_valid}")
    print(f"  Errors: {len(errors)}")
    
    # Test ensure inclusion
    if results:
        start = time.time()
        _, boxes = engine.ensure_inclusion(large_image, results, {"crop_to_elements": True})
        inclusion_time = (time.time() - start) * 1000
        
        print(f"\nInclusion Performance:")
        print(f"  Processing time: {inclusion_time:.1f}ms")
        print(f"  Boxes processed: {len(boxes)}")
    
    print("\n" + "="*50)
    print("Performance test complete!")
    
    # Performance targets
    success = detection_time < 500 and validation_time < 100
    
    if success:
        print("✓ Performance targets met!")
    else:
        print("✗ Performance needs optimization")
    
    return success


if __name__ == '__main__':
    # Run unit tests
    print("Running Inclusion Engine Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    success = run_performance_test()
    
    print("\n" + "="*50)
    if success:
        print("✓✓✓ INCLUSION ENGINE TESTS PASSED ✓✓✓")
    else:
        print("✗ Some tests failed")
    print("="*50)