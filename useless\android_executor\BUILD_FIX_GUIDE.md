# 🔧 Android DSL Executor 构建问题修复指南

## 📋 常见构建错误及解决方案

### 1. 仓库配置错误

**错误信息**:
```
Build was configured to prefer settings repositories over project repositories but repository 'Google' was added by build file 'build.gradle'
```

**原因**: 新版本Gradle要求仓库配置在`settings.gradle`中，而不是在项目的`build.gradle`中。

**解决方案**: ✅ 已修复
- 从`build.gradle`中移除了`allprojects`仓库配置
- 所有仓库现在在`settings.gradle`中统一管理

### 2. Gradle版本兼容性

**如果遇到Gradle版本问题**:

#### 2.1 检查Gradle Wrapper版本
```bash
cat gradle/wrapper/gradle-wrapper.properties
```

#### 2.2 更新到推荐版本
在`gradle/wrapper/gradle-wrapper.properties`中设置:
```properties
distributionUrl=https\://services.gradle.org/distributions/gradle-8.4-bin.zip
```

### 3. Android Gradle Plugin版本

**推荐配置**:
- Gradle: 8.4
- Android Gradle Plugin: 8.1.2
- Kotlin: 1.9.10

### 4. 依赖冲突解决

#### 4.1 清理项目
```bash
# 清理构建缓存
./gradlew clean

# 清理Gradle缓存
rm -rf ~/.gradle/caches/

# 重新构建
./gradlew build
```

#### 4.2 强制刷新依赖
```bash
./gradlew build --refresh-dependencies
```

### 5. OpenCV依赖问题

**如果OpenCV依赖下载失败**:

#### 5.1 手动下载OpenCV
1. 访问 https://opencv.org/releases/
2. 下载 OpenCV 4.8.0 Android SDK
3. 解压到项目目录下的`opencv`文件夹

#### 5.2 本地OpenCV配置
在`app/build.gradle`中添加:
```gradle
android {
    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs', 'opencv/native/libs']
        }
    }
}
```

### 6. 内存不足问题

**错误信息**: `OutOfMemoryError` 或构建缓慢

**解决方案**:

#### 6.1 增加Gradle内存
在`gradle.properties`中设置:
```properties
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8
```

#### 6.2 启用并行构建
```properties
org.gradle.parallel=true
org.gradle.caching=true
```

### 7. 网络连接问题

**如果依赖下载失败**:

#### 7.1 使用国内镜像
在`settings.gradle`中添加阿里云镜像:
```gradle
repositories {
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/central' }
    google()
    mavenCentral()
    maven { url 'https://jitpack.io' }
}
```

#### 7.2 配置代理（如需要）
在`gradle.properties`中添加:
```properties
systemProp.http.proxyHost=your.proxy.host
systemProp.http.proxyPort=8080
systemProp.https.proxyHost=your.proxy.host
systemProp.https.proxyPort=8080
```

## 🚀 推荐的构建流程

### 1. 首次构建
```bash
# 1. 清理项目
./gradlew clean

# 2. 同步项目（在Android Studio中）
File → Sync Project with Gradle Files

# 3. 构建Debug版本
./gradlew assembleDebug
```

### 2. 日常开发构建
```bash
# 增量构建
./gradlew build

# 只构建Debug版本
./gradlew assembleDebug

# 运行测试
./gradlew test
```

### 3. 发布构建
```bash
# 构建Release版本
./gradlew assembleRelease

# 生成签名APK
./gradlew bundleRelease
```

## 🔍 调试构建问题

### 1. 详细日志
```bash
# 查看详细构建日志
./gradlew build --info

# 查看调试日志
./gradlew build --debug

# 查看堆栈跟踪
./gradlew build --stacktrace
```

### 2. 依赖分析
```bash
# 查看依赖树
./gradlew app:dependencies

# 查看依赖冲突
./gradlew app:dependencyInsight --dependency kotlin-stdlib
```

### 3. 构建扫描
```bash
# 生成构建扫描报告
./gradlew build --scan
```

## ⚠️ 常见陷阱

### 1. 版本不匹配
- 确保Gradle、AGP、Kotlin版本兼容
- 参考官方兼容性表格

### 2. 缓存问题
- 定期清理`.gradle`缓存
- 使用`--refresh-dependencies`强制更新

### 3. 网络问题
- 检查防火墙和代理设置
- 使用稳定的网络连接

### 4. 磁盘空间
- 确保有足够的磁盘空间（建议>5GB）
- 定期清理构建产物

## 📱 Android Studio配置

### 1. JDK设置
```
File → Project Structure → SDK Location → JDK Location
推荐使用JDK 17
```

### 2. Gradle设置
```
File → Settings → Build → Gradle
- Use Gradle from: 'gradle-wrapper.properties' file
- Gradle JVM: Project SDK
```

### 3. 内存设置
```
Help → Edit Custom VM Options
添加: -Xmx4096m
```

## 🎯 验证构建成功

构建成功后应该看到:
```
BUILD SUCCESSFUL in Xs
```

并且在以下位置找到APK文件:
```
app/build/outputs/apk/debug/app-debug.apk
```

## 📞 获取帮助

如果仍然遇到问题:
1. 检查Android Studio的Build Output
2. 查看Gradle Console的详细错误信息
3. 搜索具体的错误信息
4. 参考官方文档和社区解决方案

---

**最后更新**: 2024年1月15日
