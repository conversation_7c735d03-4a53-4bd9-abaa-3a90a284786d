# Android DSL Executor

ShowForAI Android执行器 - 基于DSL的移动端自动化脚本执行器

## 🎯 项目概述

Android DSL Executor是ShowForAI平台的移动端执行组件，负责在Android设备上执行由AI生成的自动化脚本。采用与桌面版相同的DSL规范，实现视觉优先的三层级联定位策略。

## 🏗️ 技术架构

### 核心组件
- **ExecutionEngine**: DSL脚本执行引擎
- **ScreenCaptureService**: 屏幕捕捉服务 (MediaProjection)
- **AutomationService**: 自动化操作服务 (AccessibilityService)
- **LocationStrategies**: 三层定位策略实现
- **VisualMatcher**: OpenCV图像匹配
- **OCRProcessor**: ML Kit文本识别

### 技术栈
- **开发语言**: Kotlin + Java
- **UI框架**: Jetpack Compose + 传统View
- **架构模式**: MVVM + Repository Pattern
- **依赖注入**: Hilt
- **图像处理**: OpenCV for Android 4.x
- **OCR**: Google ML Kit Text Recognition
- **权限管理**: Android Runtime Permissions

## 🔧 核心功能

### 1. 三层级联定位策略
```
1. 视觉匹配 (Visual Matching) - OpenCV模板匹配
2. OCR文本识别 (Text Recognition) - ML Kit OCR
3. 坐标降级 (Coordinate Fallback) - 归一化坐标
```

### 2. 支持的DSL指令
- `CLICK`: 点击操作
- `SWIPE`: 滑动手势
- `INPUT_TEXT`: 文本输入
- `WAIT`: 等待操作
- `SCROLL`: 滚动操作
- `LONG_PRESS`: 长按操作

### 3. 权限管理
- 屏幕捕捉权限 (MediaProjection)
- 无障碍服务权限 (AccessibilityService)
- 存储权限 (读取DSL脚本文件)

## 📁 项目结构

```
android_executor/
├── app/
│   ├── src/main/
│   │   ├── java/com/showforai/executor/
│   │   │   ├── core/                 # 核心执行引擎
│   │   │   │   ├── engine/           # DSL执行引擎
│   │   │   │   ├── location/         # 定位策略
│   │   │   │   ├── actions/          # 动作执行器
│   │   │   │   └── vision/           # 图像识别
│   │   │   ├── services/             # Android服务
│   │   │   │   ├── ScreenCaptureService.kt
│   │   │   │   └── AutomationService.kt
│   │   │   ├── ui/                   # 用户界面
│   │   │   │   ├── main/             # 主界面
│   │   │   │   ├── execution/        # 执行界面
│   │   │   │   └── settings/         # 设置界面
│   │   │   ├── data/                 # 数据层
│   │   │   │   ├── models/           # 数据模型
│   │   │   │   └── repositories/     # 数据仓库
│   │   │   └── utils/                # 工具类
│   │   ├── res/                      # 资源文件
│   │   └── AndroidManifest.xml       # 应用清单
│   ├── build.gradle                  # 应用级构建配置
│   └── proguard-rules.pro           # 代码混淆规则
├── build.gradle                     # 项目级构建配置
├── settings.gradle                  # 项目设置
└── gradle.properties               # Gradle属性
```

## 🚀 开发阶段

### Phase 1: 基础架构 (当前)
- [x] 项目结构创建
- [ ] Gradle配置和依赖管理
- [ ] 核心数据模型定义
- [ ] 权限管理框架

### Phase 2: 核心服务
- [ ] MediaProjection屏幕捕捉服务
- [ ] AccessibilityService自动化服务
- [ ] OpenCV集成和图像匹配
- [ ] ML Kit OCR集成

### Phase 3: 执行引擎
- [ ] DSL解析器
- [ ] 三层定位策略实现
- [ ] 动作执行器
- [ ] 智能等待机制

### Phase 4: 用户界面
- [ ] 主界面设计
- [ ] 脚本管理界面
- [ ] 执行监控界面
- [ ] 设置和权限引导

### Phase 5: 测试和优化
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化
- [ ] 兼容性测试

## 📋 开发要求

### 环境要求
- Android Studio Arctic Fox+
- Android SDK API 21+ (Android 5.0+)
- Kotlin 1.8+
- Gradle 7.0+

### 设备要求
- Android 5.0+ (API 21+)
- 2GB+ RAM
- 100MB+ 存储空间

## 🔒 权限说明

### 必需权限
```xml
<!-- 屏幕捕捉 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

<!-- 文件访问 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

<!-- 网络访问 (可选，用于日志上传) -->
<uses-permission android:name="android.permission.INTERNET" />
```

### 特殊权限
- **MediaProjection**: 屏幕录制权限
- **AccessibilityService**: 无障碍服务权限

## 🎮 使用流程

1. **权限授予**: 用户首次启动时引导开启必要权限
2. **脚本加载**: 从存储中加载DSL脚本文件
3. **执行监控**: 实时显示执行进度和状态
4. **结果反馈**: 执行完成后显示结果和日志

## 🔧 调试说明

### 日志标签
- `DSLExecutor`: 主执行引擎
- `ScreenCapture`: 屏幕捕捉
- `Automation`: 自动化操作
- `VisualMatcher`: 图像匹配
- `OCRProcessor`: OCR识别

### 调试命令
```bash
# 查看应用日志
adb logcat | grep "DSLExecutor"

# 安装调试版本
./gradlew installDebug

# 运行测试
./gradlew test
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件
