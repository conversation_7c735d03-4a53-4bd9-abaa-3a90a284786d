"""
建议的网络检查修复方案
这是要替换 src/showforai/sync/offline_manager.py 中 _check_connectivity 方法的代码
"""

def _check_connectivity(self) -> bool:
    """
    检查网络连接 - 改进版本
    针对中国网络环境优化，直接检查AI服务器可用性
    
    Returns:
        True if network is available, False otherwise
    """
    # 方法1：直接检查AI服务器（最准确）
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5秒超时
        # 直接测试AI服务器连接
        result = sock.connect_ex(("*************", 8080))
        sock.close()
        if result == 0:
            self.logger.debug("Network check: AI server directly accessible")
            return True
    except Exception as e:
        self.logger.debug(f"AI server check failed: {e}")
    
    # 方法2：检查国内DNS（备选）
    try:
        socket.gethostbyname("www.baidu.com")
        self.logger.debug("Network check: Baidu DNS resolved")
        return True
    except:
        pass
    
    # 方法3：检查国内DNS服务器（最后备选）
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        # 阿里DNS
        result = sock.connect_ex(("*********", 53))
        sock.close()
        if result == 0:
            self.logger.debug("Network check: Ali DNS accessible")
            return True
    except:
        pass
    
    self.logger.warning("Network check: All methods failed, assuming offline")
    return False

# ========== 或者更简单的版本 ==========

def _check_connectivity_simple(self) -> bool:
    """
    检查网络连接 - 简化版本
    直接检查AI服务器是否可访问
    
    Returns:
        True if network is available, False otherwise
    """
    try:
        # 只检查AI服务器，因为这是我们真正需要的
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(("*************", 8080))
        sock.close()
        return result == 0
    except:
        return False