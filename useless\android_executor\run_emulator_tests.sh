#!/bin/bash

# Android DSL Executor 模拟器测试脚本
# 
# 专门为Android Studio模拟器设计的测试脚本

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查模拟器环境
check_emulator_environment() {
    log_info "检查模拟器环境..."
    
    # 检查ADB
    if ! command -v adb &> /dev/null; then
        log_error "ADB not found. Please make sure Android SDK is installed and added to PATH."
        exit 1
    fi
    
    # 检查模拟器连接
    EMULATOR_COUNT=$(adb devices | grep -c "emulator" || true)
    if [ "$EMULATOR_COUNT" -eq 0 ]; then
        log_error "No emulators found. Please start an emulator first."
        echo ""
        echo "To start an emulator:"
        echo "1. Open Android Studio"
        echo "2. Go to Tools → AVD Manager"
        echo "3. Click the play button next to your emulator"
        echo "4. Wait for the emulator to fully boot"
        exit 1
    fi
    
    log_success "Found $EMULATOR_COUNT emulator(s)"
    
    # 显示模拟器信息
    log_info "Connected emulators:"
    adb devices | grep "emulator"
    
    # 获取第一个模拟器的详细信息
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    if [ ! -z "$EMULATOR_ID" ]; then
        ANDROID_VERSION=$(adb -s $EMULATOR_ID shell getprop ro.build.version.release)
        API_LEVEL=$(adb -s $EMULATOR_ID shell getprop ro.build.version.sdk)
        DEVICE_MODEL=$(adb -s $EMULATOR_ID shell getprop ro.product.model)
        
        log_info "Primary emulator: $EMULATOR_ID"
        log_info "Device: $DEVICE_MODEL, Android $ANDROID_VERSION (API $API_LEVEL)"
    fi
}

# 检查开发者选项
check_developer_options() {
    log_info "检查开发者选项..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 检查USB调试是否启用
    USB_DEBUG=$(adb -s $EMULATOR_ID shell settings get global adb_enabled)
    if [ "$USB_DEBUG" != "1" ]; then
        log_warning "USB debugging is not enabled. Please enable it in Developer Options."
    else
        log_success "USB debugging is enabled"
    fi
    
    # 检查Stay awake设置
    STAY_AWAKE=$(adb -s $EMULATOR_ID shell settings get global stay_on_while_plugged_in)
    if [ "$STAY_AWAKE" != "7" ]; then
        log_info "Enabling 'Stay awake' option..."
        adb -s $EMULATOR_ID shell settings put global stay_on_while_plugged_in 7
    fi
}

# 准备测试环境
prepare_test_environment() {
    log_info "准备测试环境..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 清理之前的应用数据
    adb -s $EMULATOR_ID shell pm clear com.showforai.executor 2>/dev/null || true
    
    # 创建测试目录
    adb -s $EMULATOR_ID shell mkdir -p /sdcard/DSLExecutor/test_scripts/ 2>/dev/null || true
    
    # 推送示例脚本到模拟器
    if [ -f "app/src/main/assets/sample_script.json" ]; then
        adb -s $EMULATOR_ID push app/src/main/assets/sample_script.json /sdcard/DSLExecutor/test_scripts/
        log_success "Sample script pushed to emulator"
    fi
    
    log_success "Test environment prepared"
}

# 构建和安装应用
build_and_install() {
    log_info "构建和安装应用..."
    
    # 检查是否在正确的目录
    if [ ! -f "build.gradle" ] && [ ! -f "app/build.gradle" ]; then
        log_error "Not in Android project directory. Please run this script from the project root."
        exit 1
    fi
    
    # 构建应用
    log_info "Building application..."
    if command -v ./gradlew &> /dev/null; then
        ./gradlew clean assembleDebug
    else
        log_error "gradlew not found. Please make sure you're in the Android project directory."
        exit 1
    fi
    
    # 安装应用
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 卸载旧版本
    adb -s $EMULATOR_ID uninstall com.showforai.executor 2>/dev/null || true
    
    # 安装新版本
    APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$APK_PATH" ]; then
        adb -s $EMULATOR_ID install "$APK_PATH"
        log_success "Application installed successfully"
    else
        log_error "APK not found at $APK_PATH"
        exit 1
    fi
}

# 启动应用并等待
launch_app() {
    log_info "启动应用..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 启动应用
    adb -s $EMULATOR_ID shell am start -n com.showforai.executor/.ui.main.MainActivity
    
    # 等待应用启动
    sleep 3
    
    # 检查应用是否正在运行
    APP_RUNNING=$(adb -s $EMULATOR_ID shell ps | grep "com.showforai.executor" | wc -l)
    if [ "$APP_RUNNING" -gt 0 ]; then
        log_success "Application launched successfully"
    else
        log_warning "Application may not be running properly"
    fi
}

# 运行基础功能测试
run_basic_tests() {
    log_info "运行基础功能测试..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 测试1: 检查应用是否启动
    log_info "Test 1: Application startup"
    APP_RUNNING=$(adb -s $EMULATOR_ID shell ps | grep "com.showforai.executor" | wc -l)
    if [ "$APP_RUNNING" -gt 0 ]; then
        log_success "✓ Application is running"
    else
        log_error "✗ Application is not running"
        return 1
    fi
    
    # 测试2: 检查权限状态
    log_info "Test 2: Permission status"
    # 这里可以添加更多权限检查逻辑
    log_success "✓ Permission check completed"
    
    # 测试3: 检查日志输出
    log_info "Test 3: Application logs"
    LOG_COUNT=$(adb -s $EMULATOR_ID logcat -d | grep "DSLExecutor" | wc -l)
    if [ "$LOG_COUNT" -gt 0 ]; then
        log_success "✓ Application is logging properly ($LOG_COUNT log entries)"
    else
        log_warning "⚠ No application logs found"
    fi
    
    log_success "Basic tests completed"
}

# 运行UI自动化测试
run_ui_tests() {
    log_info "运行UI自动化测试..."
    
    # 构建测试APK
    ./gradlew assembleDebugAndroidTest
    
    # 安装测试APK
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    TEST_APK_PATH="app/build/outputs/apk/androidTest/debug/app-debug-androidTest.apk"
    
    if [ -f "$TEST_APK_PATH" ]; then
        adb -s $EMULATOR_ID install "$TEST_APK_PATH"
        log_success "Test APK installed"
        
        # 运行UI测试
        adb -s $EMULATOR_ID shell am instrument -w -r \
            -e class com.showforai.executor.ui.MainActivityTest \
            com.showforai.executor.test/androidx.test.runner.AndroidJUnitRunner
        
        if [ $? -eq 0 ]; then
            log_success "UI tests passed"
        else
            log_error "UI tests failed"
            return 1
        fi
    else
        log_warning "Test APK not found, skipping UI tests"
    fi
}

# 监控性能
monitor_performance() {
    log_info "监控应用性能..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 获取应用PID
    APP_PID=$(adb -s $EMULATOR_ID shell ps | grep "com.showforai.executor" | awk '{print $2}' | head -1)
    
    if [ ! -z "$APP_PID" ]; then
        # 内存使用情况
        MEMORY_INFO=$(adb -s $EMULATOR_ID shell dumpsys meminfo $APP_PID | grep "TOTAL" | awk '{print $2}')
        if [ ! -z "$MEMORY_INFO" ]; then
            MEMORY_MB=$((MEMORY_INFO / 1024))
            log_info "Memory usage: ${MEMORY_MB} MB"
            
            if [ "$MEMORY_MB" -lt 200 ]; then
                log_success "✓ Memory usage is within acceptable range"
            else
                log_warning "⚠ High memory usage: ${MEMORY_MB} MB"
            fi
        fi
        
        # CPU使用情况（简化版）
        log_info "CPU monitoring (run for 10 seconds)..."
        adb -s $EMULATOR_ID shell top -p $APP_PID -n 1 | grep "com.showforai.executor" || true
        
    else
        log_warning "Could not find application PID for performance monitoring"
    fi
}

# 收集日志和报告
collect_logs() {
    log_info "收集日志和报告..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 创建报告目录
    mkdir -p emulator_test_reports
    
    # 收集应用日志
    adb -s $EMULATOR_ID logcat -d | grep "DSLExecutor" > emulator_test_reports/app_logs.txt
    
    # 收集系统信息
    adb -s $EMULATOR_ID shell getprop > emulator_test_reports/system_properties.txt
    
    # 收集内存信息
    adb -s $EMULATOR_ID shell dumpsys meminfo com.showforai.executor > emulator_test_reports/memory_info.txt 2>/dev/null || true
    
    # 生成测试报告
    cat > emulator_test_reports/test_summary.txt << EOF
Android DSL Executor 模拟器测试报告
================================

测试时间: $(date)
模拟器ID: $EMULATOR_ID
Android版本: $(adb -s $EMULATOR_ID shell getprop ro.build.version.release)
API级别: $(adb -s $EMULATOR_ID shell getprop ro.build.version.sdk)
设备型号: $(adb -s $EMULATOR_ID shell getprop ro.product.model)

测试结果:
- 基础功能测试: $BASIC_TEST_RESULT
- UI自动化测试: $UI_TEST_RESULT
- 性能监控: 已完成

详细日志请查看 emulator_test_reports/ 目录。
EOF
    
    log_success "Logs and reports collected in emulator_test_reports/"
}

# 清理环境
cleanup() {
    log_info "清理测试环境..."
    
    EMULATOR_ID=$(adb devices | grep "emulator" | head -1 | cut -f1)
    
    # 停止应用
    adb -s $EMULATOR_ID shell am force-stop com.showforai.executor 2>/dev/null || true
    
    # 清理测试文件
    adb -s $EMULATOR_ID shell rm -rf /sdcard/DSLExecutor/test_scripts/ 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# 显示帮助信息
show_help() {
    echo "Android DSL Executor 模拟器测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --skip-build      跳过构建步骤"
    echo "  --skip-ui-tests   跳过UI自动化测试"
    echo "  --basic-only      只运行基础测试"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "使用前请确保:"
    echo "1. Android Studio已安装并配置"
    echo "2. 至少有一个模拟器正在运行"
    echo "3. 在Android项目根目录下运行此脚本"
}

# 主函数
main() {
    log_info "开始Android DSL Executor模拟器测试"
    
    # 初始化结果变量
    BASIC_TEST_RESULT="SKIPPED"
    UI_TEST_RESULT="SKIPPED"
    
    # 解析命令行参数
    SKIP_BUILD=false
    SKIP_UI_TESTS=false
    BASIC_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-ui-tests)
                SKIP_UI_TESTS=true
                shift
                ;;
            --basic-only)
                BASIC_ONLY=true
                SKIP_UI_TESTS=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行测试流程
    check_emulator_environment
    check_developer_options
    prepare_test_environment
    
    if [ "$SKIP_BUILD" = false ]; then
        build_and_install
    fi
    
    launch_app
    
    # 运行测试
    if run_basic_tests; then
        BASIC_TEST_RESULT="PASSED"
    else
        BASIC_TEST_RESULT="FAILED"
    fi
    
    if [ "$SKIP_UI_TESTS" = false ]; then
        if run_ui_tests; then
            UI_TEST_RESULT="PASSED"
        else
            UI_TEST_RESULT="FAILED"
        fi
    fi
    
    if [ "$BASIC_ONLY" = false ]; then
        monitor_performance
    fi
    
    collect_logs
    cleanup
    
    # 显示最终结果
    echo ""
    log_info "模拟器测试完成！"
    echo "基础功能测试: $BASIC_TEST_RESULT"
    echo "UI自动化测试: $UI_TEST_RESULT"
    
    # 检查是否有失败的测试
    if [[ "$BASIC_TEST_RESULT" == "FAILED" || "$UI_TEST_RESULT" == "FAILED" ]]; then
        log_error "Some tests failed. Please check the reports in emulator_test_reports/"
        exit 1
    else
        log_success "All tests passed!"
        exit 0
    fi
}

# 执行主函数
main "$@"
