# ShowForAI 产品原则

## 核心执行原则

### 1. 图像识别为核心
- **坚持原则**：始终基于图像识别运行，不使用其他定位方式
- **质量优先**：不过分降低检测阈值，宁可失败也不误点击
- **接受失败**：允许一定的执行失败率，保证执行准确性
- **步进执行**：脚本执行是步进式的，每步必须成功才能继续下一步

### 2. 智能等待机制

#### 辅助模式（Auxiliary Mode）
- **等待策略**：无限等待，在后台持续识别
- **应用场景**：适合需要人工介入或等待不确定事件的场景
- **实现方式**：循环检测目标元素，直到找到为止
- **界面状态**：保持界面可见，不最小化
- **避让机制**：检测到Active模式运行时，跳过动作执行但继续识别，不会被终止

#### 主动模式（Active Mode）
- **等待策略**：步进式执行，基于录制时的操作间隔
- **初始等待**：`开始识别时间 = 录制时的操作间隔`（精确还原用户操作节奏）
- **持续识别**：首次识别失败后，每隔1秒重新识别，直到成功
- **执行原则**：必须识别成功才能继续下一步，确保脚本的准确执行
- **重试机制理念**：UI响应通常不会立即跟上用户点击速度，持续识别是在等待界面状态变化（如加载完成、动画结束、遮挡消失等）
- **界面状态**：执行时最小化界面，完成后恢复
- **优先级**：拥有鼠标键盘控制权的最高优先级

#### 执行模式架构
- **独立线程**：Active和Auxiliary在两个独立的执行线程中运行，互不干扰
- **共享状态**：通过原子变量（AtomicBool）实现轻量级协调
- **控制权管理**：同一时刻只有一个模式拥有鼠标键盘控制权
- **避让原则**：Auxiliary检测到Active运行时主动避让，但不终止执行

### 3. 脚本处理流程

#### 录制阶段
1. **持续缓存机制**：以10FPS持续截取屏幕，存入循环缓冲区（最多15帧）
2. **操作记录**：记录每个用户操作（类型、坐标、时间戳）
3. **间隔计算**：自动计算相邻操作之间的时间间隔，用于执行时的等待和重试
4. **点击前图像获取**：当用户点击时，从缓冲区获取点击前第3帧（约0.3秒前）的图像
5. **避免鼠标干扰**：使用点击前的图像确保不包含鼠标指针和点击效果

#### 上传处理阶段（关键流程 - 请仔细理解）

##### 分辨率标准化详细说明
1. **录制时的原始截图处理**
   - 用户录制时，无论其屏幕分辨率是多少（1920×1080、2560×1440、3840×2160等）
   - 客户端会将每个操作对应的原始截图统一缩放为**768×768分辨率**
   - **重要**：这个768×768的图像是后续所有处理的基础，包括AI识别和BBOX裁切
   - 同时记录原始屏幕分辨率，用于执行时的比例计算

2. **上传到服务器的数据**
   - **标准化截图**：768×768分辨率的图像（不是原始分辨率）
   - **操作序列**：完整的操作信息
     - 操作类型（点击、双击、右键、拖拽、输入等）
     - 操作坐标（基于原始分辨率的坐标）
     - 时间戳（精确到毫秒）
     - 操作间隔（与上一操作的时间差）
     - 额外参数（如输入的文本内容、拖拽终点等）
   - **元数据**：原始屏幕分辨率、设备信息、录制时间等

3. **AI识别处理（服务器端）**
   - AI服务接收的是**768×768的标准化图像**
   - AI在这个768×768图像上识别所有可点击元素
   - 返回的BBOX坐标是**基于768×768图像的坐标**
   - 例如：如果一个按钮在768×768图像中的位置是(100, 200, 150, 50)，这就是返回的BBOX

4. **BBOX信息的关键理解**
   - **BBOX坐标系**：完全基于768×768的标准化图像，与原始分辨率无关
   - **BBOX用途**：用于在768×768图像上裁切出被点击的元素
   - **强制包含规则**：AI识别时确保被点击元素的BBOX包含鼠标点击坐标（转换到768×768坐标系后的坐标）

5. **服务器存储内容**
   - 768×768标准化图片（唯一的图像版本）
   - BBOX信息（基于768×768的坐标）
   - 操作序列（完整的操作信息）
     - 所有操作类型和参数
     - 原始分辨率下的坐标
     - 时间戳和间隔信息
   - 元数据信息
     - 原始分辨率（用于执行时的适配）
     - 脚本名称、描述
     - 创建时间、作者信息等

#### 本地脚本生成（BBOX裁切流程）

##### 关键裁切流程 - 必须理解

###### 整体数据流架构
```
录制 → 上传768图像 → AI识别 → 返回BBOX → 【客户端裁切】 → 存储元素图像 → 执行时使用
```

1. **接收服务器数据**
   - 客户端接收完整数据包：
     - 768×768标准化图像（非原始分辨率）
     - BBOX信息（基于768×768坐标系）
     - 操作序列（包含所有操作的完整信息）
     - 元数据（原始分辨率、脚本信息等）
   - **注意**：客户端不会收到原始分辨率的图像，只有768×768的版本

2. **BBOX裁切操作（客户端处理阶段）**
   - **裁切时机**：在接收到服务器数据后立即进行，显示"正在生成脚本..."进度条时
   - **裁切基准**：在768×768的图像上进行裁切
   - **裁切坐标**：直接使用服务器返回的BBOX坐标
   - **裁切结果**：得到被点击元素的图像片段（作为后续匹配的模板）
   - 举例：如果BBOX是(100, 200, 150, 50)，就从768×768图像的(100, 200)位置裁切出150×50的区域
   
   **关键理解**：
   - 每个动作的BBOX裁切是独立的
   - 裁切后的元素图像才是真正的"模板"
   - 执行时不需要再处理BBOX，直接使用裁切好的元素图像

3. **本地存储结构**
   
   **方案A - 替换存储（推荐）**：
   ```
   RecordedAction {
       image_base64: String,    // 存储裁切后的元素图像（而不是768完整图像）
       bbox: Option<BBox>,      // 可选，仅用于调试/显示目的
       action_type: String,
       x: f64, y: f64,         // 原始点击坐标
       ...
   }
   ```
   
   **方案B - 双图像存储**：
   ```
   RecordedAction {
       full_image_base64: String,    // 768×768完整图像（可选，用于调试）
       element_image_base64: String, // 裁切后的元素图像（执行时使用）
       bbox: BBox,                   // 保留BBOX信息
       ...
   }
   ```

4. **为什么这样设计**
   - **统一标准**：所有处理都基于768×768，避免分辨率差异带来的复杂性
   - **服务器优化**：只存储一个版本的图像，节省存储空间
   - **AI效率**：AI模型只需要处理固定尺寸的输入
   - **裁切准确性**：BBOX和图像使用相同坐标系，确保裁切精确
   - **执行效率**：执行时直接使用裁切好的元素图像，无需重复裁切
   - **存储优化**：元素图像通常远小于768×768，节省存储空间

#### 执行阶段
1. 读取本地裁切的元素图像作为模板
2. 根据智能等待机制等待元素出现
3. 在当前屏幕中搜索匹配的元素
4. 如果首次识别失败，持续循环识别直到成功（每秒一次）
5. 找到元素后执行对应操作
6. 记录实际执行时间供下一步参考

#### 云同步流程
1. 从服务器获取完整数据：
   - 768×768标准化图片（非原始分辨率）
   - BBOX信息（基于768×768坐标系）
   - 操作序列（包含操作类型、坐标、时间戳等）
   - 脚本元数据（名称、描述、原始分辨率信息等）
2. 接收设备本地进行裁切处理
3. 生成本地可执行的脚本数据
4. 缓存裁切结果以优化后续执行

#### 脚本分享策略
1. **分享模式**：生成指向服务器脚本ID的分享链接
2. **存储优化**：服务器仅存储一份标准化数据（768×768图片、BBOX信息、操作序列等），多用户共享
3. **首次使用**：用户访问分享链接时，下载标准化数据并本地裁切
4. **本地缓存**：裁切结果缓存在本地，避免重复处理
5. **更新机制**：脚本更新时，清除本地缓存重新生成

## 安全原则

### 重放攻击防护
- **实现方式**：
  - 每个API请求添加时间戳
  - 使用nonce（一次性随机数）
  - 请求签名验证
- **时效性**：请求时间戳超过5分钟自动失效

## 性能原则

### 需要可视化反馈的环节
1. **录制上传**：显示"正在上传录制数据..."
2. **AI处理**：显示"正在识别界面元素..."
3. **脚本生成**：显示"正在生成脚本..."
4. **脚本执行**：显示"正在执行第 X/Y 步..."
5. **元素搜索**：显示"正在查找目标元素..."（仅在超过3秒时显示）
6. **云端同步**：显示"正在同步数据..."

## 离线模式设计理念（重要 - 必须这样设计）

### 核心设计原则
**这是正确且必要的设计决策**，离线模式的功能划分基于技术架构和用户体验的深度考虑。

### 为什么脚本可以离线执行
1. **技术基础**：执行只需要本地的模板图像和图像匹配算法
2. **数据完整性**：已下载的脚本包含了所有执行所需的数据
   - 裁切后的元素图像（模板）
   - 操作序列和时间戳
   - 执行参数
3. **无需AI**：执行阶段使用传统图像匹配，不依赖AI服务
4. **用户价值**：保证用户在网络不稳定时仍能使用已有脚本

### 为什么录制必须禁用（关键理解）
1. **录制的本质目的**：
   - 录制不是为了录制本身，而是为了生成可执行的脚本
   - 没有AI识别，录制的数据无法转化为脚本，失去意义

2. **技术依赖链**：
   - 录制 → 上传768×768图像 → AI识别 → 返回BBOX → 本地裁切 → 生成脚本
   - 这个链条中AI识别是不可或缺的环节
   - 离线状态下链条断裂，无法完成整个流程

3. **用户体验考虑**：
   - **错误的设计**：允许离线录制，用户录制完成后发现无法生成脚本，体验极差
   - **正确的设计**：直接禁用录制，明确告知用户需要联网，避免无效操作
   - **心理预期管理**：提前阻止而非事后失败，符合用户心理预期

4. **数据一致性**：
   - 避免产生"半成品"数据（只有录制数据，没有AI识别结果）
   - 防止离线录制的数据与在线处理产生时序问题
   - 确保所有脚本都经过完整的处理流程

### 设计合理性论证

#### 对比错误方案
**错误方案**：允许离线录制，等联网后再处理
- **问题1**：用户录制后可能忘记上传
- **问题2**：录制时的UI状态可能已经改变，影响AI识别
- **问题3**：增加客户端复杂度，需要管理待上传队列
- **问题4**：用户体验断裂，录制和使用之间有巨大延迟

#### 我们的正确方案
**正确方案**：离线禁用录制，在线才能录制
- **优势1**：清晰的功能边界，用户理解简单
- **优势2**：保证录制到使用的流程连贯性
- **优势3**：避免产生无法使用的数据
- **优势4**：技术实现简洁，减少边缘情况

### 支持的离线功能（设计合理）
- **脚本执行**：已下载到本地的脚本包含所有必要数据
- **本地登录**：使用缓存的认证信息
- **历史查看**：浏览本地已有的脚本列表
- **设置调整**：修改本地配置参数

### 不支持的离线功能（必须在线）
- **录制功能**：需要AI识别服务
- **脚本生成**：依赖服务器端AI处理
- **云端同步**：需要网络传输
- **脚本分享**：需要访问服务器
- **新脚本获取**：需要从服务器下载

### 用户提示设计
离线状态下：
- 录制按钮显示为灰色禁用状态
- 鼠标悬停显示："录制需要网络连接以进行AI识别"
- 提供"为什么需要网络？"的帮助链接
- 清晰传达：这是设计决策，不是bug

## 开发优先级调整

### 不需要开发的功能
- 设备心跳监控
- 多策略元素定位（仅使用图像识别）
- 2FA认证（后续考虑）
- 撤销系统
- 过多的键盘快捷键（F9结束录制已足够）
- AI调用缓存（执行时不调用AI）
- 支付流程（暂不开发）

### 必须开发的功能
- 智能等待机制（基于录制时间戳）
- 重放攻击防护（API请求签名和时间戳验证）
- 可视化进度反馈（关键操作的状态显示）
- 步进执行机制（持续识别直到成功，保持检测阈值不变）

### 暂缓开发的功能（标记待实现）
- **脚本分享更新机制**：当作者更新脚本后，其他用户如何获取更新
- **版本管理系统**：脚本的版本控制和更新通知

## 脚本数据架构说明

### 核心理念
**不需要标准的脚本文件**，脚本是由多个数据组件动态组装而成的逻辑实体。

### 数据组成
1. **操作序列**：操作类型、坐标、时间戳（存储在数据库）
2. **标准化截图**：768×768分辨率的截图（服务器存储）
3. **BBOX信息**：基于768×768图像的AI识别元素边界（服务器存储）
4. **元素图像**：本地裁切的元素图片（本地缓存）
5. **脚本元数据**：名称、描述、创建时间、原始分辨率等（数据库）

### 存储策略
- **服务器端**：768×768标准图片 + BBOX信息 + 操作序列 + 元数据（含原始分辨率）
- **客户端**：裁切后的元素图像（缓存） + 执行所需数据
- **分享场景**：通过脚本ID指向服务器数据，按需下载和裁切

### 分辨率适配与图像识别策略

#### 核心理解
- **模板来源**：从768×768标准化图像上裁切的元素图像
- **执行环境**：用户实际屏幕分辨率可能与录制者不同
- **核心挑战**：同一UI元素在不同分辨率下尺寸不同，需要鲁棒的识别方案

#### 多级图像识别降级策略（鲁棒性设计）

##### 设计理念
使用多种图像识别方式不是违背"纯图像识别"原则，而是为了提高图像识别的鲁棒性。所有方法都是基于图像内容的识别，只是算法复杂度和适用场景不同。

##### 第一级：直接模板匹配（最快速）
- **适用场景**：执行者与录制者分辨率相近或UI缩放比例一致
- **算法选择**：OpenCV标准模板匹配（cv2.matchTemplate）
- **检测阈值**：0.85（高置信度要求）
- **执行逻辑**：
  1. 根据分辨率比例缩放模板图像
  2. 在当前屏幕中进行模板匹配
  3. 阈值≥0.85时认为匹配成功
- **优势**：速度快，适合大多数标准场景

##### 第二级：ORB特征点匹配（平衡速度与精度）
- **触发条件**：第一级匹配失败或置信度<0.85
- **算法特点**：二进制特征描述符，对缩放和旋转鲁棒
- **检测阈值管理**：
  - **特征点数量阈值**：至少检测到15个匹配的特征点
  - **匹配质量阈值**：Lowe's ratio test，比率<0.7
  - **几何验证**：RANSAC验证，内点比例>60%
- **保持质量原则**：即使是降级方案，仍保持严格的匹配标准
- **执行逻辑**：
  1. 提取模板和屏幕图像的ORB特征
  2. 进行特征匹配和几何验证
  3. 满足所有阈值条件才认为匹配成功

##### 第三级：SIFT特征点匹配（最高精度）
- **触发条件**：ORB匹配失败或置信度不足
- **算法特点**：尺度不变特征，最强的鲁棒性
- **检测阈值管理**：
  - **特征点数量阈值**：至少检测到20个匹配的特征点
  - **匹配质量阈值**：Lowe's ratio test，比率<0.65（更严格）
  - **几何验证**：RANSAC验证，内点比例>70%（更严格）
  - **单应性矩阵验证**：确保变换矩阵合理
- **质量保证**：虽然是最后的降级方案，但使用最严格的验证标准
- **执行逻辑**：
  1. 提取SIFT特征（更耗时但更精确）
  2. 进行精确的特征匹配
  3. 多重验证确保匹配正确性

##### 第四级：多尺度模板匹配（特定场景兜底）
- **触发条件**：前三级都失败，但UI元素可能只是简单缩放
- **缩放范围**：基于分辨率比例±20%范围，步长5%
- **检测阈值**：每个尺度都要求≥0.80的匹配度
- **质量原则**：宁可识别失败，也不降低阈值到0.80以下
- **执行逻辑**：
  1. 计算可能的缩放比例范围
  2. 在每个尺度进行模板匹配
  3. 选择最高匹配度的结果（必须≥0.80）

##### 识别失败处理
- **原则坚持**：所有级别都保持高质量阈值，不因降级而降低标准
- **失败反馈**：明确告知用户"无法准确定位目标元素"
- **不妥协质量**：绝不通过降低阈值来强行"成功"
- **用户选择**：
  - 辅助模式：继续等待
  - 主动模式：报告失败，等待用户处理

### 优势
1. **存储优化**：服务器不存储重复的裁切图像
2. **灵活更新**：可以单独更新某个组件
3. **高效分享**：多人共享同一份服务器数据
4. **本地优化**：缓存机制减少重复裁切

## 关于AI调用说明
AI调用仅发生在录制上传阶段，服务器端处理后返回bbox信息（不返回裁切图像），由客户端根据bbox信息本地裁切，执行时使用本地裁切的图像进行匹配，不再调用AI服务。