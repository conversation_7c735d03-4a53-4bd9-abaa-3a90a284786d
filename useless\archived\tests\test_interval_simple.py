"""
Simple test to verify interval recording functionality.

This test verifies the core interval recording without full executor initialization.
"""

import sys
import time
import json
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from showforai.recorder.action_buffer import ActionBuffer
from showforai.script.script_model import Script
from showforai.processing.script_generator import ScriptGenerator
from showforai.executor.script_loader import Action


def test_interval_in_action_dataclass():
    """Test that Action dataclass properly handles delay_ms."""
    print("=" * 60)
    print("TESTING ACTION DATACLASS INTERVAL HANDLING")
    print("=" * 60)
    
    # Create actions with specific delays
    actions = [
        Action(
            sequence=1,
            type="click",
            timestamp=0,
            delay_ms=0  # First action
        ),
        Action(
            sequence=2,
            type="click",
            timestamp=500,
            delay_ms=500  # 500ms delay
        ),
        Action(
            sequence=3,
            type="type",
            timestamp=1500,
            delay_ms=1000,  # 1000ms delay
            text="Test"
        ),
    ]
    
    print("\nAction delay_ms values:")
    for action in actions:
        print(f"  Action {action.sequence}: type={action.type:10} delay_ms={action.delay_ms}ms")
    
    # Verify all have delay_ms attribute
    for action in actions:
        assert hasattr(action, 'delay_ms'), f"Action {action.sequence} missing delay_ms attribute"
        assert isinstance(action.delay_ms, int), f"Action {action.sequence} delay_ms not an integer"
    
    print("\n✅ Action dataclass properly handles delay_ms!")
    return True


def test_interval_preserved_in_dict():
    """Test that intervals are preserved when converting to dict."""
    print("\n" + "=" * 60)
    print("TESTING INTERVAL PRESERVATION IN DICT CONVERSION")
    print("=" * 60)
    
    buffer = ActionBuffer()
    
    # Add actions with precise timing
    base_time = 1000.0
    timings = [
        (base_time, "click", (100, 100)),
        (base_time + 0.250, "click", (200, 200)),  # 250ms delay
        (base_time + 0.750, "double_click", (300, 300)),  # 500ms delay
        (base_time + 1.500, "right_click", (400, 400)),  # 750ms delay
    ]
    
    for timestamp, action_type, pos in timings:
        if action_type == "click":
            buffer.add_click(pos[0], pos[1], timestamp)
        elif action_type == "double_click":
            buffer.add_double_click(pos[0], pos[1], timestamp)
        elif action_type == "right_click":
            buffer.add_right_click(pos[0], pos[1], timestamp)
    
    # Calculate delays
    buffer.calculate_delays()
    
    # Convert to dict
    actions_dict = buffer.to_dict(use_milliseconds=True)
    
    print("\nConverted actions with delay_ms:")
    expected_delays = [0, 250, 500, 750]
    for i, action_dict in enumerate(actions_dict):
        delay_ms = action_dict.get('delay_ms', -1)
        expected = expected_delays[i]
        status = "✅" if abs(delay_ms - expected) <= 1 else "❌"
        print(f"  Action {i}: delay_ms={delay_ms}ms (expected {expected}ms) {status}")
        
        # Verify delay_ms is in dict
        assert 'delay_ms' in action_dict, f"Action {i} dict missing delay_ms"
        # Allow 1ms tolerance for floating point precision
        assert abs(delay_ms - expected) <= 1, f"Action {i} delay incorrect"
    
    print("\n✅ Intervals are preserved in dict conversion!")
    return True


def test_interval_in_script_generation():
    """Test that script generation preserves intervals."""
    print("\n" + "=" * 60)
    print("TESTING INTERVAL IN SCRIPT GENERATION")
    print("=" * 60)
    
    # Create recording data with delays
    recording_data = {
        'metadata': {
            'version': '1.0',
            'session_id': 'test',
            'recorded_at': time.time()
        },
        'actions': [
            {
                'type': 'click',
                'delay_ms': 0,
                'position': {'x': 100, 'y': 100},
                'timestamp': 0,
                'sequence': 1
            },
            {
                'type': 'click',
                'delay_ms': 333,
                'position': {'x': 200, 'y': 200},
                'timestamp': 333,
                'sequence': 2
            },
            {
                'type': 'double_click',
                'delay_ms': 667,
                'position': {'x': 300, 'y': 300},
                'timestamp': 1000,
                'sequence': 3
            },
        ]
    }
    
    # Generate script
    generator = ScriptGenerator()
    script = generator.generate_script(recording_data)
    
    print("\nGenerated script actions:")
    for i, action in enumerate(script['actions']):
        delay_ms = action.get('delay_ms', -1)
        expected = recording_data['actions'][i]['delay_ms']
        status = "✅" if delay_ms == expected else "❌"
        print(f"  Action {i}: delay_ms={delay_ms}ms (expected {expected}ms) {status}")
        
        # Verify delay is preserved
        assert 'delay_ms' in action, f"Generated action {i} missing delay_ms"
        assert action['delay_ms'] == expected, f"Generated action {i} delay not preserved"
    
    print("\n✅ Script generation preserves intervals!")
    return True


def test_interval_in_cloud_sync():
    """Test that intervals would be preserved in cloud sync."""
    print("\n" + "=" * 60)
    print("TESTING INTERVAL PRESERVATION FOR CLOUD SYNC")
    print("=" * 60)
    
    # Create a script with actions containing delays
    original_script = Script(
        name="Test Script",
        description="Testing interval preservation",
        actions=[
            {
                'type': 'click',
                'delay_ms': 0,
                'position': {'x': 100, 'y': 100},
                'sequence': 1
            },
            {
                'type': 'click',
                'delay_ms': 123,
                'position': {'x': 200, 'y': 200},
                'sequence': 2
            },
            {
                'type': 'type',
                'delay_ms': 456,
                'text': 'Test text',
                'sequence': 3
            },
        ]
    )
    
    # Serialize to dict (what gets sent to cloud)
    serialized = original_script.to_dict()
    
    print("\nSerialized actions (for cloud upload):")
    for i, action in enumerate(serialized['actions']):
        delay_ms = action.get('delay_ms', -1)
        print(f"  Action {i}: delay_ms={delay_ms}ms")
        assert 'delay_ms' in action, f"Serialized action {i} missing delay_ms"
    
    # Deserialize (what happens when pulling from cloud)
    restored_script = Script.from_dict(serialized)
    
    print("\nRestored actions (after cloud download):")
    for i, action in enumerate(restored_script.actions):
        delay_ms = action.get('delay_ms', -1)
        expected = original_script.actions[i]['delay_ms']
        status = "✅" if delay_ms == expected else "❌"
        print(f"  Action {i}: delay_ms={delay_ms}ms (expected {expected}ms) {status}")
        assert action['delay_ms'] == expected, f"Restored action {i} delay not preserved"
    
    print("\n✅ Intervals would be preserved through cloud sync!")
    return True


def test_millisecond_precision():
    """Test that millisecond precision is maintained."""
    print("\n" + "=" * 60)
    print("TESTING MILLISECOND PRECISION")
    print("=" * 60)
    
    buffer = ActionBuffer()
    
    # Test sub-second precision
    base = 1000.0
    precise_timings = [
        (base, 0),
        (base + 0.001, 1),  # 1ms
        (base + 0.011, 10),  # 10ms
        (base + 0.111, 100),  # 100ms
        (base + 0.6111, 500),  # 500ms
        (base + 1.6111, 1000),  # 1000ms
    ]
    
    for timestamp, _ in precise_timings:
        buffer.add_click(100, 100, timestamp)
    
    buffer.calculate_delays()
    
    print("\nMillisecond precision test:")
    for i, action in enumerate(buffer.actions):
        expected = precise_timings[i][1]
        actual = action.delay_ms
        diff = abs(actual - expected)
        status = "✅" if diff <= 1 else "❌"  # Allow 1ms tolerance
        print(f"  Action {i}: Expected={expected:4}ms, Actual={actual:4}ms, Diff={diff}ms {status}")
        
        # Allow 1ms tolerance for floating point precision
        assert diff <= 1, f"Precision lost: expected {expected}ms, got {actual}ms"
    
    print("\n✅ Millisecond precision is maintained!")
    return True


def main():
    """Run all simple interval tests."""
    print("=" * 60)
    print("SIMPLE INTERVAL RECORDING TEST SUITE")
    print("=" * 60)
    
    try:
        # Run all tests
        test_interval_in_action_dataclass()
        test_interval_preserved_in_dict()
        test_interval_in_script_generation()
        test_interval_in_cloud_sync()
        test_millisecond_precision()
        
        print("\n" + "=" * 60)
        print("✅ ALL SIMPLE TESTS PASSED!")
        print("=" * 60)
        print("\nVerified Functionality:")
        print("✅ Action dataclass has delay_ms field")
        print("✅ ActionBuffer calculates delays correctly")
        print("✅ Delays are preserved in dict conversion")
        print("✅ Script generation preserves delays")
        print("✅ Cloud sync would preserve delays")
        print("✅ Millisecond precision is maintained")
        print("\n🎉 INTERVAL RECORDING IS FULLY FUNCTIONAL!")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()